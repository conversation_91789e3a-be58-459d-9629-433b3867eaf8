package com.sigmob.ad.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.config.api.RtbApiConfig;
import com.sigmob.ad.core.config.headerbidding.HeaderBiddingConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.SspPriceStrategy;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.exception.SspEnterException;
import com.sigmob.ad.core.network.cnaaip.RegionType;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.publishing.app.AppRecommendation;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.AppInfoDao;
import com.sigmob.ad.dao.model.AppRespPriceConfig;
import com.sigmob.ad.service.biz.FilterConditions;
import com.sigmob.sigdsp.pb.App;
import com.sigmob.sigdsp.pb.BidRequest;
import com.sigmob.sigdsp.pb.Version;
import com.twofishes.config.manager.ConfigManager;

import java.util.*;

import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class AppService {

  private static final Mono<Optional<Integer>> EMPTY_APP_REQ_FLOOR_CONFIG =
      Mono.just(Optional.empty());

  private static final Mono<Optional<Map<String, AppRespPriceConfig>>> EMPTY_APP_RESP_PRICE_CONFIG =
      Mono.just(Optional.empty());

  private static final Mono<Map<String, AppRespPriceConfig>> EMPTY_AD_RESP_PRICE_CONFIG =
      Mono.just(Collections.emptyMap());

  private static final Mono<Optional<AppRespPriceConfig>> EMPTY_HB_AD_RESP_PRICE_CONFIG =
      Mono.just(Optional.empty());

  private final FilterConditions filterConditions;
  private final AppInfoDao appInfoDao;

  /** 查询应用信息 */
  public Mono<Optional<AppInfo>> getAppInfo(int id) {
    return appInfoDao.getAppInfo(id);
  }

  public Mono<Boolean> isSupportRecommendationReset(BidRequest bidRequest, Version sdkVersion) {
    App app = bidRequest.getApp();
    String appId = app.getAppId();

    if (bidRequest.hasUser() && bidRequest.getUser().getDisablePersonalizedRecommendation()) {
      return appInfoDao
          .getAppRecommendation(appId)
          .flatMap(
              opt -> {
                if (opt.isEmpty()) {
                  return Mono.just(false);
                }
                List<AppRecommendation.Rule> list = opt.get();
                if (CollectionUtils.isEmpty(list)) {
                  return Mono.just(false);
                }

                String versionToString = DeviceUtil.versionToString(sdkVersion);
                String appVersionToString = app.getAppVersion().getVersionStr();

                for (AppRecommendation.Rule rule : list) {
                  String appVersion = rule.getAppVersion();
                  if (StringUtils.isNotEmpty(appVersion)) {
                    if (!appVersion.equals(appVersionToString)) {
                      continue;
                    }
                  }
                  String sdkVersionConfig = rule.getSdkVersion();
                  if (StringUtils.isNotEmpty(sdkVersionConfig)) {
                    if (!sdkVersionConfig.equals(versionToString)) {
                      continue;
                    }
                  }
                  return Mono.just(true);
                }
                return Mono.just(false);
              })
          .onErrorResume(e -> Mono.just(false));
    }
    return Mono.just(false);
  }

  /**
   * 查找api流量对应sigmob媒体信息
   *
   * @param developerId
   * @param appId
   * @param osType
   * @param appBundle
   * @param requestFlowType
   * @return
   */
  public Mono<AppInfo> getApiSigmobAppInfo(
      Long developerId,
      int appId,
      int osType,
      String appBundle,
      RequestFlowType requestFlowType,
      String requestIp) {
    return getAppInfo(appId)
        .map(
            appInfoOptional -> {
              if (appInfoOptional.isEmpty()) {
                String errorMsg = "No such application for appid:" + appId;
                LogUtil.localWarn("rtbApiRequestToBidRequest:{}", errorMsg);
                throw Exceptions.propagate(
                    new SspEnterException(ErrorCode.REQUEST_ERROR_NO_SUCH_APP, errorMsg));
              }
              AppInfo appInfo = appInfoOptional.get();

              if (!developerId.equals(appInfo.getDeveloperId())) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.THIRDPARTY_ADX.NO_DEVELOPER_MAPPING.getCode(),
                        "config developerId("
                            + developerId
                            + ") not match app developerId("
                            + appInfo.getDeveloperId()
                            + ")"));
              }

              // 如果运营在中控关闭了app，则禁止访问sigmob，包括实时api
              if (appInfo.isSigmobAdClose()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_APP_CLOSED_BY_OPERATION,
                        "app(" + appId + ") has been closed by operation staff!"));
              }

              // 媒体自己关闭了app
              /*if (appInfo.isClosed()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_APP_IS_CLOSED, "app(" + appId + ") has closed!"));
              }*/

              var appOsType = appInfo.getOsType();
              if (null != appOsType && !appOsType.equals(osType)) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_OS_TYPE_NOT_MATCH_APP_TYPE,
                        "device osType is not match app's osType(" + appOsType + ")!"));
              }

              // 暂时只有标准api才校验packageName
              // http://wiki.sigmob.cn/pages/viewpage.action?pageId=41944290
              //              if (RequestFlowType.STANDARD.equals(requestFlowType)) {
              //                var packageName = appInfo.getPackageName();
              //                if (null != packageName && !packageName.equals(appBundle)) {
              //                  throw Exceptions.propagate(
              //                      new SspEnterException(
              //                          ErrorCode.REQUEST_BUNDLE_PACKAGE_NAME_NOT_MATCH,
              //                          "request media.bundle(" + appBundle + ") invalid"));
              //                }
              //              }

              if (filterConditions.filterByRegion(appId, 0, requestIp, RegionType.city)) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_REGION_BANNED,
                        "request ip(" + requestIp + ") region banned"));
              }

              return appInfo;
            });
  }

  public Mono<Optional<AppInfo>> getApiSigmobAppInfoOptional(
      Long developerId,
      int appId,
      int osType,
      String appBundle,
      RequestFlowType requestFlowType,
      String requestIp) {
    return getAppInfo(appId)
        .map(
            appInfoOptional -> {
              if (appInfoOptional.isEmpty()) {
                return appInfoOptional;
              }
              AppInfo appInfo = appInfoOptional.get();

              if (!developerId.equals(appInfo.getDeveloperId())) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.THIRDPARTY_ADX.NO_DEVELOPER_MAPPING.getCode(),
                        "config developerId("
                            + developerId
                            + ") not match app developerId("
                            + appInfo.getDeveloperId()
                            + ")"));
              }

              // 如果运营在中控关闭了app，则禁止访问sigmob，包括实时api
              if (appInfo.isSigmobAdClose()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_APP_CLOSED_BY_OPERATION,
                        "app(" + appId + ") has been closed by operation staff!"));
              }

              // 媒体自己关闭了app
              /*if (appInfo.isClosed()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_APP_IS_CLOSED, "app(" + appId + ") has closed!"));
              }*/

              var appOsType = appInfo.getOsType();
              if (null != appOsType && !appOsType.equals(osType)) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_OS_TYPE_NOT_MATCH_APP_TYPE,
                        "device osType is not match app's osType(" + appOsType + ")!"));
              }

              // 暂时只有标准api才校验packageName
              // http://wiki.sigmob.cn/pages/viewpage.action?pageId=41944290
              if (RequestFlowType.STANDARD.equals(requestFlowType)) {
                var packageName = appInfo.getPackageName();
                if (null != packageName && !packageName.equals(appBundle)) {
                  throw Exceptions.propagate(
                      new SspEnterException(
                          ErrorCode.REQUEST_BUNDLE_PACKAGE_NAME_NOT_MATCH,
                          "request media.bundle(" + appBundle + ") invalid"));
                }
              }

              if (filterConditions.filterByRegion(appId, 0, requestIp, RegionType.city)) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_REGION_BANNED,
                        "request ip(" + requestIp + ") region banned"));
              }

              return appInfoOptional;
            });
  }

  public Mono<Optional<Integer>> getAppReqFloorConfig(int appId, String adSlotId) {
    return appInfoDao
        .getAppReqFloorConfig(appId, adSlotId)
        .onErrorResume(
            e -> {
              if (!(e instanceof SigmobException)) {
                LogUtil.localError(
                    "getAppReqFloorConfig(appId:{}, adSlotId:{}) error", appId, adSlotId, e);
              }
              return EMPTY_APP_REQ_FLOOR_CONFIG;
            });
  }

  public Mono<Map<String, AppRespPriceConfig>> getAdRespPriceConfig(
      int requestFlowType,
      boolean isHb,
      String developerId,
      int appId,
      String adSlotId,
      List<String> dspIdList) {

    if (requestFlowType != RequestFlowType.HUAWEI.getCode()
        && requestFlowType != RequestFlowType.OPPO.getCode()
        && requestFlowType != RequestFlowType.STANDARD.getCode()
        && requestFlowType != RequestFlowType.GROMORE.getCode()
        && requestFlowType != RequestFlowType.VIVO.getCode()
        && (requestFlowType == RequestFlowType.SDK.getCode() && !isHb)) {
      return EMPTY_AD_RESP_PRICE_CONFIG;
    }

    List<Mono<Optional<Map<String, AppRespPriceConfig>>>> priceConfigMonoList =
        Lists.newArrayListWithCapacity(dspIdList.size() * 2);
    for (String dspId : dspIdList) {
      priceConfigMonoList.add(
          appInfoDao
              .getAppRespPriceConfig(adSlotId, dspId)
              .onErrorResume(
                  e -> {
                    if (!(e instanceof SigmobException)) {
                      LogUtil.localError(
                          "getAppRespPriceConfig(adSlotId:{}, dspId:{}) error", adSlotId, dspId, e);
                    }
                    return EMPTY_APP_RESP_PRICE_CONFIG;
                  }));
      priceConfigMonoList.add(
          appInfoDao
              .getAppRespPriceConfig(Integer.toString(appId), dspId)
              .onErrorResume(
                  e -> {
                    if (!(e instanceof SigmobException)) {
                      LogUtil.localError(
                          "getAppRespPriceConfig(appId:{}, dspId:{}) error", appId, dspId, e);
                    }
                    return EMPTY_APP_RESP_PRICE_CONFIG;
                  }));
    }

    return Mono.zip(
        priceConfigMonoList,
        result -> {
          Map<String, AppRespPriceConfig> adPriceConfigMap =
              Maps.newHashMapWithExpectedSize(dspIdList.size());
          Float baseDefaultConfig = Constants.FLOAT_VALUE_ZERO;

          if (requestFlowType == RequestFlowType.STANDARD.getCode()) {
            RtbApiConfig rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
            var standardApiConfigOptional = rtbApiConfig.getStandardApiConfig(developerId);
            baseDefaultConfig =
                standardApiConfigOptional
                    .map(RtbApiConfig.ApiConfig::getAdxExtraBidWeight)
                    .orElse(Constants.FLOAT_VALUE_ZERO);
          } else if (requestFlowType == RequestFlowType.HUAWEI.getCode()) {
            RtbApiConfig rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
            baseDefaultConfig =
                Optional.ofNullable(rtbApiConfig.getConfigs())
                    .map(configs -> configs.get(RequestFlowType.HUAWEI.getName()))
                    .map(
                        apiConfig -> {
                          var adxSpecialExtraBidWeight = apiConfig.getAdxSpecialExtraBidWeight();
                          if (null != adxSpecialExtraBidWeight
                              && !adxSpecialExtraBidWeight.equals(Constants.FLOAT_VALUE_ZERO)) {
                            return adxSpecialExtraBidWeight;
                          } else {
                            return apiConfig.getAdxExtraBidWeight();
                          }
                        })
                    .orElse(Constants.FLOAT_VALUE_ZERO);
          } else if (requestFlowType == RequestFlowType.OPPO.getCode()) {
            RtbApiConfig rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
            baseDefaultConfig =
                Optional.ofNullable(rtbApiConfig.getConfigs())
                    .map(configs -> configs.get(RequestFlowType.OPPO.getName()))
                    .map(RtbApiConfig.ApiConfig::getAdxExtraBidWeight)
                    .orElse(Constants.FLOAT_VALUE_ZERO);
          } else if (requestFlowType == RequestFlowType.SDK.getCode() || isHb) {
            HeaderBiddingConfig headerBiddingConfig = ConfigManager.get(HeaderBiddingConfig.class);
            baseDefaultConfig =
                Optional.ofNullable(headerBiddingConfig.getFirstPriceAdxExtraBidWeight()).orElse(0f)
                    * -1;
          }
          for (int i = 0; i < dspIdList.size(); i++) {
            String dspId = dspIdList.get(i);

            var slotPriceConfig = (Optional<Map<String, AppRespPriceConfig>>) result[i];
            var appPriceConfig = (Optional<Map<String, AppRespPriceConfig>>) result[i + 1];
            AppRespPriceConfig slotDefaultConfig = null;
            if (slotPriceConfig.isPresent()) {
              var slotDspPriceConfig = slotPriceConfig.get().get(dspId);
              if (null != slotDspPriceConfig) {
                adPriceConfigMap.put(dspId, slotDspPriceConfig);
                continue;
              }
              slotDefaultConfig = slotPriceConfig.get().get("0");
            }

            AppRespPriceConfig appDefaultConfig = null;
            if (appPriceConfig.isPresent()) {
              var appDspPriceConfig = appPriceConfig.get().get(dspId);
              if (null != appDspPriceConfig) {
                adPriceConfigMap.put(dspId, appDspPriceConfig);
                continue;
              }
              appDefaultConfig = appPriceConfig.get().get("0");
            }
            if (null != slotDefaultConfig) {
              adPriceConfigMap.put(dspId, slotDefaultConfig);
              continue;
            }

            if (null != appDefaultConfig) {
              adPriceConfigMap.put(dspId, appDefaultConfig);
              continue;
            }
            adPriceConfigMap.put(
                dspId,
                new AppRespPriceConfig(
                    SspPriceStrategy.FIX_FACTOR.getCode(),
                    Float.valueOf(baseDefaultConfig * 100).intValue()));
          }
          return adPriceConfigMap;
        });
  }

  /** 中控配置的sdk hb流量返回媒体价格策略 */
  public Mono<Optional<AppRespPriceConfig>> getHbAdRespPriceConfig(
      int requestFlowType, boolean isHb, int appId, String adSlotId) {
    if (requestFlowType != RequestFlowType.SDK.getCode() || !isHb) {
      return EMPTY_HB_AD_RESP_PRICE_CONFIG;
    }

    List<Mono<Optional<Map<String, AppRespPriceConfig>>>> priceConfigMonoList =
        Lists.newArrayListWithCapacity(2);
    priceConfigMonoList.add(
        appInfoDao
            .getAppRespPriceConfig(adSlotId, null)
            .onErrorResume(
                e -> {
                  if (!(e instanceof SigmobException)) {
                    LogUtil.localError("getAppRespPriceConfig(adSlotId:{}) error", adSlotId, e);
                  }
                  return EMPTY_APP_RESP_PRICE_CONFIG;
                }));
    priceConfigMonoList.add(
        appInfoDao
            .getAppRespPriceConfig(Integer.toString(appId), null)
            .onErrorResume(
                e -> {
                  if (!(e instanceof SigmobException)) {
                    LogUtil.localError("getAppRespPriceConfig(appId:{}) error", appId, e);
                  }
                  return EMPTY_APP_RESP_PRICE_CONFIG;
                }));

    return Mono.zip(
        priceConfigMonoList,
        result -> {
          var slotPriceConfig = (Optional<Map<String, AppRespPriceConfig>>) result[0];
          var appPriceConfig = (Optional<Map<String, AppRespPriceConfig>>) result[1];
          if (slotPriceConfig.isPresent()) {
            return Optional.ofNullable(slotPriceConfig.get().get("0"));
          }

          return appPriceConfig.map(
              stringAppRespPriceConfigMap -> stringAppRespPriceConfigMap.get("0"));

          //          HeaderBiddingConfig headerBiddingConfig =
          // ConfigManager.get(HeaderBiddingConfig.class);
          //          float baseDefaultConfig =
          //
          // Optional.ofNullable(headerBiddingConfig.getFirstPriceAdxExtraBidWeight()).orElse(0f)
          //                  * -1;
          //          return Optional.of(
          //              new AppRespPriceConfig(
          //                  SspPriceStrategy.FIX_FACTOR.getCode(),
          //                  Float.valueOf(baseDefaultConfig * 100).intValue()));
        });
  }

  /**
   * 获取要修改监测链接域名的APP ID
   *
   * @return
   */
  public Mono<Set<Integer>> getModifyTrackHostAppId() {
    return appInfoDao.getModifyTrackHostAppId();
  }
}
