package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.sigmob.ad.core.advertisement.AdType;
import com.sigmob.ad.core.advertisement.State;
import com.sigmob.ad.core.config.mediation.MediationAdapterConfig;
import com.sigmob.ad.core.config.mediation.SdkChannelOldVersionParam;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.constants.enums.SdkAdChannel;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.publishing.mediation.sdkstrategy.RegionType;
import com.sigmob.ad.core.publishing.mediation.sdkstrategy.StrategyBO;
import com.sigmob.ad.dao.SdkStrategySettingDao;
import com.sigmob.sigdsp.pb.BidRequest;
import com.sigmob.sigdsp.pb.Version;
import com.sigmob.ssp.pb.mediation.Strategy;
import com.sigmob.ssp.pb.mediation.management.AdSlotSdkChannelArgs;
import com.sigmob.ssp.pb.mediation.management.MediationRule;
import com.sigmob.ssp.pb.mediation.management.SdkChannel;
import com.sigmob.ssp.pb.mediation.management.SdkStrategySetting;
import com.sigmob.ssp.pb.sdksetting.SdkChannelSetting;
import com.twofishes.config.manager.ConfigManager;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class SdkStrategySettingService {

  //  private static final Version ADMOB_MULTI_SLOT_SDK_VERSION =
  //      Version.newBuilder().setMajor(2).setMinor(14).setMicro(0).build();
  //  private static final Version APPLOVIN_MULTI_SLOT_SDK_VERSION =
  //      Version.newBuilder().setMajor(2).setMinor(16).setMicro(0).build();
  //  private static final Version IRONSOURCE_MULTI_SLOT_SDK_VERSION =
  //      Version.newBuilder().setMajor(2).setMinor(16).setMicro(0).build();
  //  private static final Version UNITY_MULTI_SLOT_SDK_VERSION =
  //      Version.newBuilder().setMajor(2).setMinor(18).setMicro(0).build();

  /** SDK 2.15.2版本Android设备使用全屏适配器 */
  private static final String ANDROID_FULLSCREEN_ADAPTER_2_15_2 =
      "com.wind.windad.Adapter.SigmobRewardVideoAdAdapter";

  private final SdkStrategySettingDao sdkStrategySettingDao;
  private final SdkChannelSettingService sdkChannelSettingService;

  /**
   * @param appId
   * @return
   */
  public Optional<Strategy> generateOldAdMobStrategy(int appId) {
    //    AdmobOldVersionParam config = ConfigManager.get(AdmobOldVersionParam.class);
    //    return config.getAdmobStrategy(ai.getId());
    return ConfigManager.get(SdkChannelOldVersionParam.class)
        .getStrategy(SdkAdChannel.ADMOB.getId(), appId);
  }

  /**
   * @param appId
   * @return
   */
  public Optional<Strategy> generateOldApplovinStrategy(int appId) {
    //    ApplovinOldVersionParam config = ConfigManager.get(ApplovinOldVersionParam.class);
    //    return config.getApplovinStrategy(ai.getId());
    return ConfigManager.get(SdkChannelOldVersionParam.class)
        .getStrategy(SdkAdChannel.APPLOVIN.getId(), appId);
  }

  /**
   * @param appId
   * @return
   */
  private Optional<Strategy> generateOldIronSourceStrategy(int appId) {
    //    IronSourceOldVersionParam config = ConfigManager.get(IronSourceOldVersionParam.class);
    //    return config.getIronSourceStrategy(ai.getId());
    return ConfigManager.get(SdkChannelOldVersionParam.class)
        .getStrategy(SdkAdChannel.IRONSOURCE.getId(), appId);
  }

  /**
   * @param appId
   * @return
   */
  private Optional<Strategy> generateOldUnityAdsStrategy(int appId) {
    return ConfigManager.get(SdkChannelOldVersionParam.class)
        .getStrategy(SdkAdChannel.UNITYADS.getId(), appId);
  }

  /**
   * @param appId
   * @param adType
   * @param osType
   * @param sdkVersion
   * @param appChannelSettingOptional
   * @return
   */
  public Strategy.Builder generateDefaultSigmobStrategyBuilder(
      int appId,
      AdType adType,
      int osType,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional,
      BidRequest.Builder bidRequestBuilder) {
    Strategy.Builder sBuilder = Strategy.newBuilder();
    String sigmobChannelName = SdkAdChannel.SIGMOB.getName();
    sBuilder.setName(sigmobChannelName);
    sBuilder.setChannelId(String.valueOf(SdkAdChannel.SIGMOB.getId()));
    if (osType == OsType.ANDROID.getTypeNum()
        && DeviceUtil.compareVersion(Constants.SDK_VERSION_2_15_2, sdkVersion) == 0) {
      if (AdType.REWARDED_VIDEO.equals(adType)) {
        sBuilder.setAdapter(ANDROID_FULLSCREEN_ADAPTER_2_15_2);
      } else if (AdType.FULL_SCREEN_VIDEO.equals(adType)) {
        sBuilder.setAdapter(ANDROID_FULLSCREEN_ADAPTER_2_15_2);
      }
    } else {
      MediationAdapterConfig adapterConfig = ConfigManager.get(MediationAdapterConfig.class);
      String adapter = adapterConfig.getAdapterName(adType, osType, sigmobChannelName);
      if (DeviceUtil.sdkEnableRecommendation(
              sdkVersion,
              osType,
              !bidRequestBuilder.hasUser()
                  || !bidRequestBuilder.getUser().getDisablePersonalizedRecommendation())
          || adapterConfig.getChannelSupportRec(0, osType, sigmobChannelName)) {
        Boolean enableExtraCloseCallback =
            adapterConfig.getEnableExtraCloseCallback(appId, osType, sigmobChannelName);
        if (enableExtraCloseCallback != null) {
          sBuilder.setEnableExtraCloseCallback(enableExtraCloseCallback);
        }
        sBuilder
            .setAdapter(adapter.trim())
            .setAdExpireTime(
                sdkChannelSettingService.getChannelAdExpireTime(
                    SdkAdChannel.SIGMOB.getId(), appChannelSettingOptional));
      }
    }
    return sBuilder;
  }

  /**
   * @param appId
   * @param adType
   * @param osType
   * @param sdkVersion
   * @param appChannelSettingOptional
   * @return
   */
  public Strategy generateDefaultSigmobStrategy(
      int appId,
      AdType adType,
      int osType,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional,
      BidRequest.Builder bidRequestBuilder) {
    return this.generateDefaultSigmobStrategyBuilder(
            appId, adType, osType, sdkVersion, appChannelSettingOptional, bidRequestBuilder)
        .build();
  }

  /**
   * 配置是否可用
   *
   * @param settingOptional
   * @param regionName
   * @param ai
   * @param adType
   * @param adSlotId
   * @return
   */
  protected boolean settingCanUsed(
      Optional<SdkStrategySetting> settingOptional,
      String regionName,
      AppInfo ai,
      AdType adType,
      String adSlotId) {
    if (settingOptional.isEmpty()) {
      return false;
    }
    SdkStrategySetting setting = settingOptional.get();
    if (setting.getState() == State.CLOSED.getTypeNum()) {
      return false;
    }
    if (ai.getOsType() != setting.getOsType()) {
      return false;
    }
    if (adType.getTypeNum() != setting.getAdType()) {
      return false;
    }

    if (setting.getRegionType() == RegionType.INCLUDE.getTypeNum()) {
      if (setting.getRegionsMap().get(regionName) == null) {
        return false;
      }
    } else if (setting.getRegionType() == RegionType.EXCLUDE.getTypeNum()) {
      if (setting.getRegionsMap().get(regionName) != null) {
        return false;
      }
    }

    return setting.getSigmobAdSlotIdsMap().containsKey(adSlotId);
  }

  /**
   * @param channelArgsOptional
   * @param id
   * @return
   */
  protected Optional<SdkChannel> getChannelArgsFromSettingById(
      Optional<AdSlotSdkChannelArgs> channelArgsOptional, Long id) {
    if (channelArgsOptional.isPresent()) {
      for (SdkChannel channelArg : channelArgsOptional.get().getChannelArgsList()) {
        if (id == channelArg.getId()) {
          return Optional.ofNullable(channelArg);
        }
      }
    }
    return Optional.empty();
  }

  /**
   * @param strategyId
   * @param adSlotId
   * @param ids
   * @param adType
   * @param osType
   * @param ai
   * @param sdkVersion
   * @param appChannelSettingOptional
   * @return
   */
  protected Mono<Optional<List<Strategy>>> buildStrategy(
      long strategyId,
      String adSlotId,
      List<Long> ids,
      AdType adType,
      int osType,
      AppInfo ai,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional,
      BidRequest.Builder bidRequestBuilder) {
    Integer appId = ai.getId();
    return sdkStrategySettingDao
        .getAdSlotSdkChannelArgs(strategyId, adSlotId)
        .map(
            channelArgsOptional -> {
              MediationAdapterConfig adapterConfig =
                  ConfigManager.get(MediationAdapterConfig.class);

              List<Strategy> strategies = Lists.newArrayListWithExpectedSize(ids.size());
              for (Long id : ids) {
                Strategy.Builder builder = null;
                Optional<SdkChannel> channelArgOptional =
                    getChannelArgsFromSettingById(channelArgsOptional, id);
                if (channelArgOptional.isEmpty()) {
                  continue;
                }
                SdkChannel channelArg = channelArgOptional.get();
                int channelId = channelArg.getChannelId();
                if (channelId == SdkAdChannel.SIGMOB.getId()) {
                  builder =
                      this.generateDefaultSigmobStrategyBuilder(
                          appId,
                          adType,
                          osType,
                          sdkVersion,
                          appChannelSettingOptional,
                          bidRequestBuilder);
                } else if (channelId == SdkAdChannel.ADMOB.getId()
                    && (sdkVersion == null
                        || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_14_0)
                            < 0)) {
                  Optional<Strategy> strategyOptional = this.generateOldAdMobStrategy(appId);
                  if (strategyOptional.isPresent()) {
                    builder = strategyOptional.get().toBuilder();
                  }
                } else if (channelId == SdkAdChannel.APPLOVIN.getId()
                    && (sdkVersion == null
                        || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_16_0)
                            < 0)) {
                  Optional<Strategy> strategyOptional = this.generateOldApplovinStrategy(appId);
                  if (strategyOptional.isPresent()) {
                    builder = strategyOptional.get().toBuilder();
                  }
                } else if (channelId == SdkAdChannel.IRONSOURCE.getId()
                    && (sdkVersion == null
                        || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_16_0)
                            < 0)) {
                  Optional<Strategy> strategyOptional = this.generateOldIronSourceStrategy(appId);
                  if (strategyOptional.isPresent()) {
                    builder = strategyOptional.get().toBuilder();
                  }
                } else if (channelId == SdkAdChannel.UNITYADS.getId()
                    && (sdkVersion == null
                        || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_18_0)
                            < 0)) {
                  Optional<Strategy> strategyOptional = this.generateOldUnityAdsStrategy(appId);
                  if (strategyOptional.isPresent()) {
                    builder = strategyOptional.get().toBuilder();
                  }
                } else {
                  String channelName = channelArg.getName().toLowerCase();
                  String adapterName = adapterConfig.getAdapterName(adType, osType, channelName);
                  if (!Strings.isNullOrEmpty(adapterName)) {

                    if (DeviceUtil.sdkEnableRecommendation(
                            sdkVersion,
                            osType,
                            !bidRequestBuilder.hasUser()
                                || !bidRequestBuilder
                                    .getUser()
                                    .getDisablePersonalizedRecommendation())
                        || adapterConfig.getChannelSupportRec(0, osType, channelName)) {
                      Boolean enableExtraCloseCallback =
                          adapterConfig.getEnableExtraCloseCallback(appId, osType, channelName);
                      builder =
                          Strategy.newBuilder()
                              .setName(channelArg.getName())
                              .setChannelId(String.valueOf(channelArg.getChannelId()))
                              .setEnableExtraCloseCallback(
                                  enableExtraCloseCallback == null
                                      ? Boolean.FALSE
                                      : enableExtraCloseCallback)
                              .putAllOptions(channelArg.getOptionsMap())
                              .setAdapter(adapterName.trim())
                              .setAdExpireTime(
                                  sdkChannelSettingService.getChannelAdExpireTime(
                                      channelId, appChannelSettingOptional));
                    }
                  }
                }
                if (builder != null) {
                  strategies.add(builder.build());
                }
              }
              return Optional.of(strategies);
            });
  }

  /**
   * @param requestBuilder
   * @param ai
   * @param regionName
   * @param channelId
   * @param sdkVersion
   * @return
   */
  public Mono<Optional<Strategy>> buildStrategyByChannelId(
      BidRequest.Builder requestBuilder,
      AppInfo ai,
      String regionName,
      Integer channelId,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional,
      BidRequest.Builder bidRequestBuilder) {
    AdType adType = AdType.getType(requestBuilder.getSlots(0).getAdslotType(0));
    int osType = requestBuilder.getDevice().getOsType();
    Integer appId = ai.getId();

    if (channelId == SdkAdChannel.SIGMOB.getId()) {
      Strategy.Builder builder =
          generateDefaultSigmobStrategyBuilder(
              appId, adType, osType, sdkVersion, appChannelSettingOptional, bidRequestBuilder);
      return Mono.just(Optional.of(builder.build()));
    } else if (channelId == SdkAdChannel.ADMOB.getId()
        && (sdkVersion == null
            || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_14_0) < 0)) {
      return Mono.just(generateOldAdMobStrategy(appId));
    } else if (channelId == SdkAdChannel.APPLOVIN.getId()
        && (sdkVersion == null
            || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_16_0) < 0)) {
      return Mono.just(generateOldApplovinStrategy(appId));
    } else if (channelId == SdkAdChannel.IRONSOURCE.getId()
        && (sdkVersion == null
            || DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_16_0) < 0)) {
      return Mono.just(generateOldIronSourceStrategy(appId));
    }
    String adSlotId = requestBuilder.getSlots(0).getAdslotId();
    MediationAdapterConfig adapterConfig = ConfigManager.get(MediationAdapterConfig.class);

    return sdkStrategySettingDao
        .getStrategySettingIndex(ai.getGId())
        .flatMap(
            strategyIdsOptional -> {
              if (strategyIdsOptional.isEmpty()) {
                return Mono.just(Optional.empty());
              } else {
                return sdkStrategySettingDao
                    .getStrategySettingList(strategyIdsOptional.get())
                    .map(
                        settingsOptional ->
                            settingsOptional.orElseGet(() -> Lists.newArrayListWithCapacity(0)))
                    .flatMapMany(Flux::fromIterable)
                    .map(Optional::of)
                    .switchIfEmpty(Flux.just(Optional.empty()))
                    .filter(
                        settingOptional ->
                            settingCanUsed(settingOptional, regionName, ai, adType, adSlotId))
                    .flatMap(
                        settingOptional ->
                            sdkStrategySettingDao.getAdSlotSdkChannelArgs(
                                settingOptional.get().getId(), adSlotId))
                    .map(
                        channelArgsOptional -> {
                          if (channelArgsOptional.isPresent()) {
                            AdSlotSdkChannelArgs adSlotSdkChannelArgs = channelArgsOptional.get();
                            List<SdkChannel> channelArgsList =
                                adSlotSdkChannelArgs.getChannelArgsList();
                            if (!CollectionUtils.isEmpty(channelArgsList)) {
                              for (SdkChannel channelArg : channelArgsList) {
                                if (channelId == channelArg.getChannelId()) {
                                  String channelName = channelArg.getName().toLowerCase();
                                  String adapterName =
                                      adapterConfig.getAdapterName(adType, osType, channelName);
                                  if (!Strings.isNullOrEmpty(adapterName)) {
                                    if (DeviceUtil.sdkEnableRecommendation(
                                            sdkVersion,
                                            osType,
                                            !requestBuilder.hasUser()
                                                || !requestBuilder
                                                    .getUser()
                                                    .getDisablePersonalizedRecommendation())
                                        || adapterConfig.getChannelSupportRec(
                                            0, osType, channelName)) {

                                      Boolean enableExtraCloseCallback =
                                          adapterConfig.getEnableExtraCloseCallback(
                                              appId, osType, channelName);
                                      return Optional.of(
                                          Strategy.newBuilder()
                                              .setChannelId(
                                                  String.valueOf(channelArg.getChannelId()))
                                              .setEnableExtraCloseCallback(
                                                  enableExtraCloseCallback == null
                                                      ? Boolean.FALSE
                                                      : enableExtraCloseCallback)
                                              .setName(channelArg.getName())
                                              .putAllOptions(channelArg.getOptionsMap())
                                              .setAdapter(adapterName.trim())
                                              .setAdExpireTime(
                                                  sdkChannelSettingService.getChannelAdExpireTime(
                                                      channelId, appChannelSettingOptional))
                                              .build());
                                    }
                                  }
                                }
                              }
                            }
                          }
                          return Optional.<Strategy>empty();
                        })
                    .filter(Optional::isPresent)
                    .next()
                    .defaultIfEmpty(Optional.empty());
              }
            });
  }

  /**
   * @param udid
   * @return
   * @throws SigmobException
   */
  protected int parseUdidTailNumber(String udid) throws SigmobException {

    if (Strings.isNullOrEmpty(udid)) {
      throw new SigmobException(
          ErrorCode.REQUEST_ERROR_EMPTY_VALUE, "can not get channel,cause udid is null!");
    }

    if (udid.length() <= 6) {
      throw new SigmobException(
          ErrorCode.REQUEST_ERROR_INVALID_VALUE, "can not get channel,cause udid is invalid!");
    }
    String tnStr = udid.substring(udid.length() - 6);
    try {
      // udid后6位为16进制，转换出来后可能超过1000，因此再取一次取模，确保小于1000
      return Integer.parseInt(tnStr, 16) % 1000;
    } catch (NumberFormatException e) {
      throw new SigmobException(
          ErrorCode.REQUEST_ERROR_INVALID_VALUE, "parse user udid tail number error,udid:" + udid);
    }
  }

  /**
   * @param udid
   * @param strategyId
   * @return
   */
  protected Mono<Optional<MediationRule>> getMediationRule(String udid, long strategyId) {

    int userTailNumber = this.parseUdidTailNumber(udid);
    return sdkStrategySettingDao
        .getMediationRules(strategyId)
        .map(
            rulesOptional -> {
              if (rulesOptional.isPresent()) {
                for (MediationRule rule : rulesOptional.get().getRulesList()) {
                  if (userTailNumber >= rule.getLowerBound()
                      && userTailNumber <= rule.getUpperBound()) {
                    return Optional.of(rule);
                  }
                }
              }
              return Optional.empty();
            });
  }

  /**
   * @param settingOptional
   * @param ai
   * @param regionName
   * @param adSlotId
   * @param udid
   * @param requestBuilder
   * @param sdkVersion
   * @return
   */
  protected Mono<Optional<StrategyBO>> generateStrategy(
      Optional<SdkStrategySetting> settingOptional,
      AppInfo ai,
      String regionName,
      String adSlotId,
      String udid,
      BidRequest.Builder requestBuilder,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional) {
    AdType adType = AdType.getType(requestBuilder.getSlots(0).getAdslotType(0));

    Mono<Optional<StrategyBO>> emptyMono = Mono.just(Optional.empty());
    if (!settingCanUsed(settingOptional, regionName, ai, adType, adSlotId)) {
      return emptyMono;
    }
    SdkStrategySetting setting = settingOptional.get();
    return getMediationRule(udid, setting.getId())
        .flatMap(
            ruleOptional -> {
              if (ruleOptional.isPresent()) {
                MediationRule mediationRule = ruleOptional.get();
                return buildStrategy(
                        setting.getId(),
                        adSlotId,
                        mediationRule.getIdsList(),
                        adType,
                        requestBuilder.getDevice().getOsType(),
                        ai,
                        sdkVersion,
                        appChannelSettingOptional,
                        requestBuilder)
                    .map(
                        strategyListOptional ->
                            strategyListOptional.map(
                                strategies ->
                                    new StrategyBO(
                                        mediationRule.getMaxParallelRequestNum(), strategies)));
              } else {
                return emptyMono;
              }
            });
  }

  /**
   * @param requestBuilder
   * @param ai
   * @param regionName
   * @param sdkVersion
   * @return
   */
  public Mono<Optional<StrategyBO>> getSdkStrategy(
      BidRequest.Builder requestBuilder,
      AppInfo ai,
      String regionName,
      Version sdkVersion,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional) {

    return sdkStrategySettingDao
        .getStrategySettingIndex(ai.getGId())
        .flatMap(
            idListOptional -> {
              if (idListOptional.isEmpty()) {
                return Mono.just(Optional.empty());
              } else {
                String udid = requestBuilder.getDevice().getDid().getUdid();
                String adSlotId = requestBuilder.getSlots(0).getAdslotId();
                List<Long> idList = idListOptional.get();
                Mono<Optional<StrategyBO>> strategyMono = null;
                for (Long id : idList) {
                  if (null == strategyMono) {
                    strategyMono =
                        getStrategy(
                            id,
                            requestBuilder,
                            ai,
                            regionName,
                            sdkVersion,
                            adSlotId,
                            udid,
                            appChannelSettingOptional);
                  } else {
                    strategyMono =
                        strategyMono.flatMap(
                            strategyBoOptional -> {
                              if (strategyBoOptional.isPresent()) {
                                return Mono.just(strategyBoOptional);
                              } else {
                                return getStrategy(
                                    id,
                                    requestBuilder,
                                    ai,
                                    regionName,
                                    sdkVersion,
                                    adSlotId,
                                    udid,
                                    appChannelSettingOptional);
                              }
                            });
                  }
                }
                return strategyMono;

                //                List<Mono<Optional<StrategyBO>>> strategyMonoList =
                //                    idList.stream()
                //                        .map(
                //                            id ->
                //                                getStrategy(
                //                                    id, request, ai, regionName, sdkVersion,
                // adSlotId, udid))
                //                        .collect(Collectors.toList());
                //                return Mono.zip(
                //                    strategyMonoList,
                //                    results -> {
                //                      if (results.length > 0) {
                //                        for (Object result : results) {
                //                          Optional<StrategyBO> realResult = (Optional<StrategyBO>)
                // result;
                //                          if (realResult.isPresent()) {
                //                            return realResult;
                //                          }
                //                        }
                //                      }
                //                      return Optional.empty();
                //                    });
              }
            });
  }

  /**
   * 查询策略
   *
   * @param strategyId
   * @param requestBuilder
   * @param ai
   * @param regionName
   * @param sdkVersion
   * @param adSlotId
   * @param udid
   * @return
   */
  private Mono<Optional<StrategyBO>> getStrategy(
      Long strategyId,
      BidRequest.Builder requestBuilder,
      AppInfo ai,
      String regionName,
      Version sdkVersion,
      String adSlotId,
      String udid,
      Optional<Map<Integer, SdkChannelSetting>> appChannelSettingOptional) {

    return sdkStrategySettingDao
        .getStrategySetting(strategyId)
        .flatMap(
            settingOptional ->
                generateStrategy(
                    settingOptional,
                    ai,
                    regionName,
                    adSlotId,
                    udid,
                    requestBuilder,
                    sdkVersion,
                    appChannelSettingOptional))
        //        .doOnNext(
        //            strategiesOptional ->
        //                log.info(
        //                    "---------------------------strategyId:{},   result:{}",
        //                    strategyId,
        //                    strategiesOptional))
        .defaultIfEmpty(Optional.empty());
  }
}
