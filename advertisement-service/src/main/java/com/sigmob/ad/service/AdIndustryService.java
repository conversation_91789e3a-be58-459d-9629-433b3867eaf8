package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.AdIndustryDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Date 2022/9/15 12:31 @Description
 */
@Service
@AllArgsConstructor
public class AdIndustryService {

  private final AdIndustryDao adIndustryDao;

  @Deprecated
  public Mono<Optional<Map<String, String>>> getAdIndustry(
      int requestFlowType, Set<String> packageNames, int level) {
    if (CollectionUtils.isEmpty(packageNames)) {
      return Mono.just(Optional.empty());
    }

    var adIndustryMonoList =
        packageNames.stream()
            .filter(p -> !Strings.isNullOrEmpty(p))
            .map(
                p ->
                    adIndustryDao
                        .getAdIndustry(requestFlowType, p, level)
                        .onErrorResume(
                            e -> {
                              LogUtil.localError(e.getMessage());
                              return Mono.just(Optional.empty());
                            }))
            .collect(Collectors.toList());
    return Mono.zip(
        adIndustryMonoList,
        result -> {
          if (result.length > 0) {
            var adIndustryMap =
                Arrays.stream(result)
                    .filter(Objects::nonNull)
                    .map(r -> (Optional<Map<String, String>>) r)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .filter(r -> !CollectionUtils.isEmpty(r))
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

            if (!CollectionUtils.isEmpty(adIndustryMap)) {
              return Optional.of(adIndustryMap);
            }
          }

          return Optional.empty();
        });
  }
}
