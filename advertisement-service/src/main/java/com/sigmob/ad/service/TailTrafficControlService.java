package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.advertisement.AdType;
import com.sigmob.ad.core.advertisement.model.TailTrafficFilterResult;
import com.sigmob.ad.core.constants.enums.AbExperiment;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.util.DateUtil;
import com.sigmob.ad.dao.TailTrafficControlDao;
import com.sigmob.ssp.pb.sdksetting.SdkSetting;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 尾量请求控制服务
 *
 * <AUTHOR> @Date 2022/3/17 7:46 PM @Description
 */
@Service
@RequiredArgsConstructor
public class TailTrafficControlService {

  /** 最大连续请求无广告填充次数 —— 5 */
  private static final int MAX_NO_AD_FILTER_COUNT = 8;
  private final TailTrafficControlDao tailTrafficControlDao;

  /**
   * 是否过滤尾量请求
   *
   * @param uid
   * @param adSlotId
   * @param adSlotType
   * @param adSlotSdkSetting
   * @param globalSdkSetting
   * @return true-过滤；false-不过滤
   */
  public Mono<Optional<TailTrafficFilterResult>> filterReq(
      String uid,
      String adSlotId,
      int adSlotType,
      SdkSetting adSlotSdkSetting,
      SdkSetting globalSdkSetting,
      boolean isDebugDevice) {

    SdkSetting sdkSetting =
        SdkSetting.getDefaultInstance().equals(adSlotSdkSetting)
            ? globalSdkSetting
            : adSlotSdkSetting;

    var tailTrafficControlRate =
        sdkSetting.getAdSlotId().isBlank()
            ? sdkSetting.getCommon().getTailTrafficControlRate()
            : getTailTrafficRatioFromAdSlotSdkSetting(sdkSetting, adSlotType);
    if (isDebugDevice
        || Strings.isNullOrEmpty(uid)
        || SdkSetting.getDefaultInstance().equals(sdkSetting)
        || tailTrafficControlRate <= 0) {
      return Mono.just(Optional.empty());
    }

    // 是否配置了控量
    var deviceBucket = DeviceUtil.getDeviceBucket(uid + "_1", 100) + 1;

    // 是否白名单
    return tailTrafficControlDao
        .isWhiteListAdSlot(adSlotId)
        .flatMap(
            isWhiteListAdSlot -> {
              var tailTrafficWhitelistDiscardRate =
                  globalSdkSetting.getCommon().getTailTrafficWhitelistDiscardRate();

              if (isWhiteListAdSlot) {
                // 如果是白名单广告位，则控量比例按白名单配置走

                if (tailTrafficWhitelistDiscardRate <= 0) {
                  return Mono.just(Optional.empty());
                }

                // 该比例不能再用uid直接计算
                if (deviceBucket > tailTrafficWhitelistDiscardRate) {
                  return Mono.just(
                      Optional.of(
                          new TailTrafficFilterResult(
                              AbExperiment.TAIL_TRAFFIC_CONTROL_GROUP.getExperimentName(),
                              new BigDecimal(100 - tailTrafficWhitelistDiscardRate)
                                  .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                                  .floatValue(),
                              Boolean.FALSE,
                              Boolean.FALSE)));
                }
              } else {
                if (deviceBucket > tailTrafficControlRate) {
                  return Mono.just(
                      Optional.of(
                          new TailTrafficFilterResult(
                              AbExperiment.TAIL_TRAFFIC_CONTROL_GROUP.getExperimentName(),
                              new BigDecimal(100 - tailTrafficControlRate)
                                  .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                                  .floatValue(),
                              Boolean.FALSE,
                              Boolean.FALSE)));
                }
              }

              return tailTrafficControlDao
                  .getNoAdFillCount(uid, adSlotId, getTimeSpanCode())
                  .map(
                      noAdFillCountOptional ->
                          noAdFillCountOptional
                              .map(count -> count > MAX_NO_AD_FILTER_COUNT)
                              .orElse(Boolean.FALSE))
                  .map(
                      filterAd ->
                          Optional.of(
                              new TailTrafficFilterResult(
                                  AbExperiment.TAIL_TRAFFIC_EXPERIMENTAL_GROUP.getExperimentName(),
                                  new BigDecimal(
                                          isWhiteListAdSlot
                                              ? tailTrafficWhitelistDiscardRate
                                              : tailTrafficControlRate)
                                      .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                                      .floatValue(),
                                  filterAd,
                                  Boolean.TRUE)));
            });
  }

  /**
   * @param uid
   * @param adSlotId
   * @param tailTrafficFilterResult
   * @param hasAdFilled
   * @param isTest
   * @return reactor.core.publisher.Mono<java.lang.Boolean>
   */
  public Mono<Boolean> processAdSlotAdFillControl(
      String uid,
      String adSlotId,
      TailTrafficFilterResult tailTrafficFilterResult,
      boolean hasAdFilled,
      boolean isTest) {

    if (!isTest && null != tailTrafficFilterResult && tailTrafficFilterResult.getFilterControl()) {
      // 有广告返回
      if (hasAdFilled) {
        return tailTrafficControlDao
            .deleteNoAdCountKey(uid, adSlotId, getTimeSpanCode())
            .map(result -> Boolean.TRUE);
      } else {
        return tailTrafficControlDao
            .incrNoAdCount(uid, adSlotId, getTimeSpanCode())
            .map(result -> Boolean.TRUE);
      }
    }

    return Mono.just(Boolean.FALSE);
  }

  private int getTimeSpanCode() {
    var currentHour = DateUtil.getCurrentHour();
    if (currentHour >= 0 && currentHour < 6) {
      return TrafficControlTimeSpan.TIME_SPAN_0_6.getCode();
    } else if (currentHour >= 6 && currentHour < 12) {
      return TrafficControlTimeSpan.TIME_SPAN_6_12.getCode();
    } else if (currentHour >= 12 && currentHour < 18) {
      return TrafficControlTimeSpan.TIME_SPAN_12_18.getCode();
    } else {
      return TrafficControlTimeSpan.TIME_SPAN_18_24.getCode();
    }
  }

  /**
   * @param sdkSetting
   * @param adSlotType
   * @return int
   */
  private int getTailTrafficRatioFromAdSlotSdkSetting(SdkSetting sdkSetting, int adSlotType) {
    if (adSlotType == AdType.REWARDED_VIDEO.getTypeNum() && sdkSetting.hasRv()) {
      return sdkSetting.getRv().getTailTrafficControlRate();
    } else if (adSlotType == AdType.SPLASH.getTypeNum() && sdkSetting.hasSplash()) {
      return sdkSetting.getSplash().getTailTrafficControlRate();
    } else if (adSlotType == AdType.FULL_SCREEN_VIDEO.getTypeNum() && sdkSetting.hasFsv()) {
      return sdkSetting.getFsv().getTailTrafficControlRate();
    } else if (adSlotType == AdType.NATIVE.getTypeNum() && sdkSetting.hasNative()) {
      return sdkSetting.getNative().getTailTrafficControlRate();
    } else if(adSlotType == AdType.INTERSTITIAL.getTypeNum() && sdkSetting.hasInterstitial()){
      return sdkSetting.getInterstitial().getTailTrafficControlRate();
    }else {
      return 0;
    }
  }

  /** */
  @Getter
  @AllArgsConstructor
  enum TrafficControlTimeSpan {
    TIME_SPAN_0_6(1),
    TIME_SPAN_6_12(2),
    TIME_SPAN_12_18(3),
    TIME_SPAN_18_24(4);

    int code;
  }
}
