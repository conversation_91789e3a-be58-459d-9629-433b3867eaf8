package com.sigmob.ad.service;

import com.sigmob.ad.dao.AdSlotPriceDao;
import com.sigmob.ssp.pb.publishing.management.AdSlotPrice;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class AdSlotPriceService {

  private final AdSlotPriceDao adSlotPriceDao;

  /**
   * @param adSlotId
   * @return
   */
  public Mono<Optional<AdSlotPrice>> get(String adSlotId) {
    return adSlotPriceDao.getAdSlotPrice(adSlotId);
  }

  /**
   * @param adSlotIds
   * @return
   */
  public Mono<Optional<List<AdSlotPrice>>> getList(List<String> adSlotIds) {
    if (CollectionUtils.isEmpty(adSlotIds)) {
      return Mono.just(Optional.empty());
    }
    return adSlotPriceDao.getAdSlotPriceList(adSlotIds);
  }
}
