package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.advertisement.model.SigmobAdSlot;
import com.sigmob.ad.core.config.api.RtbApiConfig;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.dao.AdSlotMappingStrategyDao;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> @Date 2021/11/30 2:31 下午 @Description
 */
@Service
@AllArgsConstructor
public class AdSlotMappingStrategyService {

  private AdSlotMappingStrategyDao adSlotMappingStrategyDao;

  /** 根据流量查询对应sigmob广告位映射策略 */
  public Mono<Optional<SigmobAdSlot>> getSigmobAdSlot(
      RequestFlowType requestFlowType,
      RtbApiConfig.ApiConfig apiConfig,
      String apiAdSlotId,
      int apiAdType,
      String bundle,
      int bidFloor,
      String dealId,
      List<Integer> hitStrategy,
      int height,
      int width) {
    var isHuaweiTraffic = RequestFlowType.HUAWEI.equals(requestFlowType);
    var isOppoTraffic = RequestFlowType.OPPO.equals(requestFlowType);
    var isVivoTraffic = RequestFlowType.VIVO.equals(requestFlowType);
    var isGromoreTraffic = RequestFlowType.GROMORE.equals(requestFlowType);
    if (isHuaweiTraffic || isOppoTraffic || isGromoreTraffic || isVivoTraffic) {
      return adSlotMappingStrategyDao
          .getApiTrafficStrategyList(requestFlowType)
          .map(
              mappingStrategyListOptional -> {
                if (mappingStrategyListOptional.isPresent()) {
                  var mappingStrategyOptional =
                      mappingStrategyListOptional.get().stream()
                          .filter(
                              mappingStrategy -> {
                                // 匹配广告类型
                                boolean flag = mappingStrategy.getAdType() == apiAdType;
                                // 匹配包名
                                if (flag) {
                                  var bundles = mappingStrategy.getBundles();
                                  flag =
                                      CollectionUtils.isEmpty(bundles) || bundles.contains(bundle);
                                }
                                // 匹配deal id(huawei) or hitStrategy(oppo)
                                if (flag) {
                                  if (isHuaweiTraffic) {
                                    var dealIds = mappingStrategy.getDealIds();
                                    flag =
                                        CollectionUtils.isEmpty(dealIds)
                                            || dealIds.contains(dealId);
                                  } else if (isOppoTraffic) {
                                    var dealIds = mappingStrategy.getDealIds();
                                    boolean status = false;
                                    if (CollectionUtils.isEmpty(dealIds)) {
                                      status = true;
                                    } else {
                                      if (!CollectionUtils.isEmpty(hitStrategy)) {
                                        for (Integer i : hitStrategy) {
                                          if (dealIds.contains(i.toString())) {
                                            status = true;
                                            break;
                                          }
                                        }
                                      }
                                    }
                                    flag = status;
                                  } else if (isGromoreTraffic) {
                                    var dealIds = mappingStrategy.getDealIds();
                                    flag =
                                        !CollectionUtils.isEmpty(dealIds)
                                            && dealIds.stream()
                                                .anyMatch(did -> did.toLowerCase().equals(dealId));
                                  }
                                }
                                // 匹配尺寸
                                if (flag) {
                                  var sizes = mappingStrategy.getSizes();
                                  flag =
                                      CollectionUtils.isEmpty(sizes)
                                          || sizes.stream()
                                              .anyMatch(
                                                  size ->
                                                      size.getHeight() == height
                                                          && size.getWidth() == width);
                                }
                                // 匹配价格,左闭右开区间
                                if (flag) {
                                  var priceZones = mappingStrategy.getPriceZones();
                                  flag =
                                      CollectionUtils.isEmpty(priceZones)
                                          || priceZones.stream()
                                              .anyMatch(
                                                  priceZone ->
                                                      bidFloor >= priceZone.getMin()
                                                          && bidFloor < priceZone.getMax());
                                }

                                if (flag && (isHuaweiTraffic || isOppoTraffic || isVivoTraffic)) {
                                  var thirdPartyAdSlotIds =
                                      mappingStrategy.getThirdPartyAdSlotIds();
                                  flag =
                                      CollectionUtils.isEmpty(thirdPartyAdSlotIds)
                                          || thirdPartyAdSlotIds.contains(apiAdSlotId);
                                }

                                return flag;
                              })
                          .findFirst();

                  if (mappingStrategyOptional.isPresent()) {
                    var mappingStrategy = mappingStrategyOptional.get();
                    var adSlotId = mappingStrategy.getAdSlotId();
                    var appId = mappingStrategy.getAppId();
                    if (!Strings.isNullOrEmpty(adSlotId)) {
                      return Optional.of(
                          SigmobAdSlot.builder().adSlotId(adSlotId).appId(appId).build());
                    }
                  }
                }

                var sigmobAppSlotOptional = apiConfig.getSigmobAppSlot(Integer.toString(apiAdType));
                if (sigmobAppSlotOptional.isPresent()) {
                  var sigmobAppSlot = sigmobAppSlotOptional.get();
                  return Optional.of(
                      SigmobAdSlot.builder()
                          .adSlotId(sigmobAppSlot.getAdSlotId())
                          .appId(sigmobAppSlot.getAppId())
                          .build());
                }

                return Optional.empty();
              });
    } else if (RequestFlowType.XIMALAYA.equals(requestFlowType)) {
      // 喜马拉雅 api
      var sigmobAppSlotOptional = apiConfig.getSigmobAppSlot(bundle, apiAdSlotId);
      if (sigmobAppSlotOptional.isPresent()) {
        var sigmobAppSlot = sigmobAppSlotOptional.get();
        return Mono.just(
            Optional.of(
                SigmobAdSlot.builder()
                    .adSlotId(sigmobAppSlot.getAdSlotId())
                    .appId(sigmobAppSlot.getAppId())
                    .build()));
      }

      return Mono.just(Optional.empty());
    } else {
      return Mono.just(Optional.empty());
    }
  }
}
