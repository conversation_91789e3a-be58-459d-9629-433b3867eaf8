package com.sigmob.ad.service.biz;

import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.sigmob.ad.adx.rpc.grpc.AdFilterConfig;
import com.sigmob.ad.core.config.mediation.AppMiscellaneousConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.network.cnaaip.IpService;
import com.sigmob.ad.core.network.cnaaip.RegionType;
import com.sigmob.sigdsp.pb.Version;
import com.twofishes.config.manager.ConfigManager;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 根据业务需要的各种过滤条件
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FilterConditions {

  private static final Set<String> AD_REQ_INSTALLED_APP =
      Set.of(/*"com.xunmeng.pinduoduo",*/ "com.eg.android.AlipayGphone");

  @NonNull private final IpService ipService;

  //  @NonNull private final CnaaIpService cnaaIpService;

  /**
   * V3.7.0 ADX 对SDK 2.20、2.21 版本屏蔽无encard广告
   *
   * @param sdkVersion 客户端请求sdk版本
   * @return
   */
  public static boolean filterVideoNoEndCardCondition(int osType, String sdkVersion) {
    return osType != OsType.HARMONY_OS.getTypeNum()
        && (Constants.SDK_VERSION_2_20_0.getVersionStr().equals(sdkVersion)
            || Constants.SDK_VERSION_2_21_0.getVersionStr().equals(sdkVersion));
  }

  /**
   * V3.8.0 关闭页推荐广告屏蔽Android sdk 2.22.0以下版本
   *
   * @param osType
   * @param sdkVersion
   * @return
   */
  public static boolean filterCloseEndCardAdCondition(int osType, Version sdkVersion) {
    return OsType.ANDROID.getTypeNum() == osType
        && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_2_22_0) == -1;
  }

  public boolean filterByRegion(int appId, int adType, String trafficIp, RegionType regionType) {

    if (Strings.isNullOrEmpty(trafficIp)) {
      return false;
    }

    if (!DeviceUtil.isValidIp(trafficIp)) {
      return false;
    }

    var regionName = ipService.getRegionNameByIp(trafficIp, regionType);
    if (Strings.isNullOrEmpty(regionName)) {
      return false;
    }

    String key = appId + (adType == 0 ? StringUtils.EMPTY : Constants.SYMBOL_HYPHEN + adType);

    boolean filterFlag = false; // 不过滤
    var appMiscellaneousConfig = ConfigManager.get(AppMiscellaneousConfig.class);
    if (null != appMiscellaneousConfig) {
      var trafficFilter = appMiscellaneousConfig.getTrafficFilter();
      if (null != trafficFilter) {
        var regionFilterMap = trafficFilter.getRegionFilter();
        if (!CollectionUtils.isEmpty(regionFilterMap)) {
          var regionFilter = regionFilterMap.get(key);
          if (null != regionFilter) {
            var filterRegion = regionFilter.getFilterRegion(regionType);
            if (!CollectionUtils.isEmpty(filterRegion)
                && (adType != 0 && !filterRegion.contains(regionName)
                    || adType == 0 && filterRegion.contains(regionName))) {
              filterFlag = true;
            }
          }
        }
      }
    }
    return filterFlag;
  }

  public static boolean isAdReqFilterByInstalledApp(List<String> userInstalledApp) {

    if (CollectionUtils.isEmpty(userInstalledApp)) {
      return true;
    }
    // 使用Guava的Sets.intersection方法获取交集
    Set<String> intersection =
        Sets.intersection(AD_REQ_INSTALLED_APP, new HashSet<>(userInstalledApp));
    return intersection.size() != AD_REQ_INSTALLED_APP.size();
  }

  public static boolean filterAd(
      Map<Integer, Integer> filterAdMap,
      DspProtocolType dspProtocolType,
      AdFilterConfig filterConfigs,
      boolean isDeeplink,
      String title,
      String desc,
      String creativeTitle,
      String productId,
      boolean isDownloadAd,
      int i) {

    if (!DspProtocolType.SIGMOB_DSP.equals(dspProtocolType)) {
      if (isDownloadAd
          && filterConfigs.getBundlesCount() > 0
          && !Strings.isNullOrEmpty(productId)) {

        Optional<String> productIdOptional =
            filterConfigs.getBundlesList().stream()
                .map(String::toLowerCase)
                .filter(s -> s.equals(productId.toLowerCase()))
                .findFirst();
        if (productIdOptional.isPresent()) {
          filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_BUNDLE_FILTER);
          return true;
        }
      }
      if (filterConfigs.getKeywordsCount() > 0) {
        var keywordsList = filterConfigs.getKeywordsList();
        if (!title.isBlank() || !desc.isBlank() || !creativeTitle.isBlank()) {
          for (String keyword : keywordsList) {
            if (Strings.isNullOrEmpty(keyword) || keyword.trim().equals(StringUtils.EMPTY)) {
              continue;
            }
            String keywordLowerCase = keyword.toLowerCase();
            if (!title.isBlank() && title.toLowerCase().contains(keywordLowerCase)) {
              filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_KEYWORD_FILTER);
              return true;
            }
            if (!desc.isBlank() && desc.toLowerCase().contains(keywordLowerCase)) {
              filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_KEYWORD_FILTER);
              return true;
            }
            if (!creativeTitle.isBlank()
                && creativeTitle.toLowerCase().contains(keywordLowerCase)) {
              filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_KEYWORD_FILTER);
              return true;
            }
          }
        }
      }
    }
    if (isDownloadAd && filterConfigs.getDisableDownload()) {
      filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_DISABLE_DOWNLOAD_FILTER);
      return true;
    }
    if (isDeeplink && filterConfigs.getDisableDeeplink()) {
      filterAdMap.put(i, ErrorCode.RTB_SIG_DSP_AD_DISABLE_DEEPLINK_FILTER);
      return true;
    }
    return false;
  }
}
