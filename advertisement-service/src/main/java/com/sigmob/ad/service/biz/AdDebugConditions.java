//package com.sigmob.ad.service.biz;
//
//import com.sigmob.ad.core.config.dsp.AdDebugConfig;
//import com.twofishes.config.manager.ConfigManager;
//
///** 测试广告流程服务 */
//public class AdDebugConditions {
//
//  /**
//   * 是否通用测试配置
//   *
//   * @return true-走测试广告流程; false-走正常广告流程
//   */
//  public static boolean isCommonDebug(int appId, int osType, String adSlotId, int adSlotType) {
//    AdDebugConfig adDebugConfig = ConfigManager.get(AdDebugConfig.class);
//    if (null == adDebugConfig) {
//      return false;
//    }
//
//    var debugConfigOptional = adDebugConfig.getDebugConfig(adSlotId);
//    if (debugConfigOptional.isEmpty()) {
//      return false;
//    }
//    var debugConfig = debugConfigOptional.get();
//    return debugConfig.getOsType().equals(osType)
//        && debugConfig.getAppId().equals(appId)
//        && debugConfig.getAdType().equals(adSlotType);
//  }
//}
