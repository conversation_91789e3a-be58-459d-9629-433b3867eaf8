package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.publishing.mediation.userwhitelist.UserIdentify;
import com.sigmob.ad.core.publishing.mediation.userwhitelist.UserIdentifyType;
import com.sigmob.ad.dao.UserWhiteListDao;
import com.sigmob.sigdsp.pb.BidRequest;
import com.sigmob.sigdsp.pb.Device;
import com.sigmob.sigdsp.pb.DeviceId;
import com.sigmob.sigdsp.pb.Version;
import com.sigmob.ssp.pb.mediation.management.WhiteListedUser;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class UserWhiteListService {

  private final UserWhiteListDao userWhiteListDao;

  /**
   * 查询白名单用户
   *
   * @param appId
   * @param adSlotId
   * @param identifies
   * @return
   */
  public Mono<Optional<List<WhiteListedUser>>> getWhiteListedUsers(
      int appId, String adSlotId, List<UserIdentify> identifies) {
    return userWhiteListDao.getWhiteListedUserList(appId, adSlotId, identifies);
  }

  /**
   * @param appId
   * @param adSlotId
   * @param requestBuilder
   * @param sdkVersion
   * @return
   */
  public Mono<Optional<List<WhiteListedUser>>> getWhiteListedUsers(
      int appId, String adSlotId, BidRequest.Builder requestBuilder, Version sdkVersion) {
    List<UserIdentify> identifies = Lists.newArrayList();
    // 必须按照userid，idfa，udid的顺序来组装，这是白名单优先级的业务要求
    // 安卓按照userid,gaid,imei的顺序来组装
    Device device = requestBuilder.getDevice();
    DeviceId did = device.getDid();
    if (!Strings.isNullOrEmpty(did.getUserId())) {
      UserIdentify ui = new UserIdentify();
      ui.setIdentify(did.getUserId());
      ui.setType(UserIdentifyType.user_id);
      identifies.add(ui);
    }
    if (device.getOsType() == OsType.IOS.getTypeNum()) {
      String idfa = did.getIdfa();
      if (!Strings.isNullOrEmpty(idfa) && !Constants.INVALID_IDFA_VALUE.equals(idfa)) {
        UserIdentify ui = new UserIdentify();
        ui.setIdentify(idfa);
        ui.setType(UserIdentifyType.idfa);
        identifies.add(ui);
      }
      if (!Strings.isNullOrEmpty(did.getUdid())) {
        UserIdentify ui = new UserIdentify();
        ui.setIdentify(did.getUdid());
        ui.setType(UserIdentifyType.udid);
        identifies.add(ui);
      }
    } else {
      // 从2.10.0开始支持双卡imei读取,如果sdk版本大于等于2.10.0则直接使用imei1、imei2，不用imei了
      if (DeviceUtil.compareVersion(sdkVersion, Constants.SUPPORT_IMEI1_SDK_MIN_VERSION) >= 0) {
        // imei1合法，则加入查询条件
        if (DeviceUtil.isValidImei(did.getImei1())) {
          UserIdentify ui = new UserIdentify();
          ui.setIdentify(did.getImei1());
          ui.setType(UserIdentifyType.imei);
          identifies.add(ui);
        }
        // imei2合法，则加入查询条件
        if (DeviceUtil.isValidImei(did.getImei2())) {
          UserIdentify ui = new UserIdentify();
          ui.setIdentify(did.getImei2());
          ui.setType(UserIdentifyType.imei);
          identifies.add(ui);
        }
      } else if (DeviceUtil.isValidImei(did.getImei())) {
        UserIdentify ui = new UserIdentify();
        ui.setIdentify(did.getImei());
        ui.setType(UserIdentifyType.imei);
        identifies.add(ui);
      }

      if (!Strings.isNullOrEmpty(did.getGaid())) {
        UserIdentify ui = new UserIdentify();
        ui.setIdentify(did.getGaid());
        ui.setType(UserIdentifyType.gaid);
        identifies.add(ui);
      }
      if (!Strings.isNullOrEmpty(did.getOaid())) {
        UserIdentify ui = new UserIdentify();
        ui.setIdentify(did.getOaid());
        ui.setType(UserIdentifyType.oaid);
        identifies.add(ui);
      }
    }

    if (identifies.isEmpty()) {
      return Mono.just(Optional.empty());
    }
    return getWhiteListedUsers(appId, adSlotId, identifies);
  }
}
