package com.sigmob.ad.service;

import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ssp.pb.sdksetting.SdkChannelSetting;
import com.sigmob.ssp.pb.sdksetting.SdkCommonSetting;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SdkChannelSettingService {

  private final SdkSettingService sdkSettingService;

  /**
   * @param appId
   * @return
   */
  public Mono<Optional<Map<Integer, SdkChannelSetting>>> getSdkChannelSetting(int appId) {
    return sdkSettingService
        .getSdkSettingByApp(appId)
        .flatMap(
            sdkSettingOptional -> {
              if (sdkSettingOptional.isPresent()) {
                var sdkSetting = sdkSettingOptional.get();
                if (SdkCommonSetting.getDefaultInstance()
                    .equals(sdkSetting.getCommon())) { // 如果该sdkstting没有common Setting
                  if (sdkSetting.getAppId() != 0) { // 非全局配置则读取全局配置
                    return getSdkChannelSetting(0);
                  } else {
                    return Mono.just(Optional.<Map<Integer, SdkChannelSetting>>empty());
                  }
                }
                var sdkChannelSettingMap =
                    sdkSetting.getCommon().getSdkChannelSettingsList().stream()
                        .collect(Collectors.toMap(SdkChannelSetting::getChannelId, v -> v));
                return Mono.just(Optional.of(sdkChannelSettingMap));
              } else {
                if (appId != 0) {
                  return getSdkChannelSetting(0);
                } else {
                  return Mono.just(Optional.<Map<Integer, SdkChannelSetting>>empty());
                }
              }
            });
  }

  /**
   * 获取指定渠道广告过期时间<br>
   * 获取规则：先从指定对应app的渠道配置获取，如果为空则从全局应用配置获取，如果还为空则设置为默认值3600秒
   *
   * @param channelId 渠道id
   * @param appChannelSetting 指定应用id渠道设置
   * @return
   */
  public int getChannelAdExpireTime(
      final int channelId, Optional<Map<Integer, SdkChannelSetting>> appChannelSetting) {
    return appChannelSetting
        .map(channelSetting -> channelSetting.get(channelId))
        .map(SdkChannelSetting::getAdExpireTime)
        .orElse(Constants.DEFAULT_SDK_CHANNEL_AD_EXPIRE_TIME);
  }
}
