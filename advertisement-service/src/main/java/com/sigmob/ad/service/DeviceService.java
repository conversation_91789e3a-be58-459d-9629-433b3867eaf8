package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.advertisement.DidType;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.device.SigDebugSupportInfo;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.DeviceDao;
import com.sigmob.ad.dao.MockAdSlotDao;
import com.sigmob.ad.dao.SigDebugDeviceDao;
import com.sigmob.ad.dao.model.AdDebugDevice;
import com.sigmob.sigdsp.pb.BidRequest;
import com.sigmob.sigdsp.pb.DeviceId;
import com.sigmob.sigdsp.pb.Version;
import com.sigmob.ssp.pb.publishing.management.DebugDevice;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class DeviceService {

  private final DeviceDao deviceDao;

  private final MockAdSlotDao mockAdSlotDao;

  private final SigDebugDeviceDao sigDebugDeviceDao;

  public Mono<Optional<List<DebugDevice>>> getDebugDeviceList(
      BidRequest.Builder bidRequestBuilder, AppInfo ai, Version sdkVersion) {

    List<DebugDevice> deviceQueryParams = Lists.newArrayList();
    DeviceId did = bidRequestBuilder.getDevice().getDid();
    var osType = bidRequestBuilder.getDevice().getOsType();
    if (osType == OsType.IOS.getTypeNum()) {
      String idfa = did.getIdfa();
      // 非法的idfa直接返回null
      if (!DeviceUtil.isValidIdfa(idfa)) {
        return Mono.just(Optional.empty());
      }

      DebugDevice.Builder db = DebugDevice.newBuilder();
      db.setGId(ai.getGId());
      db.setUType(DidType.IDFA.getTypeNum());
      db.setIdentify(idfa);
      deviceQueryParams.add(db.build());
    } else if (osType == OsType.ANDROID.getTypeNum()) {
      // 从2.10.0开始支持双卡imei读取,如果sdk版本大于等于2.10.0则直接使用imei1、imei2，不用imei了
      if (DeviceUtil.compareVersion(sdkVersion, Constants.SUPPORT_IMEI1_SDK_MIN_VERSION) >= 0) {
        // imei1合法，则加入查询条件
        if (DeviceUtil.isValidImei(did.getImei1())) {
          DebugDevice.Builder db = DebugDevice.newBuilder();
          db.setGId(ai.getGId());
          db.setUType(DidType.IMEI.getTypeNum());
          db.setIdentify(did.getImei1());
          deviceQueryParams.add(db.build());
        }
        // imei2合法，则加入查询条件
        if (DeviceUtil.isValidImei(did.getImei2())) {
          DebugDevice.Builder db = DebugDevice.newBuilder();
          db.setGId(ai.getGId());
          db.setUType(DidType.IMEI.getTypeNum());
          db.setIdentify(did.getImei2());
          deviceQueryParams.add(db.build());
        }
      } else if (DeviceUtil.isValidImei(did.getImei())) {
        DebugDevice.Builder db = DebugDevice.newBuilder();
        db.setGId(ai.getGId());
        db.setUType(DidType.IMEI.getTypeNum());
        db.setIdentify(did.getImei());
        deviceQueryParams.add(db.build());
      }

      if (DeviceUtil.isValidUid(did.getGaid())) {
        DebugDevice.Builder db = DebugDevice.newBuilder();
        db.setGId(ai.getGId());
        db.setUType(DidType.GAID.getTypeNum());
        db.setIdentify(did.getGaid());
        deviceQueryParams.add(db.build());
      }

      if (DeviceUtil.isValidUid(did.getOaid())) {
        DebugDevice.Builder db = DebugDevice.newBuilder();
        db.setGId(ai.getGId());
        db.setUType(DidType.OAID.getTypeNum());
        db.setIdentify(did.getOaid());
        deviceQueryParams.add(db.build());
      }
    } else if (osType == OsType.HARMONY_OS.getTypeNum()) {
      if (DeviceUtil.isValidUid(did.getOaid())) {
        DebugDevice.Builder db = DebugDevice.newBuilder();
        db.setGId(ai.getGId());
        db.setUType(DidType.OAID.getTypeNum());
        db.setIdentify(did.getOaid());
        deviceQueryParams.add(db.build());
      }

      if (DeviceUtil.isValidUid(did.getOdid())) {
        DebugDevice.Builder db = DebugDevice.newBuilder();
        db.setGId(ai.getGId());
        db.setUType(DidType.ODID.getTypeNum());
        db.setIdentify(did.getOdid());
        deviceQueryParams.add(db.build());
      }
    }
    if (deviceQueryParams.isEmpty()) {
      return Mono.just(Optional.empty());
    }

    return deviceDao.getDebugDeviceList(deviceQueryParams);
  }

  /**
   * 是否调试设备
   *
   * @return true-当设备是应用的调试设备时；false-未找到设备信息或者设备不在应用调试设备列表中
   */
  public Mono<Boolean> isDebugDevice(
      BidRequest.Builder bidRequestBuilder, AppInfo ai, Version sdkVersion) {
    // 先判断测试设备
    return getDebugDeviceList(bidRequestBuilder, ai, sdkVersion)
        .map(
            debugDeviceListOptional -> {
              if (debugDeviceListOptional.isEmpty()) {
                return Boolean.FALSE;
              } else {
                for (DebugDevice debugDevice : debugDeviceListOptional.get()) {
                  if (debugDevice.getState() == 1) { // 被禁用
                    return Boolean.FALSE;
                  }
                  List<Integer> appIdListList = debugDevice.getAppIdListList();
                  if (CollectionUtils.isEmpty(appIdListList)) {
                    return Boolean.TRUE;
                  }
                  if (appIdListList.contains(ai.getId())) {
                    return Boolean.TRUE;
                  }
                }
              }
              return Boolean.FALSE;
            })
        .onErrorResume(
            Exception.class,
            e -> {
              LogUtil.localError("get debug device error:{}", e.getMessage(), e);
              return Mono.just(Boolean.FALSE);
            });
  }

  public Mono<Optional<List<AdDebugDevice>>> getValidAdDebugDeviceList(
      Map<Integer, String> deviceIdMap, Long developerId) {
    if (CollectionUtils.isEmpty(deviceIdMap)) {
      return Mono.just(Optional.empty());
    }

    // 1:idfa、2:imei 、3:gaid、 4:oaid，OAID>IMEI>GAID）
    LinkedHashMap<Integer, String> orderedIdMap =
        Maps.newLinkedHashMapWithExpectedSize(deviceIdMap.size());
    if (deviceIdMap.get(AdDebugDevice.DeviceId.IDFA.getCode()) != null) { // 如果是ios设备
      orderedIdMap.put(
          AdDebugDevice.DeviceId.IDFA.getCode(),
          deviceIdMap.get(AdDebugDevice.DeviceId.IDFA.getCode()));
    } else { // android设备
      if (deviceIdMap.get(AdDebugDevice.DeviceId.OAID.getCode()) != null) {
        orderedIdMap.put(
            AdDebugDevice.DeviceId.OAID.getCode(),
            deviceIdMap.get(AdDebugDevice.DeviceId.OAID.getCode()));
      }
      if (deviceIdMap.get(AdDebugDevice.DeviceId.IMEI.getCode()) != null) {
        orderedIdMap.put(
            AdDebugDevice.DeviceId.IMEI.getCode(),
            deviceIdMap.get(AdDebugDevice.DeviceId.IMEI.getCode()));
      }
      if (deviceIdMap.get(AdDebugDevice.DeviceId.GAID.getCode()) != null) {
        orderedIdMap.put(
            AdDebugDevice.DeviceId.GAID.getCode(),
            deviceIdMap.get(AdDebugDevice.DeviceId.GAID.getCode()));
      }
    }
    if (CollectionUtils.isEmpty(orderedIdMap)) {
      return Mono.just(Optional.empty());
    }

    return deviceDao
        .getAdDebugDeviceList(orderedIdMap, developerId)
        .map(
            resultListOptional -> {
              if (resultListOptional.isEmpty()) {
                return Optional.empty();
              }
              var adDebugDeviceList = resultListOptional.get();
              if (CollectionUtils.isEmpty(adDebugDeviceList)) {
                return Optional.empty();
              }

              return Optional.of(
                  adDebugDeviceList.stream()
                      .filter(
                          device ->
                              device.getState() != null
                                  && device.getState().equals(AdDebugDevice.State.OPEN.getCode()))
                      .collect(Collectors.toList()));
            });
  }

  /**
   * 新版测试设备 不具体关注uid类型(可能oaid当作imei去使用，这里不做强校验)
   *
   * @param deviceOsType osType
   * @param did DID
   * @param idfv IDFV
   * @param appId APPID
   * @return SigDebugSupportInfo
   */
  public Mono<SigDebugSupportInfo> isSigDebugDeviceWithErrorResume(
      int deviceOsType, DeviceId did, String idfv, int appId, String adSlotId) {
    List<String> deviceIds = getDeviceIds(deviceOsType, did, idfv);

    if (CollectionUtils.isEmpty(deviceIds)) {
      return mockAdSlotDao.checkMockAdSlotExist(adSlotId);
    }

    List<Mono<Optional<SigDebugSupportInfo>>> monoList =
        Lists.newArrayListWithCapacity(deviceIds.size());
    for (String deviceId : deviceIds) {
      Mono<Optional<SigDebugSupportInfo>> mono =
          sigDebugDeviceDao.getSigDebugDevice(deviceId, appId);
      monoList.add(mono);
    }

    return Mono.zip(
            monoList,
            resList -> {
              for (Object object : resList) {

                if (object instanceof Optional optional) {
                  if (optional.isPresent()) {
                    var supportInfo = (SigDebugSupportInfo) optional.get();
                    if (supportInfo.isDebug()) {
                      return supportInfo;
                    }
                  }
                }
              }
              return Constants.NOT_DEBUG_SUPPORT_INFO;
            })
        .switchIfEmpty(Mono.just(Constants.NOT_DEBUG_SUPPORT_INFO))
        .flatMap(
            sigDebugSupportInfo -> {
              if (sigDebugSupportInfo.isDebug()) {
                return Mono.just(sigDebugSupportInfo);
              } else {
                return mockAdSlotDao.checkMockAdSlotExist(adSlotId);
              }
            })
        .onErrorResume(
            e -> {
              // 异常不影响正常流程
              LogUtil.localError(
                  "isSigDebugDeviceWithErrorResume error, message: " + e.getMessage(), e);
              return Constants.NOT_DEBUG_SUPPORT_INFO_MONO;
            });
  }

  @NotNull
  private static List<String> getDeviceIds(int deviceOsType, DeviceId did, String idfv) {
    List<String> deviceIds = new LinkedList<>();
    if (OsType.IOS.getTypeNum() == deviceOsType) {
      if (!Strings.isNullOrEmpty(did.getIdfa())
          && !Constants.INVALID_IDFA_VALUE.equals(did.getIdfa())) {
        deviceIds.add(did.getIdfa());
      }
      if (!Strings.isNullOrEmpty(idfv)) {
        deviceIds.add(idfv);
      }
    } else if (OsType.HARMONY_OS.getTypeNum() == deviceOsType) {
      // oaid、user_id
      if (!Strings.isNullOrEmpty(did.getOaid())) {
        deviceIds.add(did.getOaid());
      }
    } else {
      if (!Strings.isNullOrEmpty(did.getOaid())) {
        deviceIds.add(did.getOaid());
      }
      if (!Strings.isNullOrEmpty(did.getImei())) {
        deviceIds.add(did.getImei());
      }
      if (!Strings.isNullOrEmpty(did.getGaid())) {
        deviceIds.add(did.getGaid());
      }
      if (!Strings.isNullOrEmpty(did.getAndroidId())) {
        deviceIds.add(did.getAndroidId());
      }
    }
    if (!Strings.isNullOrEmpty(did.getUserId())) {
      deviceIds.add(did.getUserId());
    }
    return deviceIds;
  }
}
