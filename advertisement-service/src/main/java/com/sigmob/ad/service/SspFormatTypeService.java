package com.sigmob.ad.service;

import com.google.common.collect.Lists;
import com.sigmob.ad.core.advertisement.model.SspFormatTypeInfo;
import com.sigmob.ad.core.util.AdSlotUtil;
import com.sigmob.ad.dao.SspFormatTypeDao;
import com.sigmob.ssp.pb.publishing.management.AdSlotInfo;
import com.sigmob.ssp.pb.sdksetting.SdkSetting;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * SspFormatTypeService
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SspFormatTypeService {

  private final SspFormatTypeDao sspFormatTypeDao;

  public Mono<List<SspFormatTypeInfo>> getSspFormatTypeList(
      SdkSetting slotSdkSetting, SdkSetting appSdkSetting, AdSlotInfo adSlotInfo, int adType) {
    List<String> nativeTemplateIds =
        AdSlotUtil.getNativeTemplateIds(slotSdkSetting, appSdkSetting, adSlotInfo, adType);

    if (CollectionUtils.isEmpty(nativeTemplateIds)) {
      return Mono.just(Collections.emptyList());
    }

    return getSspFormatTypeList(nativeTemplateIds);
  }

  /** 根据nativeTemplateIds获取sspFormatType */
  public Mono<List<SspFormatTypeInfo>> getSspFormatTypeList(List<String> nativeTemplateIds) {

    if (CollectionUtils.isEmpty(nativeTemplateIds)) {
      return Mono.just(Collections.emptyList());
    }
    List<Mono<Optional<SspFormatTypeInfo>>> sspFormatTypeMonoList =
        Lists.newArrayListWithCapacity(nativeTemplateIds.size());

    for (String templateId : nativeTemplateIds) {
      sspFormatTypeMonoList.add(sspFormatTypeDao.getSspFormatType(templateId));
    }
    return Mono.zip(
        sspFormatTypeMonoList,
        resultList -> {
          List<SspFormatTypeInfo> sspFormatTypeList = new ArrayList<>();
          for (Object object : resultList) {
            if (object instanceof Optional optional) {
              if (optional.isPresent()) {
                sspFormatTypeList.add((SspFormatTypeInfo) optional.get());
              }
            }
          }
          return sspFormatTypeList;
        });
  }
}
