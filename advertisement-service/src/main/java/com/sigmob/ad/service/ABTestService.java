package com.sigmob.ad.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Strings;
import com.sigmob.ad.adx.rpc.grpc.RtbRequest;
import com.sigmob.ad.core.advertisement.AdType;
import com.sigmob.ad.core.config.ABTestConfig;
import com.sigmob.ad.core.config.UserReqFilterConfig;
import com.sigmob.ad.core.constants.ABTestConstants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.SettlementMode;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.dspmanagement.TradingType;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.core.util.RandomNumberGenerator;
import com.sigmob.sigdsp.pb.Ad;
import com.sigmob.sigdsp.pb.MaterialMeta;
import com.sigmob.sigdsp.pb.Version;
import com.twofishes.config.manager.ConfigManager;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Service
@RequiredArgsConstructor
public class ABTestService {

  /** dsp全并行请求 */
  public static final ABTestService.ABTestResult<RtbConstants.ExecuteMode, Float> DSP_ALL_PARALLEL =
      new ABTestService.ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, 0f);

  /** dsp全串行请求 */
  public static final ABTestResult<RtbConstants.ExecuteMode, Float> DSP_ALL_SEQUENCE =
      new ABTestService.ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, 100f);

  public static final ABTestResult<Boolean, Double> RTB_NO_EXTRA_BID_PRICE_RATIO =
      new ABTestResult<>(Boolean.FALSE, 0d);
  private static final ABTestResult<Boolean, Float> HB_NOT_SECOND_BID =
      new ABTestService.ABTestResult<>(Boolean.FALSE, 0f);

  /** 用户请求渠道无填充请求过滤，降低服务器成本 */
  private static final int DEFAULT_BUCKET_COUNT = 100;

  private static final String AB_EXPERIMENT_DSP_REQUEST = "dspRequest";

  /** 聚合竞败二次竞价实验 */
  private static final String AB_EXPERIMENT_HB_SECOND_BID = "hbSecondBid";

  /** api流量请求dsp方式实验 */
  private static final String AB_EXPERIMENT_API_DSP_REQUEST = "apiDspRequest";

  private static final String AB_EXPERIMENT_UNKNOWN_NETWORK_TO_4G = "unknownNetworkTo4G";
  private static final String AB_EXPERIMENT_CATEGORY_CHANGE = "change";
  private static final String AB_EXPERIMENT_CATEGORY_UNCHANGE = "unChange";

  private static final String AB_EXPERIMENT_BAIDU_HTTP_TO_HTTPS = "baiduHttpToHttps";

  /** */
  private static final String AB_EXPERIMENT_ADMATERIAL_WHITELIST = "adMaterialWhiteList";

  private static final String AB_EXPERIMENT_ADMATERIAL_WHITELIST_IN = "in";
  private static final String AB_EXPERIMENT_ADMATERIAL_WHITELIST_OUT = "out";
  private static final String AB_EXPERIMENT_FLOOR_PRICE_STRATEGY = "floorPriceStrategy";
  private static final String AB_EXPERIMENT_FLOOR_PRICE_STRATEGY_ECPM = "ecpmFloor";
  private static final String AB_EXPERIMENT_FLOOR_PRICE_STRATEGY_EXPECTED = "expectedFloor";
  private static final String AB_EXPERIMENT_AD_FULLSCREEN_CLICK = "fullScreenClick";
  private static final String AB_EXPERIMENT_AD_FULLSCREEN_CLICK_DEFAULT = "all";
  private static final String AB_EXPERIMENT_AD_FILTER_IOS = "iosAdFilter";
  private static final String AB_EXPERIMENT_AD_FILTER_ANDROID = "androidAdFilter";
  private static final String AB_EXPERIMENT_AD_FILTER_YES = "yes";
  private static final String AB_EXPERIMENT_AD_FILTER_NO = "no";

  /** tobid聚合流量过期广告重新请求实验 */
  private static final String AB_EXPERIMENT_TOBID_EXPIRED_AD_RELOAD = "tobidExpiredAdReload";

  /** 非tobid聚合流量过期广告重新请求实验 */
  private static final String AB_EXPERIMENT_SIGMOB_EXPIRED_AD_RELOAD = "sigmobExpiredAdReload";

  /** 过期广告重新请求实验用来生成流量分桶随机数 */
  private static final int EXPIRED_AD_RELOAD_SEED = 1013;

  /** 设备请求dsp模式 */
  private static final String AB_EXPERIMENT_DEVICE_REQ_DSP_MODE = "deviceReqDspMode";

  /** rtb竞价渠道最终出价调价实验 */
  //  private static final String AB_EXPERIMENT_RTB_BID_PRICE = "rtbBidPrice";
  //
  //  private static final String AB_EXPERIMENT_USER_REQ_FILTER_CATEGORY = "config";

  private static final String AB_EXPERIMENT_USER_REQ_FILTER_PLACEMENT = "placementList";

  private static final String AB_EXPERIMENT_USER_REQ_FILTER_DSP = "dspList";

  private static final String AB_EXPERIMENT_USER_REQ_LIST_ATTR_BLACK = "black";

  private static final String AB_EXPERIMENT_USER_REQ_LIST_ATTR_WHITE = "white";

  /***
   * 广告过滤实验
   *
   * @param uid
   * @param osType
   * @param dspProtocolType
   * @return true-过滤；false-不过滤
   */
  public static boolean expAdFilter(String uid, int osType, int dspProtocolType) {
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    ABTestConfig.Experiment experiment = null;

    if (DspProtocolType.TENCENT_GDT.getType() == dspProtocolType) {
      if (osType == OsType.IOS.getTypeNum()) {
        experiment = abTestConfig.getExperiment(AB_EXPERIMENT_AD_FILTER_IOS);
      } else {
        experiment = abTestConfig.getExperiment(AB_EXPERIMENT_AD_FILTER_ANDROID);
      }
      ABTestConfig.Category doFilter = experiment.getCategory(AB_EXPERIMENT_AD_FILTER_YES);
      ABTestConfig.Category dontFilter = experiment.getCategory(AB_EXPERIMENT_AD_FILTER_NO);
      var doFilterBucketSet = doFilter.getBucketSet();
      var dontFilterBucketSet = dontFilter.getBucketSet();

      if (doFilterBucketSet == null || dontFilterBucketSet == null) {
        doFilter.setBucketSet();
        dontFilter.setBucketSet();
      }
      if (doFilter.getDeviceSet() == null) {
        doFilter.setDeviceSet();
      }

      int totalBucket = doFilterBucketSet.size() + dontFilterBucketSet.size();
      int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);
      return doFilter.getDeviceSet().contains(uid) // 当设备是否属于白名单实验设备
          || doFilterBucketSet.contains(deviceBucket);
    }
    return true;
  }

  /** ready广告再请求广告 */
  public static Optional<ABTestResult<String, Map<String, String>>> sdkExpiredAdReload(
      int requestFlowType,
      int osType,
      int adType,
      Version sdkVersion,
      String uid,
      Map<String, String> bidRequestExtOptions) {
    if (requestFlowType != RequestFlowType.SDK.getCode() || Strings.isNullOrEmpty(uid)) {
      return Optional.empty();
    }

    // 获取AB测试配置
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    if (null == abTestConfig) {
      return Optional.empty();
    }

    // 统一使用SIGMOB_EXPIRED_AD_RELOAD实验
    ABTestConfig.Experiment experiment =
        abTestConfig.getExperiment(AB_EXPERIMENT_SIGMOB_EXPIRED_AD_RELOAD);

    if (null == experiment) {
      return Optional.empty();
    }

    ABTestConfig.Category category = experiment.getCategory(ABTestConstants.EXPIRE_EXPERIMENT_CATEGORY_NAME_1);
    if (null == category) {
      return Optional.empty();
    }

    // 获取设备桶号，以用户ID为维度，50%流量为实验组，50%流量为对照组
    int deviceBucket = DeviceUtil.getDeviceBucket(uid, 100, EXPIRED_AD_RELOAD_SEED);
    boolean experimentGroup = true;
    if (!category.getBucketSet().contains(deviceBucket)) {
      category = experiment.getCategory(ABTestConstants.EXPIRE_EXPERIMENT_CATEGORY_NAME_2);
      experimentGroup = false;
    }

    return Optional.of(
        new ABTestResult<>(
            AB_EXPERIMENT_SIGMOB_EXPIRED_AD_RELOAD,
            Map.of(
                ABTestConstants.EXPIRE_AD_PARAM_TIME_THRESHOLD_FILED,
                String.valueOf(category.getExpireTimeThreshold()),
                ABTestConstants.EXPIRE_EXPERIMENT_GROUP_FILED,
                String.valueOf(experimentGroup))));
  }

  public static ABTestResult<Boolean, Double> getRtbBidPriceExtraRatio(
      RtbRequest rtbRequest, int tradingType) {
    if (tradingType == TradingType.RTB.getTypeNum()) {
      double dspRtbBidPriceRatio = 0.0;
      if (rtbRequest != null) {
        dspRtbBidPriceRatio = rtbRequest.getDspRtbBidPriceRatio();
      }

      BigDecimal num1 = new BigDecimal(String.valueOf(dspRtbBidPriceRatio));
      return new ABTestResult<>(num1.compareTo(BigDecimal.ZERO) > 0, dspRtbBidPriceRatio);

      //      return Optional.ofNullable(ConfigManager.get(ABTestConfig.class))
      //          .map(config -> config.getExperiment(AB_EXPERIMENT_RTB_BID_PRICE))
      //          .map(
      //              experiment -> {
      //                // DSP ID + 广告位ID
      //                var category =
      //                    experiment.getCategory(
      //                        dspId
      //                            + Constants.SYMBOL_UNDERSCORE
      //                            + Constants.SYMBOL_ASTERISK
      //                            + Constants.SYMBOL_UNDERSCORE
      //                            + adSlotId);
      //                if (null == category) {
      //                  // DSP ID + App ID
      //                  category =
      //                      experiment.getCategory(
      //                          dspId
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + appId
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + Constants.SYMBOL_ASTERISK);
      //                }
      //                if (null == category) {
      //                  // DSP ID
      //                  category =
      //                      experiment.getCategory(
      //                          dspId
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + Constants.SYMBOL_ASTERISK
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + Constants.SYMBOL_ASTERISK);
      //                }
      //                if (null == category) {
      //                  // 广告位ID
      //                  category =
      //                      experiment.getCategory(
      //                          Constants.SYMBOL_ASTERISK
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + Constants.SYMBOL_ASTERISK
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + adSlotId);
      //                }
      //                if (null == category) {
      //                  // App ID
      //                  category =
      //                      experiment.getCategory(
      //                          Constants.SYMBOL_ASTERISK
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + appId
      //                              + Constants.SYMBOL_UNDERSCORE
      //                              + Constants.SYMBOL_ASTERISK);
      //                }
      //
      //                if (null != category) {
      //                  int deviceBucket = DeviceUtil.getDeviceBucket(uid, 100);
      //                  if (category.getBucketSet().contains(deviceBucket)
      //                      || category.getDeviceSet().contains(uid)) {
      //                    var ratioLowerLimit = category.getRatioLowerLimit();
      //                    var ratioHigherLimit = category.getRatioHigherLimit();
      //                    return new ABTestResult<>(
      //                        Boolean.TRUE,
      //                        RandomNumberGenerator.nextDouble(ratioLowerLimit,
      // ratioHigherLimit));
      //                  }
      //                }
      //                return RTB_NO_EXTRA_BID_PRICE_RATIO;
      //              })
      //          .orElse(RTB_NO_EXTRA_BID_PRICE_RATIO);
    }
    return RTB_NO_EXTRA_BID_PRICE_RATIO;
  }

  /** tobid聚合的二次出价实验 */
  public static ABTestResult<Boolean, Float> expHbSecondBid() {
    return Optional.ofNullable(ConfigManager.get(ABTestConfig.class))
        .map(config -> config.getExperiment(AB_EXPERIMENT_HB_SECOND_BID))
        .map(
            experiment -> {
              ABTestConfig.Category changeCategory =
                  experiment.getCategory(ABTestConstants.AB_EXPERIMENT_HB_SECOND_BID_REBID);

              int deviceBucket = RandomNumberGenerator.randomInt(DEFAULT_BUCKET_COUNT);

              if (changeCategory.getBucketSet().contains(deviceBucket)) {
                return new ABTestResult<>(
                    Boolean.TRUE,
                    changeCategory.getRatio() == null ? 0f : changeCategory.getRatio());
              } else {
                return HB_NOT_SECOND_BID;
              }
            })
        .orElse(HB_NOT_SECOND_BID);
  }

  //  /**
  //   * 请求dsp模式
  //   * @param uid
  //   * @param adSlotId
  //   * @param requestFlowType
  //   * @param settlementMode
  //   * @param hbType
  //   * @param adType
  //   * @param sdkVersion
  //   * @param defineFloorAdType
  //   * @return
  // reactor.core.publisher.Mono<com.sigmob.ad.service.ABTestService.ABTestResult<com.sigmob.ad.core.rtb.RtbConstants.ExecuteMode,java.lang.Float>>
  //   */
  //  public Mono<ABTestResult<RtbConstants.ExecuteMode, Float>> expReqDspMode(
  //      String uid,
  //      String adSlotId,
  //      int requestFlowType,
  //      int settlementMode,
  //      int hbType,
  //      int adType,
  //      Version sdkVersion,
  //      List<Integer> defineFloorAdType) {
  //    if (settlementMode == SettlementMode.DEVELOPER_ECPM.getCode()
  //            && !CollectionUtils.isEmpty(defineFloorAdType)
  //            && defineFloorAdType.contains(adType)
  //        || hbType > 0) {
  //      return Mono.just(new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, 0f));
  //    }
  //
  //    // 信息流全并行竞价
  //    if (adType == AdType.NATIVE.getTypeNum()
  //        && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_3_4_0) >= 0) {
  //      return Mono.just(new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, 0f));
  //    }
  //  }

  /**
   * 设备请求dsp模式
   *
   * @param uid
   * @param osType
   * @return
   */
  public ABTestResult<RtbConstants.ExecuteMode, Float> expDeviceDspReqMode(String uid, int osType) {
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    ABTestConfig.Experiment experiment =
        abTestConfig.getExperiment(AB_EXPERIMENT_DEVICE_REQ_DSP_MODE);

    String osTypeName =
        osType == OsType.ANDROID.getTypeNum() ? OsType.ANDROID.getName() : OsType.IOS.getName();
    ABTestConfig.Category deviceParallelCategory = experiment.getCategory(osTypeName + "_parallel");

    if (deviceParallelCategory != null) {
      int deviceBucket = DeviceUtil.getDeviceBucket(uid, 100);
      Float sequencePercent =
          CollectionUtils.isEmpty(deviceParallelCategory.getBucketSet())
              ? 1.0f
              : new BigDecimal(100 - deviceParallelCategory.getBucketSet().size())
                  .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                  .floatValue();
      if (!CollectionUtils.isEmpty(deviceParallelCategory.getBucketSet())
              && deviceParallelCategory.getBucketSet().contains(deviceBucket)
          || !CollectionUtils.isEmpty(deviceParallelCategory.getDeviceSet())
              && deviceParallelCategory.getDeviceSet().contains(uid)) {
        return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, sequencePercent);
      } else {
        return new ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, sequencePercent);
      }
    }
    return DSP_ALL_SEQUENCE;
  }

  /***
   * dsp请求方式（并行/串行）AB实验
   *
   * @param uid
   * @param adSlotId
   * @param requestFlowType
   * @param settlementMode
   *
   * @return
   */
  @Deprecated
  public ABTestResult<RtbConstants.ExecuteMode, Float> expExecuteMode(
      String uid,
      String adSlotId,
      int requestFlowType,
      int settlementMode,
      int hbType,
      int adType,
      Version sdkVersion,
      List<Integer> defineFloorAdType,
      int sdkType,
      int osType) {

    if (settlementMode == SettlementMode.DEVELOPER_ECPM.getCode()
            && !CollectionUtils.isEmpty(defineFloorAdType)
            && defineFloorAdType.contains(adType)
        || hbType > 0) {
      return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, 0f);
    }

    // 信息流全并行竞价
    if (adType == AdType.NATIVE.getTypeNum()
        && DeviceUtil.isGt3_4_0VersionAndSdkType(sdkVersion, sdkType, osType)) {
      return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, 0f);
    }

    Float sequencePercent;
    try {
      ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
      // sdk流量使用实验dspRequest，api流量使用实验apiDspRequest
      ABTestConfig.Experiment experiment =
          abTestConfig.getExperiment(
              RequestFlowType.SDK.getCode() == requestFlowType
                  ? AB_EXPERIMENT_DSP_REQUEST
                  : AB_EXPERIMENT_API_DSP_REQUEST);
      // 按广告位分流，当有广告位id配置时，走并行请求
      ABTestConfig.Category adSlotCategory = experiment.getCategory(adSlotId + "_parallel");
      if (adSlotCategory != null) {
        int deviceBucket = DeviceUtil.getDeviceBucket(uid, 100);
        sequencePercent =
            CollectionUtils.isEmpty(adSlotCategory.getBucketSet())
                ? 1.0f
                : new BigDecimal(100 - adSlotCategory.getBucketSet().size())
                    .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                    .floatValue();
        if (!CollectionUtils.isEmpty(adSlotCategory.getBucketSet())
                && adSlotCategory.getBucketSet().contains(deviceBucket)
            || !CollectionUtils.isEmpty(adSlotCategory.getDeviceSet())
                && adSlotCategory.getDeviceSet().contains(uid)) {
          return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, sequencePercent);
        } else {
          return new ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, sequencePercent);
        }
      }

      ABTestConfig.Category parallelCategory =
          experiment.getCategory(RtbConstants.ExecuteMode.PARALLEL.getName());
      ABTestConfig.Category sequenceCategory =
          experiment.getCategory(RtbConstants.ExecuteMode.SEQUENTIAL.getName());
      int sequentialBucketCount = sequenceCategory.getBucketSet().size();
      int parallelBucketCount = parallelCategory.getBucketSet().size();
      int totalBucket = sequentialBucketCount + parallelBucketCount;
      int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);
      sequencePercent =
          new BigDecimal(sequentialBucketCount)
              .divide(new BigDecimal(totalBucket), 4, RoundingMode.HALF_UP)
              .floatValue();
      // 如果设备uid在ab实验分类配置的设备白名单中，则优先走白名单；否则根据uid计算桶号判断设备属于哪种实验
      if (sequenceCategory.getDeviceSet().contains(uid)) {
        return new ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, sequencePercent);
      } else if (parallelCategory.getDeviceSet().contains(uid)) {
        return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, sequencePercent);
      } else if (parallelCategory.getBucketSet().contains(deviceBucket)) {
        return new ABTestResult<>(RtbConstants.ExecuteMode.PARALLEL, sequencePercent);
      } else {
        // 非并行dsp请求默认瀑布流
        return new ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, sequencePercent);
      }
    } catch (Exception e) {
      LogUtil.localError("dsp request executeMode abtest config error!", e);
    }
    return new ABTestResult<>(RtbConstants.ExecuteMode.SEQUENTIAL, 0f);
  }

  /***
   * 设备未知网络类型改4G
   *
   * @param uid 设备uid
   * @return false:不修改网络类型; true:修改网络类型
   */
  @Deprecated
  public boolean expChangeNetwork(String uid) {
    return Optional.ofNullable(ConfigManager.get(ABTestConfig.class))
        .map(config -> config.getExperiment(AB_EXPERIMENT_UNKNOWN_NETWORK_TO_4G))
        .map(
            experiment -> {
              ABTestConfig.Category changeCategory =
                  experiment.getCategory(AB_EXPERIMENT_CATEGORY_CHANGE);
              ABTestConfig.Category unChangeCategory =
                  experiment.getCategory(AB_EXPERIMENT_CATEGORY_UNCHANGE);

              int totalBucket =
                  changeCategory.getBucketSet().size() + unChangeCategory.getBucketSet().size();
              int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);

              // 如果设备uid在ab实验分类配置的设备白名单中，则优先走白名单；否则根据uid计算桶号判断设备属于哪种实验
              if (changeCategory.getDeviceSet().contains(uid)) {
                return Boolean.TRUE;
              } else if (unChangeCategory.getDeviceSet().contains(uid)) {
                return Boolean.FALSE;
              } else if (changeCategory.getBucketSet().contains(deviceBucket)) {
                return Boolean.TRUE;
              } else {
                return Boolean.FALSE;
              }
            })
        .orElse(Boolean.FALSE);
  }

  public boolean expChangeBaiduHttpToHttps() {
    return Optional.ofNullable(ConfigManager.get(ABTestConfig.class))
        .map(config -> config.getExperiment(AB_EXPERIMENT_BAIDU_HTTP_TO_HTTPS))
        .map(
            experiment -> {
              ABTestConfig.Category changeCategory =
                  experiment.getCategory(AB_EXPERIMENT_CATEGORY_CHANGE);

              int deviceBucket = RandomNumberGenerator.randomInt(DEFAULT_BUCKET_COUNT);

              if (changeCategory.getBucketSet().contains(deviceBucket)) {
                return Boolean.TRUE;
              } else {
                return Boolean.FALSE;
              }
            })
        .orElse(Boolean.FALSE);
  }

  /***
   * 是否使用白名单素材配置
   *
   * @param uid
   * @param adsList
   * @param materialUrlWhiteList
   * @return true:使用素材白名单; false: 不使用素材白名单
   */
  public boolean expAdMaterialWhiteList(
      String uid, List<Ad> adsList, List<String> materialUrlWhiteList) {
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    ABTestConfig.Experiment experiment =
        abTestConfig.getExperiment(AB_EXPERIMENT_ADMATERIAL_WHITELIST);
    ABTestConfig.Category inWhiteListCategory =
        experiment.getCategory(AB_EXPERIMENT_ADMATERIAL_WHITELIST_IN);
    ABTestConfig.Category outWhiteListCategory =
        experiment.getCategory(AB_EXPERIMENT_ADMATERIAL_WHITELIST_OUT);
    int totalBucket =
        inWhiteListCategory.getBucketSet().size() + outWhiteListCategory.getBucketSet().size();
    int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);
    if (inWhiteListCategory.getDeviceSet().contains(uid) // 当设备是否属于白名单实验设备
        || inWhiteListCategory.getBucketSet().contains(deviceBucket)) {
      for (Ad ad : adsList) { // 如果素材中包括白名单素材url，则不设置关闭视频时间
        for (MaterialMeta materialMeta : ad.getMaterialsList()) {
          if (materialUrlWhiteList.contains(materialMeta.getVideoUrl())) {
            return Boolean.TRUE;
          }
        }
      }
    }
    return Boolean.FALSE;
  }

  /***
   * 广告请求底价策略实验
   *
   * @param uid
   * @return
   */
  public RtbConstants.FloorPriceStrategy expAdFloorPriceStrategy(String uid) {
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    ABTestConfig.Experiment experiment =
        abTestConfig.getExperiment(AB_EXPERIMENT_FLOOR_PRICE_STRATEGY);
    ABTestConfig.Category ecpmCategory =
        experiment.getCategory(AB_EXPERIMENT_FLOOR_PRICE_STRATEGY_ECPM);
    ABTestConfig.Category expectedCategory =
        experiment.getCategory(AB_EXPERIMENT_FLOOR_PRICE_STRATEGY_EXPECTED);

    Set<String> ecpmCategoryDeviceSet = ecpmCategory.getDeviceSet();
    if (!CollectionUtils.isEmpty(ecpmCategoryDeviceSet)
        && ecpmCategoryDeviceSet.contains(uid)) { // 当设备是否属于白名单实验设备
      return RtbConstants.FloorPriceStrategy.ECPM_FLOOR;
    }
    Set<String> expectedCategoryDeviceSet = expectedCategory.getDeviceSet();
    if (!CollectionUtils.isEmpty(expectedCategoryDeviceSet)
        && expectedCategoryDeviceSet.contains(uid)) { // 当设备是否属于白名单实验设备
      return RtbConstants.FloorPriceStrategy.EXPECTED_PRICE;
    }

    int totalBucket = ecpmCategory.getBucketSet().size() + expectedCategory.getBucketSet().size();
    int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);
    if (ecpmCategory.getBucketSet().contains(deviceBucket)) {
      return RtbConstants.FloorPriceStrategy.ECPM_FLOOR;
    }
    return RtbConstants.FloorPriceStrategy.EXPECTED_PRICE;
  }

  /***
   * 广告全屏点击实验
   *
   * @param uid
   * @param adSlotId
   * @param campaignId
   * @param type
   * @return
   */
  public boolean expAdFullScreenClick(
      String uid, String adSlotId, String campaignId, RtbConstants.AdFullscreenClickType type) {
    ABTestConfig abTestConfig = ConfigManager.get(ABTestConfig.class);
    ABTestConfig.Experiment experiment =
        abTestConfig.getExperiment(AB_EXPERIMENT_AD_FULLSCREEN_CLICK);

    if (null != experiment) {
      Optional<ABTestConfig.Category> categoryOptional =
          Optional.ofNullable(
                  experiment.getCategory(adSlotId + "_" + campaignId + "_" + type.getName()))
              .or(
                  () ->
                      Optional.ofNullable(
                              experiment.getCategory(
                                  AB_EXPERIMENT_AD_FULLSCREEN_CLICK_DEFAULT
                                      + "_"
                                      + campaignId
                                      + "_"
                                      + type.getName()))
                          .or(
                              () ->
                                  Optional.ofNullable(
                                          experiment.getCategory(
                                              adSlotId
                                                  + "_"
                                                  + AB_EXPERIMENT_AD_FULLSCREEN_CLICK_DEFAULT
                                                  + "_"
                                                  + type.getName()))
                                      .or(
                                          () ->
                                              Optional.ofNullable(
                                                  experiment.getCategory(
                                                      AB_EXPERIMENT_AD_FULLSCREEN_CLICK_DEFAULT
                                                          + "_"
                                                          + AB_EXPERIMENT_AD_FULLSCREEN_CLICK_DEFAULT
                                                          + "_"
                                                          + type.getName())))));

      if (categoryOptional.isPresent()) {
        ABTestConfig.Category category = categoryOptional.get();
        int deviceBucket = DeviceUtil.getDeviceBucket(uid, DEFAULT_BUCKET_COUNT);
        if (category.getDeviceSet().contains(uid) // 当设备是否属于白名单实验设备
            || category.getBucketSet().contains(deviceBucket)) {
          return Boolean.TRUE;
        }
      }
    }
    return Boolean.FALSE;
  }

  //  public boolean iqiyiAdUseMraid(String uid) {
  //    return Optional.ofNullable(ConfigManager.get(ABTestConfig.class))
  //        .map(config -> config.getExperiment(AB_EXPERIMENT_IQIYI_AD_TEMPLATE))
  //        .map(
  //            experiment -> {
  //              ABTestConfig.Category originalCategory =
  //                  experiment.getCategory(AB_EXPERIMENT_IQIYI_AD_TEMPLATE_ORIGINAL);
  //              ABTestConfig.Category mraidCategory =
  //                  experiment.getCategory(AB_EXPERIMENT_IQIYI_AD_TEMPLATE_MRAID);
  //
  //              int totalBucket =
  //                  originalCategory.getBucketSet().size() + mraidCategory.getBucketSet().size();
  //              int deviceBucket = DeviceUtil.getDeviceBucket(uid, totalBucket);
  //
  //              // 如果设备uid在ab实验分类配置的设备白名单中，则优先走白名单；否则根据uid计算桶号判断设备属于哪种实验
  //              if (mraidCategory.getDeviceSet().contains(uid)
  //                  || mraidCategory.getBucketSet().contains(deviceBucket)) {
  //                return Boolean.TRUE;
  //              } else {
  //                return Boolean.FALSE;
  //              }
  //            })
  //        .orElse(Boolean.FALSE);
  //  }

  public ABTestResult<Boolean, UserReqFilter> getUserReqFilter(
      String uid, String adSlotId, int dspId) {

    var userReqFilterConfig = ConfigManager.get(UserReqFilterConfig.class);
    if (null != userReqFilterConfig) {
      var experiments = userReqFilterConfig.getExperiments();
      int seed = 50021;
      var deviceBucket = DeviceUtil.getDeviceBucket(uid, 100, seed);
      // 命中实验配置
      UserReqFilterConfig.Experiment hitExperiment = null;
      // 兜底实验配置
      UserReqFilterConfig.Experiment defaultExperiment = null;
      for (var exp : experiments) {
        var expId = exp.getId();
        if (UserReqFilterConfig.DEFAULT_EXPERIMENT_ID.equalsIgnoreCase(expId)) {
          defaultExperiment = exp;
          continue;
        }

        var configs = exp.getConfigs();
        if (exp.getFilterCacheTime() > 0
            && !CollectionUtils.isEmpty(configs)
            && !CollectionUtils.isEmpty(exp.getBucketSet())
            && (exp.getBucketSet().contains(deviceBucket)
                || !CollectionUtils.isEmpty(exp.getDeviceSet())
                    && exp.getDeviceSet().contains(uid))) {
          // 是否满足广告位配置的过滤实验要求
          boolean isInAdSLotFilter = false;
          var adSlotConfig = configs.get(AB_EXPERIMENT_USER_REQ_FILTER_PLACEMENT);
          if (null == adSlotConfig) {
            isInAdSLotFilter = true;
          } else {
            var adSlotConfigAttr = adSlotConfig.getAttr();
            var slotConfigDataSet = adSlotConfig.getDataSet();

            if (AB_EXPERIMENT_USER_REQ_LIST_ATTR_WHITE.equalsIgnoreCase(adSlotConfigAttr)
                || Strings.isNullOrEmpty(adSlotConfigAttr)) {
              // 白名单
              if (!CollectionUtils.isEmpty(slotConfigDataSet)
                  && slotConfigDataSet.contains(adSlotId)) {
                isInAdSLotFilter = true;
              }
            } else {
              // 黑名单
              if (CollectionUtils.isEmpty(slotConfigDataSet)
                  || !slotConfigDataSet.contains(adSlotId)) {
                isInAdSLotFilter = true;
              }
            }
          }
          if (isInAdSLotFilter) {
            // 是否满足dsp配置的过滤实验要求
            var dspConfig = configs.get(AB_EXPERIMENT_USER_REQ_FILTER_DSP);
            if (null == dspConfig) {
              hitExperiment = exp;
              break;
            }
            var dspConfigAttr = dspConfig.getAttr();
            var dspConfigDataSet = dspConfig.getDataSet();

            if (AB_EXPERIMENT_USER_REQ_LIST_ATTR_WHITE.equalsIgnoreCase(dspConfigAttr)
                || Strings.isNullOrEmpty(dspConfigAttr)) {
              if (CollectionUtils.isEmpty(dspConfigDataSet)) {
                continue;
              }
              // 白名单
              if (dspConfigDataSet.contains(Integer.toString(dspId))) {
                hitExperiment = exp;
                break;
              } else {
                continue;
              }
            } else if (AB_EXPERIMENT_USER_REQ_LIST_ATTR_BLACK.equalsIgnoreCase(dspConfigAttr)) {
              // 黑名单
              if (CollectionUtils.isEmpty(dspConfigDataSet)) {
                hitExperiment = exp;
                break;
              }

              if (dspConfigDataSet.contains(Integer.toString(dspId))) {
                continue;
              } else {
                hitExperiment = exp;
                break;
              }
            }
          }
        }
      }
      var experiment = hitExperiment == null ? defaultExperiment : hitExperiment;
      if (null != experiment) {
        return new ABTestResult<>(
            Boolean.TRUE,
            UserReqFilter.builder()
                .experimentId(experiment.getId())
                .filterCacheTime(experiment.getFilterCacheTime())
                .maxTimeInterval(experiment.getMaxTimeInterval())
                .maxFilterInterval(experiment.getMaxFilterInterval())
                .filterReqThreshold(experiment.getFilterReqThreshold())
                .filterRspThreshold(experiment.getFilterRspThreshold())
                .shortTermCoefficient(experiment.getShortTermCoefficient())
                .longTermCoefficient(experiment.getLongTermCoefficient())
                .variationCoefficient(experiment.getVariationCoefficient())
                .reqVariationCoefficient(
                    experiment.getReqVariationCoefficient() == null
                        ? 1.0d
                        : experiment.getReqVariationCoefficient())
                .isFilter(experiment.getIsFilter())
                .expDimension(
                    experiment.getCasePlt() == null || experiment.getCasePlt().equals(Boolean.TRUE)
                        ? 0
                        : 1)
                .build());
      }
    }

    return new ABTestResult<>(Boolean.FALSE, null);
  }

  @Getter
  @Builder
  public static class UserReqFilter {

    private String experimentId;

    private Float flowRate;

    private Integer filterCacheTime;

    private Integer maxTimeInterval;

    private Integer maxFilterInterval;

    private Integer filterReqThreshold;

    private Double filterRspThreshold;

    private Double shortTermCoefficient;

    private Double longTermCoefficient;

    private Double variationCoefficient;

    private Double reqVariationCoefficient;

    private Boolean isFilter;

    private Integer expDimension;
  }

  @Getter
  @Setter
  @AllArgsConstructor
  public static class ABTestResult<T, R> {
    T result;
    R additionalResult;
  }
}
