package com.sigmob.ad.service;

import com.sigmob.ad.core.constants.enums.Currency;
import com.sigmob.ad.core.util.CurrencyUtil;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.CurrencyDao;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 货币服务
 *
 * <AUTHOR> @Date 2021/6/17 11:48 上午 @Version 1.0 @Description
 */
@Service
@RequiredArgsConstructor
public class CurrencyService {

  @NonNull private final CurrencyDao currencyDao;

  /**
   * 指定货币种类转换人民币的汇率
   *
   * @param currency 货币种类
   * @return
   */
  public Mono<BigDecimal> getExchangeRate(String currency) {

    if (Currency.CNY.getName().equals(currency)) {
      return Mono.just(BigDecimal.ONE);
    }

    return currencyDao
        .getExchangeRate(currency)
        .map(exchangeRateOptional -> exchangeRateOptional.orElse(BigDecimal.ZERO))
        .onErrorResume(
            e -> {
              LogUtil.localError("getExchangeRate({}) error", currency, e);
              return Mono.just(BigDecimal.ZERO);
            });
  }

  /**
   * 外币转化为人民币(分)
   *
   * @param money
   * @param exchangeRate
   * @return
   */
  public int convertForeignCurrencyToCNY(
      BigDecimal money, BigDecimal exchangeRate, RoundingMode roundingMode) {
    return CurrencyUtil.convertForeignCurrencyToCny(money, exchangeRate, roundingMode);
  }

  /**
   * 国内人民币（分）转成外币（元）
   *
   * @return
   */
  public double convertCNYToForeignCurrency(
      BigDecimal money, BigDecimal exchangeRate, RoundingMode roundingMode) {
    return CurrencyUtil.convertCnyToForeignCurrency(money, exchangeRate, roundingMode);
  }
}
