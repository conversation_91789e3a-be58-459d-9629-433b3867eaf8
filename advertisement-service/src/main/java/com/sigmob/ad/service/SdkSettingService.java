package com.sigmob.ad.service;

import com.sigmob.ad.dao.SdkSettingDao;
import com.sigmob.ssp.pb.sdksetting.SdkSetting;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Service
@AllArgsConstructor
public class SdkSettingService {

  private final SdkSettingDao sdkSettingDao;

  /**
   * @param adSlotId
   * @param appId
   * @return
   */
  public Mono<Optional<SdkSetting>> getSdkSettingLevelByLevel(String adSlotId, int appId) {
    return getSdkSettingByAdSlot(appId, adSlotId)
        .flatMap(
            sdkSettingOptional -> {
              if (sdkSettingOptional.isPresent()) {
                return Mono.just(sdkSettingOptional);
              } else {
                return getSdkSettingByApp(appId);
              }
            });
  }

  /**
   * @param appId
   * @param adSlotId
   * @return
   */
  public Mono<Optional<SdkSetting>> getSdkSettingByAdSlot(int appId, String adSlotId) {
    return sdkSettingDao.getSdkSettingByAdSlotId(appId, adSlotId);
  }

  /**
   * @param appId
   * @return
   */
  public Mono<Optional<SdkSetting>> getSdkSettingByApp(int appId) {

    return sdkSettingDao
        .getSdkSettingByApp(appId)
        .flatMap(
            sdkSettingOptional -> {
              if (sdkSettingOptional.isEmpty() && appId != 0) {
                return getDefaultSdkSetting();
              } else {
                return Mono.just(sdkSettingOptional);
              }
            });
  }

  /** @return */
  protected Mono<Optional<SdkSetting>> getDefaultSdkSetting() {
    return sdkSettingDao.getSdkSettingByApp(0);
  }
}
