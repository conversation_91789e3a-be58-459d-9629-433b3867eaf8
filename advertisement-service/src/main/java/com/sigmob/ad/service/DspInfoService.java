//package com.sigmob.ad.service;
//
//import com.sigmob.ad.dao.DspManagementDao;
//import com.sigmob.ssp.pb.dspmanagement.DspInfo;
//import java.util.Optional;
//import lombok.AllArgsConstructor;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Mono;
//
///**
// * <AUTHOR> @Date 2024/2/22 17:23 @Description
// */
//@Service
//@AllArgsConstructor
//public class DspInfoService {
//
//  private static final Mono<Optional<DspInfo>> EMPTY_DAO_INFO = Mono.just(Optional.empty());
//  private final DspManagementDao dspManagementDao;
//
//  public Mono<Optional<DspInfo>> getDspInfo(int dspId) {
//    return dspManagementDao
//        .getDspInfo(dspId)
//        .onErrorResume(
//            e -> EMPTY_DAO_INFO);
//  }
//}
