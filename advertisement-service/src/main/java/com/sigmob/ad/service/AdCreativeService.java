package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.happyelements.rdcenter.commons.util.MD5Util;
import com.sigmob.ad.core.constants.enums.StandardDspCreativeType;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.AdCreativeDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.sigmob.ad.core.exception.ErrorCode.AD_CREATIVE_EXAMINE_RESULT;

@Service
@AllArgsConstructor
public class AdCreativeService {

  private final AdCreativeDao adCreativeDao;

  /**
   * 广告创意审核结果
   *
   * @param dspId
   * @param advertiserId
   * @param creativeId
   * @param title
   * @param desc
   * @param imgUrl
   * @param videoUrl
   * @param videoImgUrl
   * @return
   */
  public Mono<AD_CREATIVE_EXAMINE_RESULT> isExamined(
      int dspId,
      String advertiserId,
      String creativeId,
      String title,
      String desc,
      String imgUrl,
      String videoUrl,
      String videoImgUrl) {
    return adCreativeDao
        .getExamineResult(dspId, advertiserId, creativeId)
        .map(
            resultListOptional -> {
              if (resultListOptional.isEmpty()
                  || CollectionUtils.isEmpty(resultListOptional.get())
                  || resultListOptional.get().size() < 4) {
                LogUtil.localError(
                    "Examine ad creative({}) from dsp({}) advertiser({}) lack of examineResult:{}",
                    creativeId,
                    dspId,
                    advertiserId,
                    JsonSerializationUtils.objToJson(resultListOptional));
                return AD_CREATIVE_EXAMINE_RESULT.NO_EXAMINE_INFO;
              } else {
                List<Object> resultList = resultListOptional.get();
                Object attrObj = resultList.get(0);
                Object expireTimeObj = resultList.get(1);
                Object examineStateObj = resultList.get(2);
                Object signatureObj = resultList.get(3);
                if (null == attrObj
                    || null == expireTimeObj
                    || null == examineStateObj
                    || null == signatureObj) {
                  LogUtil.localError(
                      "Examine ad creative({}) from dsp({}) advertiser({}) lack of fields:{}",
                      creativeId,
                      dspId,
                      advertiserId,
                      JsonSerializationUtils.objToJson(resultList));
                  return AD_CREATIVE_EXAMINE_RESULT.MISSING_FIELD;
                }

                Integer attr = Integer.valueOf((String) attrObj);
                //                Integer interactionType = (Integer) resultList.get(1);
                long expireTime = Long.parseLong((String) expireTimeObj);
                Integer examineState = Integer.valueOf((String) examineStateObj);
                String signature = (String) signatureObj;

                if (!(examineState.equals(1) || examineState.equals(2))) {
                  LogUtil.localWarn(
                      "ad creative({}) from dsp({}) advertiser({}) filtered due to examineState:{}",
                      creativeId,
                      dspId,
                      advertiserId,
                      examineState);
                  return AD_CREATIVE_EXAMINE_RESULT.FAIL;
                }
                long nowSeconds = System.currentTimeMillis() / 1000;
                if (expireTime < nowSeconds) {
                  LogUtil.localWarn(
                      "ad creative({}) from dsp({}) advertiser({}) filtered due to expired! expireTime:{}, now:{}",
                      creativeId,
                      dspId,
                      advertiserId,
                      expireTime,
                      nowSeconds);
                  return AD_CREATIVE_EXAMINE_RESULT.EXPIRED;
                }
                boolean signatureVerified = Boolean.FALSE;
                if (attr.equals(StandardDspCreativeType.SPLASH_IMAGE.getTypeNum())) {
                  signatureVerified =
                      signature.equals(
                          MD5Util.encrypt(creativeId + title + Strings.nullToEmpty(desc) + imgUrl));
                } else if (attr.equals(StandardDspCreativeType.VIDEO_TEMPLATE.getTypeNum())) {
                  signatureVerified =
                      signature.equals(
                          MD5Util.encrypt(
                              creativeId
                                  + title
                                  + Strings.nullToEmpty(desc)
                                  + videoUrl
                                  + videoImgUrl));
                } else if (attr.equals(StandardDspCreativeType.VIDEO_HTML_SNIPPET.getTypeNum())) {
                  signatureVerified =
                      signature.equals(
                          MD5Util.encrypt(
                              creativeId + title + Strings.nullToEmpty(desc) + videoUrl));
                } else if (attr.equals(StandardDspCreativeType.ONLY_VIDEO.getTypeNum())) {
                  signatureVerified =
                      signature.equals(
                          MD5Util.encrypt(
                              creativeId + title + Strings.nullToEmpty(desc) + videoUrl));
                }
                if (signatureVerified) {
                  return AD_CREATIVE_EXAMINE_RESULT.PASS;
                } else {
                  return AD_CREATIVE_EXAMINE_RESULT.SIGNATURE_FAILED;
                }
              }
            })
        .onErrorResume( // 发生异常默认返回True
            Exception.class,
            e -> {
              LogUtil.localError(
                  "Examine ad creative({}) from dsp({}) advertiser({}) error!",
                  creativeId,
                  advertiserId,
                  dspId,
                  e);
              return Mono.just(AD_CREATIVE_EXAMINE_RESULT.ERROR);
            });
  }
}
