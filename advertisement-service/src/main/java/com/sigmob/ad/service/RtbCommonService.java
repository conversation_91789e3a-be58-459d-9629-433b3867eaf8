package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.constants.enums.DeveloperState;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SspEnterException;
import com.sigmob.ad.core.publishing.developer.Developer;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.ApiAdSlotMappingDao;
import com.sigmob.ad.dao.ApiAppMappingDao;
import com.sigmob.ad.dao.DeveloperDao;
import com.sigmob.ssp.pb.publishing.management.AdSlotInfo;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class RtbCommonService {

  private final AppService appService;

  private final AdSlotInfoService adSlotInfoService;

  private final ApiAppMappingDao apiAppMappingDao;

  private final ApiAdSlotMappingDao apiAdSlotMappingDao;

  private final DeveloperDao developerDao;

  /**
   * 查找三方appId在sigmob的配置
   *
   * @param developerId
   * @param osType
   * @param appBundle
   * @param thirdPartyAppId
   * @param requestFlowType
   * @param requestIp
   * @return
   *     reactor.core.publisher.Mono<java.util.Optional<com.sigmob.ad.core.publishing.app.AppInfo>>
   */
  public Mono<Optional<AppInfo>> getSigmobAppOptional(
      Long developerId,
      int osType,
      String appBundle,
      String thirdPartyAppId,
      RequestFlowType requestFlowType,
      String requestIp) {
    if (Strings.isNullOrEmpty(thirdPartyAppId)) {
      return Mono.just(Optional.empty());
    } else {
      return apiAppMappingDao
          .getApiAppMapping(developerId, thirdPartyAppId)
          .flatMap(
              apiAppMappingOptional -> {
                if (apiAppMappingOptional.isPresent()) {
                  Integer appId = apiAppMappingOptional.get().getAppId();
                  if (null != appId && appId > 0) {
                    return appService.getApiSigmobAppInfoOptional(
                        developerId, appId, osType, appBundle, requestFlowType, requestIp);
                  }
                }
                return Mono.just(Optional.empty());
              });
    }
  }

  /**
   * 查找三方appId在sigmob的配置
   *
   * @param developerId
   * @param osType 请求设备的操作系统类型
   * @param appBundle
   * @param thirdPartyAppId
   * @param requestFlowType
   * @return
   */
  public Mono<AppInfo> getSigmobApp(
      Long developerId,
      int osType,
      String appBundle,
      String thirdPartyAppId,
      RequestFlowType requestFlowType,
      String requestIp) {
    return apiAppMappingDao
        .getApiAppMapping(developerId, thirdPartyAppId)
        .flatMap(
            apiAppMappingOptional -> {
              if (apiAppMappingOptional.isPresent()) {
                Integer appId = apiAppMappingOptional.get().getAppId();
                if (null != appId && appId > 0) {
                  return appService.getApiSigmobAppInfo(
                      developerId, appId, osType, appBundle, requestFlowType, requestIp);
                }
              }
              return Mono.error(
                  new SspEnterException(
                      ErrorCode.THIRDPARTY_ADX.NO_APP_MAPPING.getCode(),
                      "cannot find sigmob appId for thirdPartyAppId(" + thirdPartyAppId + ")"));
            });
  }

  /**
   * 查找三方广告位对应在sigmob的配置
   *
   * @param developerId
   * @param thirdPartyAppId
   * @param thirdPartyAdSlotId
   * @return
   */
  public Mono<String> getSigmobAdSlotId(
      long developerId, String thirdPartyAppId, String thirdPartyAdSlotId) {
    return getSigmobAdSlotIdOptional(developerId, thirdPartyAppId, thirdPartyAdSlotId)
        .map(
            adSlotIdOptional -> {
              if (adSlotIdOptional.isEmpty() || Strings.isNullOrEmpty(adSlotIdOptional.get())) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.THIRDPARTY_ADX.NO_AD_SLOT_MAPPING.getCode(),
                        "cannot find sigmob adSlotId for thirdPartyAdSlotId("
                            + thirdPartyAdSlotId
                            + ")"));
              }
              return adSlotIdOptional.get();
            });
  }

  public Mono<Optional<String>> getSigmobAdSlotIdOptional(
      long developerId, String thirdPartyAppId, String thirdPartyAdSlotId) {
    return apiAdSlotMappingDao
        .getApiAdSlotMapping(developerId, thirdPartyAppId, thirdPartyAdSlotId)
        .map(
            adSlotIdOptional ->
                adSlotIdOptional.isEmpty()
                    ? Optional.empty()
                    : Optional.ofNullable(adSlotIdOptional.get().getAdSlotId()));
  }

  /**
   * 查找开发者账户信息
   *
   * @param developerId
   * @return
   */
  public Mono<Developer> getSigmobDeveloper(long developerId) {

    return developerDao
        .getApiDeveloper(developerId)
        .map(
            developerOptional -> {
              if (developerOptional.isEmpty()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.THIRDPARTY_ADX.NO_DEVELOPER_MAPPING.getCode(),
                        "cannot find sigmob developer for id(" + developerId + ")"));
              }
              Developer developer = developerOptional.get();
              Integer developerState = developer.getState();
              if (developerState.equals(DeveloperState.INACTIVE.ordinal())
                  || developerState.equals(DeveloperState.BANNED.ordinal())) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.THIRDPARTY_ADX.DEVELOPER_STATE_INVALID.getCode(),
                        " sigmob developer(" + developerId + ") invalid. state=" + developerState));
              }
              //              if
              // (!apiDeveloper.getAccessMode().equals(DeveloperAccessMode.API.getCode())) {
              //                throw Exceptions.propagate(
              //                    new SspEnterException(
              //                        ErrorCode.THIRDPARTY_ADX.DEVELOPER_STATE_INVALID.getCode(),
              //                        " sigmob developer("
              //                            + developerId
              //                            + ") access mode error!. accessMode="
              //                            + apiDeveloper.getAccessMode()));
              //              }
              return developer;
            });
  }

  public Mono<Optional<Developer>> getSigmobDeveloperOptional(long developerId) {

    return developerDao
        .getApiDeveloper(developerId)
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "getSigmobDeveloperOptional(developerId:{}) error", developerId, e);
              return Mono.just(Optional.empty());
            });
  }

  /**
   * @param appId
   * @param adSlotId
   * @param sigmobAdType
   * @return
   */
  public Mono<AdSlotInfo> getAdSlotInfo(int appId, String adSlotId, int sigmobAdType) {

    return adSlotInfoService
        .getAdSlotInfo(appId, adSlotId)
        .map(
            adSlotInfoOptional -> {
              // 广告单元不存在
              if (adSlotInfoOptional.isEmpty()) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_AD_SLOT_NOT_EXISTS,
                        "slot id not exists!appId:" + appId + ", slotId:" + adSlotId));
              }
              AdSlotInfo adSlotInfo = adSlotInfoOptional.get();
              // 如果运营在中控关闭了广告单元，则禁止访问sigmob，包括实时api
              if (adSlotInfo.getAdState() == 2) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_AD_SLOT_CLOSED_BY_OPERATION,
                        "ad slot has been closed by operation staff!appId:"
                            + appId
                            + ",slotId:"
                            + adSlotId));
              }

              // 媒体关闭了广告单元
              if (adSlotInfo.getState() != 1) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_AD_SLOT_IS_CLOSED,
                        "ad slot has been closed!appId:" + appId + ",slotId:" + adSlotId));
              }

              // 请求的广告类型与配置的广告位广告类型不匹配
              if (0 != sigmobAdType && adSlotInfo.getType() != sigmobAdType) {
                throw Exceptions.propagate(
                    new SspEnterException(
                        ErrorCode.REQUEST_AD_SLOT_NOT_MATCH_AD_TYPE,
                        "request adType("
                            + sigmobAdType
                            + ") not match config adSlot adType("
                            + adSlotInfo.getType()
                            + ")"));
              }

              return adSlotInfo;
            });
  }
}
