package com.sigmob.ad.service;

import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.AdvertiserDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.sigmob.ad.core.exception.ErrorCode.ADVERTISER_EXAMINE_RESULT;

@Service
@AllArgsConstructor
public class AdvertiserService {

  private final AdvertiserDao advertiserDao;

  /**
   * 广告主是否审核通过
   *
   * @param dspId
   * @param advertiserId
   * @return
   */
  public Mono<ADVERTISER_EXAMINE_RESULT> isExamined(int dspId, String advertiserId) {
    return advertiserDao
        .getExamineResult(dspId, advertiserId)
        .map(
            resultListOptional -> {
              if (resultListOptional.isEmpty()
                  || CollectionUtils.isEmpty(resultListOptional.get())
                  || resultListOptional.get().size() < 2) {
                LogUtil.localError(
                    "Examine advertiser({}) from dsp({}) lack of examineResult:{}",
                    advertiserId,
                    dspId,
                    JsonSerializationUtils.objToJson(resultListOptional));
                return ADVERTISER_EXAMINE_RESULT.NO_EXAMINE_INFO;
              } else {
                List<Object> resultList = resultListOptional.get();
                Object examineTypeObj = resultList.get(0);
                Object examineStateObj = resultList.get(1);
                if (null == examineTypeObj || null == examineStateObj) {
                  LogUtil.localError(
                      "Examine advertiser({}) from dsp({}) lack of fields:{}",
                      advertiserId,
                      dspId,
                      JsonSerializationUtils.objToJson(resultList));
                  return ADVERTISER_EXAMINE_RESULT.MISSING_FIELD;
                }
                Integer examineType = Integer.valueOf((String) examineTypeObj);
                Integer examineState = Integer.valueOf((String) examineStateObj);

                if (examineType.equals(0)) { // 免审
                  return ADVERTISER_EXAMINE_RESULT.PASS;
                } else { // 抽审
                  if (examineState.equals(1) || examineState.equals(2)) { // 待审或审核通过
                    return ADVERTISER_EXAMINE_RESULT.PASS;
                  }
                  return ADVERTISER_EXAMINE_RESULT.FAIL;
                }
              }
            })
        .onErrorResume(
            Exception.class,
            e -> {
              LogUtil.localError(
                  "Examine advertiser({}) from dsp({}) error!", advertiserId, dspId, e);
              return Mono.just(ADVERTISER_EXAMINE_RESULT.ERROR);
            });
  }
}
