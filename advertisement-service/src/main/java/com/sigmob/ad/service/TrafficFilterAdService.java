package com.sigmob.ad.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.ad.core.config.api.RtbApiConfig;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.TrafficFilterAdDao;
import com.sigmob.sigdsp.pb.Ad;
import com.twofishes.config.manager.ConfigManager;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Date 2022/9/16 11:05 @Description
 */
@Service
@AllArgsConstructor
public class TrafficFilterAdService {

  private TrafficFilterAdDao trafficFilterAdDao;

  /** */
  public Mono<Map<Integer, Integer>> filterAd(
      int requestFlowType, long developerId, String uid, List<Ad.Builder> adsBuilderList) {
    RtbApiConfig rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
    if (null != rtbApiConfig) {
      var filterTimeConfigOptional =
          Optional.ofNullable(rtbApiConfig.getConfigs())
              .map(configs -> configs.get(RequestFlowType.getApiName(requestFlowType, developerId)))
              .map(RtbApiConfig.ApiConfig::getAdToUserInterval);
      if (filterTimeConfigOptional.isPresent() && filterTimeConfigOptional.get() > 0) {
        var adList = adsBuilderList.stream().filter(ad -> ad.getErrorCode() == 0).toList();
        if (!adList.isEmpty()) {
          List<Mono<Boolean>> executeMono = Lists.newArrayListWithCapacity(adList.size());
          adList.forEach(
              ad ->
                  executeMono.add(
                      trafficFilterAdDao
                          .isAdFilter(requestFlowType, ad.getAdSourceChannel(), ad.getCrid(), uid)
                          .onErrorResume(
                              e -> {
                                LogUtil.localError(e.getMessage());
                                return Mono.just(Boolean.FALSE);
                              })));
          Map<Integer, Integer> filterAdMap = Maps.newHashMapWithExpectedSize(adList.size());
          return Mono.zip(
              executeMono,
              result -> {
                for (int i = 0; i < result.length; i++) {
                  boolean isFilter = (Boolean) result[i];
                  if (isFilter) {
                    var adBuilder = adList.get(i);
                    for (int j = 0; j < adsBuilderList.size(); j++) {
                      if (adBuilder.getVid().equals(adsBuilderList.get(j).getVid())) {
                        filterAdMap.put(j, ErrorCode.DSP_RESPONSE_AD_FILTER_FOR_USER_BY_TRAFFIC);
                      }
                    }
                    adBuilder.setErrorCode(ErrorCode.DSP_RESPONSE_AD_FILTER_FOR_USER_BY_TRAFFIC);
                  }
                }
                return filterAdMap;
              });
        }
      }
    }

    return Mono.just(Maps.newHashMap());
  }

  /** 设置dsp返回相同广告在一段时间内不再下发 */
  public Mono<Boolean> setTrafficAdFilter(
      int requestFlowType, RtbResponse rtbResponse, RtbApiConfig.ApiConfig apiConfig, String uid) {
    if (null != apiConfig && DeviceUtil.isValidUid(uid)) {
      var adToUserInterval = apiConfig.getAdToUserInterval();
      if (null != adToUserInterval && adToUserInterval > 0) {
        if (rtbResponse.getErrorCode() == 0 && rtbResponse.hasBidResponse()) {
          var bidResponse = rtbResponse.getBidResponse();
          if (bidResponse.getAdsCount() > 0) {
            var adList =
                bidResponse.getAdsList().stream()
                    .filter(ad -> ad.getErrorCode() == 0)
                    .collect(Collectors.toList());
            if (adList.size() > 0) {
              List<Mono<Boolean>> executeMono = Lists.newArrayListWithCapacity(adList.size());
              adList.forEach(
                  ad ->
                      executeMono.add(
                          trafficFilterAdDao
                              .setFilterAd(
                                  requestFlowType,
                                  ad.getAdSourceChannel(),
                                  ad.getCrid(),
                                  uid,
                                  Duration.ofSeconds(adToUserInterval))
                              .onErrorResume(
                                  e -> {
                                    LogUtil.localError(e.getMessage());
                                    return Mono.just(Boolean.FALSE);
                                  })));
              return Mono.zip(executeMono, result -> Boolean.TRUE);
            }
          }
        }
      }
    }

    return Mono.just(Boolean.FALSE);
  }
}
