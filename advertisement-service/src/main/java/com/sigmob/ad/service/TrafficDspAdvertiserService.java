package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.TrafficDspAdvertiserDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Date 2022/9/15 12:30 @Description
 */
@Service
@AllArgsConstructor
public class TrafficDspAdvertiserService {

  private final TrafficDspAdvertiserDao trafficDspAdvertiserDao;

  //  @Deprecated
  //  public Mono<Optional<Map<String, String>>> getDspTrafficAdvertiser(
  //      int requestFlowType, Set<String> dspIds) {
  //
  //    if (CollectionUtils.isEmpty(dspIds)) {
  //      return Mono.just(Optional.empty());
  //    }
  //
  //    var dspAdvertiserMonoList =
  //        dspIds.stream()
  //            .filter(p -> !Strings.isNullOrEmpty(p))
  //            .map(
  //                p ->
  //                    trafficDspAdvertiserDao
  //                        .getDspAdvertiserId(p, requestFlowType)
  //                        .onErrorResume(
  //                            e -> {
  //                              LogUtil.localError(e.getMessage());
  //                              return Mono.just(Optional.empty());
  //                            }))
  //            .collect(Collectors.toList());
  //    return Mono.zip(
  //        dspAdvertiserMonoList,
  //        result -> {
  //          if (result.length > 0) {
  //            var dspAdvertiserMap =
  //                Arrays.stream(result)
  //                    .filter(Objects::nonNull)
  //                    .map(r -> (Optional<Map<String, String>>) r)
  //                    .filter(Optional::isPresent)
  //                    .map(Optional::get)
  //                    .filter(r -> !CollectionUtils.isEmpty(r))
  //                    .flatMap(map -> map.entrySet().stream())
  //                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  //
  //            if (!CollectionUtils.isEmpty(dspAdvertiserMap)) {
  //              return Optional.of(dspAdvertiserMap);
  //            }
  //          }
  //
  //          return Optional.empty();
  //        });
  //  }
}
