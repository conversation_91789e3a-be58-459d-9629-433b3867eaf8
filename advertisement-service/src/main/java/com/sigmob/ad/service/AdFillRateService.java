package com.sigmob.ad.service;

import com.sigmob.ad.core.config.AdFillRatePriceStrategyConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.SettlementSetting;
import com.sigmob.ad.dao.AdFillRateDao;
import com.twofishes.config.manager.ConfigManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 广告位填充率
 *
 * <AUTHOR> @Date 2023/3/3 15:18 @Description
 */
@RequiredArgsConstructor
@Service
public class AdFillRateService {

  private static final Mono<Optional<Double>> MONO_NOT_ADJUST_ERPM = Mono.just(Optional.empty());

  private final AdFillRateDao adFillRateDao;

  /**
   * 广告保填充率价格调整系数
   *
   * <AUTHOR>
   * @date 2023/3/6 15:54
   * @param adSlotId
   * @param settlementSetting
   * @param adFillRatio
   * @return reactor.core.publisher.Mono<java.util.Optional<java.lang.Double>>
   */
  public Mono<Optional<Double>> getAdFillPriceStrategyWf(
      String adSlotId, int expectedFloor, int settlementSetting, int adFillRatio) {

    if (settlementSetting != SettlementSetting.FIXED_ERPM.getCode() || adFillRatio <= 0) {
      return MONO_NOT_ADJUST_ERPM;
    } else {

      var instance = Calendar.getInstance();
      var currentDay = instance.get(Calendar.DAY_OF_MONTH);
      var currentHour = instance.get(Calendar.HOUR_OF_DAY);
      var currentMinute = instance.get(Calendar.MINUTE);
      int lastMinute =
          currentMinute == 0 ? (currentHour == 0 ? currentMinute : 59) : currentMinute - 1;
      int lastHour =
          currentHour == 0
              ? (currentMinute == 0 ? 24 : currentHour)
              : (currentMinute == 0 ? currentHour - 1 : currentHour);

      String lastIndex = Integer.toString(lastHour) + lastMinute;
      AdFillRatePriceStrategyConfig config = ConfigManager.get(AdFillRatePriceStrategyConfig.class);
      var priceAdjustCoefficient = config.getPriceAdjustCoefficient();

      return adFillRateDao.getAdFillPriceWf(
          adSlotId,
          currentDay,
          lastIndex,
          adFillRatio,
          new BigDecimal(adFillRatio)
              .divide(new BigDecimal(100), 2, RoundingMode.CEILING)
              .doubleValue(),
          expectedFloor,
          priceAdjustCoefficient.getR1(),
          priceAdjustCoefficient.getR2(),
          priceAdjustCoefficient.getR3());
    }
  }

  public Mono<Boolean> addAdFillCount(
      String adSlotId, int expectedFloor, int settlementSetting, int adFillRatio, boolean fillAd) {
    if (settlementSetting != SettlementSetting.FIXED_ERPM.getCode()
        || adFillRatio <= 0
        || !fillAd) {
      return Constants.MONO_FALSE;
    } else {

      var instance = Calendar.getInstance();
      var currentDay = instance.get(Calendar.DAY_OF_MONTH);
      var currentHour = instance.get(Calendar.HOUR_OF_DAY);
      var currentMinute = instance.get(Calendar.MINUTE);
      int lastMinute =
          currentMinute == 0 ? (currentHour == 0 ? currentMinute : 59) : currentMinute - 1;
      int lastHour =
          currentHour == 0
              ? (currentMinute == 0 ? 24 : currentHour)
              : (currentMinute == 0 ? currentHour - 1 : currentHour);

      String lastIndex = Integer.toString(lastHour) + lastMinute;

      return adFillRateDao
          .addAdFillCount(adSlotId, expectedFloor, adFillRatio, currentDay, lastIndex)
          .defaultIfEmpty(Boolean.FALSE);
    }
  }

  public Mono<Optional<Map<Object, Object>>> getAdFillStrategyStat(
      String adSlotId, int expectedFloor, int originFillRate) {
    var instance = Calendar.getInstance();
    var currentDay = instance.get(Calendar.DAY_OF_MONTH);

    return adFillRateDao.getAdFillStrategyStat(adSlotId, expectedFloor, originFillRate, currentDay);
  }
}
