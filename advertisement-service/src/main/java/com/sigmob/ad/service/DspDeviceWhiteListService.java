package com.sigmob.ad.service;

import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.dao.DspDeviceWhiteListDao;
import com.sigmob.sigdsp.pb.DeviceId;
import com.sigmob.sigdsp.pb.Version;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class DspDeviceWhiteListService {

  private final DspDeviceWhiteListDao dspDeviceWhiteListDao;

  public Mono<Optional<Integer>> getWhiteListDspId(
      DeviceId did, String adSlotId, OsType osType, Version sdkVersion) {
    if (OsType.ANDROID.equals(osType)) {
      return dspDeviceWhiteListDao.getAndroidWhiteListDspId(did, adSlotId, sdkVersion);
    } else if (OsType.HARMONY_OS.equals(osType)) {
      return dspDeviceWhiteListDao.getHarmonyOsWhiteListDspId(did, adSlotId);
    } else {
      return dspDeviceWhiteListDao.getIosWhiteListDspId(did, adSlotId);
    }
  }
}
