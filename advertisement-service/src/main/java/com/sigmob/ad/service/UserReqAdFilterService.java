package com.sigmob.ad.service;

import com.sigmob.ad.core.config.mediation.AppMiscellaneousConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.UserReqAdFilterDao;
import com.twofishes.config.manager.ConfigManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> @Date 2024/3/22 15:22 @Description
 */
@Service
@RequiredArgsConstructor
public class UserReqAdFilterService {

  private final UserReqAdFilterDao userReqAdFilterDao;

  public Mono<Boolean> filterUserReqAd(int appId, String uid) {
        AppMiscellaneousConfig config = ConfigManager.get(AppMiscellaneousConfig.class);
        if (null != config) {
          if (config.disableUserReqAd(appId)) {
    return userReqAdFilterDao
        .filterUserAdReq(uid)
        .onErrorResume(
            e -> {
              LogUtil.localError("filterUserReq(appId:{}, uid:{}) error", appId, uid, e);
              return Constants.MONO_FALSE;
            });
          }
    }
    return Constants.MONO_FALSE;
  }
}
