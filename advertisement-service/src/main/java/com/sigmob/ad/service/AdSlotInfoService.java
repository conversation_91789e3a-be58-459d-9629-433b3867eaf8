package com.sigmob.ad.service;

import com.sigmob.ad.adx.rpc.grpc.RtbRequest;
import com.sigmob.ad.core.advertisement.AdType;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.SettlementMode;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SspEnterException;
import com.sigmob.ad.core.publishing.developer.Developer;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.ad.dao.AdSlotInfoDao;
import com.sigmob.ssp.pb.publishing.management.AdSlotInfo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class AdSlotInfoService {

  private final AdSlotInfoDao adSlotInfoDao;

  private final ABTestService abTestService;

  /**
   * 查询广告位信息
   *
   * @param appId 应用id
   * @param slotId 广告位id
   * @return
   */
  public Mono<Optional<AdSlotInfo>> getAdSlotInfo(int appId, String slotId) {
    return adSlotInfoDao.getAdSlotInfo(appId, slotId);
  }

  public Mono<AdSlotInfo> getAdSlotInfoAndCheck(int appId, String slotId) {
    return getAdSlotInfo(appId, slotId)
        .flatMap(
            res ->
                res.<Mono<? extends AdSlotInfo>>map(Mono::just)
                    .orElseGet(
                        () ->
                            Mono.error(
                                Exceptions.propagate(
                                    new SspEnterException(
                                        ErrorCode.REQUEST_AD_SLOT_NOT_EXISTS,
                                        "请求的广告位"
                                            + slotId
                                            + "不存在，或者appId:"
                                            + appId
                                            + "与广告位不匹配。请检查请求参数！")))));
  }

  /**
   * 获取广告位请求dsp方式（并行 or 串行）
   *
   * @param rtbRequest
   * @param developer
   * @return
   *     reactor.core.publisher.Mono<com.sigmob.ad.service.ABTestService.ABTestResult<com.sigmob.ad.core.rtb.RtbConstants.ExecuteMode,java.lang.Float>>
   */
  public Mono<ABTestService.ABTestResult<RtbConstants.ExecuteMode, Float>> getSlotReqDspMode(
      RtbRequest rtbRequest, Developer developer) {
    var adSlot = rtbRequest.getBidRequest().getSlots(0);
    var slotId = adSlot.getAdslotId();
    var requestFlowType = rtbRequest.getRequestFlowType();
    var settlementMode = adSlot.getSettlementMode();
    var adType = adSlot.getAdslotType(0);
    var hbType = rtbRequest.getHbType();
    var defineFloorAdType = developer.getDefineFloorAdType();

    // api 流量、header bidding流量、信息流广告、开发自设底价的指定广告类型并行请求
    if (requestFlowType != RequestFlowType.SDK.getCode()
        || hbType > 0
        || settlementMode == SettlementMode.DEVELOPER_ECPM.getCode()
            && !CollectionUtils.isEmpty(defineFloorAdType)
            && defineFloorAdType.contains(adType)
        || adType == AdType.NATIVE.getTypeNum()) {
      return Mono.just(ABTestService.DSP_ALL_PARALLEL);
    }

    return adSlotInfoDao
        .getAdSlotParallelRatio(slotId)
        .map(
            parallelRatioOptional -> {
              if (parallelRatioOptional.isEmpty()) {
                return abTestService.expDeviceDspReqMode(
                    rtbRequest.getBidRequest().getDevice().getDid().getUid(),
                    rtbRequest.getBidRequest().getDevice().getOsType());
              } else {
                var parallelRatio = parallelRatioOptional.get();
                if (parallelRatio <= 0) {
                  return ABTestService.DSP_ALL_SEQUENCE;
                } else if (parallelRatio >= 100) {
                  return ABTestService.DSP_ALL_PARALLEL;
                }
                var uid = rtbRequest.getBidRequest().getDevice().getDid().getUid();
                int deviceBucket = DeviceUtil.getDeviceBucket(uid, 100);
                Float sequencePercent =
                    new BigDecimal(100 - parallelRatio)
                        .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                        .floatValue();
                if (deviceBucket < parallelRatio) {
                  return new ABTestService.ABTestResult<>(
                      RtbConstants.ExecuteMode.PARALLEL, sequencePercent);
                } else {
                  return new ABTestService.ABTestResult<>(
                      RtbConstants.ExecuteMode.SEQUENTIAL, sequencePercent);
                }
              }
            });
  }

  /**
   * 获取广告位对应appId
   *
   * @param slotId 广告位id
   * @return
   */
  public Mono<Optional<Integer>> getAdSlotAppId(String slotId) {
    return adSlotInfoDao.getAdSlotAppId(slotId);
  }
}
