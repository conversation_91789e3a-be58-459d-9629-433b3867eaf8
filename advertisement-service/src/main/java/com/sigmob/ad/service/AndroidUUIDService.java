package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SspBusinessException;
import com.sigmob.ad.dao.AndroidUUIdDao;
import com.sigmob.sigdsp.pb.DeviceId;
import com.sigmob.sigdsp.pb.Version;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class AndroidUUIDService {

  private final AndroidUUIdDao androidUUIdDao;

  /**
   * 检查Android设备id是否正确
   *
   * @param did did
   * @return true-正确;false-错误
   */
  public Mono<Boolean> checkValidUid(DeviceId did, boolean ge10) {
    return androidUUIdDao
        .getUid(did, ge10)
        .map(
            suidOptional -> {
              if (suidOptional.isEmpty()) {
                return Boolean.FALSE;
              }
              String suid = suidOptional.get();
              if (!DeviceUtil.isValidUid(suid) || !suid.equals(did.getUid())) {
                return Boolean.FALSE;
              } else {
                return Boolean.TRUE;
              }
            })
        .defaultIfEmpty(Boolean.FALSE);
  }

  public Mono<Optional<String>> getUId(DeviceId did, boolean ge10) {
    return androidUUIdDao.getUid(did, ge10).defaultIfEmpty(Optional.empty());
  }

  public Mono<Optional<String>> generateUId(DeviceId did, boolean ge10) {
    String imei = did.getImei();
    String imeiMd5 = did.getImeiMd5();
    String gaid = did.getGaid();
    String gaidMd5 = did.getGaidMd5();
    String androidId = did.getAndroidId();
    String androidIdMd5 = did.getAndroidIdMd5();
    String oaid = did.getOaid();
    String oaidMd5 = did.getOaidMd5();

    Mono<Optional<String>> findUidMono = Mono.just(Optional.<String>empty());
    if (DeviceUtil.isValidImei(imei)
        || DeviceUtil.isValidImeiMd5(imeiMd5)
        || DeviceUtil.isValidUid(gaid)
        || DeviceUtil.isValidMd5(gaidMd5)
        || DeviceUtil.isValidUid(oaid)
        || DeviceUtil.isValidMd5(oaidMd5)) {
      findUidMono = getUId(did, ge10);
    }

    return Mono.from(findUidMono)
        .map(
            uuidOptional -> {
              var validatedDid = did.toBuilder();
              if (uuidOptional.isPresent()) {
                validatedDid.setUid(uuidOptional.get());
              } else {
                String uuid = StringUtils.EMPTY;

                if (!ge10) {
                  // imei
                  if (DeviceUtil.isValidImei(imei)) {
                    validatedDid.setImei(imei);
                    if (Strings.isNullOrEmpty(uuid)) {
                      uuid = imei;
                    }
                  } else {
                    validatedDid.clearImei();
                  }

                  // imei md5
                  if (DeviceUtil.isValidImeiMd5(imeiMd5)) {
                    validatedDid.setImeiMd5(imeiMd5.toLowerCase());
                    if (Strings.isNullOrEmpty(uuid)) {
                      uuid = imeiMd5;
                    }
                  } else {
                    if (DeviceUtil.isValidImei(imei)) {
                      String newImeiMd5 = DigestUtils.md5DigestAsHex(imei.getBytes());
                      validatedDid.setImeiMd5(newImeiMd5);
                    }
                  }
                } else {
                  validatedDid.clearImei();
                  validatedDid.clearImeiMd5();
                }

                // gaid
                if (DeviceUtil.isValidUid(gaid)) {
                  validatedDid.setGaid(gaid);
                  if (Strings.isNullOrEmpty(uuid)) {
                    uuid = gaid;
                  }
                } else {
                  validatedDid.clearGaid();
                }

                // gaid md5
                if (DeviceUtil.isValidMd5(gaidMd5)) {
                  validatedDid.setGaidMd5(gaidMd5);
                  if (Strings.isNullOrEmpty(uuid)) {
                    uuid = gaidMd5;
                  }
                } else {
                  if (DeviceUtil.isValidUid(gaid)) {
                    String newGaidMd5 = DigestUtils.md5DigestAsHex(gaid.getBytes());
                    validatedDid.setGaidMd5(newGaidMd5);
                  } else {
                    validatedDid.clearGaidMd5();
                  }
                }

                if (ge10) {
                  // oaid
                  if (DeviceUtil.isValidUid(oaid)) {
                    validatedDid.setOaid(oaid);
                    if (Strings.isNullOrEmpty(uuid)) {
                      uuid = oaid;
                    }
                  } else {
                    validatedDid.clearOaid();
                  }

                  // oaid md5
                  if (DeviceUtil.isValidMd5(oaidMd5)) {
                    validatedDid.setOaidMd5(oaidMd5);
                    if (Strings.isNullOrEmpty(uuid)) {
                      uuid = oaidMd5;
                    }
                  } else {
                    if (DeviceUtil.isValidUid(oaid)) {
                      String newOaidMd5 = DigestUtils.md5DigestAsHex(oaid.getBytes());
                      validatedDid.setOaidMd5(newOaidMd5);
                    } else {
                      validatedDid.clearOaidMd5();
                    }
                  }
                } else {
                  validatedDid.clearOaid();
                  validatedDid.clearOaidMd5();
                }

                if (DeviceUtil.isValidAndroidId(androidId)) {
                  validatedDid.setAndroidId(androidId);
                  if (Strings.isNullOrEmpty(uuid)) {
                    uuid = androidId;
                  }
                } else {
                  validatedDid.clearAndroidId();
                }

                if (DeviceUtil.isValidMd5(androidIdMd5)) {
                  validatedDid.setAndroidIdMd5(androidIdMd5);
                  if (Strings.isNullOrEmpty(uuid)) {
                    uuid = androidIdMd5;
                  }
                } else {
                  if (DeviceUtil.isValidAndroidId(androidId)) {
                    String newAndroidIdMd5 = DigestUtils.md5DigestAsHex(androidId.getBytes());
                    validatedDid.setAndroidIdMd5(newAndroidIdMd5);
                  } else {
                    validatedDid.clearAndroidIdMd5();
                  }
                }

                validatedDid.setUid(uuid);
              }
              return validatedDid;
            })
        .flatMap(
            validatedDid -> {
              if (!DeviceUtil.isValidUid(validatedDid.getUid())) {
                String udid = validatedDid.getUdid();
                if (StringUtils.isEmpty(udid)) {
                  throw Exceptions.propagate(
                      new SspBusinessException(
                          ErrorCode.REQUEST_ERROR_INVALID_UID,
                          "can not generated uid for android!"));
                }
                return Mono.just(Optional.of(udid));
              }
              return Mono.zip(
                      androidUUIdDao
                          .saveUid(
                              validatedDid.getUid(),
                              validatedDid.getGaid(),
                              validatedDid.getGaidMd5(),
                              validatedDid.getImei(),
                              validatedDid.getImeiMd5(),
                              validatedDid.getAndroidId(),
                              validatedDid.getAndroidIdMd5(),
                              validatedDid.getOaid(),
                              validatedDid.getOaidMd5())
                          .onErrorReturn(Boolean.FALSE),
                      saveDeviceId(validatedDid.build()))
                  .map(result -> Optional.of(validatedDid.getUid()))
                  .defaultIfEmpty(Optional.of(validatedDid.getUid()));
            });
  }

  public Mono<Optional<DeviceId>> getDeviceId(DeviceId did, boolean ge10) {
    Optional<String> uid = Optional.of(did.getUid());
    Mono<Optional<String>> uidMono =
        uid.filter(v -> !v.isBlank())
            .map(value -> Mono.just(Optional.of(value)))
            .orElseGet(() -> androidUUIdDao.getUid(did, ge10).defaultIfEmpty(Optional.empty()));
    return uidMono.flatMap(
        uidOptional ->
            uidOptional.map(androidUUIdDao::getDeviceId).orElse(Mono.just(Optional.empty())));
  }

  public Mono<Boolean> saveDeviceId(DeviceId did) {
    return Optional.ofNullable(did).isPresent()
        ? androidUUIdDao.saveDeviceId(did)
        : Mono.just(false);
  }

  /** 如果android 10.0.0以上设备没有oaid，通过其它设备id信息试图找回oaid */
  @Deprecated
  public Mono<Optional<String>> retrieveDeviceId(int osType, DeviceId did, Version osVersion) {
    return (osType == OsType.ANDROID.getTypeNum()
            && did.getOaid().isBlank()
            && null != osVersion
            && !Version.getDefaultInstance().equals(osVersion)
            && DeviceUtil.compareVersion(osVersion, Constants.OS_VERSION_10_0_0) >= 0)
        ? getDeviceId(did, true)
            .defaultIfEmpty(Optional.empty())
            .map(
                retrieveDidOptional -> {
                  if (retrieveDidOptional.isPresent()) {
                    var oaid = retrieveDidOptional.get().getOaid();
                    if (DeviceUtil.isValidUid(oaid)) {
                      return Optional.of(oaid);
                    }
                  }
                  return Optional.empty();
                })
        : Mono.just(Optional.empty());
  }
}
