package com.sigmob.ad.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SspEnterException;
import com.sigmob.ad.core.util.RandomNumberGenerator;
import com.sigmob.ad.dao.AdControlCountDao;
import com.sigmob.ad.dao.AdControlDao;
import com.sigmob.ad.dao.model.AdSlotImpressionControl;
import com.sigmob.ad.dao.model.UserEventControl;
import com.sigmob.ssp.pb.adcontrol.AppControlSetting;
import com.sigmob.ssp.pb.adcontrol.RequestLimit;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@AllArgsConstructor
public class AdControllerService {

  private final AdControlDao dao;

  private final AdControlCountDao countDao;

  /**
   * 查询广告控量频次控制
   *
   * @param appId
   * @param adSlotId
   * @return
   */
  public Mono<Optional<AppControlSetting>> getAppControlSetting(int appId, String adSlotId) {
    return dao.getAppControlSetting(appId, adSlotId);
  }

  /**
   * 广告位曝光数量统计
   *
   * @param dt
   * @param sigAdSlot
   * @return
   */
  public Mono<Optional<AdSlotImpressionControl>> getAdSlotImpression(
      DateTime dt, String sigAdSlot) {
    return countDao.getAdSlotImpression(dt, sigAdSlot);
  }

  /**
   * 设备在广告位曝光数量统计
   *
   * @param dt
   * @param sigAdSlot
   * @return
   */
  public Mono<Optional<UserEventControl>> getUserDeviceEvent(
      DateTime dt, String uid, String sigAdSlot) {
    return countDao.getUserDeviceEvent(dt, uid, sigAdSlot);
  }

  /**
   * 是否达到广告位曝光限制
   *
   * @param appId
   * @param adSlotId
   * @param dt
   * @param dspProtocolType 请求dsp协议类型，如果为null只判断总限制
   * @return
   */
  public Mono<Boolean> isReachAdSlotImpressionLimit(
      int appId, String adSlotId, DateTime dt, DspProtocolType dspProtocolType) {
    return getAppControlSetting(appId, adSlotId)
        .zipWhen(
            appControlSettingOptional -> {
              if (appControlSettingOptional.isPresent()
                  && !RequestLimit.getDefaultInstance()
                      .equals(appControlSettingOptional.get().getRequestLimit())
                  && appControlSettingOptional.get().getRequestLimit().getImpression() > 0
                  && appControlSettingOptional.get().getRequestLimit().getDisableImpression()) {
                return getAdSlotImpression(dt, adSlotId);
              } else {
                return Mono.just(Optional.<AdSlotImpressionControl>empty());
              }
            },
            (appControlSettingOptional, adSlotImpressionOptional) ->
                isReachAdSlotImpressionLimit(
                    appControlSettingOptional, adSlotImpressionOptional, dspProtocolType))
        .defaultIfEmpty(Boolean.FALSE);
  }

  /**
   * 是否达到广告位曝光限制
   *
   * @param appId
   * @param adSlotId
   * @param dt
   * @param dspProtocolType 请求dsp协议类型，如果为null只判断总限制
   * @param appControlSetting
   * @return
   */
  public Mono<Boolean> isReachAdSlotImpressionLimit(
      int appId,
      String adSlotId,
      DateTime dt,
      DspProtocolType dspProtocolType,
      AppControlSetting appControlSetting) {

    var requestLimit = appControlSetting.getRequestLimit();
    if (!RequestLimit.getDefaultInstance().equals(requestLimit)
        && requestLimit.getImpression() > 0
        && requestLimit.getDisableImpression()) {

    } else {
    }

    return getAppControlSetting(appId, adSlotId)
        .zipWhen(
            appControlSettingOptional -> {
              if (appControlSettingOptional.isPresent()
                  && !RequestLimit.getDefaultInstance()
                      .equals(appControlSettingOptional.get().getRequestLimit())
                  && appControlSettingOptional.get().getRequestLimit().getImpression() > 0
                  && appControlSettingOptional.get().getRequestLimit().getDisableImpression()) {
                return getAdSlotImpression(dt, adSlotId);
              } else {
                return Mono.just(Optional.<AdSlotImpressionControl>empty());
              }
            },
            (appControlSettingOptional, adSlotImpressionOptional) ->
                isReachAdSlotImpressionLimit(
                    appControlSettingOptional, adSlotImpressionOptional, dspProtocolType))
        .defaultIfEmpty(Boolean.FALSE);
  }

  /**
   * @param appControlSettingOptional
   * @param adSlotImpressionControlOptional
   * @param dspProtocolType 不为null则判断"直投" or "API"类型请求限制
   * @return
   */
  public Boolean isReachAdSlotImpressionLimit(
      Optional<AppControlSetting> appControlSettingOptional,
      Optional<AdSlotImpressionControl> adSlotImpressionControlOptional,
      DspProtocolType dspProtocolType) {
    if (appControlSettingOptional.isEmpty()
        || adSlotImpressionControlOptional.isEmpty()
        || RequestLimit.getDefaultInstance()
            .equals(appControlSettingOptional.get().getRequestLimit())) {
      return Boolean.FALSE;
    }

    var requestLimit = appControlSettingOptional.get().getRequestLimit();
    var impressionLimit = requestLimit.getImpression();
    var disableImpression = requestLimit.getDisableImpression();
    var adSlotImpressionControl = adSlotImpressionControlOptional.get();
    //    if (null == dspProtocolType) {
    if (!disableImpression
        || adSlotImpressionControl.getTotal() == null
        || adSlotImpressionControl.getTotal() < impressionLimit) {
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
    //    } else {
    //      if (DspProtocolType.SIGMOB_DSP.equals(dspProtocolType)) { // 直投
    //
    //        var disableSigmobDspImpression = requestLimit.getDisableSigmobDspImpression();
    //        var sigmobDspImpressionLimit = requestLimit.getSigmobDspImpression();
    //        if (!disableSigmobDspImpression
    //            || adSlotImpressionControl.getSigdsp() == null
    //            || adSlotImpressionControl.getSigdsp() < sigmobDspImpressionLimit) {
    //          return Boolean.FALSE;
    //        }
    //        return Boolean.TRUE;
    //      } else { // 非直投API
    //        var disableThirdPartyDspImpression = requestLimit.getDisableThirdPartyDspImpression();
    //        var thirdPartyDspImpressionLimit = requestLimit.getThirdPartyDspImpression();
    //        if (!disableThirdPartyDspImpression
    //            || adSlotImpressionControl.getThirdparty() == null
    //            || adSlotImpressionControl.getThirdparty() < thirdPartyDspImpressionLimit) {
    //          return Boolean.FALSE;
    //        }
    //        return Boolean.TRUE;
    //      }
    //    }
  }

  /**
   * 是否达到设备事件频次总限制
   *
   * @param uid
   * @param adSlotId
   * @param appControlSetting
   * @param dt
   * @param dspProtocolType 请求dsp协议类型，如果为null只判断总限制
   * @param deviceEventList
   * @return 0——无限制；1——请求频次限制；2-曝光次数限制
   */
  public Mono<List<Integer>> isReachDeviceEventLimit(
      String uid,
      String adSlotId,
      AppControlSetting appControlSetting,
      DateTime dt,
      DspProtocolType dspProtocolType,
      List<DeviceEvent> deviceEventList) {

    var requestLimit = appControlSetting.getRequestLimit();
    Map<Integer, Integer> invalidEventResult =
        Maps.newHashMapWithExpectedSize(deviceEventList.size());
    for (int i = 0; i < deviceEventList.size(); i++) {
      var deviceEvent = deviceEventList.get(i);
      if (DeviceEvent.REQUEST.equals(deviceEvent) && requestLimit.getRequestDeviceFrequency() == 0
          || DeviceEvent.IMPRESSION.equals(deviceEvent)
              && !requestLimit.getDisableDeviceFrequency()) {
        invalidEventResult.put(i, DeviceEvent.NONE.getCode());
      }
    }
    if (invalidEventResult.size() == deviceEventList.size()) {
      return Mono.just(Lists.newArrayList(invalidEventResult.values()));
    }

    return getUserDeviceEvent(dt, uid, adSlotId)
        .defaultIfEmpty(Optional.empty())
        .map(
            userEventControlOptional -> {
              List<Integer> result = Lists.newArrayListWithCapacity(deviceEventList.size());
              for (int i = 0; i < deviceEventList.size(); i++) {
                if (invalidEventResult.containsKey(i)) {
                  result.add(DeviceEvent.NONE.getCode());
                } else {
                  var deviceEvent = deviceEventList.get(i);
                  if (DeviceEvent.REQUEST.equals(deviceEvent)) {
                    result.add(
                        isReachDeviceRequestLimit(appControlSetting, userEventControlOptional)
                            ? DeviceEvent.REQUEST.getCode()
                            : DeviceEvent.NONE.getCode());
                  } else {
                    result.add(
                        isReachDeviceImpressionLimit(
                                Optional.of(appControlSetting),
                                userEventControlOptional,
                                dspProtocolType)
                            ? DeviceEvent.IMPRESSION.getCode()
                            : DeviceEvent.NONE.getCode());
                  }
                }
              }
              return result;
            });
  }

  /**
   * 是否达到设备请求广告次数上限
   *
   * @param appControlSetting
   * @param userEventControlOptional
   * @return java.lang.Boolean
   */
  public Boolean isReachDeviceRequestLimit(
      AppControlSetting appControlSetting, Optional<UserEventControl> userEventControlOptional) {
    var requestLimit = appControlSetting.getRequestLimit();
    if (userEventControlOptional.isEmpty()
        || RequestLimit.getDefaultInstance().equals(requestLimit)) {
      return Boolean.FALSE;
    }

    var userEventControl = userEventControlOptional.get();
    var requestDeviceFrequency = requestLimit.getRequestDeviceFrequency();
    if (requestDeviceFrequency > 0 && userEventControl.getReqTotal() >= requestDeviceFrequency) {
      return Boolean.TRUE;
    }
    return Boolean.FALSE;
  }

  /**
   * @param appControlSettingOptional
   * @param userEventControlOptional
   * @param dspProtocolType 请求dsp协议类型，如果为null只判断总限制；不为null则判断"直投" or "API"类型请求限制
   * @return
   */
  public Boolean isReachDeviceImpressionLimit(
      Optional<AppControlSetting> appControlSettingOptional,
      Optional<UserEventControl> userEventControlOptional,
      DspProtocolType dspProtocolType) {
    if (appControlSettingOptional.isEmpty()
        || userEventControlOptional.isEmpty()
        || RequestLimit.getDefaultInstance()
            .equals(appControlSettingOptional.get().getRequestLimit())) {
      return Boolean.FALSE;
    }

    var requestLimit = appControlSettingOptional.get().getRequestLimit();
    int configDeviceFrequencyLimit = requestLimit.getDeviceFrequency();
    var disableDeviceFrequency = requestLimit.getDisableDeviceFrequency();
    var userEventControl = userEventControlOptional.get();
    //    if (null == dspProtocolType) {
    if (!disableDeviceFrequency
        || configDeviceFrequencyLimit <= 0
        || userEventControl.getTotal() == null
        || userEventControl.getTotal() < configDeviceFrequencyLimit) {
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
    //    } else {
    //      if (DspProtocolType.SIGMOB_DSP.equals(dspProtocolType)) { // 直投
    //        var disableSigmobDspDeviceFrequency =
    // requestLimit.getDisableSigmobDspDeviceFrequency();
    //        var sigmobDspDeviceFrequencyLimit = requestLimit.getSigmobDspDeviceFrequency();
    //        if (!disableSigmobDspDeviceFrequency
    //            || userEventControl.getSigdsp() == null
    //            || userEventControl.getSigdsp() < sigmobDspDeviceFrequencyLimit) {
    //          return Boolean.FALSE;
    //        }
    //        return Boolean.TRUE;
    //      } else { // 非直投API
    //        var disableThirdPartyDspFrequency = requestLimit.getDisableThirdPartyDspFrequency();
    //        var thirdPartyDspFrequencyLimit = requestLimit.getThirdPartyDspFrequency();
    //        if (!disableThirdPartyDspFrequency
    //            || userEventControl.getThirdparty() == null
    //            || userEventControl.getThirdparty() < thirdPartyDspFrequencyLimit) {
    //          return Boolean.FALSE;
    //        }
    //        return Boolean.TRUE;
    //      }
    //    }
  }

  /**
   * 检查广告请求控量情况
   *
   * @param appControlSetting
   * @param uid
   * @param appId
   * @param adSlotId
   * @return
   */
  public Mono<Boolean> checkRequestFlowVisitLimit(
      AppControlSetting appControlSetting, String uid, int appId, String adSlotId) {
    if (null != appControlSetting) {

      if (appControlSetting.hasRequestLimit()) {
        DateTime dt = new DateTime(Constants.DATE_TIMEZONE);
        var requestLimit = appControlSetting.getRequestLimit();
        var requestRejectRatio = requestLimit.getRequestRejectRatio();
        // 中控配置了丢弃比例
        if (requestRejectRatio == 100
            || requestRejectRatio > 0
                && RandomNumberGenerator.randomInt(100) <= requestRejectRatio) {
          return Mono.error(
              new SspEnterException(
                  ErrorCode.DEVICE_REQUEST_DISCARDED,
                  "deviceUid("
                      + uid
                      + ") request app("
                      + appId
                      + ") adSlot("
                      + adSlotId
                      + ") discarded!"));
        } else {
          Mono<List<Integer>> isReachDeviceEventLimitMono =
              Strings.isNullOrEmpty(uid)
                  ? Mono.just(List.of(DeviceEvent.NONE.getCode(), DeviceEvent.NONE.getCode()))
                  : isReachDeviceEventLimit(
                      uid,
                      adSlotId,
                      appControlSetting,
                      dt,
                      null,
                      List.of(DeviceEvent.REQUEST, DeviceEvent.IMPRESSION));

          return Mono.zip(
                  isReachAdSlotImpressionLimit(appId, adSlotId, dt, null),
                  isReachDeviceEventLimitMono)
              .map(
                  result -> {
                    var adSlotImpressionResult = result.getT1();
                    var deviceEventResult = result.getT2();
                    var deviceRequestResult = deviceEventResult.get(0);
                    var deviceImpressionResult = deviceEventResult.get(1);
                    if (!adSlotImpressionResult
                        && deviceRequestResult.equals(DeviceEvent.NONE.getCode())
                        && deviceImpressionResult.equals(DeviceEvent.NONE.getCode())) {
                      return Boolean.TRUE;
                    } else {
                      if (adSlotImpressionResult) {
                        throw Exceptions.propagate(
                            new SspEnterException(
                                ErrorCode.AD_SLOT_REQUEST_FORBIDDEN_BY_AD_CONTROL,
                                "app("
                                    + appId
                                    + ") adSlot("
                                    + adSlotId
                                    + ") forbidden by ad control!"));
                      } else if (!deviceRequestResult.equals(DeviceEvent.NONE.getCode())) {
                        throw Exceptions.propagate(
                            new SspEnterException(
                                ErrorCode.DEVICE_REQUEST_FREQUENCY_LIMIT,
                                "deviceUid("
                                    + uid
                                    + ") request app("
                                    + appId
                                    + ") adSlot("
                                    + adSlotId
                                    + ") times limited!"));
                      } else {
                        throw Exceptions.propagate(
                            new SspEnterException(
                                ErrorCode.AD_SLOT_REQUEST_FORBIDDEN_BY_DEVICE_FREQUENCY,
                                "deviceUid("
                                    + uid
                                    + ") request app("
                                    + appId
                                    + ") adSlot("
                                    + adSlotId
                                    + ") forbidden by ad control device frequency!"));
                      }
                    }
                  });
        }
      }
    }
    return Constants.MONO_TRUE;
  }

  /**
   * 检查广告请求控量情况
   *
   * @param uid
   * @param appId
   * @param adSlotId
   * @return
   */
  public Mono<Boolean> checkRequestFlowVisitLimit(String uid, int appId, String adSlotId) {

    return getAppControlSetting(appId, adSlotId)
        .flatMap(
            appControlSettingOptional ->
                checkRequestFlowVisitLimit(
                    appControlSettingOptional.orElse(null), uid, appId, adSlotId));
  }

  @AllArgsConstructor
  @Getter
  enum DeviceEvent {
    NONE(0),
    REQUEST(1),
    IMPRESSION(2);

    final int code;
  }

  //  public Flux<String> adControlKeys(){
  //    return countDao.adControlKeys();
  //  }
}
