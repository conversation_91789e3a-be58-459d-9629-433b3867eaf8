# ADX竞价系统核心组件交互图

## 组件交互关系图

```mermaid
flowchart TB
    %% 样式定义
    classDef handler fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef task fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dsp fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef model fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    %% 请求入口层
    subgraph 请求入口层
        A[HeaderBiddingV2Handler] -->|处理POST /hb/v2/bid| B[SigBiddingService]
        C[WindmillBiddingService] -->|ToBid聚合竞价| B
        D[AdxService/gRPC] -->|getAdV2| E[AdService]
    end
    
    %% 核心服务层
    subgraph 核心服务层
        B -->|调用| E
        E -->|构建| F[AdRequestTask]
    end
    
    %% 任务执行层
    subgraph 任务执行层
        F -->|继承| G[DspRequestTask]
        G -->|具体实现| H[DspRtbRequestTask]
        G -->|具体实现| I[DspDealRequestTask]
        G -->|具体实现| J[DspPdbRequestTask]
        G -->|具体实现| K[DspAdNetworkRequestTask]
        
        H -->|并行| L[DspParallelRequestTask]
        I -->|并行| L
        J -->|并行| L
        K -->|串行| M[DspSerialRequestTask]
        
        L -->|包含| N[SingleAdRequestTask]
        M -->|包含| N
    end
    
    %% DSP接口层
    subgraph DSP接口层
        N -->|调用| O[IDsp]
        O -->|实现| P[AbstractRtbDsp]
        P -->|具体DSP| Q[BaiduDsp]
        P -->|具体DSP| R[TencentDsp]
        P -->|具体DSP| S[HuaweiDsp]
        P -->|具体DSP| T[OppoDsp]
        P -->|具体DSP| U[ByteDanceDsp]
        P -->|具体DSP| V[KuaishouDsp]
        P -->|具体DSP| W[其他30+DSP]
    end
    
    %% 数据模型层
    subgraph 数据模型层
        X[BidAd] -->|竞价广告模型| H
        X -->|竞价广告模型| I
        X -->|竞价广告模型| J
        Y[SingleTaskInfo] -->|任务信息| N
        Z[RtbRequest] -->|请求模型| O
        AA[RtbResponse] -->|响应模型| O
    end
    
    %% 样式应用
    class A,C,D handler
    class B,E service
    class F,G,H,I,J,K,L,M,N task
    class O,P,Q,R,S,T,U,V,W dsp
    class X,Y,Z,AA model
```

## 核心类继承关系图

```mermaid
flowchart TB
    %% 样式定义
    classDef interface fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px,stroke-dasharray: 5 5
    classDef abstract fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef concrete fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    
    %% 基础接口
    A[AdRequestTask] -.->|interface| B
    B[IDsp] -.->|interface| C
    
    %% 抽象基类
    subgraph 抽象基类
        B[DspRequestTask] -->|extends| A
        C[AbstractRtbDsp] -->|implements| B
    end
    
    %% 具体实现类
    subgraph RTB任务
        D[DspRtbRequestTask] -->|extends| B
        E[DspParallelRequestTask] -->|extends| D
        F[DspSerialRequestTask] -->|extends| D
    end
    
    subgraph 其他任务类型
        G[DspDealRequestTask] -->|extends| B
        H[DspPdbRequestTask] -->|extends| B
        I[DspAdNetworkRequestTask] -->|extends| B
    end
    
    subgraph 单个任务
        J[SingleAdRequestTask] -->|extends| B
    end
    
    subgraph DSP实现
        K[BaiduDsp] -->|extends| C
        L[TencentDsp] -->|extends| C
        M[HuaweiDsp] -->|extends| C
        N[OppoDsp] -->|extends| C
        O[ByteDanceDsp] -->|extends| C
        P[KuaishouDsp] -->|extends| C
    end
    
    %% 样式应用
    class A interface
    class B,C abstract
    class D,E,F,G,H,I,J,K,L,M,N,O,P concrete
```

## 数据流转图

```mermaid
flowchart LR
    %% 样式定义
    classDef request fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef process fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef response fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef cache fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    
    %% 请求构建
    A[HTTP请求] --> B[请求处理器]
    B --> C{构建RtbRequest}
    
    C -->|Header Bidding| D[BidRequest构建]
    C -->|Windmill| E[Token解析]
    C -->|gRPC| F[Proto转换]
    
    %% 预处理
    D --> G[请求预处理]
    E --> G
    F --> G
    
    G --> H[过滤检查]
    H --> I{是否通过?}
    
    I -->|否| J[返回错误]
    I -->|是| K[构建任务]
    
    %% 任务执行
    K --> L[选择执行模式]
    L --> M{任务类型}
    
    M -->|RTB| N[并行请求DSP]
    M -->|Deal| O[优先级排序]
    M -->|PDB| P[私有竞价]
    M -->|网盟| Q[串行请求]
    
    %% DSP交互
    N --> R[发送到多个DSP]
    R --> S[DSP响应收集]
    S --> T[价格计算]
    
    %% 结果处理
    T --> U[价格排序]
    U --> V[选择胜者]
    V --> W[结算价计算]
    W --> X[响应构建]
    X --> Y[返回RtbResponse]
    
    %% 缓存处理
    H -->|缓存命中| Z[返回缓存结果]
    Y -->|更新缓存| AA[写入缓存]
    
    %% 样式应用
    class A,B,C,D,E,F,G,H,J,K,L,N,O,P,Q,R,S,T,U,V,W,X,Y request
    class I,M process
    class Z,AA cache
```

## 核心配置和策略图

```mermaid
flowchart TB
    %% 样式定义
    classDef config fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef strategy fill:#fff8e1,stroke:#ff6f00,stroke-width:2px
    classDef filter fill:#fbe9e7,stroke:#bf360c,stroke-width:2px
    
    %% 配置中心
    subgraph 配置中心
        A[ConfigManager] -->|管理| B[SdkConfig]
        A -->|管理| C[AdSlotConfig]
        A -->|管理| D[DspConfig]
        A -->|管理| E[FilterConfig]
    end
    
    %% 策略模块
    subgraph 策略模块
        F[ABTestStrategy] -->|实验分流| G[执行策略]
        G -->|并行模式| H[ParallelStrategy]
        G -->|串行模式| I[SerialStrategy]
        G -->|混合模式| J[MixedStrategy]
        
        K[PriceStrategy] -->|价格策略| L[FloorPriceStrategy]
        L -->|底价计算| M[DynamicFloorPrice]
        L -->|底价计算| N[FixedFloorPrice]
        
        O[ExperimentStrategy] -->|价格实验| P[Strategy002]
        O -->|价格实验| Q[Strategy003]
    end
    
    %% 过滤器链
    subgraph 过滤器链
        R[RequestFilter] -->|过滤| S[RegionFilter]
        R -->|过滤| T[DeviceFilter]
        R -->|过滤| U[UserFilter]
        R -->|过滤| V[FrequencyFilter]
        R -->|过滤| W[CategoryFilter]
    end
    
    %% 使用关系
    X[SigBiddingService] -->|使用| A
    Y[AdService] -->|使用| F
    Y -->|使用| K
    Z[DspRequestTask] -->|使用| R
    
    %% 样式应用
    class A,B,C,D,E config
    class F,G,H,I,J,K,L,M,N,O,P,Q strategy
    class R,S,T,U,V,W filter
```

## 关键流程时序图

### 完整竞价时序

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant SSP as SSP服务
    participant ADX as ADX服务
    participant Cache as 缓存
    participant Filter as 过滤器
    participant Task as 竞价任务
    participant DSPs as DSP集群
    participant Price as 价格引擎
    
    Client->>SSP: 竞价请求
    SSP->>ADX: RTB请求
    
    Note over ADX: 请求预处理
    ADX->>Cache: 查询缓存
    Cache-->>ADX: 缓存结果
    
    alt 缓存命中
        Cache-->>SSP: 返回缓存结果
        SSP-->>Client: 响应
    else 缓存未命中
        ADX->>Filter: 执行过滤链
        Filter-->>ADX: 过滤结果
        
        ADX->>Task: 创建竞价任务
        Task->>Task: 选择执行策略
        
        par 并行请求
            Task->>DSPs: 发送BidRequest
            DSPs-->>Task: 收集响应
        end
        
        Task->>Price: 价格决策
        Price->>Price: 价格排序
        Price-->>Task: 胜者广告
        
        Task-->>ADX: RtbResponse
        ADX->>Cache: 更新缓存
        ADX-->>SSP: 竞价结果
        SSP-->>Client: 最终响应
    end
```

### 价格计算时序

```mermaid
sequenceDiagram
    participant Task as 竞价任务
    participant Filter as 价格过滤器
    participant Sort as 排序器
    participant Experiment as 实验引擎
    participant Calc as 结算价计算
    
    Task->>Filter: 输入DSP响应列表
    Filter->>Filter: 获取底价配置
    Filter->>Filter: 过滤低价响应
    
    Filter-->>Sort: 有效响应列表
    Sort->>Sort: 构建BidAd对象
    Sort->>Experiment: 应用价格实验
    
    Experiment-->>Sort: 调整后价格
    Sort->>Sort: 按价格降序排序
    Sort-->>Task: 排序后列表
    
    Task->>Calc: 选择获胜者
    Calc->>Calc: DSP数量判断
    
    alt 单DSP
        Calc->>Calc: 结算价=底价
    else 多DSP
        Calc->>Calc: 第二价格计算
    end
    
    Calc-->>Task: 最终结算价
```

这些图表完整展示了ADX竞价系统的：
1. **组件架构**: 各个服务类和处理器之间的关系
2. **继承体系**: 核心类的继承和实现关系
3. **数据流转**: 请求和响应在系统中的流转过程
4. **配置策略**: 配置管理和策略实现
5. **执行时序**: 关键操作的时序关系

通过这些图表，可以清晰地理解ADX竞价系统的整体架构和核心实现逻辑。