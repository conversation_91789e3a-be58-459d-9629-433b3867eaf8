//PUT _index_template/template_ssp_log
{
  "index_patterns": [
    "rtbCallback-log*"
  ],
  "priority": 100,
  "data_stream": {
  },
  "template": {
    "settings": {
      "index": {
        "lifecycle": {
          "name": "rtbCallback-log"
        },
        "refresh_interval": "45s",
        "number_of_shards": "20",
        "codec": "best_compression",
        "translog": {
          "sync_interval": "30s",
          "flush_threshold_size": "1024mb",
          "durability": "async"
        },
        "highlight": {
          "max_analyzed_offset": 6000000
        },
        "number_of_replicas": "0"
      }
    },
    "mappings": {
      "properties": {
        "document_id": {
          "type": "keyword",
          "index": false,
          "norms": false
        },
        "@timestamp": {
          "type": "date"
        },
        "@version": {
          "type": "keyword",
          "index": false,
          "norms": false
        },
        "appId": {
          "type": "keyword",
          "ignore_above": 256,
          "norms": false
        },
        "callbackResult": {
          "type": "keyword",
          "norms": false
        },
        "rid": {
          "type": "keyword",
          "ignore_above": 256,
          "norms": false
        },
        "cid": {
          "type": "keyword",
          "norms": false
        },
        "pid": {
          "type": "keyword",
          "norms": false
        },
        "uid": {
          "type": "keyword",
          "norms": false
        },
        "adType": {
          "type": "short"
        },
        "durl": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "protocol": {
          "type": "keyword",
          "norms": false
        },
        "event": {
          "properties": {
            "original": {
              "type": "keyword",
              "ignore_above": 256,
              "index": false,
              "norms": false
            }
          }
        },
        "level": {
          "type": "keyword",
          "norms": false
        },
        "message": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "req": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "apiRequestId": {
          "type": "keyword",
          "norms": false
        },
        "resp": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "rtbRequest": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "rtbResponse": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "throwable": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "sdkVersion": {
          "type": "keyword",
          "norms": false
        },
        "tags": {
          "type": "keyword",
          "ignore_above": 256,
          "index": false,
          "norms": false
        },
        "topic": {
          "type": "keyword",
          "norms": false
        },
        "user_agent": {
          "properties": {
            "device": {
              "properties": {
                "name": {
                  "type": "keyword",
                  "ignore_above": 256,
                  "index": false,
                  "norms": false
                }
              }
            },
            "name": {
              "type": "keyword",
              "ignore_above": 256,
              "index": false,
              "norms": false
            },
            "os": {
              "properties": {
                "full": {
                  "type": "keyword",
                  "ignore_above": 256,
                  "index": false,
                  "norms": false
                },
                "name": {
                  "type": "keyword",
                  "ignore_above": 256,
                  "index": false,
                  "norms": false
                },
                "version": {
                  "type": "keyword",
                  "ignore_above": 256,
                  "index": false,
                  "norms": false
                }
              }
            },
            "version": {
              "type": "keyword",
              "ignore_above": 256,
              "index": false,
              "norms": false
            }
          }
        },
        "useragent": {
          "type": "text",
          "index": false,
          "norms": false
        },
        "dspId": {
          "type": "short"
        },
        "idfa": {
          "type": "keyword",
          "norms": false
        },
        "imei": {
          "type": "keyword",
          "norms": false
        },
        "oaid": {
          "type": "keyword",
          "norms": false
        },
        "android_id": {
          "type": "keyword",
          "norms": false
        },
        "rtbId": {
          "type": "keyword",
          "norms": false
        },
        "protocol": {
          "type": "keyword",
          "norms": false
        },
        "hb": {
          "type": "byte"
        },
        "os": {
          "type": "short"
        },
        "nbr": {
          "type": "integer"
        },
        "requestType": {
          "type": "short"
        },
        "access": {
          "type": "byte"
        },
        "campaign_id": {
          "type": "keyword",
          "norms": false
        },
        "redirect_url": {
          "type": "keyword",
          "index": false,
          "norms": false
        },
        "channel": {
          "type": "keyword",
          "norms": false
        },
        "dsp_adSlot_id": {
          "type": "keyword",
          "norms": false
        },
        "return_ad": {
          "type": "short"
        },
        "logTime": {
          "type": "keyword",
          "norms": false
        },
        "host": {
          "type": "keyword",
          "norms": false
        },
        "errorCode": {
          "type": "integer"
        }
      }
    }
  }
}