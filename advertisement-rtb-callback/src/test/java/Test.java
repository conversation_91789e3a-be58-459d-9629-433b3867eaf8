import com.google.common.collect.Lists;
import com.sigmob.ad.rtb.callback.domain.RiskyCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

public class Test {

  public static void main(String[] args) {
    test();
  }

  public static void test() {

    String url =
        "http://localhost:9005/tobid/mmp?thirdPlacementId=5448000003&hb=0&ecpm=1&d=uwfugkiF1yNfBWEJdr6a%2BrFZpLBx6IAkz7cg4RSV6SUt0Ntte0Nsgd3X3xZF39Czh2vf8%2F0MPoGCFqBzMA%2FbVtXvi9ULXAgarkvnAqjUe%2FIPe8l2UVPbwQ%2BP2KC%2BxrfVQgMVd%2FnudRqULRzjN%2FeUt3oVJwXmRxFI%2BiOyTieUYzsOW1YSwcXt%2Ffz%2BCCXRdm9nGTE55rxwrAVifKPVPGztYQhBuVaAVHTB32gqzpaMWA0qUceBIqwwSW%2BacDJuw03MEueCibWWmuCT041uMAfITm2ommoc1uvyw2tClbsbGWT6W%2Fd1yIUlOGW5ukoqYVzEwTc%2FevVStmbv4YP%2F47PMl3Ez6NK4DYIPVfspFCyOwkrDvpQSm99%2B1R1LluTjSarUELtphs%2BL4xzorkzPoPeRl5ZzTVxqK%2BeXDiXmbNhp505f8PsROMSRJ5aQe%2BW08tAG6DBlPt5LsOQHQPAmXazXqTaNmJ0Df8Zmkoty8hkc26tEFR7Fpx23yLe1Vl1BF%2FRCAlGO4m7Seu7NSHIrI7Z%2BZ%2FXJpIRUNr%2BLobYzkWY6E8c%3D&loadId=e27a2a06-5847-4955-b5fb-c26e784ec7a4&appId=2605&placementId=3570962751953533&platform={macro}&userCode={sdd}";

    List<String> keys = Lists.newArrayListWithCapacity(16);
    List<String> values = Lists.newArrayListWithCapacity(16);

    keys.add("{sdd}");
    values.add("sdd");
    keys.add("{macro1}");
    values.add("sdd");

    RiskyCode riskyCode = new RiskyCode(0, List.of("1", "3", "55"));
    String join = StringUtils.join(riskyCode.code(), ",");
    System.out.println(join);

    System.out.println(url);
    String string =
        StringUtils.replaceEach(url, keys.toArray(new String[0]), values.toArray(new String[0]));
    System.out.println(string);
    System.out.println(string.equals(url));
  }
}
