package com.sigmob.ad.rtb.callback.handler;

import java.util.concurrent.CountDownLatch;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

class SsvHandlerTest {

  public static void main(String[] args) {
    test();
  }

  private static void test() {

    CountDownLatch countDownLatch = new CountDownLatch(1);

    WebClient.create()
        .get()
        .uri(
            "http://localhost:9005/ssv/rv?rid=713eb716-f8d6-11ef-a324-00163e167664&aid=2354&pid=ec488de9a1b&uid=8864qsfj123&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyZXF1ZXN0SWQiOiI3MTNlYjcxNi1mOGQ2LTExZWYtYTMyNC0wMDE2M2UxNjc2NjQiLCJpc3MiOiJzaWciLCJwSWQiOiJlYzQ4OGRlOWExYiJ9.lisz_7l0zXMO1RITxAGvsnefMgqgq75Cr42GXlo42DQ&curl=9k_hYo7IFCUMG53Jqb-Gr8AEoOPykdGHWVxEFrdQ-6sJfeksjVVTam6h4C9xd7_0-y8Lx1vC-gJABAaJkGVoph0VYfolR49m1l0lkuP5wtvS_I9rAHOJSqmzkDl3hLEUW6ZWfSnCgxG6lyjqVaWtUrfXvbsDOJGWH1UmmjiPloVJlB4uPDjk6QkZ0S6NP36V84uubO2ZNdSPxv8KIG4i_dyfWauhAq5RRsG86lHRUR9HXru-zvOH21SN2oRJ-OTJGiignEABt7t9uZosMKjdx720EqrKVDOX6pqEHmagL86Ek7Bt2PTh_Q1f0YzwR0bGfYDh4oGuiy01pA5dBtImYQ")
        .exchangeToMono(
            result -> {
              System.out.println("http code:" + result.statusCode());

              return Mono.just("empty");
            })
        .subscribe(
            result -> {
              System.out.println("response:" + result);
              countDownLatch.countDown();
            },
            e -> {
              e.printStackTrace();
              countDownLatch.countDown();
            });

    try {
      countDownLatch.await();
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }
}
