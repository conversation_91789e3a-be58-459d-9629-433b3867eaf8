package com.sigmob.ad.rtb.callback.handler;

import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Base64;
import java.util.concurrent.CountDownLatch;

class ToBidSsvHandlerTest {

  public static void main(String[] args) {
    test();
  }

  private static void test() {
    CountDownLatch countDownLatch = new CountDownLatch(1);

    String json =
        "{\"loadId\":\"206df793-fe9d-46d6-822d-aeda894f511382\",\"placementId\":\"9832515539413140\",\"userId\":\"1232\",\"custom\":\"{\\\"user_id\\\":\\\"1232\\\",\\\"scene_id\\\":\\\"567\\\",\\\"scene_desc\\\":\\\"转盘抽奖\\\"}\",\"channelId\":\"9\",\"thirdAppId\":\"2605\",\"thirdPlacementId\":\"f6bf8de2d13\",\"ecpm\":1050000,\"uid\":\"437f248dc529a547542c11a7e7ebbe43ab114c5037a01d52ec478944c96dc01a\",\"sdkVersion\":\"4.5.41\",\"networkAdType\":5,\"mask\":\"60d1636478ea8c19eec1673020520bade5429636e713f274f5966a4b32c87b78\",\"checkMask\":0,\"timestamp\":1750761340044,\"gameVersion\":\"4.5.41.db3f056e0\",\"extBody\":\"\"}";

    ToBidRvCallbackRequest toBidRvCallbackRequest =
        JsonSerializationUtils.jsonToObj(json, ToBidRvCallbackRequest.class);

    WebClient.create()
        .post()
        .uri(
            "http://localhost:9005/tobid/rv?tbvsss=pdd%7CH00IcWyrzgN2SRppJ7zXljBquMheqLIntCTIMj8RAfMHA9Tt08rWG6IJdJHypaAhBH8DCmuN0Tcyne8VlzserQ%3D%3D&appId=2605&sdkVersion=4.5.41")
        // .uri("http://localhost:9005/t/i/wlT?t=1731489700791&appId=2605&sdkVersion=4.1.20")
        .headers(
            httpHeaders -> {
              httpHeaders.setContentType(MediaType.APPLICATION_JSON);
              httpHeaders.add("X-Forwarded-For", "************, ************");
              // httpHeaders.add("c", "1");
            })
        .body(BodyInserters.fromValue(toBidRvCallbackRequest))
        .exchangeToMono(
            result -> {
              System.out.println("http code:" + result.statusCode());
              System.out.println("contentLength:" + result.headers().contentLength());
              return result.bodyToMono(String.class);
            })
        .subscribe(
            result -> {
              System.out.println("response:" + new String(result));
              countDownLatch.countDown();
            },
            e -> {
              e.printStackTrace();
              countDownLatch.countDown();
            });

    try {
      if (countDownLatch.getCount() != 0) {

        countDownLatch.await();
      }
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }
}
