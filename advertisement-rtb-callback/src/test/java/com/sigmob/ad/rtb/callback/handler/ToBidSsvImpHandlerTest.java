package com.sigmob.ad.rtb.callback.handler;

import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Base64;
import java.util.concurrent.CountDownLatch;

class ToBidSsvImpHandlerTest {

  public static void main(String[] args) {
    test();
  }

  private static void test() {

    CountDownLatch countDownLatch = new CountDownLatch(1);

    String string =
        "CB7GAC8cyPE8geN9wwZ0gC/gC1UxnY8TU7R/gFDyRSgAAADWitnWnPEycFJFem35QXfNFF0atf18a3g12M4aDue75J9amaK4kyD/29+CPksafC76Tu868HaAmJW9sLsH/nk3uIiMJ88IYY0rKY2wTQ6iGWSY5m4R2zQdGa4TltO0D/Et6tdesaHQlK23nP/NaiJ/GnH57fqAGT2xFnDVx86e1DjN9TmE8OIZtQuy7++TjO/EW9vuWucPq6u0t+4OnuDEBJbDiyHrmpD6I1qpxgXf6RdE42za0PupU3ZDIKhHL15FX0WM9rzHLawZwHEFx9ZeeW5IL5Ra07D+uFWYjpk0KtSEp/ExCvEk2X5IOt/zWwlclax/i4n1Nfs1WDkE6ki/H1t/UnXRYNm0PJBcaKB8fU+9UQIqhTxtEjwI/sfes/QPdvZEbLunvsgoNvaH0TlcB4DG7rFfpG6O1vvAPYn6JWDCSU+ljRSIBRGckrzW/w+WjFxWl+z+mrq7ulXxjpjhPLR5HuaL9hZ81/8xaSSX7DKHiFyETn6YdYjOkjgIV9BmokbsK6N5vi0fqO8h40mUt2xWccWZcsA2X7hLY7zKVyZZHdVdV5g7O3etFo8YOTgUcNxh/6d5vmsVWABNdn7IScLFJRHZgf5R76TynEo2HlYsqTSNEM143Qd0x8XlB20zWETi4F2mdRZzzG/DJeCFJka2ncl3f3E8OMsoboaF8TWPj8bCfVYsOf99f9sdSOafV/xTBti2qJHQ0GOphJBGZ4z5B64vVzcQUgdJtSSWnpinK80Bd/7zN1qju1z3LrXa21KCxCdXzUxCsT39V1uTFYcxlkH4DvSiFz35z9zgrTB/rm4gTE5pbQ1518j2qWF7x0kHcgA4N5owEoNlxOml5jOGs1PAJmK3GvtgJ1Mk/BI=";

    WebClient.create()
        .post()
        .uri("http://localhost:9005/t/i/wlT?t=1731489700791&appId=2605&sdkVersion=4.1.20")
        .headers(
            httpHeaders -> {
              httpHeaders.setContentType(MediaType.APPLICATION_JSON);
              httpHeaders.add("X-Forwarded-For", "************, ************");
              httpHeaders.add("c", "1");
            })
        .body(BodyInserters.fromValue(Base64.getDecoder().decode(string.getBytes())))
        .exchangeToMono(
            result -> {
              System.out.println("http code:" + result.statusCode());
              System.out.println("contentLength:" + result.headers().contentLength());
              return result.bodyToMono(String.class);
            })
        .subscribe(
            result -> {
              System.out.println("response:" + new String(result));
              countDownLatch.countDown();
            },
            e -> {
              e.printStackTrace();
              countDownLatch.countDown();
            });

    try {
      countDownLatch.await();
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }
}
