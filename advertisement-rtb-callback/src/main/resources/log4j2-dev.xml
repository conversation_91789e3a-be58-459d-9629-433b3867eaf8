<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="debug">
    <properties>
        <property name="App">advertisement-rtb-callback-dev</property>
        <property name="logDir">/usr/local/var/logs/ad/rtb/callback</property>
        <property name="historyLogDir">/usr/local/var/logs/ad/rtb/callback/history</property>
        <property name="ssvLogDir">/usr/local/var/logs/rtb/ssv</property>
        <property name="ssvHistoryLogDir">/usr/local/var/logs/rtb/ssv/history</property>
        <property name="redirectLogDir">/usr/local/var/logs/rtb/redirect</property>
        <property name="redirectHistoryLogDir">/usr/local/var/logs/rtb/redirect/history</property>
        <property name="logPattern">%d{HH:mm:ss.SSS} [%thread] %-5level [%c{1.}:%M:%L] - %msg%n</property>
        <property name="ssvLogPattern">%d{HH:mm:ss.SSS} %msg%n</property>
        <property name="redirectLogPattern">%d{HH:mm:ss.SSS} %msg%n</property>
        <property name="aliyunLogPattern">%d %-5level [%thread] %logger{0}: %msg</property>
    </properties>
    <thresholdFilter level="info"/>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <!-- 设置控制台只输出level及以上级别的信息(onMatch),其他的直接拒绝(onMismatch)-->
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <!-- 设置输出格式,不设置默认为:%m%n -->
            <PatternLayout pattern="${logPattern}"/>
        </Console>
        <RollingRandomAccessFile name="DEBUG_FILE" fileName="${logDir}/debug.log" immediateFlush="true" filePattern="${historyLogDir}/debug_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${logPattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${historyLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz" />
                    <IfLastModified age="240H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <Loghub name="aliyun"
                project="ssp-rtb-callback"
                logStore="test"
                endpoint="cn-beijing.log.aliyuncs.com"
                accessKeyId="LTAIZLaN56OR2OgK"
                accessKeySecret="lttfvn7XaLoPq6gTNd05QKLRaDJLDz"
                totalSizeInBytes="104857600"
                maxBlockMs="60000"
                ioThreadCount="8"
                batchSizeThresholdInBytes="524288"
                batchCountThreshold="4096"
                lingerMs="2000"
                retries="10"
                baseRetryBackoffMs="100"
                maxRetryBackoffMs="100"
                topic="ssp-dev"
                timeFormat="yyyy-MM-dd'T'HH:mm:ssZ"
                timeZone="Asia/Shanghai"
                ignoreExceptions="true"
                mdcFields="callbackResult,rid,cid,rtbCallback,pid,aid,ssv,campaign_id,redirect_url,os,appId,adSlotId,timestamp,reqType,uid,loadId,platform">
            <Filters>
                <!-- This filter accepts info, warn, error, fatal and denies debug/trace -->
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Loghub>
    </Appenders>
    <Loggers>
        <logger name="com.aliyun" level="INFO"/>
        <logger name="org.apache" level="INFO"/>
        <logger name="com.github.zero" level="INFO"/>
        <logger name="aliyunLog" level="INFO" additivity="false">
            <ThresholdFilter level="info"/>
            <appender-ref ref="aliyun"/>
        </logger>
        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="DEBUG_FILE" />
        </logger>
        <logger name="localInfoLog" level="INFO" additivity="false">
            <appender-ref ref="DEBUG_FILE" />
        </logger>
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="DEBUG_FILE"/>
        </Root>
    </Loggers>
</Configuration>