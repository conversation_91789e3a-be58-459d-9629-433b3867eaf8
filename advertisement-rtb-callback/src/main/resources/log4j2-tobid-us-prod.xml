<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
    <properties>
        <property name="App">advertisement-rtb-callback-prod</property>
        <property name="logDir">/home/<USER>/logs/rtb/callback</property>
        <property name="historyLogDir">/home/<USER>/logs/rtb/callback/history</property>
        <property name="ssvLogDir">/home/<USER>/logs/rtb/ssv</property>
        <property name="ssvHistoryLogDir">/home/<USER>/logs/rtb/ssv/history</property>
        <property name="redirectLogDir">/home/<USER>/logs/rtb/redirect</property>
        <property name="redirectHistoryLogDir">/home/<USER>/logs/rtb/redirect/history</property>
        <property name="hbLogDir">/home/<USER>/logs/rtb/hb</property>
        <property name="hbHistoryLogDir">/home/<USER>/logs/rtb/hb/history</property>

        <property name="logPattern">%d{HH:mm:ss.SSS} [%tid.%tn] %-5level - %msg%n</property>
        <property name="ssvLogPattern">%d{HH:mm:ss.SSS} %msg%n</property>
        <property name="redirectLogPattern">%d{HH:mm:ss.SSS} %msg%n</property>
        <property name="hbLogPattern">%d{HH:mm:ss.SSS} %msg%n</property>
        <property name="aliyunLogPattern">%d %-5level [%thread] %logger{0}: %msg</property>
    </properties>
    <thresholdFilter level="info"/>
    <Appenders>
<!--        <Console name="Console" target="SYSTEM_OUT">-->
<!--            &lt;!&ndash; 设置控制台只输出level及以上级别的信息(onMatch),其他的直接拒绝(onMismatch)&ndash;&gt;-->
<!--            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>-->
<!--            &lt;!&ndash; 设置输出格式,不设置默认为:%m%n &ndash;&gt;-->
<!--            <PatternLayout pattern="${logPattern}"/>-->
<!--        </Console>-->
        <RollingRandomAccessFile name="INFO_FILE" fileName="${logDir}/info.log" immediateFlush="true" filePattern="${historyLogDir}/info_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${logPattern}"/>
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${historyLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz" />
                    <IfLastModified age="72H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="WARN_FILE" fileName="${logDir}/warn.log" immediateFlush="false" filePattern="${historyLogDir}/warn_%d{yyyy-MM-dd}.log.gz">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${logPattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${historyLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz" />
                    <IfLastModified age="72H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="ERROR_FILE" fileName="${logDir}/error.log" immediateFlush="true" filePattern="${historyLogDir}/error_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${logPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10">
                <Delete basePath="${historyLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz" />
                    <IfLastModified age="72H" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="SSV_INFO_FILE" fileName="${ssvLogDir}/info.log" immediateFlush="true"
                                 filePattern="${ssvHistoryLogDir}/info_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${ssvLogPattern}"/>
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${ssvHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="SSV_WARN_FILE" fileName="${ssvLogDir}/warn.log" immediateFlush="true"
                                 filePattern="${ssvHistoryLogDir}/warn_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${ssvLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${ssvHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="SSV_ERROR_FILE" fileName="${ssvLogDir}/error.log" immediateFlush="true"
                                 filePattern="${ssvHistoryLogDir}/error_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${ssvLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${ssvHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="REDIRECT_INFO_FILE" fileName="${redirectLogDir}/info.log" immediateFlush="true"
                                 filePattern="${redirectHistoryLogDir}/info_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${ssvLogPattern}"/>
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${redirectLogPattern}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="REDIRECT_WARN_FILE" fileName="${redirectLogDir}/warn.log" immediateFlush="true"
                                 filePattern="${redirectHistoryLogDir}/warn_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${redirectLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${redirectHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="REDIRECT_ERROR_FILE" fileName="${redirectLogDir}/error.log" immediateFlush="true"
                                 filePattern="${redirectHistoryLogDir}/error_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${redirectLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${redirectHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>


        <RollingRandomAccessFile name="HB_INFO_FILE" fileName="${hbLogDir}/info.log" immediateFlush="true"
                                 filePattern="${hbHistoryLogDir}/info_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${hbLogPattern}"/>
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${hbHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="HB_WARN_FILE" fileName="${hbLogDir}/warn.log" immediateFlush="true"
                                 filePattern="${hbHistoryLogDir}/warn_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${hbLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${hbHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="HB_ERROR_FILE" fileName="${hbLogDir}/error.log" immediateFlush="true"
                                 filePattern="${hbHistoryLogDir}/error_%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="${hbLogPattern}"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${hbHistoryLogDir}" maxDepth="2">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="72H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <Loghub name="aliyun"
                project="ssp-log-usw"
                logStore="adserver"
                endpoint="us-west-1-intranet.log.aliyuncs.com"
                accessKeyId="LTAIZLaN56OR2OgK"
                accessKeySecret="lttfvn7XaLoPq6gTNd05QKLRaDJLDz"
                totalSizeInBytes="104857600"
                maxBlockMs="60000"
                ioThreadCount="8"
                batchSizeThresholdInBytes="524288"
                batchCountThreshold="4096"
                lingerMs="2000"
                retries="10"
                baseRetryBackoffMs="100"
                maxRetryBackoffMs="100"
                topic="tobid-callback-prod"
                timeFormat="yyyy-MM-dd'T'HH:mm:ssZ"
                timeZone="Asia/Singapore"
                ignoreExceptions="true"
                mdcFields="callbackResult,rid,cid,rtbCallback,pid,aid,ssv,campaign_id,redirect_url,os,appId,adSlotId,timestamp,reqType,uid,loadId,platform,errCode">
            <!--            <PatternLayout-->
            <!--                    pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx"/>-->
            <Filters>
                <!-- This filter accepts info, warn, error, fatal and denies debug/trace -->
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Loghub>
    </Appenders>
    <Loggers>
        <logger name="aliyunLog" level="info" additivity="false">
            <appender-ref ref="aliyun"/>
        </logger>
        <logger name="toBidAliYunLog" level="info" additivity="false">
            <appender-ref ref="aliyun"/>
        </logger>
        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="INFO_FILE"/>
        </logger>
        <logger name="localInfoLog" level="INFO" additivity="false">
            <appender-ref ref="INFO_FILE"/>
        </logger>
        <logger name="localWarnLog" level="WARN" additivity="false">
            <appender-ref ref="WARN_FILE"/>
        </logger>
        <logger name="localErrorLog" level="ERROR" additivity="false">
            <appender-ref ref="ERROR_FILE"/>
        </logger>
        <logger name="ssvInfoLog" level="INFO" additivity="false">
            <appender-ref ref="SSV_INFO_FILE" />
        </logger>
        <logger name="ssvWarnLog" level="WARN" additivity="false">
            <appender-ref ref="SSV_WARN_FILE" />
        </logger>
        <logger name="ssvErrorLog" level="ERROR" additivity="false">
            <appender-ref ref="SSV_ERROR_FILE" />
        </logger>

        <logger name="redirectInfoLog" level="INFO" additivity="false">
            <appender-ref ref="REDIRECT_INFO_FILE"/>
        </logger>
        <logger name="redirectWarnLog" level="WARN" additivity="false">
            <appender-ref ref="REDIRECT_WARN_FILE"/>
        </logger>
        <logger name="redirectErrorLog" level="ERROR" additivity="false">
            <appender-ref ref="REDIRECT_ERROR_FILE"/>
        </logger>

        <logger name="hbInfoLog" level="INFO" additivity="false">
            <appender-ref ref="HB_INFO_FILE"/>
        </logger>
        <logger name="hbWarnLog" level="WARN" additivity="false">
            <appender-ref ref="HB_WARN_FILE"/>
        </logger>
        <logger name="hbErrorLog" level="ERROR" additivity="false">
            <appender-ref ref="HB_ERROR_FILE"/>
        </logger>
<!--        <Root level="INFO">-->
<!--            <AppenderRef ref="Console"/>-->
<!--            <AppenderRef ref="INFO_FILE"/>-->
<!--            <AppenderRef ref="WARN_FILE"/>-->
<!--            <AppenderRef ref="ERROR_FILE"/>-->
<!--        </Root>-->
    </Loggers>
</Configuration>