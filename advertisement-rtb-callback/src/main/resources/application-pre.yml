#应用服务名称
spring:
  application:
    name: advertisement-rtb-callback-pre
  aop:
    proxy-target-class: on
  jackson:
    default-property-inclusion: non_null
  kafka:
    template:
      default-topic: callback-log-prod
    producer:
      bootstrap-servers: *************:9092,*************:9092,*************:9092
      batch-size: 2621440B
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      retries: 0
      compression-type: gzip
      acks: 0
      buffer-memory: 134217728B
      properties:
        max.block.ms: 1000
        request.timeout.ms: 2000
        linger.ms: 500
        max.request.size: 10485760
  #  main:
  #    web-application-type: none
  redis:
    hostName: r-2zecovvhgvkhcyk3wv.redis.rds.aliyuncs.com
    port: 6379
    password: Sigmob123
    # 连接超时时间（毫秒）
    timeout: 1000
    # Redis默认情况下有16个分片，这里配置具体使用的分片，默认是0
    database: 0
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 4
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: 1s
        # 连接池中的最大空闲连接 默认 8
        max-idle: 4
        # 连接池中的最小空闲连接 默认 0
        min-idle: 4
      shutdown-timeout: 100ms
  settingsredis:
    hostName: ${spring.redis.hostName}
    port: ${spring.redis.port}
    password: ${spring.redis.password}
    timeout: ${spring.redis.timeout}`
    database: 1
  adcontroldataredis:
    hostName: ${spring.redis.hostName}
    port: ${spring.redis.port}
    password: ${spring.redis.password}
    timeout: ${spring.redis.timeout}
    database: 4
  tobidcacheredis:
    hostName: r-2ze49go7rcqiryp4e8.redis.rds.aliyuncs.com
    port: 6379
    password: Sigmob123
    timeout: 2000
    database: 0
  adcacheredis:
    hostName: r-2zeedg2oy1ivnbg44e.redis.rds.aliyuncs.com
    port: 6379
    password: Sigmob123
    timeout: 1000
    database: 1


local:
  cache:
    name: redis_local_cache
    initialCapacity: 1
    maximumSize: 1
    expireAfterWrite: 1
user:
  cache:
    name: redis_user_cache
    initialCapacity: 1
    maximumSize: 1
    expireAfterWrite: 1

#应用web服务端口
server:
  port: 9984
  compression:
    enabled: true
  max-http-header-size: 8192KB
  netty:
    max-initial-line-length: 8192KB

#应用服务描述信息
info:
  name: ${spring.application.name}
  description: service description
  environment: ${spring.profiles.active}
  version: 1.0

#日志配置
logging.config: classpath:log4j2-pre.xml
#本地ip库文件配置
ipdata.config: ip/GeoIP2-Country.mmdb
#config配置文件
config-manager.config: classpath:config/config-manager-info-pre.xml


#micrometer监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,loggers,prometheus
#        exclude: env,beans
      base-path: /monitor
    jmx:
      exposure:
        exclude: '*'
  endpoint:
    health:
      show-details: never
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        resilience4j.ciruitbreaker.calls: true
        method.timed: true
      sla:
        http.server.requests: 10ms,20ms,50ms,100ms,200ms,500ms,1000ms,1500ms,2000ms,2500ms,3000ms
    export:
      appoptics:
        enabled: true
      prometheus:
        enabled: true
    web:
      client:
        request:
          autotime:
            enabled: false
            percentiles-histogram: false
            percentiles: 0.5,0.75,0.9,0.95,0.99
      server:
        request:
          autotime:
            enabled: true
            percentiles-histogram: true
            percentiles: 0.5,0.75,0.9,0.95,0.99
