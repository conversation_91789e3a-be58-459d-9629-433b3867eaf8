package com.sigmob.ad.rtb.callback.handler;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.config.tobid.AppConfig;
import com.sigmob.ad.core.constants.WindmillConstants;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.*;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.publishing.windmill.StrategyScene;
import com.sigmob.ad.core.security.crypto.AesUtils;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.core.util.MessageDigestUtil;
import com.sigmob.ad.core.util.SsvUtil;
import com.sigmob.ad.dao.AppInfoDao;
import com.sigmob.ad.dao.MediationStrategyDao;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.dao.SsvRiskyDao;
import com.sigmob.ad.rtb.callback.dao.ToBidImpDao;
import com.sigmob.ad.rtb.callback.datalog.Ac682MessageBuilder;
import com.sigmob.ad.rtb.callback.domain.RewardProcessInfo;
import com.sigmob.ad.rtb.callback.domain.RiskyCode;
import com.sigmob.ad.rtb.callback.domain.SsvCallbackResponse;
import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import com.sigmob.ad.rtb.callback.util.RequestUtil;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import com.sigmob.sigdsp.pb.Version;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

import com.twofishes.config.manager.ConfigManager;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * ToBid服务端激励回调媒体服务端
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ToBidSsvHandler extends CallbackBaseHandler {

  @NonNull private final WebClient webClient;

  @NonNull private final DataLogService dataLogService;

  @NonNull private final MediationStrategyDao mediationStrategyDao;

  @NonNull private final ToBidImpDao toBidImpDao;

  @NonNull private final SsvRiskyDao ssvRiskyDao;

  @NonNull private final AppInfoDao appInfoDao;

  @NonNull
  private final ToBidRvCallbackRequest defaultToBidRvCallbackRequest = new ToBidRvCallbackRequest();

  /** 激励视频奖励服务端验证回调 */
  @NonNull
  public Mono<ServerResponse> rvCallback(@NonNull ServerRequest request) {

    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String clientIp = getClientIp(httpRequest);
    String uri = httpRequest.getURI().toString();
    var appIdStr = request.queryParam(LogUtil.LOG_FIELD_APP_ID).orElse(StringUtils.EMPTY);
    var sdkVerStr = request.queryParam(LogUtil.LOG_FIELD_SDK_VERSION).orElse(StringUtils.EMPTY);
    var ssvEnc = request.queryParam(WindmillConstants.WINDMILL_SSV_ENC).orElse(StringUtils.EMPTY);
    Integer appId = !appIdStr.isBlank() ? NumberUtils.toInt(appIdStr) : null;
    var currentTimeMillis = System.currentTimeMillis();

    Map<String, Object> extraLogInfoMap = Maps.newHashMapWithExpectedSize(16);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(16);

    extraLogInfoMap.put(
        LogUtil.LOG_FIELD_HEADER, JsonSerializationUtils.objToJson(request.headers()));
    extraLoginFiled.put(RtbCallbackConstants.SsvParam.APP_ID.getName(), appIdStr);
    extraLoginFiled.put(LogUtil.LOG_FIELD_SDK_VERSION, sdkVerStr);
    extraLogInfoMap.put(LogUtil.LOG_FIELD_TIMESTAMP, currentTimeMillis);

    String aesHeaderCbc = RequestUtil.getAesHeaderCbc(request);

    return getToBidRvCallbackRequestMono(request, sdkVerStr)
        .switchIfEmpty(Mono.just(defaultToBidRvCallbackRequest))
        .defaultIfEmpty(defaultToBidRvCallbackRequest)
        .publishOn(Schedulers.boundedElastic())
        .onErrorMap(
            Exception.class,
            e ->
                new SspBusinessException(
                    ErrorCode.INVALID_JSON_DATA, "can not get data from request body", e))
        .flatMap(
            toBidRvCallbackRequest -> {
              var placementId = toBidRvCallbackRequest.getPlacementId();
              var userId = toBidRvCallbackRequest.getUserId();
              var loadId = toBidRvCallbackRequest.getLoadId();
              String channelId = toBidRvCallbackRequest.getChannelId();

              extraLogInfoMap.put(LogUtil.LOG_FIELD_REQ, uri);
              extraLogInfoMap.put(
                  LogUtil.LOG_FIELD_BODY, JsonSerializationUtils.objToJson(toBidRvCallbackRequest));
              extraLogInfoMap.put("enc", ssvEnc);

              extraLoginFiled.put(RtbCallbackConstants.SsvParam.LOAD_ID.getName(), loadId);
              extraLoginFiled.put(RtbCallbackConstants.SsvParam.USER_ID.getName(), userId);
              extraLoginFiled.put(RtbCallbackConstants.SsvParam.APP_ID.getName(), appIdStr);
              extraLoginFiled.put(
                  RtbCallbackConstants.SsvParam.PLACEMENT_ID.getName(), placementId);
              extraLoginFiled.put(
                  RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.TO_BID_SSV.getName());

              if (Strings.isNullOrEmpty(loadId)
                  || null == appId
                  || Strings.isNullOrEmpty(placementId)
                  || Strings.isNullOrEmpty(userId)
                  || Strings.isNullOrEmpty(channelId)) {

                return badRequestResponse(
                    loadId,
                    appId,
                    clientIp,
                    aesHeaderCbc,
                    toBidRvCallbackRequest,
                    placementId,
                    channelId,
                    currentTimeMillis,
                    currentTimeMillis,
                    extraLogInfoMap,
                    extraLoginFiled);
              }

              return getServerResponseMono(
                  ssvEnc,
                  clientIp,
                  aesHeaderCbc,
                  toBidRvCallbackRequest,
                  appId,
                  currentTimeMillis,
                  extraLogInfoMap,
                  extraLoginFiled);
            })
        .onErrorResume(
            Exception.class,
            e -> {
              LogUtil.error(extraLogInfoMap, extraLoginFiled, e);
              return OK_RESPONSE;
            });
  }

  private static void checkUrlEnc(
      @Nullable String ssvEnc,
      String placementId,
      String loadId,
      String sdkVersion,
      Map<String, String> extraLoginFiled) {

    if (StringUtils.isBlank(ssvEnc)) {
      ErrorCode.ToBid loseCallbackEnc = ErrorCode.ToBid.LOSE_CALLBACK_ENC;
      int code = loseCallbackEnc.getCode();
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
      throw new ToBidSsvException(code, loseCallbackEnc.getMsg(), 3);
    }

    boolean startsWith = ssvEnc.startsWith("pdd|");
    ErrorCode.ToBid changePriceError = ErrorCode.ToBid.CHANGE_PRICE_ERROR;
    if (!startsWith) {
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(changePriceError.getCode()));
      throw new ToBidSsvException(changePriceError.getCode(), "enc解密失败, 缺失pdd", 3);
    }
    byte[] ecbWithCs5 =
        AesUtils.decryptByEcbWithCs5(
            AesUtils.AES_CRYPTO_KEY_CALLBACK,
            Base64.getDecoder().decode(ssvEnc.substring(4).getBytes(StandardCharsets.UTF_8)));

    if (ecbWithCs5 == null) {
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(changePriceError.getCode()));
      throw new ToBidSsvException(changePriceError.getCode(), "enc解密失败", 3);
    }

    String string = new String(ecbWithCs5);
    String str = placementId + ":" + loadId;
    if (StringUtils.isNotBlank(sdkVersion)) {
      str = str + ":" + sdkVersion;
    }
    if (!string.equals(str) && !string.contains(str)) {
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(changePriceError.getCode()));
      throw new ToBidSsvException(
          changePriceError.getCode(), "enc被篡改" + string + ", str:" + str, 3);
    }
  }

  @NotNull
  private Mono<ServerResponse> getServerResponseMono(
      @Nullable String ssvEnc,
      String clientIp,
      String aesHeaderCbc,
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      Integer appId,
      long currentTimeMillis,
      Map<String, Object> extraLogInfoMap,
      Map<String, String> extraLoginFiled) {

    @Nullable String sdkVersion = toBidRvCallbackRequest.getSdkVersion();
    String loadId = toBidRvCallbackRequest.getLoadId();
    String placementId = toBidRvCallbackRequest.getPlacementId();
    String userId = toBidRvCallbackRequest.getUserId();
    String channelId = toBidRvCallbackRequest.getChannelId();
    Map<Integer, RiskyCode> riskyMap = Maps.newHashMap();

    Mono<Optional<ToBidRvCallbackRequest>> impInfoOptionalMono = toBidImpDao.getMaskInfo(loadId);
    Mono<Boolean> checkedRewardMono = toBidImpDao.checkReward(loadId);
    Mono<Optional<StrategyScene>> strategySceneMono =
        mediationStrategyDao.getStrategyScene(placementId);
    Mono<Optional<AppInfo>> appInfoMono = appInfoDao.getAppInfo(appId);
    var riskyChannelMono = ssvRiskyDao.getZipRiskyUserId(userId, appId, channelId);
    return Mono.zip(
            impInfoOptionalMono,
            checkedRewardMono,
            strategySceneMono,
            appInfoMono,
            riskyChannelMono)
        .flatMap(
            result -> {
              Optional<ToBidRvCallbackRequest> maskOptional = result.getT1();
              boolean checkReward = result.getT2();
              Optional<StrategyScene> strategySceneOptional = result.getT3();
              Optional<AppInfo> appInfoOptional = result.getT4();
              var riskyCode = result.getT5().orElse(new RiskyCode(0, null));

              riskyMap.put(1, riskyCode);

              if (!checkReward) {
                ErrorCode.ToBid errorCode = ErrorCode.ToBid.REWARD_REPEAT;
                extraLoginFiled.put(
                    LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode.getCode()));
                throw new ToBidSsvException(errorCode.getCode(), errorCode.getMsg(), 3);
              }
              if (strategySceneOptional.isEmpty()) {
                int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
                extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
                throw new ToBidSsvException(errorCode, "sceneId config does not exist", 3);
              }
              if (appInfoOptional.isEmpty()) {
                int noSuchApp = ErrorCode.REQUEST_ERROR_NO_SUCH_APP;
                String errorMsg = "No such app for appId:" + appId;
                extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(noSuchApp));
                throw new ToBidSsvException(noSuchApp, errorMsg, 3);
              }
              AppInfo appInfo = appInfoOptional.get();
              StrategyScene strategyScene = strategySceneOptional.get();
              checkStrategyScene(extraLoginFiled, strategyScene);

              ToBidRvCallbackRequest cacheRequest = maskOptional.orElse(null);

              AppConfig appConfig = ConfigManager.get(AppConfig.class);

              Boolean needCheckRvMask =
                  Optional.ofNullable(appConfig).map(r -> r.needCheckRvMask(appId)).orElse(true);
              if (needCheckRvMask) {
                if (StringUtils.isNotBlank(aesHeaderCbc)) {
                  checkExt(toBidRvCallbackRequest, extraLoginFiled, extraLogInfoMap);
                } else {
                  checkUrlEnc(ssvEnc, placementId, loadId, sdkVersion, extraLoginFiled);
                }

                checkMask(
                    toBidRvCallbackRequest, appInfo, extraLoginFiled, cacheRequest, strategyScene);
              }
              checkRisky(extraLoginFiled, riskyCode.rt());

              var callbackUrl = getCallbackUrl(toBidRvCallbackRequest, riskyCode, strategyScene);
              extraLogInfoMap.put(
                  RtbCallbackConstants.SsvParam.CALLBACK_URL.getName(), callbackUrl);
              URI callbackUri;
              try {
                callbackUri =
                    UriComponentsBuilder.fromHttpUrl(callbackUrl)
                        .encode(StandardCharsets.UTF_8)
                        .build()
                        .toUri();
              } catch (Exception e) {
                int errorCode = ErrorCode.SSV.REQUEST_CALLBACK_URL_ERROR.getCode();
                extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
                throw new ToBidSsvException(errorCode, e.getMessage(), 3);
              }

              return getServerResponse(
                  toBidRvCallbackRequest,
                  aesHeaderCbc,
                  callbackUri,
                  callbackUrl,
                  appId,
                  clientIp,
                  riskyCode,
                  currentTimeMillis,
                  extraLogInfoMap,
                  extraLoginFiled);
            })
        .onErrorResume(
            e -> {
              int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
              int processType = 0;
              if (e instanceof ToBidSsvException toBidSsvException) {
                errorCode = toBidSsvException.getCode();
                processType = toBidSsvException.getProcessType();
              }
              LogUtil.error(extraLogInfoMap, extraLoginFiled, e);
              send682DataLog(
                  appId,
                  clientIp,
                  aesHeaderCbc,
                  toBidRvCallbackRequest,
                  null,
                  errorCode,
                  riskyMap.getOrDefault(1, new RiskyCode(0, null)),
                  processType,
                  currentTimeMillis,
                  currentTimeMillis);
              return OK_RESPONSE;
            });
  }

  private static void checkExt(
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      Map<String, String> extraLoginFiled,
      Map<String, Object> extraLogInfoMap) {
    // check
    String extBody = toBidRvCallbackRequest.getExtBody();
    int checkMask = toBidRvCallbackRequest.getCheckMask();
    if (StringUtils.isBlank(extBody)) {
      ErrorCode.SSV needExtInfo = ErrorCode.SSV.NEED_EXT_INFO;
      int code = needExtInfo.getCode();
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
      throw new ToBidSsvException(code, needExtInfo.getMessage(), 3);
    } else {
      // fix sdk bug
      // http://wiki.sigmob.cn/pages/viewpage.action?pageId=103839949
      if (true) {
        return;
      }
      Map<String, String> queryMap = SsvUtil.queryToMap(extBody);
      // isRoot	t1	0:代表没有root 1代表手机root
      // isVPN	t2	0:代表未开启VPN 1代表未开启VPN
      // isHook	t3	0代表未检测到Hook注入 1代表检测到Hook注入
      // isDebugable	t4	0代表手机没有开启debug调试 1代表开启debug模式
      // isSystemProxy	t5	0代表有系统代理，1 代表没有开启系统代理
      // isAccessibilityService	t6	0代表未开启无障碍服务，1代表开启无障碍服务
      // updateTime	t7	系统更新时间即/data/data文件时间戳
      // timeStamp	t8	时间戳
      // appSha256	t9	apk证书签名sha256
      // eventQueue (java内存)	t10
      // "事件ID|时间戳|channel_id|channel_adslot_id,事件ID|时间戳|channel_id|channel_adslot_id"
      // jniEventQueue	t11	"事件ID|时间戳|额外信息base64,事件ID|时间戳|额外信息base64"
      // impResponseSignCode	t12	曝光返回签名信息
      // eventQueueSign	t13	广告序列签名signature
      // event_mask	t14	m字段NDK 解密后根据element_id解析的mask
      if (checkMask == 1) {
        if (MapUtils.isEmpty(queryMap)
            || StringUtils.isBlank(queryMap.get("t10"))
            || StringUtils.isBlank(queryMap.get("t11"))
            || StringUtils.isBlank(queryMap.get("t12"))
            || StringUtils.isBlank(queryMap.get("t14"))) {
          ErrorCode.SSV needExtInfo = ErrorCode.SSV.NEED_EXT_INFO;
          int code = needExtInfo.getCode();
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
          throw new ToBidSsvException(code, needExtInfo.getMessage(), 3);
        }
        extraLogInfoMap.putAll(queryMap);
        String impCode = queryMap.get("t12");
        //  aes impCode加密处理
        byte[] decrypt =
            AesUtils.decryptByEcbWithCs5(
                AesUtils.AES_CRYPTO_KEY_CALLBACK,
                Base64.getDecoder().decode(impCode.getBytes(StandardCharsets.UTF_8)));
        if (null == decrypt) {
          ErrorCode.SSV needExtInfo = ErrorCode.SSV.NEED_EXT_INFO;
          int code = needExtInfo.getCode();
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
          throw new ToBidSsvException(code, needExtInfo.getMessage(), 3);
        }
        String eventMask = queryMap.get("t14");
        String mask = toBidRvCallbackRequest.getMask();
        if (!eventMask.equals(mask)) {
          var errorCode = ErrorCode.SSV.MASK_NOT_EQUALS_EXT_INFO_MASK;
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode.getCode()));
          throw new ToBidSsvException(errorCode.getCode(), errorCode.getMessage(), 3);
        }
      } else {
        if (MapUtils.isEmpty(queryMap)
            || StringUtils.isBlank(queryMap.get("t10"))
            || StringUtils.isBlank(queryMap.get("t11"))) {
          ErrorCode.SSV needExtInfo = ErrorCode.SSV.NEED_EXT_INFO;
          int code = needExtInfo.getCode();
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
          throw new ToBidSsvException(code, needExtInfo.getMessage(), 3);
        }
      }
    }
  }

  //  private static RiskyCode getRiskyCode(String riskyChannel) {
  //    // 兼容老逻辑
  //    if (riskyChannel.length() <= 1) {
  //      if (RtbCallbackConstants.SsvCheckType.SKIP.getType().equals(riskyChannel)) {
  //        return new RiskyCode(2, null);
  //      } else if (RtbCallbackConstants.SsvCheckType.BLOCK.getType().equals(riskyChannel)) {
  //        return new RiskyCode(1, null);
  //      }
  //      return new RiskyCode(0, null);
  //    }
  //
  //    RiskyCode riskyCode = JsonSerializationUtils.jsonToObj(riskyChannel, RiskyCode.class);
  //    if (riskyCode == null) {
  //      return new RiskyCode(0, null);
  //    }
  //    return riskyCode;
  //  }

  private static void checkRisky(Map<String, String> extraLoginFiled, int risky) {
    extraLoginFiled.put(LogUtil.LOG_FIELD_RISKY, String.valueOf(risky));
    if (risky == 1) {
      ErrorCode.ToBid riskyUser = ErrorCode.ToBid.RISKY_USER;
      int errCode = riskyUser.getCode();
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errCode));

      throw new ToBidSsvException(errCode, riskyUser.getMsg(), 1);
    }
  }

  private static void checkStrategyScene(
      Map<String, String> extraLoginFiled, StrategyScene strategyScene) {
    var callbackUrl = strategyScene.getRewardUrl();
    String rewardName = strategyScene.getRewardName();
    String appSecurityKey = strategyScene.getAppSecurityKey();
    if (ObjectUtils.anyNull(callbackUrl, appSecurityKey, rewardName)) {
      int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
      extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
      throw new ToBidSsvException(
          errorCode,
          "sceneId reward config is error " + JsonSerializationUtils.objToJson(strategyScene),
          3);
    }
  }

  private static void checkMask(
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      AppInfo appInfo,
      Map<String, String> extraLoginFiled,
      @Nullable ToBidRvCallbackRequest cacheRequest,
      StrategyScene strategyScene) {
    @Nullable String sdkVersion = toBidRvCallbackRequest.getSdkVersion();

    if (null == sdkVersion) {
      return;
    }
    Version version = DeviceUtil.strToVersion(sdkVersion);

    Integer osType = appInfo.getOsType();
    if ((osType == OsType.ANDROID.getTypeNum()
        && DeviceUtil.compareVersion(version, WindmillConstants.VER_4_0_3) >= 0)) {
      int checkMask = toBidRvCallbackRequest.getCheckMask();
      String mask = toBidRvCallbackRequest.getMask();
      if (StringUtils.isEmpty(mask)) {
        int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
        extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
        throw new ToBidSsvException(errorCode, "body mask can not be empty", 3);
      }

      // 校验mask
      if (checkMask == 1) {
        if (null == cacheRequest || StringUtils.isEmpty(cacheRequest.getMask())) {
          ErrorCode.ToBid impNotExist = ErrorCode.ToBid.IMP_NOT_EXIST;
          int errorCode = impNotExist.getCode();
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
          throw new ToBidSsvException(errorCode, impNotExist.getMsg(), 3);
        }

        String cacheMask = cacheRequest.getMask();
        if (!mask.equals(cacheMask)) {
          ErrorCode.ToBid maskNotEquals = ErrorCode.ToBid.MASK_NOT_EQUALS;
          int errorCode = maskNotEquals.getCode();
          extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
          throw new ToBidSsvException(
              errorCode, "imp mask: " + cacheMask + maskNotEquals.getMsg(), 3);
        }
      }

      String maskValue =
          getMaskValue(toBidRvCallbackRequest, sdkVersion, strategyScene.getAppSecurityKey());
      if (!mask.equals(maskValue)) {
        ErrorCode.ToBid maskNotEquals = ErrorCode.ToBid.MASK_NOT_EQUALS;
        int errorCode = maskNotEquals.getCode();
        extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
        throw new ToBidSsvException(errorCode, maskNotEquals.getMsg(), 3);
      }
    }
  }

  private static String getMaskValue(
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      @NotNull String sdkVersion,
      String strategySceneKey) {
    // check mask content
    Map<String, String> treeMap = new TreeMap<>();

    String loadId = toBidRvCallbackRequest.getLoadId();
    String placementId = toBidRvCallbackRequest.getPlacementId();
    /// String userId = toBidRvCallbackRequest.getUserId();

    treeMap.put(WindmillConstants.MaskKey.loadId, loadId);
    treeMap.put(WindmillConstants.MaskKey.ecpm, String.valueOf(toBidRvCallbackRequest.getEcpm()));
    treeMap.put(
        WindmillConstants.MaskKey.timestamp, String.valueOf(toBidRvCallbackRequest.getTimestamp()));
    treeMap.put(WindmillConstants.MaskKey.placementId, placementId);
    treeMap.put(
        WindmillConstants.MaskKey.channelId, String.valueOf(toBidRvCallbackRequest.getChannelId()));
    treeMap.put(
        WindmillConstants.MaskKey.thirdPlacementId, toBidRvCallbackRequest.getThirdPlacementId());
    /// treeMap.put(WindmillConstants.MaskKey.userId, userId);
    treeMap.put(
        WindmillConstants.MaskKey.checkMask, String.valueOf(toBidRvCallbackRequest.getCheckMask()));
    treeMap.put(WindmillConstants.MaskKey.sdkVersion, sdkVersion);

    StringBuilder str = new StringBuilder();
    for (String value : treeMap.values()) {
      str.append(value);
    }

    /// LogUtil.localInfo(Constants.EMPTY_JSON, str);

    // sha256 + 聚合广告位SecurityKey
    String string = str + strategySceneKey;
    /// LogUtil.localInfo(Constants.EMPTY_JSON, string);

    return MessageDigestUtil.getSHA256Str(string);
  }

  private Mono<ServerResponse> getServerResponse(
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      String aesCbc,
      URI callbackUri,
      String callbackUrl,
      Integer appId,
      String clientIp,
      RiskyCode riskyCode,
      long currentTimeMillis,
      Map<String, Object> extraLogInfoMap,
      Map<String, String> extraLoginFiled) {
    var placementId = toBidRvCallbackRequest.getPlacementId();
    var loadId = toBidRvCallbackRequest.getLoadId();
    // 一定发生回调, 因此不是2（block）就是0
    int processType = riskyCode.rt() == 2 ? 2 : 0;

    return webClient
        .get()
        .uri(callbackUri)
        .acceptCharset(StandardCharsets.UTF_8)
        .exchangeToMono(
            clientResponse -> {
              var sendCallbackTime = System.currentTimeMillis();

              if (!clientResponse.statusCode().equals(HttpStatus.OK)) {
                return clientResponse
                    .bodyToMono(String.class)
                    .defaultIfEmpty(StringUtils.EMPTY)
                    .switchIfEmpty(Mono.just(StringUtils.EMPTY))
                    .doOnError(
                        e -> RtbLogUtil.ssvWarn("request({}) {} error!", loadId, callbackUrl, e))
                    .map(
                        reason -> {
                          int httpCode = clientResponse.rawStatusCode();
                          extraLogInfoMap.put("code", httpCode);
                          throw Exceptions.propagate(
                              new DspBusinessException(
                                  httpCode,
                                  "request"
                                      + callbackUrl
                                      + " error, return http code:"
                                      + httpCode
                                      + ",requestId:"
                                      + loadId
                                      + ",reason:"
                                      + Strings.nullToEmpty(reason)
                                      + " ,appId:"
                                      + appId
                                      + ",adSlotId:"
                                      + placementId));
                        });
              }
              return clientResponse
                  .bodyToMono(String.class)
                  .switchIfEmpty(
                      Mono.error(
                          new DspBusinessException(
                              ErrorCode.DSP_API_INVALID_HTTP_STATUS,
                              "request "
                                  + callbackUrl
                                  + " error, response body is null! appId:"
                                  + appId
                                  + ",adSlotId:"
                                  + placementId
                                  + ",requestId:"
                                  + loadId)))
                  .flatMap(
                      responseBody -> {
                        extraLogInfoMap.put("code", 200);
                        extraLogInfoMap.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, responseBody);

                        int errorCode = getErrorCodeFromResponseBody(responseBody);
                        extraLoginFiled.put(
                            LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
                        LogUtil.info(extraLogInfoMap, extraLoginFiled);

                        send682DataLog(
                            appId,
                            clientIp,
                            aesCbc,
                            toBidRvCallbackRequest,
                            callbackUrl,
                            errorCode,
                            riskyCode,
                            processType,
                            sendCallbackTime,
                            System.currentTimeMillis());

                        return OK_RESPONSE;
                      });
            })
        .retryWhen(Retry.fixedDelay(3, Duration.ofMillis(500)))
        .timeout(Duration.ofSeconds(3))
        .onErrorResume(
            Exception.class,
            e -> {
              int errCode = getCode(e);
              extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errCode));
              LogUtil.error(extraLogInfoMap, extraLoginFiled, e);
              send682DataLog(
                  appId,
                  clientIp,
                  aesCbc,
                  toBidRvCallbackRequest,
                  callbackUrl,
                  errCode,
                  riskyCode,
                  processType,
                  currentTimeMillis,
                  System.currentTimeMillis());
              return OK_RESPONSE;
            });
  }

  private static int getErrorCodeFromResponseBody(String responseBody) {
    SsvCallbackResponse ssvResponse =
        JsonSerializationUtils.jsonToObj(responseBody, SsvCallbackResponse.class);
    if (null == ssvResponse || ssvResponse.getIsValid() == null) {
      return ErrorCode.SSV.CALLBACK_RESPONSE_ERROR.getCode();
    } else {
      return ssvResponse.getIsValid() ? 0 : ErrorCode.SSV.CALLBACK_RESPONSE_FALSE.getCode();
    }
  }

  private String getCallbackUrl(
      ToBidRvCallbackRequest toBidRvCallbackRequest,
      RiskyCode riskyCode,
      StrategyScene strategyScene) {

    String appSecurityKey = strategyScene.getAppSecurityKey();
    String loadId = toBidRvCallbackRequest.getLoadId();

    var callbackUrl = replaceUrlMacro(strategyScene, toBidRvCallbackRequest);

    // 风险 not block用户
    if (String.valueOf(riskyCode.rt()).equals(RtbCallbackConstants.SsvCheckType.SKIP.getType())) {
      callbackUrl = callbackUrl + "&is_risky=" + 1;
      List<String> riskyCodeList = riskyCode.code();
      if (CollectionUtils.isNotEmpty(riskyCodeList)) {
        callbackUrl = callbackUrl + "&risky_code=" + StringUtils.join(riskyCodeList, ",");
      }
    }

    String mask = toBidRvCallbackRequest.getMask();
    if (StringUtils.isEmpty(mask)) {
      return callbackUrl;
    }

    String versionSign = MessageDigestUtil.getSHA256Str(loadId + appSecurityKey + "2.0");
    return callbackUrl + "&ssvVersion=" + "2.0" + "&versionSign=" + versionSign;
  }

  public static void main(String[] args) {
    String json =
        """
              {"loadId":"7f74d7da673d80c480a0df855b133334","placementId":"1339313235219173","userId":"1988377","custom":"{\\"role_id\\":\\"292284698770764353\\",\\"server_id\\":\\"340\\",\\"extra\\":\\"8\\"}","channelId":"16","thirdAppId":"1208022086","thirdPlacementId":"2183712233158949","ecpm":10779,"uid":"AB25F3EF-7C50-4C10-BDAE-742462875EB7","sdkVersion":"4.1.0","networkAdType":4,"mask":"f8e4d8909eb55b05d11272a7d24d4ca6c0cdb314f83301e784a525600a314c6b","checkMask":0,"timestamp":1734511799,"gameVersion":"1.0.3","extBody":""}
              """;
    ToBidRvCallbackRequest bidRvCallbackRequest =
        JsonSerializationUtils.jsonToObj(json, ToBidRvCallbackRequest.class);

    String sdkVersion = "4.1.0";
    String strategyScene = "qlx3eguo7yaoneowugwf814kaiqpw01g";
    assert bidRvCallbackRequest != null;
    String maskValue = getMaskValue(bidRvCallbackRequest, sdkVersion, strategyScene);
    System.out.println(maskValue);
  }

  private static int getCode(Exception e) {
    int code = ErrorCode.SSV.REQUEST_CALLBACK_URL_ERROR.getCode();
    if (e instanceof ToBidSsvException) {
      code = ((ToBidSsvException) e).getCode();
    } else if (e instanceof DspBusinessException) {
      code = ((DspBusinessException) e).getCode();
    } else if (e.getCause() != null) {
      Throwable throwable = e.getCause();
      // RetryExhaustedException
      if (throwable instanceof ToBidSsvException) {
        code = ((ToBidSsvException) throwable).getCode();
      } else if (throwable.getCause() != null) {
        Throwable cause = throwable.getCause();
        if (cause instanceof SigmobException) {
          code = ((SigmobException) cause).getCode();
        }
      }
    }
    return code;
  }

  private String replaceUrlMacro(
      @NonNull StrategyScene strategyScene, ToBidRvCallbackRequest request) {

    List<String> keys = Lists.newArrayListWithCapacity(24);
    List<String> values = Lists.newArrayListWithCapacity(24);

    var url = strategyScene.getRewardUrl();

    String rewardName = strategyScene.getRewardName();
    String appSecurityKey = strategyScene.getAppSecurityKey();
    int rewardCount = strategyScene.getRewardNum();
    String sign = MessageDigestUtil.getSHA256Str(appSecurityKey + ":" + request.getLoadId());

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.USER_ID.getName());
    values.add(request.getUserId());

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.LOAD_ID.getName());
    values.add(request.getLoadId());

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.REWARD_AMOUNT.getName());
    values.add(String.valueOf(rewardCount));

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.REWARD_NAME.getName());
    values.add(rewardName);

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.SIGN.getName());
    values.add(sign);

    if (StringUtils.isNotBlank(request.getCustom())) {
      keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.EXTRA_INFO.getName());
      values.add(URLEncoder.encode(request.getCustom(), StandardCharsets.UTF_8));
    }

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.PLACEMENT_ID.getName());
    values.add(request.getPlacementId());

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.CHANNEL_ID.getName());
    values.add(request.getChannelId());

    @Nullable String thirdPlacementId = request.getThirdPlacementId();
    if (StringUtils.isNotBlank(thirdPlacementId)) {
      keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_PLACEMENT_ID.getName());
      values.add(thirdPlacementId);
    }

    // 3.0.0 price support
    String priceMacroName = RtbCallbackConstants.ToBidThirdPartySsvParam.PRICE.getName();
    if (url.contains(priceMacroName)) {
      keys.add(priceMacroName);
      String string = StringUtils.EMPTY;
      Integer ecpm = request.getEcpm();
      if (null != ecpm) {
        String encrypted =
            AesUtils.encryptOrDefault(appSecurityKey, String.valueOf(ecpm), StringUtils.EMPTY);
        if (StringUtils.isNotBlank(encrypted)) {
          string = URLEncoder.encode(encrypted, StandardCharsets.UTF_8);
        }
      }
      values.add(string);
    }

    // 3.0.1 real support
    String realPlacementIdNameMacro =
        RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_REAL_PLACEMENT_ID.getName();
    if (url.contains(realPlacementIdNameMacro)) {
      keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_REAL_PLACEMENT_ID.getName());
      values.add(
          StringUtils.isBlank(request.getThirdRealPlacementId())
              ? StringUtils.EMPTY
              : request.getThirdRealPlacementId());
    }

    // 3.3.0 support THIRD_TRANS_ID
    String thirdTransIdNameMacro =
        RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_TRANS_ID.getName();
    if (url.contains(thirdTransIdNameMacro)) {
      keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_TRANS_ID.getName());
      values.add(
          StringUtils.isBlank(request.getThirdTransId())
              ? StringUtils.EMPTY
              : request.getThirdTransId());
    }

    // 3.9.0 NETWORK_AD_TYPE support
    String thirdAdTypeNameMacro =
        RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_AD_TYPE.getName();
    if (url.contains(thirdAdTypeNameMacro)) {
      keys.add(thirdAdTypeNameMacro);
      values.add(
          null == request.getNetworkAdType() ? StringUtils.EMPTY : request.getNetworkAdType() + "");
    }

    // 3.9.0 REWARD_TIMING support
    String rewardTimingNameMacro =
        RtbCallbackConstants.ToBidThirdPartySsvParam.REWARD_TIMING.getName();
    if (url.contains(rewardTimingNameMacro)) {
      keys.add(rewardTimingNameMacro);
      values.add(null == request.getRewardType() ? "0" : request.getRewardType() + "");
    }

    String thirdCodePriceNameMacro =
        RtbCallbackConstants.ToBidThirdPartySsvParam.THIRD_CODE_PRICE.getName();
    if (url.contains(thirdCodePriceNameMacro)) {
      keys.add(thirdCodePriceNameMacro);
      values.add(
          StringUtils.isEmpty(request.getThirdCodePrice())
              ? StringUtils.EMPTY
              : request.getThirdCodePrice());
    }
    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.APP_VERSION.getName());
    values.add(
        StringUtils.isNotBlank(request.getGameVersion())
            ? request.getGameVersion()
            : StringUtils.EMPTY);

    keys.add(RtbCallbackConstants.ToBidThirdPartySsvParam.SDK_VERSION.getName());
    values.add(
        StringUtils.isNotBlank(request.getSdkVersion())
            ? request.getSdkVersion()
            : StringUtils.EMPTY);

    return StringUtils.replaceEach(url, keys.toArray(new String[0]), values.toArray(new String[0]));
  }

  private Mono<ServerResponse> badRequestResponse(
      String loadId,
      Integer appId,
      String clientIp,
      String aesHeaderCbc,
      ToBidRvCallbackRequest request,
      String placementId,
      String channelId,
      long sendCallbackTime,
      long finishCallbackTime,
      Map<String, Object> extraLogInfoMap,
      Map<String, String> extraLogFields) {
    String errorMsg =
        "ssv param error! load id:"
            + loadId
            + ", aid:"
            + appId
            + ", pid:"
            + placementId
            + ", channelId:"
            + channelId;
    int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
    extraLogFields.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
    LogUtil.error(extraLogInfoMap, extraLogFields, new ToBidSsvException(errorCode, errorMsg));
    send682DataLog(
        appId,
        clientIp,
        aesHeaderCbc,
        request,
        null,
        errorCode,
        new RiskyCode(1, null),
        3,
        sendCallbackTime,
        finishCallbackTime);
    return OK_RESPONSE;
  }

  /** 发送682号点打点日志 */
  private void send682DataLog(
      Integer appId,
      String clientIp,
      String aesHeaderCbc,
      ToBidRvCallbackRequest request,
      String appCallbackUrl,
      int callbackCode,
      RiskyCode risky,
      int processType,
      long sendCallbackTime,
      long finishCallbackTime) {
    var placementId = request.getPlacementId();
    var userId = request.getUserId();
    var loadId = request.getLoadId();
    RewardProcessInfo rewardProcessInfo =
        new RewardProcessInfo(
            risky.rt() > 0 ? 1 : processType > 0 ? 1 : 0, risky.code(), processType);
    try {
      Ac682MessageBuilder messageBuilder =
          new Ac682MessageBuilder(
              loadId,
              RtbCallbackConstants.SsvCategory.REWARD.getType(),
              clientIp,
              request,
              appId,
              placementId,
              userId,
              aesHeaderCbc,
              appCallbackUrl,
              callbackCode,
              rewardProcessInfo,
              sendCallbackTime,
              finishCallbackTime);
      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 682 Datalog error!", e);
    }
  }

  /** 激励视频奖励服务端验证回调 */
  @NonNull
  public Mono<ServerResponse> test(@NonNull ServerRequest request) {

    var params = request.queryParams();

    Map<String, Object> extraLogInfoMap = Maps.newHashMapWithExpectedSize(16);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(16);

    extraLogInfoMap.put("param", params);
    LogUtil.info(extraLogInfoMap, extraLoginFiled);
    SsvCallbackResponse ssvResponse = new SsvCallbackResponse();
    ssvResponse.setIsValid(true);
    return ok().body(BodyInserters.fromValue(ssvResponse));
  }
}
