package com.sigmob.ad.rtb.callback.datalog;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.datalog.AcType;
import com.sigmob.ad.core.datalog.DataLogField;
import com.sigmob.ad.core.datalog.DataLogMessageBuilder;
import com.sigmob.ad.core.util.SsvUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.domain.RewardProcessInfo;
import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class Ac682MessageBuilder extends DataLogMessageBuilder {

  private final String loadId;

  private final String category;

  private final String clientIp;

  private final ToBidRvCallbackRequest request;

  private final Integer appId;

  private final String placementId;

  private final String userId;

  private final String aesHeaderCbc;

  private final String appCallbackUrl;

  private final int callbackCode;

  private final RewardProcessInfo rewardProcessInfo;

  private final long sendCallbackTime;

  private final long finishCallbackTime;

  private static final AcType AC_TYPE = AcType.SIX_EIGHT_TWO;

  public Ac682MessageBuilder(
      String loadId,
      String category,
      String clientIp,
      ToBidRvCallbackRequest request,
      Integer appId,
      String placementId,
      String userId,
      @Nullable String aesHeaderCbc,
      String appCallbackUrl,
      int callbackCode,
      RewardProcessInfo rewardProcessInfo,
      long sendCallbackTime,
      long finishCallbackTime) {
    this.loadId = loadId;
    this.category = category;
    this.clientIp = clientIp;
    this.request = request;
    this.appId = appId;
    this.placementId = placementId;
    this.userId = userId;
    this.aesHeaderCbc = aesHeaderCbc;
    this.appCallbackUrl = appCallbackUrl;
    this.callbackCode = callbackCode;
    // this.risky = risky;
    this.rewardProcessInfo = rewardProcessInfo;
    this.sendCallbackTime = sendCallbackTime;
    this.finishCallbackTime = finishCallbackTime;
  }

  @Override
  protected Map<String, Object> buildMessageBody() {

    Map<String, Object> dataLog = Maps.newHashMapWithExpectedSize(100);

    dataLog.put(DataLogField.LOAD_ID, loadId);
    dataLog.put(DataLogField.CATEGORY, category);
    dataLog.put(DataLogField.CHANNEL_ID, request.getChannelId());
    dataLog.put(DataLogField.REQUEST_UID, request.getUid());
    if (RtbCallbackConstants.SsvCategory.REWARD.getType().equals(category)) {
      if (null != rewardProcessInfo) {
        dataLog.put(DataLogField.PRICE_IS_RISKY, rewardProcessInfo.getIsRisky());
        dataLog.put(DataLogField.PROCESS_TYPE, rewardProcessInfo.getProcessType());
        if (CollectionUtils.isNotEmpty(rewardProcessInfo.getCode())) {
          dataLog.put(DataLogField.RISKY_CODE, StringUtils.join(rewardProcessInfo.getCode(), ","));
        }
      }
    }
    if (StringUtils.isNotEmpty(request.getThirdTransId())) {
      dataLog.put(DataLogField.THIRD_TRANS_ID, request.getThirdTransId());
    }
    if (request.getTimestamp() > 0) {
      dataLog.put(DataLogField.LOAD_TIMESTAMP, request.getTimestamp());
    }
    if (StringUtils.isNotBlank(aesHeaderCbc)) {
      dataLog.put(DataLogField.NDK_VERSION, aesHeaderCbc);
    }
    // queryMap
    if (StringUtils.isNotBlank(request.getExtBody())) {
      Map<String, String> queryMap = SsvUtil.queryToMap(request.getExtBody());
      if (MapUtils.isNotEmpty(queryMap)) {
        dataLog.putAll(queryMap);
      }
    }

    if (StringUtils.isNotBlank(clientIp)) {
      dataLog.put(DataLogField.CLIENT_IP, clientIp);
    }

    dataLog.put(DataLogField.APP_ID_NEW, null == appId ? StringUtils.EMPTY : appId.toString());
    dataLog.put(DataLogField.PLACEMENT_ID, placementId);
    if (StringUtils.isNotBlank(request.getSdkVersion())) {
      dataLog.put(DataLogField.SDK_VERSION, request.getSdkVersion());
    }
    if (null != request.getEcpm()) {
      dataLog.put(DataLogField.ECPM_PRICE, String.valueOf(request.getEcpm()));
    }
    if (StringUtils.isNotBlank(request.getMask())) {
      dataLog.put(DataLogField.MASK, request.getMask());
    }

    if (null != request.getNetworkAdType()) {
      dataLog.put(DataLogField.THIRD_PLACEMENT_AD_TYPE, request.getNetworkAdType());
    }

    dataLog.put(DataLogField.CHECK_MASK, request.getCheckMask());
    if (StringUtils.isNotBlank(request.getThirdPlacementId())) {
      dataLog.put(DataLogField.THIRD_PLACEMENT_ID, String.valueOf(request.getThirdPlacementId()));
    }
    if (StringUtils.isNotBlank(request.getThirdRealPlacementId())) {
      dataLog.put(
          DataLogField.THIRD_REAL_PLACEMENT_ID, String.valueOf(request.getThirdRealPlacementId()));
    }
    dataLog.put(
        DataLogField.APP_CALLBACK_URL_RV,
        URLEncoder.encode(Strings.nullToEmpty(appCallbackUrl), StandardCharsets.UTF_8));
    dataLog.put(DataLogField.APP_CALLBACK_CODE, Integer.toString(callbackCode));
    dataLog.put(DataLogField.IS_VALID_RV, Integer.toString(callbackCode == 0 ? 1 : 0));
    dataLog.put(DataLogField.CALLBACK_TIMESTAMP, Long.toString(sendCallbackTime));
    dataLog.put(DataLogField.DEVICE_USER_ID, userId);
    dataLog.put(DataLogField.VALID_TIMESTAMP, Long.toString(finishCallbackTime));

    return dataLog;
  }

  @Override
  protected int getOsType() {
    return 0;
  }

  @Override
  protected int getAppId() {
    return appId;
  }

  @Override
  protected AcType getAcType() {
    return AC_TYPE;
  }

  @Override
  protected String getUserId() {
    return userId;
  }
}
