package com.sigmob.ad.rtb.callback.handler;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.config.tobid.WindmillConfig;
import com.sigmob.ad.core.constants.WindmillConstants;
import com.sigmob.ad.rtb.callback.datalog.Ac636MessageBuilder;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.exception.*;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.domain.MmpCallbackResponse;
import com.twofishes.config.manager.ConfigManager;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/** ToBid服务端回调mmp */
@Component
@RequiredArgsConstructor
public class ToBidMmpHandler extends CallbackBaseHandler {

  @NonNull private final WebClient webClient;

  @NonNull private final DataLogService dataLogService;

  @NonNull
  public Mono<ServerResponse> mmpCallback(@NonNull ServerRequest request) {

    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String uri = httpRequest.getURI().toString();

    var event =
        request
            .queryParam(WindmillConstants.TrackClientMacro.EVENT.getName())
            .orElse(StringUtils.EMPTY);
    var appIdStr =
        request
            .queryParam(WindmillConstants.MmpCallbackParam.APP_ID.getName())
            .orElse(StringUtils.EMPTY);
    int appId = NumberUtils.toInt(appIdStr);

    var sceneId =
        request
            .queryParam(WindmillConstants.MmpCallbackParam.PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);
    var loadId =
        request
            .queryParam(WindmillConstants.MmpCallbackParam.LOAD_ID.getName())
            .orElse(StringUtils.EMPTY);

    var platform =
        request
            .queryParam(WindmillConstants.MmpCallbackParam.PLATFORM.getName())
            .orElse(StringUtils.EMPTY);

    var timestamp =
        request
            .queryParam(WindmillConstants.TrackClientMacro.TIMESTAMP.getName())
            .orElse(StringUtils.EMPTY);

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(8);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(8);

    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);

    String mmp = RtbCallbackConstants.ReqType.TO_BID_MMP.getName();

    extraLoginFiled.put(WindmillConstants.MmpCallbackParam.LOAD_ID.getName(), loadId);
    extraLoginFiled.put(WindmillConstants.MmpCallbackParam.APP_ID.getName(), appIdStr);
    extraLoginFiled.put(RtbCallbackConstants.PLACEMENT_ID, sceneId);
    extraLoginFiled.put(WindmillConstants.MmpCallbackParam.PLATFORM.getName(), platform);
    extraLoginFiled.put(RtbCallbackConstants.REQ_TYPE, mmp);
    extraLoginFiled.put(WindmillConstants.TrackClientMacro.EVENT.getName(), event);

    WindmillConfig windmillConfig = ConfigManager.get(WindmillConfig.class);
    boolean notEmpty = null != windmillConfig;
    boolean thirdUrlExist =
        notEmpty
            && MapUtils.isNotEmpty(windmillConfig.getMmpUrlConfig())
            && windmillConfig.getMmpUrlConfig().containsKey(platform);
    if (Strings.isNullOrEmpty(loadId)
        || 0 == appId
        || !thirdUrlExist
        || Strings.isNullOrEmpty(sceneId)
        || Strings.isNullOrEmpty(event)
        || Strings.isNullOrEmpty(platform)) {

      var currentTimeMillis = System.currentTimeMillis();
      return badRequestResponse(
          loadId,
          appId,
          sceneId,
          event,
          platform,
          thirdUrlExist ? windmillConfig.getMmpUrlConfig().get(platform) : "empty",
          timestamp,
          currentTimeMillis,
          currentTimeMillis,
          extraLogInfos,
          extraLoginFiled);
    }
    // check 可以直接使用
    String mmpUrl = windmillConfig.getMmpUrlConfig().get(platform);

    mmpUrl = replaceUrl(mmpUrl, uri);
    extraLogInfos.put("mmpUrl", mmpUrl);

    URI callbackUri;
    try {
      callbackUri =
          UriComponentsBuilder.fromHttpUrl(mmpUrl).encode(StandardCharsets.UTF_8).build().toUri();
    } catch (Exception e) {
      /// RtbLogUtil.ssvError("{} parse mmp callback url:{} error!", loadId, mmpUrl, e);
      LogUtil.error(extraLogInfos, extraLoginFiled, e);
      send636DataLog(
          loadId,
          appId,
          sceneId,
          event,
          platform,
          mmpUrl,
          ErrorCode.Mmp.REQUEST_CALLBACK_URL_ERROR.getCode(),
          e.getMessage(),
          timestamp,
          System.currentTimeMillis(),
          System.currentTimeMillis());
      return OK_RESPONSE;
    }

    String finalMmpUrl = mmpUrl;
    return webClient
        .get()
        .uri(callbackUri)
        .acceptCharset(StandardCharsets.UTF_8)
        .exchangeToMono(
            clientResponse -> {
              var sendCallbackTime = System.currentTimeMillis();
              if (!clientResponse.statusCode().equals(HttpStatus.OK)) {
                return clientResponse
                    .bodyToMono(String.class)
                    .defaultIfEmpty(StringUtils.EMPTY)
                    .switchIfEmpty(Mono.just(StringUtils.EMPTY))
                    .doOnError(e -> LogUtil.error(extraLogInfos, extraLoginFiled, e))
                    .map(
                        reason -> {
                          int httpCode = clientResponse.rawStatusCode();
                          extraLogInfos.put("code", httpCode);
                          throw Exceptions.propagate(
                              new DspBusinessException(
                                  httpCode,
                                  "request url:"
                                      + finalMmpUrl
                                      + " error, return http code:"
                                      + httpCode
                                      + ", loadId:"
                                      + loadId
                                      + ", reason:"
                                      + Strings.nullToEmpty(reason)
                                      + ", appId:"
                                      + appIdStr
                                      + ", sceneId:"
                                      + sceneId));
                        });
              } else {
                return clientResponse
                    .bodyToMono(String.class)
                    .switchIfEmpty(
                        Mono.error(
                            new DspBusinessException(
                                ErrorCode.DSP_API_INVALID_HTTP_STATUS,
                                "request url:"
                                    + finalMmpUrl
                                    + " error, response body is null! appId:"
                                    + appIdStr
                                    + ", loadId:"
                                    + loadId
                                    + ", sceneId:"
                                    + sceneId)))
                    .flatMap(
                        responseBody -> {
                          extraLogInfos.put("code", 200);

                          extraLogInfos.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, responseBody);
                          MmpCallbackResponse response =
                              JsonSerializationUtils.jsonToObj(
                                  responseBody, MmpCallbackResponse.class);
                          if (null == response) {
                            return Mono.error(
                                new SigmobException(
                                    ErrorCode.Mmp.CALLBACK_RESPONSE_ERROR.getCode(),
                                    ErrorCode.Mmp.CALLBACK_RESPONSE_ERROR.getMessage()));
                          }

                          LogUtil.info(extraLogInfos, extraLoginFiled);

                          send636DataLog(
                              loadId,
                              appId,
                              sceneId,
                              event,
                              platform,
                              finalMmpUrl,
                              response.getCode(),
                              response.getMsg(),
                              timestamp,
                              sendCallbackTime,
                              System.currentTimeMillis());

                          return OK_RESPONSE;
                        });
              }
            })
        .retryWhen(Retry.fixedDelay(3, Duration.ofMillis(500)))
        .timeout(Duration.ofSeconds(3))
        .onErrorResume(
            Exception.class,
            e -> {
              int code = getErrorCode(e);
              // RtbLogUtil.ssvError("request({}) callback url:{} error!", loadId, finalMmpUrl, e);
              LogUtil.error(extraLogInfos, extraLoginFiled, e);
              send636DataLog(
                  loadId,
                  appId,
                  sceneId,
                  event,
                  platform,
                  finalMmpUrl,
                  code,
                  e.getMessage(),
                  timestamp,
                  System.currentTimeMillis(),
                  System.currentTimeMillis());
              return OK_RESPONSE;
            });
  }

  public static int getErrorCode(Exception e) {
    int code = ErrorCode.Mmp.REQUEST_CALLBACK_URL_ERROR.getCode();
    if (e instanceof SigmobException) {
      code = ((SigmobException) e).getCode();
    } else if (e instanceof DspBusinessException) {
      code = ((DspBusinessException) e).getCode();
    } else if (e.getCause() != null) {
      Throwable throwable = e.getCause();
      // RetryExhaustedException
      if (throwable instanceof SigmobException) {
        code = ((SigmobException) throwable).getCode();
      } else if (throwable.getCause() != null) {
        Throwable cause = throwable.getCause();
        if (cause instanceof SigmobException) {
          code = ((SigmobException) cause).getCode();
        }
      }
    }
    return code;
  }

  private String replaceUrl(String mmpUrl, String uri) {

    if (uri.contains("?")) {
      String[] split = uri.split("\\?");
      int len = 2;
      if (split.length == len) {
        // remove platform 参数
        String string =
            removeParam(split[1], WindmillConstants.MmpCallbackParam.PLATFORM.getName());
        return mmpUrl + "?" + string;
      }
    }
    return mmpUrl;
  }

  /**
   * 去除url指定参数
   *
   * @param url url
   * @param name name
   */
  private static String removeParam(String url, String... name) {
    for (String s : name) {
      // 使用replaceAll正则替换,replace不支持正则
      url = url.replaceAll("&&?" + s + "=[^&]*", "");
    }
    return url;
  }

  public static void main(String[] args) {
    String url =
        "https://www.lyf.com/user/info?uid=1&enc=88182&platform=1001&t=1597372964477&id=123";

    System.out.println(url);
    String string = removeParam(url, "platform", "id");
    // 结果: https://www.lyf.com/user/info?uid=1&t=1597372964477
    System.out.println(string);
  }

  private Mono<ServerResponse> badRequestResponse(
      String loadId,
      Integer appId,
      String placementId,
      String event,
      String platform,
      String thirdUrl,
      String timestamp,
      long sendCallbackTime,
      long finishCallbackTime,
      Map<String, Object> extraLogInfos,
      Map<String, String> extraLogFields) {
    String errorMsg =
        "mmp param error! load id:"
            + loadId
            + ", aid:"
            + appId
            + ", pid:"
            + placementId
            + ", event:"
            + event
            + ", platform:"
            + platform
            + ", thirdUrl:"
            + thirdUrl;
    LogUtil.localError(
        "errorMsg:" + errorMsg + ", info:{}", JsonSerializationUtils.objToJson(extraLogInfos));
    LogUtil.error(extraLogInfos, extraLogFields, new SigmobException(errorMsg));
    send636DataLog(
        loadId,
        appId,
        placementId,
        event,
        platform,
        thirdUrl,
        ErrorCode.Mmp.PARAM_ERROR.getCode(),
        errorMsg,
        timestamp,
        sendCallbackTime,
        finishCallbackTime);

    return OK_RESPONSE;
  }

  /** 发送636号点打点日志 */
  private void send636DataLog(
      String loadId,
      Integer appId,
      String placementId,
      String event,
      String platform,
      String mmpUrl,
      int callbackCode,
      String msg,
      String timestamp,
      long sendCallbackTime,
      long finishCallbackTime) {
    try {
      var messageBuilder =
          new Ac636MessageBuilder(
              loadId,
              appId,
              placementId,
              event,
              platform,
              mmpUrl,
              callbackCode,
              msg,
              timestamp,
              sendCallbackTime,
              finishCallbackTime);

      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 636 Datalog error!", e);
    }
  }
}
