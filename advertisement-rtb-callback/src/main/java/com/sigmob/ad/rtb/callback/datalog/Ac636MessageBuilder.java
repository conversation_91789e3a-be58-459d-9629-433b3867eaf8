package com.sigmob.ad.rtb.callback.datalog;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import com.sigmob.ad.core.datalog.AcType;
import com.sigmob.ad.core.datalog.DataLogField;
import com.sigmob.ad.core.datalog.DataLogMessageBuilder;
import org.apache.commons.lang3.StringUtils;

public class Ac636MessageBuilder extends DataLogMessageBuilder {

  private final String loadId;

  private final Integer appId;

  private final String placementId;

  private final String event;
  private final String platform;

  private final String appCallbackUrl;

  private final int callbackCode;
  private final String msg;

  private final long sendCallbackTime;
  private final String timestamp;

  private final long finishCallbackTime;

  private static final AcType AC_TYPE = AcType.SIX_THREE_SIX;

  public Ac636MessageBuilder(
      String loadId,
      Integer appId,
      String placementId,
      String event,
      String platform,
      String appCallbackUrl,
      int callbackCode,
      String msg,
      String timestamp,
      long sendCallbackTime,
      long finishCallbackTime) {
    this.loadId = loadId;
    this.appId = appId;
    this.placementId = placementId;
    this.event = event;
    this.platform = platform;
    this.appCallbackUrl = appCallbackUrl;
    this.callbackCode = callbackCode;
    this.msg = msg;
    this.timestamp = timestamp;
    this.sendCallbackTime = sendCallbackTime;
    this.finishCallbackTime = finishCallbackTime;
  }

  @Override
  protected Map<String, Object> buildMessageBody() {

    Map<String, Object> dataLog = Maps.newHashMapWithExpectedSize(100);

    dataLog.put(DataLogField.LOAD_ID, loadId);
    dataLog.put(DataLogField.APP_ID_NEW, null == appId ? StringUtils.EMPTY : appId.toString());
    dataLog.put(DataLogField.EVENT, event);
    dataLog.put(DataLogField.PLACEMENT_ID, placementId);
    dataLog.put(DataLogField.PLATFORM, platform);
    dataLog.put(
        DataLogField.TRACK_ING_URL,
        URLEncoder.encode(Strings.nullToEmpty(appCallbackUrl), StandardCharsets.UTF_8));
    dataLog.put(DataLogField.ERRORCODE, Integer.toString(callbackCode));
    dataLog.put(DataLogField.ERROR_MSG, msg);
    dataLog.put(DataLogField.TIMESTAMP, timestamp);
    dataLog.put(DataLogField.CALLBACK_TIMESTAMP, Long.toString(sendCallbackTime));
    dataLog.put(DataLogField.VALID_TIMESTAMP, Long.toString(finishCallbackTime));

    return dataLog;
  }

  @Override
  protected int getOsType() {
    return 0;
  }

  @Override
  protected int getAppId() {
    return appId;
  }

  @Override
  protected AcType getAcType() {
    return AC_TYPE;
  }

  @Override
  protected String getUserId() {
    return "";
  }
}
