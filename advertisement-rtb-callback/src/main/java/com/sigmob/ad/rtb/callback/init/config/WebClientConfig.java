package com.sigmob.ad.rtb.callback.init.config;

import io.netty.channel.ChannelOption;
import io.netty.channel.epoll.EpollChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.client.reactive.ReactorResourceFactory;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.DefaultUriBuilderFactory;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
@RequiredArgsConstructor
public class WebClientConfig {

  //  @NonNull private final MeterRegistry meterRegistry;

  private static final NioEventLoopGroup nioEventLoopGroup =
      new NioEventLoopGroup(
          Runtime.getRuntime().availableProcessors(),
          new ThreadPoolExecutor(
              Runtime.getRuntime().availableProcessors(),
              Runtime.getRuntime().availableProcessors(),
              0L,
              TimeUnit.SECONDS,
              new ArrayBlockingQueue<>(20000),
              new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                  Thread t = new Thread(r, "webclient-http-nio-" + counter.getAndIncrement());
                  return t;
                }
              },
              new ThreadPoolExecutor.DiscardPolicy()));

  @Bean
  public ReactorClientHttpConnector reactorClientHttpConnector() {
    ReactorResourceFactory resourceFactory = new ReactorResourceFactory();
    resourceFactory.setLoopResources(b -> nioEventLoopGroup);
    resourceFactory.setUseGlobalResources(false);
    resourceFactory.setConnectionProvider(
        ConnectionProvider.builder("rtbCallbackConnection")
            .maxConnections(2000)
            .maxIdleTime(Duration.ofSeconds(30L))
            .maxLifeTime(Duration.ofSeconds(60L))
            .pendingAcquireTimeout(Duration.ofMillis(200))
            .build());

    return new ReactorClientHttpConnector(
        resourceFactory,
        mapper ->
            //            mapper.tcpConfiguration(
            //                client ->
            //                    client
            //                        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000)
            //                        .doOnConnected(
            //                            conn ->
            //                                conn.addHandler(
            //                                        new ReadTimeoutHandler(
            //                                            2000, TimeUnit.MILLISECONDS)) // 读超时设置为2s
            //                                    .addHandler(
            //                                        new WriteTimeoutHandler(1000,
            // TimeUnit.MILLISECONDS))))
            HttpClient.create()
                //                        .protocol(HttpProtocol.H2)
                .secure()
                .compress(true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(EpollChannelOption.TCP_KEEPIDLE, 75)
                .option(EpollChannelOption.TCP_KEEPINTVL, 60)
                .option(EpollChannelOption.TCP_KEEPCNT, 8)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000)
                .doOnConnected(
                    conn ->
                        conn.addHandler(
                                new ReadTimeoutHandler(2000, TimeUnit.MILLISECONDS)) // 读超时设置为2s
                            .addHandler(new WriteTimeoutHandler(2000, TimeUnit.MILLISECONDS))));
  }

  @Bean
  public WebClient webClient(ReactorClientHttpConnector r) {
    return WebClient.builder()
        .clientConnector(r)
        .codecs(
            clientCodecConfigurer -> {
              clientCodecConfigurer.defaultCodecs().maxInMemorySize(1024 * 100); // 设置接收数据缓存最大为100KB
            })
        .build();
  }

  /** 无encode */
  @Bean
  public WebClient webClientNoEncode(ReactorClientHttpConnector r) {
    DefaultUriBuilderFactory factory = new DefaultUriBuilderFactory();
    //  并关闭自动编码
    factory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.NONE);

    return WebClient.builder()
        .uriBuilderFactory(factory)
        .clientConnector(r)
        .codecs(
            clientCodecConfigurer -> {
              clientCodecConfigurer.defaultCodecs().maxInMemorySize(1024 * 100); // 设置接收数据缓存最大为100KB
            })
        .build();
  }
}
