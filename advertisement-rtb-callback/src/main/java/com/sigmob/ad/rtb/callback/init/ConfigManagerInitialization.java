package com.sigmob.ad.rtb.callback.init;

import com.google.common.base.Strings;
import com.sigmob.ad.core.config.api.RtbApiConfig;
import com.sigmob.ad.core.datalog.dispatcher.DynamicConfiguration;
import com.sigmob.ad.core.util.LogUtil;
import com.twofishes.config.manager.ConfigManager;
import com.twofishes.config.util.ConfigConstant;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.util.ResourceUtils;

import java.io.FileNotFoundException;

/** 在所有bean初始化之前初始化并加载config配置 */
public class ConfigManagerInitialization implements ApplicationContextInitializer {

  /** 启动预加载需要的动态配置 */
  private void preLoadingConfig() {
    var rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
    DynamicConfiguration dcConfig = ConfigManager.get(DynamicConfiguration.class);
    LogUtil.localInfo("rtbApiConfig:{}, DynamicConfiguration:{}", rtbApiConfig, dcConfig);
  }

  @Override
  public void initialize(ConfigurableApplicationContext applicationContext) {
    LogUtil.localInfo("config manager initializing start......");
    String configProperties = System.getProperty(ConfigConstant.CONFIG_INFO_FILE_PATH_KEY);
    if (Strings.isNullOrEmpty(configProperties)) {
      try {
        String configManagerFilePath =
            applicationContext.getEnvironment().getProperty("config-manager.config");
        System.setProperty(
            ConfigConstant.CONFIG_INFO_FILE_PATH_KEY,
            ResourceUtils.getFile(configManagerFilePath).getAbsolutePath());
      } catch (FileNotFoundException e) {
        LogUtil.localError("configManager file not found!", e);
      }
    }

    LogUtil.localInfo("config manager appName:{}", ConfigManager.getAppName());
    preLoadingConfig();
    LogUtil.localInfo("config manager initializing end......");
  }
}
