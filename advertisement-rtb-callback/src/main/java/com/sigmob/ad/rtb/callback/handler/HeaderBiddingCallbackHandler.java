package com.sigmob.ad.rtb.callback.handler;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.WindmillBiddingConstants;
import com.sigmob.ad.core.constants.WindmillConstants;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.SettlementSetting;
import com.sigmob.ad.core.datalog.Ac534MessageBuilder;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.exception.RtbCallbackException;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.ad.core.security.crypto.AesUtils;
import com.sigmob.ad.core.security.crypto.TencentBidPriceCrypto;
import com.sigmob.ad.core.util.CommonBizUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.service.AdCacheService;
import com.sigmob.ad.rtb.callback.service.DspManagementService;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import com.sigmob.ad.service.AppService;
import com.sigmob.ssp.pb.dspmanagement.DspApiAppInfo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.sigmob.ssp.pb.dspmanagement.DspInfo;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * header bidding 回调处理
 *
 * <AUTHOR> @Date 2021/6/23 11:30 上午 @Version 1.0 @Description
 */
@Component
@RequiredArgsConstructor
public class HeaderBiddingCallbackHandler extends CallbackBaseHandler {

  @NonNull private final WebClient webClient;
  @NonNull private final DataLogService dataLogService;
  @NonNull private final AppService appService;
  @NonNull private final AdCacheService adCacheService;
  @NonNull private final DspManagementService dspManagementService;

  public Mono<ServerResponse> callback(ServerRequest request) {

    String result = request.pathVariable(RtbCallbackConstants.CALLBACK_RESULT);
    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String uri = httpRequest.getURI().toString();
    var rid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);
    var cid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName())
            .orElse(StringUtils.EMPTY);

    final String dspNotifyUrl =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName())
            .orElse(StringUtils.EMPTY);

    String encryptAuctionPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    String userId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.USER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var appId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adSlotId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var hbId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);

    var priceForSsp =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName())
            .orElse(StringUtils.EMPTY);

    var developerId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiFlowType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_TYPE.getName())
            .orElse(Integer.toString(RequestFlowType.SDK.getCode()));

    var settlementMode =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName())
            .orElse(Integer.toString(SettlementSetting.BIDDING.getCode()));

    var hbType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.HB_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var hbPlatform =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.HB_PLATFORM.getName())
            .orElse(StringUtils.EMPTY);

    var vid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.VID.getName())
            .orElse(StringUtils.EMPTY);

    var transAdType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var bidLossCode =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName())
            .orElse(StringUtils.EMPTY);

    var cur =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.CUR.getName())
            .orElse(StringUtils.EMPTY);

    var priceForSspOri =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP_ORI.getName())
            .orElse(StringUtils.EMPTY);

    var winAndId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.WIN_ADNID.getName())
            .orElse(StringUtils.EMPTY);

    var highestPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.HIGHEST_LOSS_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    var deviceUid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVICE_UID.getName())
            .orElse(StringUtils.EMPTY);
    var hasEncodedDeviceUid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.HAS_ENCODED_DEVICE_UID.getName())
            .orElse("0");
    var dspTaskId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TASK_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var currencyRate =
        request.queryParam(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName()).orElse("1");

    var osType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.OS_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var dspProtocolType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var adxMaxPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    var adxId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspSellType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var dspAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiPlacementId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(26);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(7);

    extraLoginFiled.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, result);
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName(), rid);
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName(), cid);
    extraLoginFiled.put(
        RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.HEADER_BIDDING.getName());
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName(), adSlotId);
    // uid 解密
    String uid = getBase64Value(deviceUid, "1".equals(hasEncodedDeviceUid));
    String taskId = getBase64Value(dspTaskId, true);
    extraLoginFiled.put("uid", uid);
    extraLoginFiled.put("adType", adType);
    extraLoginFiled.put("ftype", apiFlowType);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName(), dspNotifyUrl);
    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);
    extraLogInfos.put("userId", userId);
    extraLogInfos.put("taskId", taskId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName(), encryptAuctionPrice);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.APP_ID.getName(), appId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName(), hbId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName(), priceForSsp);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName(), developerId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName(), settlementMode);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.HB_TYPE.getName(), hbType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.HB_PLATFORM.getName(), hbPlatform);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.VID.getName(), vid);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName(), transAdType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.CUR.getName(), cur);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.WIN_ADNID.getName(), winAndId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.HIGHEST_LOSS_PRICE.getName(), highestPrice);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP_ORI.getName(), priceForSspOri);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName(), bidLossCode);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName(), currencyRate);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.OS_TYPE.getName(), osType);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName(), dspProtocolType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName(), adxMaxPrice);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_ID.getName(), adxId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName(), dspSellType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName(), dspAppId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName(), dspApiAppId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName(), dspApiPlacementId);

    if (rid.isBlank()) {
      String errorMsg = "hb(" + hbPlatform + ")  callback require requestId, uri:" + uri;
      RtbLogUtil.hbWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return OK_RESPONSE;
    }

    if (cid.isBlank()) {
      String errorMsg = "hb(" + hbPlatform + ")  callback require adSourceChannel, uri:" + uri;
      RtbLogUtil.hbWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return OK_RESPONSE;
    }

    if (appId.isBlank()) {
      String errorMsg = "hb(" + hbPlatform + ")  callback require appId, uri:" + uri;
      RtbLogUtil.hbWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return OK_RESPONSE;
    }

    if (!NumberUtils.isCreatable(appId)) {
      String errorMsg = "hb(" + hbPlatform + ")  callback appId(" + appId + ") invalid, uri:" + uri;
      RtbLogUtil.hbWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return OK_RESPONSE;
    }

    int dspId = Integer.parseInt(cid);
    return Mono.zip(
            appService.getAppInfo(Integer.parseInt(appId)), dspManagementService.getDspInfo(dspId))
        .flatMap(
            zip -> {
              var appInfoOptional = zip.getT1();
              Optional<DspInfo> dspInfoOptional = zip.getT2();
              if (appInfoOptional.isEmpty()) {
                String errorMsg =
                    "hb(" + hbPlatform + ")  callback appId(" + appId + ") invalid, uri:" + uri;
                RtbLogUtil.hbWarn(errorMsg);
                LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
                return OK_RESPONSE;
              }

              Integer hbTypeInt = NumberUtils.toInt(hbType);
              String errorMsg;
              //              String secret = appInfoOptional.get().getAppKey();
              String decryptPrice = encryptAuctionPrice;
              //              try {
              //                decryptPrice = Constants.AES_CRYPTO.decryptWithUrlSafe(secret,
              // encryptAuctionPrice);
              //              } catch (Exception ignored) {
              //
              //              }
              final String auctionPrice = decryptPrice;
              if (RtbCallbackConstants.CallbackResult.WIN.getName().equals(result)) {
                if (encryptAuctionPrice.isBlank()) {
                  errorMsg =
                      "hb(" + hbPlatform + ")  callback require encrypt auction price, uri:" + uri;
                  return badRequestResponse(
                      RtbCallbackConstants.CallbackResult.WIN.getCode(),
                      rid,
                      cid,
                      RequestFlowType.SDK.getName(),
                      userId,
                      appId,
                      adSlotId,
                      hbId,
                      appId,
                      adSlotId,
                      priceForSsp,
                      developerId,
                      extraLogInfos,
                      errorMsg,
                      uri,
                      hbTypeInt,
                      hbPlatform,
                      vid,
                      transAdType,
                      cur,
                      priceForSspOri,
                      bidLossCode,
                      winAndId,
                      highestPrice,
                      uid,
                      taskId,
                      adType,
                      currencyRate,
                      osType,
                      adxId,
                      dspSellType,
                      dspApiAppId,
                      dspApiPlacementId);
                }

                if (!NumberUtils.isCreatable(decryptPrice)
                    && !hbPlatform.equals(WindmillConstants.WINDMILL_PLATFORM)) {
                  errorMsg =
                      "hb("
                          + hbPlatform
                          + ") callback auction price invalid, decryptPrice:"
                          + decryptPrice;
                  RtbLogUtil.hbWarn(errorMsg);
                  LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
                  return badRequestResponse(
                      RtbCallbackConstants.CallbackResult.WIN.getCode(),
                      rid,
                      cid,
                      RequestFlowType.SDK.getName(),
                      userId,
                      appId,
                      adSlotId,
                      hbId,
                      appId,
                      adSlotId,
                      priceForSsp,
                      developerId,
                      extraLogInfos,
                      errorMsg,
                      uri,
                      hbTypeInt,
                      hbPlatform,
                      vid,
                      transAdType,
                      cur,
                      priceForSspOri,
                      bidLossCode,
                      winAndId,
                      highestPrice,
                      uid,
                      taskId,
                      adType,
                      currencyRate,
                      osType,
                      adxId,
                      dspSellType,
                      dspApiAppId,
                      dspApiPlacementId);
                }

                if (!dspNotifyUrl.isBlank()) {

                  String[] dspNotifyUrls =
                      dspNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);
                  List<Mono<Boolean>> notifyUrlMono =
                      Lists.newArrayListWithCapacity(dspNotifyUrls.length);
                  for (String notifyUrl : dspNotifyUrls) {

                    URI callbackUri;
                    try {
                      callbackUri =
                          UriComponentsBuilder.fromHttpUrl(notifyUrl).build(false).toUri();
                    } catch (Exception e) {
                      RtbLogUtil.hbError(
                          "{} parse callback url:{} error:" + e.getMessage(), rid, notifyUrl);
                      try {
                        notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
                        callbackUri =
                            UriComponentsBuilder.fromHttpUrl(notifyUrl).build(true).toUri();
                      } catch (Exception ie) {
                        RtbLogUtil.hbError(
                            "urlDecode error! url:{}, exception:{}", notifyUrl, ie.getMessage());
                        notifyUrlMono.add(Mono.just(Boolean.FALSE));
                        continue;
                      }
                    }
                    final String requestUrl = notifyUrl;

                    notifyUrlMono.add(
                        webClient
                            .get()
                            .uri(callbackUri)
                            .acceptCharset(StandardCharsets.UTF_8)
                            .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                            .doOnError(
                                e ->
                                    RtbLogUtil.hbError("request({}) {} error!", rid, requestUrl, e))
                            .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                            .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
                  }

                  return Mono.zip(
                          notifyUrlMono,
                          results -> {
                            boolean finalResult = Boolean.TRUE;
                            int errorNotifyUrlIdx = -1;
                            for (int i = 0; i < results.length; i++) {
                              Boolean notifyResult = (Boolean) results[i];
                              if (Boolean.FALSE.equals(notifyResult)) {
                                finalResult = Boolean.FALSE;
                                errorNotifyUrlIdx = i;
                                break;
                              }
                            }
                            if (finalResult) {
                              LogUtil.info(extraLogInfos, extraLoginFiled);
                            } else {
                              LogUtil.error(
                                  extraLogInfos,
                                  extraLoginFiled,
                                  new Exception(
                                      "send No." + errorNotifyUrlIdx + " notify url error"));
                            }
                            sendThirtyFourDatalog(
                                rid,
                                cid,
                                auctionPrice,
                                RtbCallbackConstants.CallbackResult.WIN.getCode(),
                                userId,
                                RequestFlowType.SDK.getName(),
                                appId,
                                adSlotId,
                                hbId,
                                appId,
                                adSlotId,
                                priceForSsp,
                                developerId,
                                hbTypeInt,
                                hbPlatform,
                                vid,
                                transAdType,
                                cur,
                                priceForSspOri,
                                bidLossCode,
                                winAndId,
                                highestPrice,
                                uid,
                                taskId,
                                adType,
                                currencyRate,
                                osType,
                                adxId,
                                dspSellType,
                                dspApiAppId,
                                dspApiPlacementId);
                            if (finalResult) {
                              return OK_RESPONSE;
                            } else {
                              return INTERNAL_SERVER_ERROR_RESPONSE;
                            }
                          })
                      .flatMap(r -> r);
                }
                LogUtil.info(extraLogInfos, extraLoginFiled);

                sendThirtyFourDatalog(
                    rid,
                    cid,
                    auctionPrice,
                    RtbCallbackConstants.CallbackResult.WIN.getCode(),
                    userId,
                    RequestFlowType.SDK.getName(),
                    appId,
                    adSlotId,
                    hbId,
                    appId,
                    adSlotId,
                    priceForSsp,
                    developerId,
                    hbTypeInt,
                    hbPlatform,
                    vid,
                    transAdType,
                    cur,
                    priceForSspOri,
                    bidLossCode,
                    winAndId,
                    highestPrice,
                    uid,
                    taskId,
                    adType,
                    currencyRate,
                    osType,
                    adxId,
                    dspSellType,
                    dspApiAppId,
                    dspApiPlacementId);
                return OK_RESPONSE;
              } else if (RtbCallbackConstants.CallbackResult.LOSE.getName().equals(result)) {
                int adDspProtocolType = NumberUtils.toInt(dspProtocolType);
                Mono<Optional<DspApiAppInfo>> dspAppMono = Mono.just(Optional.empty());
                if (!Strings.isNullOrEmpty(dspAppId)
                    && NumberUtils.isDigits(dspAppId)
                    && !("0").equals(dspAppId)
                    && adDspProtocolType == DspProtocolType.OPPO_RTB.getType()) {
                  var dspAppIdInt = Integer.parseInt(dspAppId);
                  dspAppMono = dspManagementService.getDspApiAppInfo(dspId, dspAppIdInt);
                }

                return dspAppMono.flatMap(
                    dspApiAppInfoOptional -> {
                      String replacePrice = auctionPrice;
                      List<Mono<Boolean>> notifyUrlMono = Lists.newLinkedList();
                      if (!dspNotifyUrl.isBlank()) {
                        if (NumberUtils.isCreatable(adxMaxPrice)) {
                          if (NumberUtils.isCreatable(replacePrice)) {
                            if (Double.parseDouble(adxMaxPrice)
                                > Double.parseDouble(replacePrice)) {
                              replacePrice = adxMaxPrice;
                            }
                          } else {
                            replacePrice = adxMaxPrice;
                          }
                        }

                        String[] dspNotifyUrls =
                            dspNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);

                        for (String notifyUrl : dspNotifyUrls) {
                          if (notifyUrl.contains("#")) {
                            notifyUrl = notifyUrl.replace("#", "%23");
                          }

                          if (adDspProtocolType == DspProtocolType.KUAI_SHOU.getType()) {
                            if (!Strings.isNullOrEmpty(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.KUAISHOU_MACRO_LOSE_NOTICE_PRICE, replacePrice);
                            }
                          } else if (adDspProtocolType == DspProtocolType.HUICHUAN.getType()) {
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.HUICHUAN_MACRO_PRICE, replacePrice);
                            }
                          } else if (adDspProtocolType == DspProtocolType.STANDARD.getType()
                              && CommonBizUtils.isHuaweiStandardDsp(dspId)
                              && dspInfoOptional.isPresent()) {
                            if (NumberUtils.isCreatable(replacePrice)) {
                              String encryptGcmAppendIvAndBase64 = replacePrice;
                              // 华为标准DSP 通知中的价格
                              DspInfo dspInfo = dspInfoOptional.get();
                              if (dspInfo.getWinNoticeEncrypt()) {
                                byte[] iv = new byte[12];
                                new SecureRandom().nextBytes(iv);
                                encryptGcmAppendIvAndBase64 =
                                    AesUtils.encryptGcmAppendIvAndBase64(
                                        dspInfo.getSecretKey(),
                                        replacePrice.getBytes(StandardCharsets.UTF_8),
                                        iv);
                              }
                              notifyUrl =
                                  notifyUrl.replace(
                                      WindmillBiddingConstants.MACRO_AUCTION_PRICE,
                                      encryptGcmAppendIvAndBase64);
                            }
                          } else if (adDspProtocolType == DspProtocolType.TENCENT_GDT.getType()) {
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.TENCENT_MEDIA_MACRO_PRICE,
                                      URLEncoder.encode(
                                          Strings.nullToEmpty(
                                              TencentBidPriceCrypto.encrypt(
                                                  TencentBidPriceCrypto.PUBLIC_KEY, replacePrice)),
                                          StandardCharsets.UTF_8));
                            }
                          } else if (adDspProtocolType
                              == DspProtocolType.BAIDU_MOBADS_PB.getType()) {
                            if (NumberUtils.isCreatable(bidLossCode)) {
                              switch (bidLossCode) {
                                case "2" -> {
                                  notifyUrl =
                                      notifyUrl.replace(
                                          RtbConstants.MACRO_BAIDU_REPLACE_REASON,
                                          RtbConstants.BaiduReason.BIDDING_PRICE_FAILED.getCode());
                                  if (NumberUtils.isCreatable(replacePrice)) {
                                    notifyUrl =
                                        notifyUrl.replace(
                                            RtbConstants.MACRO_BAIDU_REPLACE_PRICE, replacePrice);
                                  }
                                }
                                case "2000" ->
                                    notifyUrl =
                                        notifyUrl.replace(
                                            RtbConstants.MACRO_BAIDU_REPLACE_REASON,
                                            RtbConstants.BaiduReason.TIME_OUT.getCode());
                                case "2001" ->
                                    notifyUrl =
                                        notifyUrl.replace(
                                            RtbConstants.MACRO_BAIDU_REPLACE_REASON,
                                            RtbConstants.BaiduReason.APP_FILTER_MATERIAL_PROBLEM
                                                .getCode());
                                default ->
                                    notifyUrl =
                                        notifyUrl.replace(
                                            RtbConstants.MACRO_BAIDU_REPLACE_REASON,
                                            RtbConstants.BaiduReason.OTHERS.getCode());
                              }
                            } else {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.MACRO_BAIDU_REPLACE_REASON,
                                      RtbConstants.BaiduReason.BIDDING_PRICE_FAILED.getCode());
                              if (NumberUtils.isCreatable(replacePrice)) {
                                notifyUrl =
                                    notifyUrl.replace(
                                        RtbConstants.MACRO_BAIDU_REPLACE_PRICE, replacePrice);
                              }
                            }
                          } else if (adDspProtocolType == DspProtocolType.OPPO_RTB.getType()) {
                            boolean replacedNotifyUrl = false;
                            if (NumberUtils.isCreatable(replacePrice)) {
                              if (dspApiAppInfoOptional.isPresent()) {
                                var dspApiAppInfo = dspApiAppInfoOptional.get();
                                if (!dspApiAppInfo.getAppKey().isBlank()) {
                                  var appKey =
                                      Base64.getDecoder().decode(dspApiAppInfo.getAppKey());
                                  notifyUrl =
                                      notifyUrl.replace(
                                          RtbConstants.OPPO_MEDIA_MACRO_PRICE,
                                          URLEncoder.encode(
                                              Strings.nullToEmpty(
                                                  AesUtils.encrypt(appKey, replacePrice)),
                                              StandardCharsets.UTF_8));
                                  replacedNotifyUrl = true;
                                }
                              }
                              if (!replacedNotifyUrl) {
                                notifyUrl =
                                    notifyUrl.replace(
                                        RtbConstants.OPPO_MEDIA_MACRO_PRICE, replacePrice);
                              }
                            }
                          } else if (adDspProtocolType == DspProtocolType.HUAWEI.getType()) {
                            notifyUrl =
                                notifyUrl.replace(
                                    RtbConstants.HUAWEI_MEDIA_MACRO_AUCTION_LOSS, "102");
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.HUAWEI_MEDIA_MACRO_PRICE,
                                      new BigDecimal(replacePrice)
                                          .divide(new BigDecimal(100), 2, RoundingMode.FLOOR)
                                          .toString());
                            }
                          }
                          final String requestUrl = notifyUrl;

                          URI callbackUri;
                          try {
                            callbackUri =
                                UriComponentsBuilder.fromHttpUrl(notifyUrl).build(false).toUri();
                          } catch (Exception e) {
                            RtbLogUtil.hbError(
                                "{} parse callback url:{} error!", rid, notifyUrl, e);
                            try {
                              notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
                              callbackUri =
                                  UriComponentsBuilder.fromHttpUrl(notifyUrl).build(true).toUri();
                            } catch (Exception ie) {
                              RtbLogUtil.hbError(
                                  "urlDecode error! url:{}, exception:{}",
                                  notifyUrl,
                                  ie.getMessage());
                              notifyUrlMono.add(Mono.just(Boolean.FALSE));
                              continue;
                            }
                          }

                          notifyUrlMono.add(
                              webClient
                                  .get()
                                  .uri(callbackUri)
                                  .acceptCharset(StandardCharsets.UTF_8)
                                  .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                                  .doOnError(
                                      e ->
                                          RtbLogUtil.hbError(
                                              "request({}) {} error!", rid, requestUrl, e))
                                  .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                                  .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
                        }
                      }
                      notifyUrlMono.add(adCacheService.expireAdCache(rid, 10));
                      return Mono.zip(
                              notifyUrlMono,
                              results -> {
                                boolean finalResult = Boolean.TRUE;
                                int errorNotifyUrlIdx = -1;
                                for (int i = 0; i < results.length; i++) {
                                  Boolean notifyResult = (Boolean) results[i];
                                  if (Boolean.FALSE.equals(notifyResult)
                                      && i != results.length - 1) {
                                    finalResult = Boolean.FALSE;
                                    errorNotifyUrlIdx = i;
                                    break;
                                  }
                                }
                                if (finalResult) {
                                  LogUtil.info(extraLogInfos, extraLoginFiled);
                                } else {
                                  LogUtil.error(
                                      extraLogInfos,
                                      extraLoginFiled,
                                      new Exception(
                                          "send No." + errorNotifyUrlIdx + " notify url error"));
                                }
                                sendThirtyFourDatalog(
                                    rid,
                                    cid,
                                    auctionPrice,
                                    RtbCallbackConstants.CallbackResult.LOSE.getCode(),
                                    userId,
                                    RequestFlowType.SDK.getName(),
                                    appId,
                                    adSlotId,
                                    hbId,
                                    appId,
                                    adSlotId,
                                    priceForSsp,
                                    developerId,
                                    hbTypeInt,
                                    hbPlatform,
                                    vid,
                                    transAdType,
                                    cur,
                                    priceForSspOri,
                                    bidLossCode,
                                    winAndId,
                                    highestPrice,
                                    uid,
                                    taskId,
                                    adType,
                                    currencyRate,
                                    osType,
                                    adxId,
                                    dspSellType,
                                    dspApiAppId,
                                    dspApiPlacementId);
                                if (finalResult) {
                                  return OK_RESPONSE;
                                } else {
                                  return INTERNAL_SERVER_ERROR_RESPONSE;
                                }
                              })
                          .flatMap(r -> r);
                    });

              } else {
                errorMsg = "bad hb(" + hbPlatform + ") callback request, uri:" + uri;
                RtbLogUtil.hbWarn(errorMsg);
                LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
                return OK_RESPONSE;
              }
            })
        .onErrorResume(
            e -> {
              String errorMsg = "bad hb(" + hbPlatform + ") callback request, uri:" + uri;
              RtbLogUtil.hbError(errorMsg, e);
              LogUtil.error(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
              return INTERNAL_SERVER_ERROR_RESPONSE;
            });
  }

  protected Mono<ServerResponse> badRequestResponse(
      int callbackStatus,
      String requestId,
      String adSourceChannel,
      String adxApi,
      String userId,
      String appId,
      String adSlotId,
      String apiRequestId,
      String apiAppId,
      String apiAdSlotId,
      String priceForSsp,
      String developerId,
      Map<String, Object> extraLogInfos,
      String errorMsg,
      String uri,
      Integer hbType,
      String hbPlatform,
      String impId,
      String transAdType,
      String cur,
      String priceForSspOri,
      String bidLossCode,
      String winAdnId,
      String highestPrice,
      String uid,
      String taskId,
      String adType,
      String currencyRate,
      String osType,
      String adxId,
      String dspSellType,
      String dspApiAppId,
      String dspApiPlacementId) {
    LogUtil.localWarn(errorMsg, uri);
    LogUtil.warn(extraLogInfos, null, new RtbCallbackException(errorMsg));
    sendThirtyFourDatalog(
        requestId,
        adSourceChannel,
        StringUtils.EMPTY,
        callbackStatus,
        userId,
        adxApi,
        appId,
        adSlotId,
        apiRequestId,
        apiAppId,
        apiAdSlotId,
        priceForSsp,
        developerId,
        hbType,
        hbPlatform,
        impId,
        transAdType,
        cur,
        priceForSspOri,
        bidLossCode,
        winAdnId,
        highestPrice,
        uid,
        taskId,
        adType,
        currencyRate,
        osType,
        adxId,
        dspSellType,
        dspApiAppId,
        dspApiPlacementId);
    return OK_RESPONSE;
  }

  protected final void sendThirtyFourDatalog(
      String requestId,
      String adSourceChannel,
      String auctionPrice,
      int callbackStatus,
      String userId,
      String adxApi,
      String appId,
      String adSlotId,
      String hbId,
      String apiAppId,
      String apiAdSlotId,
      String priceForSsp,
      String deveLoperId,
      Integer hbType,
      String hbPlatform,
      String impId,
      String transAdType,
      String cur,
      String priceForSspOri,
      String bidLossCode,
      String winAdnId,
      String highestPrice,
      String uid,
      String taskId,
      String adType,
      String currencyRate,
      String osType,
      String adxId,
      String dspSellType,
      String dspApiAppId,
      String dspApiPlacementId) {
    try {
      Ac534MessageBuilder messageBuilder =
          new Ac534MessageBuilder(
              requestId,
              adSourceChannel,
              auctionPrice,
              callbackStatus,
              userId,
              adxApi,
              appId,
              adSlotId,
              hbId,
              apiAppId,
              apiAdSlotId,
              priceForSsp,
              deveLoperId,
              hbType,
              hbPlatform,
              impId,
              transAdType,
              cur,
              priceForSspOri,
              bidLossCode,
              winAdnId,
              highestPrice,
              uid,
              taskId,
              0,
              adType,
              currencyRate,
              osType,
              adxId,
              dspSellType,
              dspApiAppId,
              dspApiPlacementId);
      var message = messageBuilder.build();
      if (null == message) {
        RtbLogUtil.hbError(
            "send 534th Datalog for request:" + requestId + " error! message is null");
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      RtbLogUtil.hbError("send 534th Datalog for request:" + requestId + " error!", e);
    }
  }
}
