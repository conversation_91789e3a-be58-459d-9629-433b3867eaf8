package com.sigmob.ad.rtb.callback.handler;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.callback.pb.CallbackData;
import com.sigmob.ad.core.callback.pb.SaasAdxPointData;
import com.sigmob.ad.core.config.api.RtbApiConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.WindmillBiddingConstants;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.SettlementMode;
import com.sigmob.ad.core.constants.thirdparty.rtb.StandardApiConstants;
import com.sigmob.ad.core.datalog.Ac534MessageBuilder;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.datalog.model.AcThirtyFourParam;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.exception.RtbCallbackException;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.ad.core.rtb.api.yoyo.PriceUtil;
import com.sigmob.ad.core.rtb.utils.VivoPriceCrypto;
import com.sigmob.ad.core.security.crypto.*;
import com.sigmob.ad.core.util.CommonBizUtils;
import com.sigmob.ad.core.util.DeflateCompressorUtil;
import com.sigmob.ad.core.util.LinkUtil;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.core.util.ProtobufFormatUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.service.AdCacheService;
import com.sigmob.ad.rtb.callback.service.DspManagementService;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import com.sigmob.ad.service.AppService;
import com.sigmob.ad.service.RtbCommonService;
import com.sigmob.ssp.pb.dspmanagement.DspApiAppInfo;
import com.sigmob.ssp.pb.dspmanagement.DspInfo;
import com.twofishes.config.manager.ConfigManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.SignatureException;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@Component
@RequiredArgsConstructor
public class RtbCallbackHandler extends CallbackBaseHandler {

  private static final HuaweiCrypto huaweiCrypto = new HuaweiCrypto();
  private static final CryptoByAesAndBase64 cryptoByAesAndBase64 = new CryptoByAesAndBase64();
  @NonNull private final WebClient webClient;
  @NonNull private final WebClient webClientNoEncode;
  @NonNull private final AppService appService;
  @NonNull private final RtbCommonService rtbCommonService;
  @NonNull private final DataLogService dataLogService;
  @NonNull private final AdCacheService adCacheService;
  @NonNull private final DspManagementService dspManagementService;

  private static final Map<String, String> HUAWAI_TO_YYB_ERRORCODE_MAP =
      Maps.newHashMapWithExpectedSize(16);

  static {
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("102", "2001");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("500099", "3001");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("209", "3002");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("5118", "3002");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("2006", "2003");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("5077", "2003");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("5136", "2003");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("510001", "2003");
    HUAWAI_TO_YYB_ERRORCODE_MAP.put("6000", "2004");
  }

  /** oppo、huawei...api竞价通知 */
  public Mono<ServerResponse> callback(ServerRequest request) {

    String result = request.pathVariable(RtbCallbackConstants.CALLBACK_RESULT);
    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String uri = httpRequest.getURI().toString();
    var rid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);
    var cid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName())
            .orElse(StringUtils.EMPTY);

    var dspNotifyUrl =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName())
            .orElse(StringUtils.EMPTY);

    String encryptAuctionPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    String userId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.USER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var appId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adSlotId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiRequestId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiAdSlotId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var priceForSsp =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName())
            .orElse(StringUtils.EMPTY);

    var developerId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiFlowType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_TYPE.getName())
            .orElse(Integer.toString(0));

    var settlementMode =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName())
            .orElse(Integer.toString(SettlementMode.BIDDING.getCode()));

    var vid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.VID.getName())
            .orElse(StringUtils.EMPTY);

    var transAdType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    String sspErrorCode =
        request
            .queryParam("alc")
            .orElse(
                request
                    .queryParam(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName())
                    .orElse(StringUtils.EMPTY));

    var uid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVICE_UID.getName())
            .orElse(StringUtils.EMPTY);
    var hasEncodedDeviceUid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.HAS_ENCODED_DEVICE_UID.getName())
            .orElse("0");

    var taskId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TASK_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var currencyRate =
        request.queryParam(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName()).orElse("1");

    var osType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.OS_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var creativeId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_CREATIVE_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspProtocolType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var adxMaxPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    var adxId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspSellType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var dspAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiPlacementId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var callbackData =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.CALLBACK_DATA.getName())
            .orElse(StringUtils.EMPTY);

    var saasAdxPointDataString =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SAAS_ADX_POINT_DATA.getName())
            .orElse(StringUtils.EMPTY);

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(26);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(8);

    extraLoginFiled.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, result);
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName(), rid);
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName(), cid);
    extraLoginFiled.put(
        RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.RTB_CALLBACK.getName());
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName(), adSlotId);
    // uid 解密
    uid = getBase64Value(uid, "1".equals(hasEncodedDeviceUid));
    taskId = getBase64Value(taskId, true);
    extraLoginFiled.put("uid", uid);
    extraLoginFiled.put("adType", adType);
    extraLoginFiled.put("ftype", apiFlowType);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName(), dspNotifyUrl);
    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);
    extraLogInfos.put("userId", userId);
    extraLogInfos.put("taskId", taskId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName(), encryptAuctionPrice);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.APP_ID.getName(), appId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName(), apiRequestId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_APP_ID.getName(), apiAppId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_PLACEMENT_ID.getName(), apiAdSlotId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName(), priceForSsp);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName(), developerId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName(), settlementMode);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.VID.getName(), vid);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName(), transAdType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName(), sspErrorCode);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName(), currencyRate);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.OS_TYPE.getName(), osType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.AD_CREATIVE_ID.getName(), creativeId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName(), dspProtocolType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName(), adxMaxPrice);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_ID.getName(), adxId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName(), dspSellType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName(), dspAppId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName(), dspApiAppId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName(), dspApiPlacementId);
    CallbackData callbackDataPb = null;
    int interactionType = 0;
    if (!callbackData.isBlank()) {
      try {
        byte[] decodeBase64 = org.apache.commons.codec.binary.Base64.decodeBase64(callbackData);
        byte[] deCompress = DeflateCompressorUtil.deCompress(decodeBase64);

        callbackDataPb = CallbackData.newBuilder().mergeFrom(deCompress).build();
        extraLogInfos.put("cd", ProtobufFormatUtil.toJsonString(callbackDataPb));

        interactionType = callbackDataPb.getMediaInteractionType();

      } catch (Exception ignore) {
      }
    }

    SaasAdxPointData saasAdxPointData = null;
    if (!saasAdxPointDataString.isBlank()) {
      try {
        byte[] decodeBase64 =
            org.apache.commons.codec.binary.Base64.decodeBase64(saasAdxPointDataString);
        byte[] deCompress = DeflateCompressorUtil.deCompress(decodeBase64);
        saasAdxPointData = SaasAdxPointData.newBuilder().mergeFrom(deCompress).build();
      } catch (Exception ignore) {
      }
    }

    String adxName =
        RequestFlowType.getApiName(
            NumberUtils.isCreatable(apiFlowType) ? Integer.parseInt(apiFlowType) : 0,
            NumberUtils.isCreatable(developerId) ? Integer.parseInt(developerId) : 0);
    String errorMsg;

    if (rid.isBlank()) {
      errorMsg = "rtb(" + adxName + ") callback require requestId, uri:" + uri;
      LogUtil.localWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }
    if (cid.isBlank()) {
      errorMsg = "rtb(" + adxName + ") callback require adSourceChannel, uri:" + uri;
      LogUtil.localWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }
    int adDspProtocolType = NumberUtils.toInt(dspProtocolType);
    boolean isYyb = adDspProtocolType == DspProtocolType.YYB.getType();
    int settlementModeCode = Integer.parseInt(settlementMode);

    var loseAcThirtyFourParam =
        new AcThirtyFourParam(
            rid,
            cid,
            encryptAuctionPrice,
            RtbCallbackConstants.CallbackResult.LOSE.getCode(),
            userId,
            adxName,
            appId,
            adSlotId,
            apiRequestId,
            apiAppId,
            apiAdSlotId,
            priceForSsp,
            developerId,
            null,
            null,
            vid,
            transAdType,
            sspErrorCode,
            uid,
            taskId,
            interactionType,
            adType,
            currencyRate,
            osType,
            creativeId,
            adxId,
            dspSellType,
            dspApiAppId,
            dspApiPlacementId,
            saasAdxPointData);

    RtbApiConfig rtbApiConfig = ConfigManager.get(RtbApiConfig.class);
    if (null == rtbApiConfig
        || CollectionUtils.isEmpty(rtbApiConfig.getConfigs())
        || null == rtbApiConfig.getConfigs().get(adxName)) {
      errorMsg = "rtb(" + adxName + ") callback config cannot be null, uri:" + uri;
      LogUtil.localWarn(errorMsg);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return INTERNAL_SERVER_ERROR_RESPONSE;
    }
    var apiConfig = rtbApiConfig.getConfigs().get(adxName);

    if (RtbCallbackConstants.CallbackResult.WIN.getName().equals(result)) {

      if (encryptAuctionPrice.isBlank() && settlementModeCode == SettlementMode.BIDDING.getCode()) {
        errorMsg = "rtb(" + adxName + ") callback require encrypt auction price, uri:" + uri;
        return badRequestResponse(loseAcThirtyFourParam, extraLogInfos, errorMsg, uri);
      }

      String secret = apiConfig.getSecret();

      String decryptPrice = null;
      if (settlementModeCode == SettlementMode.BIDDING.getCode()) {

        if (RequestFlowType.OPPO.getName().equals(adxName)) {
          decryptPrice = Constants.AES_CRYPTO.decrypt(secret.substring(0, 16), encryptAuctionPrice);
        } else if (RequestFlowType.ADX_DATA.getName().equals(adxName)
            || RequestFlowType.IREADER.getName().equals(adxName)
            || RequestFlowType.DOUGUO.getName().equals(adxName)
            || RequestFlowType.DONGMAN.getName().equals(adxName)) {
          try {
            decryptPrice = Constants.AES_CRYPTO.decryptWithUrlSafe(secret, encryptAuctionPrice);
          } catch (Exception e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.MO_WEATHER.getName().equals(adxName)
            || RequestFlowType.YDZX.getName().equals(adxName)
            || RequestFlowType.VUNGLE.getName().equals(adxName)
            || RequestFlowType.DONGQIUDI.getName().equals(adxName)) {
          decryptPrice = encryptAuctionPrice;
        } else if (RequestFlowType.VIVO.getName().equals(adxName)) {
          try {
            decryptPrice =
                Double.toString(
                    VivoPriceCrypto.urlSafeDecode(
                        encryptAuctionPrice,
                        RtbConstants.API_VIVO_ENCRYPTION_KEY,
                        RtbConstants.API_VIVO_INTEGRITY_KEY));
          } catch (Exception e) {
            throw new RuntimeException(e);
          }
        } else if (RequestFlowType.KINGSOFT.getName().equals(adxName)
            || RequestFlowType.MAIMAI.getName().equals(adxName)
            || RequestFlowType.GROMORE.getName().equals(adxName)) {
          try {
            if (RequestFlowType.KINGSOFT.getName().equals(adxName)) {
              decryptPrice =
                  Double.toString(
                      RtbConstants.API_KINGSOFT_PRICE.decodePriceValue(encryptAuctionPrice));
            } else if (RequestFlowType.MAIMAI.getName().equals(adxName)) {
              decryptPrice =
                  Double.toString(
                      RtbConstants.API_MAIMAI_PRICE.decodePriceMacros(encryptAuctionPrice));
            } else if (RequestFlowType.GROMORE.getName().equals(adxName)) {
              decryptPrice =
                  Double.toString(
                      RtbConstants.API_GROMORE_PRICE.decodePriceMacros(encryptAuctionPrice));
            }
          } catch (SignatureException e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.MGTV.getName().equals(adxName)) {
          try {
            String decryptData =
                Constants.AES_CRYPTO.decryptWithUrlSafe(secret, encryptAuctionPrice);
            if (!Strings.isNullOrEmpty(decryptData)) {
              decryptPrice = decryptData.split("\\|")[0];
            }
          } catch (Exception e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.HUAWEI.getName().equals(adxName)) {
          try {
            decryptPrice = huaweiCrypto.decrypt(secret, encryptAuctionPrice);
          } catch (Exception e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.XIMALAYA.getName().equals(adxName)) {
          try {
            decryptPrice =
                Constants.AES_CRYPTO.decryptNoPaddingWithUrlSafe(secret, encryptAuctionPrice);
            if (!Strings.isNullOrEmpty(decryptPrice)) {
              decryptPrice = decryptPrice.trim();
            }
          } catch (Exception e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.HW_SEAD.getName().equals(adxName)
            || RequestFlowType.YUEYOU.getName().equals(adxName)) {
          try {
            decryptPrice = cryptoByAesAndBase64.decrypt(secret, encryptAuctionPrice);
            if (!Strings.isNullOrEmpty(decryptPrice)) {
              decryptPrice = decryptPrice.trim();
            }
          } catch (Exception e) {
            LogUtil.localError(
                "rtb("
                    + adxName
                    + ") callback encryptPrice("
                    + encryptAuctionPrice
                    + ") decrypt error");
            decryptPrice = encryptAuctionPrice;
          }
        } else if (RequestFlowType.YOYO.getName().equals(adxName)) {
          try {
            decryptPrice =
                PriceUtil.decode(
                        encryptAuctionPrice,
                        RtbConstants.API_YOYO_ENCRYPTION_KEY,
                        RtbConstants.API_YOYO_INTEGRITY_KEY)
                    .trim();
          } catch (Exception e) {
            throw new RuntimeException(e);
          }
        }
      }
      String auctionPrice = decryptPrice;
      var winAcThirtyFourParam =
          new AcThirtyFourParam(
              rid,
              cid,
              auctionPrice,
              RtbCallbackConstants.CallbackResult.WIN.getCode(),
              userId,
              adxName,
              appId,
              adSlotId,
              apiRequestId,
              apiAppId,
              apiAdSlotId,
              priceForSsp,
              developerId,
              null,
              null,
              vid,
              transAdType,
              sspErrorCode,
              uid,
              taskId,
              interactionType,
              adType,
              currencyRate,
              osType,
              creativeId,
              adxId,
              dspSellType,
              dspApiAppId,
              dspApiPlacementId,
              saasAdxPointData);
      if (!dspNotifyUrl.isBlank()) {
        if (!LinkUtil.isValidHttpUrl(dspNotifyUrl)) {
          dspNotifyUrl = URLDecoder.decode(dspNotifyUrl, StandardCharsets.UTF_8);
        }
        String[] dspNotifyUrls = dspNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);
        List<Mono<Boolean>> notifyUrlMono = Lists.newArrayListWithCapacity(dspNotifyUrls.length);
        for (String notifyUrl : dspNotifyUrls) {
          if (!isYyb && !notifyUrl.contains("monitor.ads.8le8le.com/rgWin")) {
            try {
              notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
            } catch (Exception e) {
              LogUtil.localError(
                  "urlDecode error! url:{}, exception:{}", notifyUrl, e.getMessage());
            }
          }
          final String requestUrl = notifyUrl;

          // yyb单独一套逻辑
          if (isYyb) {
            String callbackUri;
            try {
              callbackUri = UriComponentsBuilder.fromHttpUrl(notifyUrl).build(false).toUriString();
            } catch (Exception e) {
              LogUtil.localError(
                  "yyb {} parse callback url:{} error:" + e.getMessage(), rid, notifyUrl);
              LogUtil.error(extraLogInfos, extraLoginFiled, e);
              notifyUrlMono.add(Mono.just(Boolean.FALSE));
              continue;
            }
            Mono<Boolean> booleanMono =
                getResponseCode(callbackUri, extraLogInfos, extraLoginFiled, rid, requestUrl);
            notifyUrlMono.add(booleanMono);
          } else {

            URI callbackUri = null;
            try {
              callbackUri = UriComponentsBuilder.fromHttpUrl(notifyUrl).build().toUri();
            } catch (Exception e) {
              LogUtil.localError(
                  "{} parse callback url:{} error:" + e.getMessage(), rid, notifyUrl);
              notifyUrlMono.add(Mono.just(Boolean.FALSE));
              continue;
            }
            notifyUrlMono.add(
                webClient
                    .get()
                    .uri(callbackUri)
                    .acceptCharset(StandardCharsets.UTF_8)
                    .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                    .doOnError(e -> LogUtil.localError("request({}) {} error!", rid, requestUrl, e))
                    .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                    .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
          }
        }

        return Mono.zip(
                notifyUrlMono,
                results -> {
                  boolean finalResult = Boolean.TRUE;
                  int errorNotifyUrlIdx = -1;
                  for (int i = 0; i < results.length; i++) {
                    Boolean notifyResult = (Boolean) results[i];
                    if (Boolean.FALSE.equals(notifyResult)) {
                      finalResult = Boolean.FALSE;
                      errorNotifyUrlIdx = i;
                      break;
                    }
                  }
                  if (finalResult) {
                    LogUtil.info(extraLogInfos, extraLoginFiled);
                  } else {
                    LogUtil.error(
                        extraLogInfos,
                        extraLoginFiled,
                        new Exception("send No." + errorNotifyUrlIdx + " notify url error"));
                  }
                  sendThirtyFourDatalog(winAcThirtyFourParam);
                  if (finalResult) {
                    return OK_RESPONSE;
                  } else {
                    return INTERNAL_SERVER_ERROR_RESPONSE;
                  }
                })
            .flatMap(r -> r);
      }
      LogUtil.info(extraLogInfos, extraLoginFiled);
      sendThirtyFourDatalog(winAcThirtyFourParam);

    } else if (RtbCallbackConstants.CallbackResult.LOSE.getName().equals(result)) {
      sendThirtyFourDatalog(loseAcThirtyFourParam);

      int requestFlowType = NumberUtils.toInt(apiFlowType);
      var isHuaweiFlowType = requestFlowType == RequestFlowType.HUAWEI.getCode();
      var isOppoFlowType = requestFlowType == RequestFlowType.OPPO.getCode();
      var isVivoFlowType = requestFlowType == RequestFlowType.VIVO.getCode();

      final String finalDspNotifyUrl = dspNotifyUrl;
      int dspId = Integer.parseInt(cid);
      Mono<Optional<DspApiAppInfo>> dspAppMono = getDspAppMono(dspAppId, adDspProtocolType, cid);
      return Mono.zip(dspAppMono, dspManagementService.getDspInfo(dspId))
          .flatMap(
              zip -> {
                var dspApiAppInfoOptional = zip.getT1();
                Optional<DspInfo> dspInfoOptional = zip.getT2();

                if (isHuaweiFlowType || isOppoFlowType || isVivoFlowType) {
                  List<Mono<Boolean>> notifyUrlMono = Lists.newArrayList();
                  if (!finalDspNotifyUrl.isBlank()) {
                    String validDspNotifyUrl = finalDspNotifyUrl;
                    if (!LinkUtil.isValidHttpUrl(finalDspNotifyUrl)) {
                      validDspNotifyUrl =
                          URLDecoder.decode(finalDspNotifyUrl, StandardCharsets.UTF_8);
                    }
                    String[] dspNotifyUrls =
                        validDspNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);
                    String replacePrice = null;
                    String loseCallbackPrice = encryptAuctionPrice;
                    if (NumberUtils.isCreatable(adxMaxPrice)) {
                      if (NumberUtils.isCreatable(encryptAuctionPrice)) {
                        if (Integer.parseInt(adxMaxPrice) > Integer.parseInt(encryptAuctionPrice)) {
                          loseCallbackPrice = adxMaxPrice;
                        }
                      } else {
                        loseCallbackPrice = adxMaxPrice;
                      }
                    }

                    if (adDspProtocolType == DspProtocolType.KUAI_SHOU.getType()
                        && NumberUtils.isCreatable(loseCallbackPrice)) {
                      replacePrice = loseCallbackPrice;
                    }
                    for (String notifyUrl : dspNotifyUrls) {
                      notifyUrl =
                          notifyUrl.replace(
                              getDspOVHCallbackMacro(requestFlowType),
                              getDspOVHCallbackCode(
                                  requestFlowType, adDspProtocolType, sspErrorCode, apiConfig));
                      if (notifyUrl.contains("#")) {
                        notifyUrl = notifyUrl.replace("#", "%23");
                      }
                      if (adDspProtocolType == DspProtocolType.KUAI_SHOU.getType()) {
                        if (!Strings.isNullOrEmpty(replacePrice)) {
                          notifyUrl =
                              notifyUrl.replace(
                                  RtbConstants.KUAISHOU_MACRO_LOSE_NOTICE_PRICE, replacePrice);
                        }
                      } else if (isYyb) {
                        if (NumberUtils.isCreatable(loseCallbackPrice)) {
                          notifyUrl =
                              notifyUrl.replace(
                                  RtbConstants.YYB_MACRO_AUCTION_PRICE, loseCallbackPrice);
                        }
                      } else if (adDspProtocolType == DspProtocolType.HUICHUAN.getType()) {
                        if (NumberUtils.isCreatable(loseCallbackPrice)) {
                          notifyUrl =
                              notifyUrl.replace(
                                  RtbConstants.HUICHUAN_MACRO_PRICE, loseCallbackPrice);
                        }
                      } else if (adDspProtocolType == DspProtocolType.BAIDU_MOBADS_PB.getType()) {
                        if (NumberUtils.isCreatable(loseCallbackPrice)) {
                          notifyUrl =
                              notifyUrl.replace(
                                  RtbConstants.STANDARD_DSP_MACRO_PRICE, loseCallbackPrice);
                        }
                      } else if (adDspProtocolType == DspProtocolType.OPPO_RTB.getType()) {
                        boolean replacedNotifyUrl = false;
                        if (NumberUtils.isCreatable(loseCallbackPrice)) {
                          if (dspApiAppInfoOptional.isPresent()) {
                            var dspApiAppInfo = dspApiAppInfoOptional.get();
                            if (!dspApiAppInfo.getAppKey().isBlank()) {
                              var appKey = Base64.getDecoder().decode(dspApiAppInfo.getAppKey());
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.OPPO_MEDIA_MACRO_PRICE,
                                      URLEncoder.encode(
                                          Strings.nullToEmpty(
                                              AesUtils.encrypt(appKey, loseCallbackPrice)),
                                          StandardCharsets.UTF_8));
                              replacedNotifyUrl = true;
                            }
                          }
                          if (!replacedNotifyUrl) {
                            notifyUrl =
                                notifyUrl.replace(
                                    RtbConstants.OPPO_MEDIA_MACRO_PRICE, loseCallbackPrice);
                          }
                        }
                      } else if (adDspProtocolType == DspProtocolType.HUAWEI.getType()) {
                        notifyUrl =
                            notifyUrl.replace(RtbConstants.HUAWEI_MEDIA_MACRO_AUCTION_LOSS, "102");
                        if (NumberUtils.isCreatable(loseCallbackPrice)) {
                          notifyUrl =
                              notifyUrl.replace(
                                  RtbConstants.HUAWEI_MEDIA_MACRO_PRICE,
                                  new BigDecimal(loseCallbackPrice)
                                      .divide(new BigDecimal(100), 2, RoundingMode.FLOOR)
                                      .toString());
                        }
                      } else if (adDspProtocolType == DspProtocolType.STANDARD.getType()
                          && CommonBizUtils.isHuaweiStandardDsp(dspId)
                          && dspInfoOptional.isPresent()) {
                        if (NumberUtils.isCreatable(replacePrice)) {
                          String encryptGcmAppendIvAndBase64 = replacePrice;
                          // 华为标准DSP 通知中的价格
                          DspInfo dspInfo = dspInfoOptional.get();
                          if (dspInfo.getWinNoticeEncrypt()) {
                            byte[] iv = new byte[12];
                            new SecureRandom().nextBytes(iv);
                            encryptGcmAppendIvAndBase64 =
                                AesUtils.encryptGcmAppendIvAndBase64(
                                    dspInfo.getSecretKey(),
                                    replacePrice.getBytes(StandardCharsets.UTF_8),
                                    iv);
                          }
                          notifyUrl =
                              notifyUrl.replace(
                                  WindmillBiddingConstants.MACRO_AUCTION_PRICE,
                                  encryptGcmAppendIvAndBase64);
                        }
                      }
                      String requestUrl = notifyUrl.trim();
                      if (isYyb) {
                        String callbackUri;
                        try {
                          callbackUri =
                              UriComponentsBuilder.fromHttpUrl(requestUrl)
                                  .build(false)
                                  .toUriString();
                        } catch (Exception e) {
                          LogUtil.localError(
                              "{} parse callback url:{} error:" + e.getMessage(), rid, requestUrl);
                          LogUtil.error(extraLogInfos, extraLoginFiled, e);
                          notifyUrlMono.add(Mono.just(Boolean.FALSE));
                          continue;
                        }

                        Mono<Boolean> booleanMono =
                            getResponseCode(
                                callbackUri, extraLogInfos, extraLoginFiled, rid, requestUrl);
                        notifyUrlMono.add(booleanMono);

                      } else {

                        URI callbackUri;
                        try {
                          callbackUri =
                              UriComponentsBuilder.fromHttpUrl(requestUrl).build(false).toUri();
                        } catch (Exception e) {
                          LogUtil.localError(
                              "{} parse callback url:{} error:" + e.getMessage(), rid, requestUrl);
                          try {
                            requestUrl = URLDecoder.decode(requestUrl, StandardCharsets.UTF_8);
                            callbackUri =
                                UriComponentsBuilder.fromHttpUrl(requestUrl).build(true).toUri();
                          } catch (Exception ie) {
                            RtbLogUtil.hbError(
                                "urlDecode error! url:{}, exception:{}",
                                requestUrl,
                                ie.getMessage());
                            notifyUrlMono.add(Mono.just(Boolean.FALSE));
                            continue;
                          }
                        }
                        final String finalRequestUrl = requestUrl;
                        notifyUrlMono.add(
                            webClient
                                .get()
                                .uri(callbackUri)
                                .acceptCharset(StandardCharsets.UTF_8)
                                .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                                .doOnError(
                                    e ->
                                        LogUtil.localError(
                                            "request({}) {} error!", rid, finalRequestUrl, e))
                                .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                                .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
                      }
                    }
                  }
                  Mono<Boolean> setAdFilterMono = Mono.just(Boolean.TRUE);
                  if (RequestFlowType.HUAWEI.getName().equals(adxName)
                      && apiConfig.isFilterAd(sspErrorCode)
                      && !Strings.isNullOrEmpty(creativeId)) {
                    // 默认过滤一天
                    int filterAdTimeInSecond =
                        Optional.ofNullable(apiConfig.getFilterAdTime()).orElse(86400);
                    filterAdTimeInSecond = filterAdTimeInSecond <= 0 ? 86400 : filterAdTimeInSecond;

                    setAdFilterMono =
                        adCacheService.setApiFilterAd(
                            RequestFlowType.HUAWEI.getCode(),
                            cid,
                            creativeId,
                            filterAdTimeInSecond);
                  }
                  notifyUrlMono.add(setAdFilterMono);

                  return Mono.zip(
                          notifyUrlMono,
                          results -> {
                            boolean finalResult = Boolean.TRUE;
                            for (Object o : results) {
                              Boolean notifyResult = (Boolean) o;
                              if (Boolean.FALSE.equals(notifyResult)) {
                                finalResult = Boolean.FALSE;
                                break;
                              }
                            }
                            LogUtil.info(extraLogInfos, extraLoginFiled);
                            if (finalResult) {
                              return OK_RESPONSE;
                            } else {
                              return INTERNAL_SERVER_ERROR_RESPONSE;
                            }
                          })
                      .flatMap(r -> r);
                }
                LogUtil.info(extraLogInfos, extraLoginFiled);
                return OK_RESPONSE;
              });

    } else {
      errorMsg = "bad rtb(" + adxName + ") callback request, uri:" + uri;
      LogUtil.localWarn(errorMsg);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }
    return OK_RESPONSE;
  }

  private Mono<Optional<DspApiAppInfo>> getDspAppMono(
      String dspAppId, int adDspProtocolType, String cid) {
    Mono<Optional<DspApiAppInfo>> dspAppMono = Mono.just(Optional.empty());
    if (!Strings.isNullOrEmpty(dspAppId)
        && NumberUtils.isDigits(dspAppId)
        && !("0").equals(dspAppId)
        && adDspProtocolType == DspProtocolType.OPPO_RTB.getType()) {
      var dspAppIdInt = Integer.parseInt(dspAppId);
      dspAppMono = dspManagementService.getDspApiAppInfo(Integer.parseInt(cid), dspAppIdInt);
    }
    return dspAppMono;
  }

  private Mono<Boolean> getResponseCode(
      String callbackUri,
      Map<String, Object> extraLogInfos,
      Map<String, String> extraLoginFiled,
      String rid,
      String finalRequestUrl) {
    try {
      return webClientNoEncode
          .get()
          .uri(callbackUri)
          .acceptCharset(StandardCharsets.UTF_8)
          .exchangeToMono(
              clientResponse -> {
                var rawStatusCode = clientResponse.rawStatusCode();
                extraLogInfos.put("code", rawStatusCode);

                return clientResponse
                    .bodyToMono(String.class)
                    .flatMap(
                        responseBody -> {
                          extraLogInfos.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, responseBody);
                          /// LogUtil.info(extraLogInfos, extraLoginFiled);
                          return Mono.just(Boolean.TRUE);
                        });
              })
          .doOnError(
              e -> {
                /// LogUtil.localError("request({}) {} error!", rid, finalRequestUrl, e);
                LogUtil.error(extraLogInfos, extraLoginFiled, e);
              })
          .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
          .onErrorResume(
              Exception.class,
              e -> {
                LogUtil.error(extraLogInfos, extraLoginFiled, e);
                return Mono.just(Boolean.FALSE);
              });
    } catch (Exception e) {
      LogUtil.error(extraLogInfos, extraLoginFiled, e);
      return Mono.just(Boolean.FALSE);
    }
  }

  private String getDspOVHCallbackCode(
      int requestFlowType,
      int dspProtocolType,
      String callbackCode,
      RtbApiConfig.ApiConfig apiConfig) {
    if (dspProtocolType == DspProtocolType.YYB.getType()) {
      if (requestFlowType == RequestFlowType.HUAWEI.getCode()) {
        return HUAWAI_TO_YYB_ERRORCODE_MAP.getOrDefault(callbackCode, "3003");
      }
    }

    if (null != apiConfig) {
      var dspCallbackCodes = apiConfig.getDspCallbackCode();
      if (!CollectionUtils.isEmpty(dspCallbackCodes)) {
        if (dspCallbackCodes.contains(callbackCode)) {
          if (requestFlowType == RequestFlowType.HUAWEI.getCode()) {
            return "huawei_" + callbackCode;
          } else if (requestFlowType == RequestFlowType.VIVO.getCode()) {
            return "vivo_" + callbackCode;
          } else if (requestFlowType == RequestFlowType.OPPO.getCode()) {
            return "oppo_" + callbackCode;
          }
        }
      }
    }
    if (requestFlowType == RequestFlowType.HUAWEI.getCode()) {
      return "huawei_102"; // 公开竞价时输给了更高价格
    } else if (requestFlowType == RequestFlowType.VIVO.getCode()) {
      return "vivo_00"; // 媒体没有，自定义的
    } else if (requestFlowType == RequestFlowType.OPPO.getCode()) {
      return "oppo_20066";
    }
    return StringUtils.EMPTY;
  }

  private String getDspOVHCallbackMacro(int requestFlowType) {
    if (requestFlowType == RequestFlowType.HUAWEI.getCode()) {
      return RtbConstants.TrafficCallbackDspErrorCodeMacro.HUAWEI_ERROR_CODE_MACRO.getMacro();
    } else if (requestFlowType == RequestFlowType.VIVO.getCode()) {
      return RtbConstants.TrafficCallbackDspErrorCodeMacro.VIVO_ERROR_CODE_MACRO.getMacro();
    } else if (requestFlowType == RequestFlowType.OPPO.getCode()) {
      return RtbConstants.TrafficCallbackDspErrorCodeMacro.OPPO_ERROR_CODE_MACRO.getMacro();
    } else {
      return StringUtils.EMPTY;
    }
  }

  /** standard竞价通知 */
  public Mono<ServerResponse> standardCallback(ServerRequest request) {

    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String uri = httpRequest.getURI().toString();
    final String result = request.pathVariable(RtbCallbackConstants.CALLBACK_RESULT);
    var rid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);
    var cid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName())
            .orElse(StringUtils.EMPTY);

    final String dspNotifyUrl =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName())
            .orElse(StringUtils.EMPTY);

    String encryptAuctionPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    String userId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.USER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var appId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adSlotId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiRequestId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var apiAdSlotId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    var priceForSsp =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName())
            .orElse(StringUtils.EMPTY);

    var developerId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var settlementMode =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName())
            .orElse(Integer.toString(SettlementMode.BIDDING.getCode()));

    var apiFlowType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.API_TYPE.getName())
            .orElse(Integer.toString(RequestFlowType.STANDARD.getCode()));

    var vid = request.queryParam(RtbCallbackConstants.CallBackParam.VID.getName()).orElse(rid);

    var transAdType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var uid =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DEVICE_UID.getName())
            .orElse(StringUtils.EMPTY);
    var taskId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.TASK_ID.getName())
            .orElse(StringUtils.EMPTY);

    var adType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var currencyRate =
        request.queryParam(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName()).orElse("1");

    var osType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.OS_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var creativeId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_CREATIVE_ID.getName())
            .orElse(StringUtils.EMPTY);

    var standardSspTempWinUrl =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.STANDARD_SSP_TEMP_WIN_URL.getName())
            .orElse(StringUtils.EMPTY);

    var standardSspTempLoseUrl =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.STANDARD_SSP_TEMP_LOSE_URL.getName())
            .orElse(StringUtils.EMPTY);

    var dspProtocolType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var adxMaxPrice =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName())
            .orElse(StringUtils.EMPTY);

    var adxId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.ADX_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspSellType =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName())
            .orElse(StringUtils.EMPTY);

    var dspAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiAppId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName())
            .orElse(StringUtils.EMPTY);

    var dspApiPlacementId =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(26);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(8);

    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName(), rid);
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.AD_SOURCE_CHANNEL.getName(), cid);
    extraLoginFiled.put(
        RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.RTB_CALLBACK.getName());
    extraLoginFiled.put(RtbCallbackConstants.CallBackParam.PLACEMENT_ID.getName(), adSlotId);
    extraLoginFiled.put("uid", uid);
    extraLoginFiled.put("adType", adType);
    extraLoginFiled.put("ftype", apiFlowType);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_NOTIFY_URL.getName(), dspNotifyUrl);
    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);
    extraLogInfos.put("userId", userId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AUCTION_PRICE.getName(), encryptAuctionPrice);

    extraLogInfos.put(RtbCallbackConstants.CallBackParam.APP_ID.getName(), appId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_REQUEST_ID.getName(), apiRequestId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_APP_ID.getName(), apiAppId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.API_PLACEMENT_ID.getName(), apiAdSlotId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.PRICE_FOR_SSP.getName(), priceForSsp);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DEVELOPER_ID.getName(), developerId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SETTLEMENT_MODE.getName(), settlementMode);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.VID.getName(), vid);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.TRANS_AD_TYPE.getName(), transAdType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.CURRENCY_RATE.getName(), currencyRate);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.OS_TYPE.getName(), osType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.AD_CREATIVE_ID.getName(), creativeId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.AD_DSP_PROTOCOL_TYPE.getName(), dspProtocolType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_MAX_PRICE.getName(), adxMaxPrice);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.ADX_ID.getName(), adxId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_SELL_TYPE.getName(), dspSellType);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_APP_ID.getName(), dspAppId);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.DSP_API_APP_ID.getName(), dspApiAppId);
    extraLogInfos.put(
        RtbCallbackConstants.CallBackParam.DSP_API_PLACEMENT_ID.getName(), dspApiPlacementId);

    String adxName = RequestFlowType.STANDARD.getName();

    String sspErrorCode =
        request
            .queryParam(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName())
            .orElse(StringUtils.EMPTY);
    extraLogInfos.put(RtbCallbackConstants.CallBackParam.SSP_ERROR_CODE.getName(), sspErrorCode);
    final int realErrorCode =
        !NumberUtils.isCreatable(sspErrorCode) ? -1 : Integer.parseInt(sspErrorCode);
    extraLoginFiled.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, Integer.toString(realErrorCode));

    //    LogUtil.localInfo(
    //        JsonSerializationUtils.jacksonObjToJson(
    //            Stream.concat(extraLogInfos.entrySet().stream(),
    // extraLoginFiled.entrySet().stream())
    //                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))));

    String errorMsg;

    if (!RequestFlowType.STANDARD.getName().equals(adxName)) {
      errorMsg = "rtb(" + rid + ") callback flowType error, adxName:" + adxName;
      LogUtil.localWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }

    if (rid.isBlank()) {
      errorMsg = "rtb(" + adxName + ") callback require requestId, uri:" + uri;
      LogUtil.localWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }
    if (cid.isBlank()) {
      errorMsg = "rtb(" + adxName + ") callback require adSourceChannel, uri:" + uri;
      LogUtil.localWarn(errorMsg, uri);
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException(errorMsg));
      return BAD_REQUEST_RESPONSE;
    }

    return rtbCommonService
        .getSigmobDeveloper(Long.parseLong(developerId))
        .flatMap(
            developer -> {
              String secret = developer.getPublicKey();
              String decryptPrice = encryptAuctionPrice;
              try {
                if (!NumberUtils.isCreatable(encryptAuctionPrice)) {
                  decryptPrice =
                      Constants.AES_CRYPTO.decryptWithUrlSafe(secret, encryptAuctionPrice);
                }
              } catch (Exception e) {
                //                LogUtil.localError(
                //                    "rtb({}) request({}) callback decrypt price({}) with key({})
                // error:{}",
                //                    adxName,
                //                    rid,
                //                    encryptAuctionPrice,
                //                    secret,
                //                    e.getMessage());
              }

              String dspFinalNotifyUrl = dspNotifyUrl;
              String callbackResult = result;
              if (Strings.isNullOrEmpty(dspNotifyUrl)) {
                if (!NumberUtils.isCreatable(sspErrorCode)) {
                  dspFinalNotifyUrl = null;
                } else if (sspErrorCode.equals(
                    Integer.toString(StandardApiConstants.SspDecisionCode.WIN.getCode()))) {
                  callbackResult = RtbCallbackConstants.CallbackResult.WIN.getName();
                  if (!Strings.isNullOrEmpty(standardSspTempWinUrl)) {
                    dspFinalNotifyUrl = standardSspTempWinUrl;
                  }
                } else {
                  callbackResult = RtbCallbackConstants.CallbackResult.LOSE.getName();
                  if (!Strings.isNullOrEmpty(standardSspTempLoseUrl)) {
                    dspFinalNotifyUrl = standardSspTempLoseUrl;
                  }
                }
              }

              if (RtbCallbackConstants.CallbackResult.WIN.getName().equals(callbackResult)) {
                //                int settlementModeCode = Integer.parseInt(settlementMode);
                //                if (!RequestFlowType.STANDARD.getName().equals(adxName)
                //                    || (!NumberUtils.isCreatable(decryptPrice)
                //                        && settlementModeCode ==
                // SettlementMode.BIDDING.getCode())) {
                //                  String errMsg =
                //                      "rtb(" + adxName + ") request callback auction price
                // invalid, uri:" + uri;
                //                  LogUtil.localWarn(errMsg);
                //                  LogUtil.warn(extraLogInfos, extraLoginFiled, new
                // RtbCallbackException(errMsg));
                //                  return badRequestResponse(
                //                      new AcThirtyFourParam(
                //                          rid,
                //                          cid,
                //                          decryptPrice,
                //                          realErrorCode,
                //                          userId,
                //                          adxName,
                //                          appId,
                //                          adSlotId,
                //                          apiRequestId,
                //                          apiAppId,
                //                          apiAdSlotId,
                //                          priceForSsp,
                //                          developerId,
                //                          null,
                //                          null,
                //                          vid,
                //                          transAdType,
                //                          sspErrorCode,
                //                          uid,
                //                          adType,
                //                          currencyRate,
                //                          osType,
                //                          creativeId),
                //                      extraLogInfos,
                //                      errMsg,
                //                      uri);
                //                }
                String auctionPrice = decryptPrice;
                var winAcThirtyFourParam =
                    new AcThirtyFourParam(
                        rid,
                        cid,
                        auctionPrice,
                        realErrorCode,
                        userId,
                        adxName,
                        appId,
                        adSlotId,
                        apiRequestId,
                        apiAppId,
                        apiAdSlotId,
                        priceForSsp,
                        developerId,
                        null,
                        null,
                        vid,
                        transAdType,
                        sspErrorCode,
                        uid,
                        taskId,
                        0,
                        adType,
                        currencyRate,
                        osType,
                        creativeId,
                        adxId,
                        dspSellType,
                        dspApiAppId,
                        dspApiPlacementId,
                        null);
                if (!Strings.isNullOrEmpty(dspFinalNotifyUrl)) {

                  String[] dspNotifyUrls =
                      dspFinalNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);
                  List<Mono<Boolean>> notifyUrlMono =
                      Lists.newArrayListWithCapacity(dspNotifyUrls.length);
                  for (String notifyUrl : dspNotifyUrls) {

                    URI callbackUri = null;
                    try {
                      callbackUri = UriComponentsBuilder.fromHttpUrl(notifyUrl).build(true).toUri();
                    } catch (Exception e) {
                      /// LogUtil.localError("{} parse callback url:{} error!", rid, notifyUrl, e);
                      try {
                        notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
                        callbackUri =
                            UriComponentsBuilder.fromHttpUrl(notifyUrl).build(true).toUri();
                      } catch (Exception ie) {
                        /// LogUtil.localError( "urlDecode error! url:{}, exception:{}", notifyUrl,
                        // ie.getMessage());
                        notifyUrlMono.add(Mono.just(Boolean.FALSE));
                        continue;
                      }
                    }
                    final String requestUrl = notifyUrl;
                    notifyUrlMono.add(
                        webClient
                            .get()
                            .uri(callbackUri)
                            .acceptCharset(StandardCharsets.UTF_8)
                            .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                            .doOnError(
                                e ->
                                    LogUtil.localError("request({}) {} error!", rid, requestUrl, e))
                            .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                            .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));

                    return Mono.zip(
                            notifyUrlMono,
                            results -> {
                              boolean finalResult = Boolean.TRUE;
                              int errorNotifyUrlIdx = -1;
                              for (int i = 0; i < results.length; i++) {
                                Boolean notifyResult = (Boolean) results[i];
                                if (Boolean.FALSE.equals(notifyResult)) {
                                  finalResult = Boolean.FALSE;
                                  errorNotifyUrlIdx = i;
                                  break;
                                }
                              }
                              if (finalResult) {
                                LogUtil.info(extraLogInfos, extraLoginFiled);
                              } else {
                                LogUtil.error(
                                    extraLogInfos,
                                    extraLoginFiled,
                                    new Exception(
                                        "send No." + errorNotifyUrlIdx + " notify url error"));
                              }
                              sendThirtyFourDatalog(winAcThirtyFourParam);
                              if (finalResult) {
                                return OK_RESPONSE;
                              } else {
                                return INTERNAL_SERVER_ERROR_RESPONSE;
                              }
                            })
                        .flatMap(r -> r);
                  }
                }
                LogUtil.info(extraLogInfos, extraLoginFiled);
                sendThirtyFourDatalog(winAcThirtyFourParam);

              } else if (RtbCallbackConstants.CallbackResult.LOSE
                  .getName()
                  .equals(callbackResult)) {
                LogUtil.info(extraLogInfos, extraLoginFiled);
                sendThirtyFourDatalog(
                    new AcThirtyFourParam(
                        rid,
                        cid,
                        decryptPrice,
                        realErrorCode,
                        userId,
                        adxName,
                        appId,
                        adSlotId,
                        apiRequestId,
                        apiAppId,
                        apiAdSlotId,
                        priceForSsp,
                        developerId,
                        null,
                        null,
                        vid,
                        transAdType,
                        sspErrorCode,
                        uid,
                        taskId,
                        0,
                        adType,
                        currencyRate,
                        osType,
                        creativeId,
                        adxId,
                        dspSellType,
                        dspApiAppId,
                        dspApiPlacementId,
                        null));

                int adDspProtocolType = NumberUtils.toInt(dspProtocolType);
                Mono<Optional<DspApiAppInfo>> dspAppMono =
                    getDspAppMono(dspAppId, adDspProtocolType, cid);
                final String finalDecryptPrice = decryptPrice;
                final String dspLoseFinalNotifyUrl = dspFinalNotifyUrl;
                return dspAppMono.flatMap(
                    dspApiAppInfoOptional -> {
                      List<Mono<Boolean>> notifyUrlMono = Lists.newLinkedList();
                      if (!Strings.isNullOrEmpty(dspLoseFinalNotifyUrl)) {
                        String[] dspNotifyUrls =
                            dspLoseFinalNotifyUrl.split(Constants.MULTI_DSP_NOTIFY_URL_DELIMITER);

                        String loseCallbackPrice = encryptAuctionPrice;
                        if (NumberUtils.isCreatable(adxMaxPrice)) {
                          if (NumberUtils.isCreatable(encryptAuctionPrice)) {
                            if (Integer.parseInt(adxMaxPrice)
                                > Integer.parseInt(encryptAuctionPrice)) {
                              loseCallbackPrice = adxMaxPrice;
                            }
                          } else {
                            loseCallbackPrice = adxMaxPrice;
                          }
                        }

                        String replacePrice =
                            NumberUtils.isCreatable(loseCallbackPrice)
                                ? loseCallbackPrice
                                : NumberUtils.isCreatable(finalDecryptPrice)
                                    ? finalDecryptPrice
                                    : null;
                        for (String notifyUrl : dspNotifyUrls) {
                          if (!LinkUtil.isValidHttpUrl(notifyUrl)) {
                            notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
                          }
                          if (notifyUrl.contains("#")) {
                            notifyUrl = notifyUrl.replace("#", "%23");
                          }
                          if (adDspProtocolType == DspProtocolType.KUAI_SHOU.getType()) {
                            if (!Strings.isNullOrEmpty(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.KUAISHOU_MACRO_LOSE_NOTICE_PRICE, replacePrice);
                            }
                          } else if (adDspProtocolType == DspProtocolType.BAIDU_MOBADS_PB.getType()
                              || adDspProtocolType == DspProtocolType.HUICHUAN.getType()) {
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.MACRO_BAIDU_REPLACE_PRICE, replacePrice);
                            }
                          } else if (adDspProtocolType == DspProtocolType.TENCENT_GDT.getType()) {
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.TENCENT_MEDIA_MACRO_PRICE,
                                      URLEncoder.encode(
                                          Strings.nullToEmpty(
                                              TencentBidPriceCrypto.encrypt(
                                                  TencentBidPriceCrypto.PUBLIC_KEY, replacePrice)),
                                          StandardCharsets.UTF_8));
                            }
                          } else if (adDspProtocolType == DspProtocolType.OPPO_RTB.getType()) {
                            boolean replacedNotifyUrl = false;
                            if (NumberUtils.isCreatable(replacePrice)) {
                              if (dspApiAppInfoOptional.isPresent()) {
                                var dspApiAppInfo = dspApiAppInfoOptional.get();
                                if (!dspApiAppInfo.getAppKey().isBlank()) {
                                  var appKey =
                                      Base64.getDecoder().decode(dspApiAppInfo.getAppKey());
                                  notifyUrl =
                                      notifyUrl.replace(
                                          RtbConstants.OPPO_MEDIA_MACRO_PRICE,
                                          URLEncoder.encode(
                                              Strings.nullToEmpty(
                                                  AesUtils.encrypt(appKey, replacePrice)),
                                              StandardCharsets.UTF_8));
                                  replacedNotifyUrl = true;
                                }
                              }
                              if (!replacedNotifyUrl) {
                                notifyUrl =
                                    notifyUrl.replace(
                                        RtbConstants.OPPO_MEDIA_MACRO_PRICE, replacePrice);
                              }
                            }
                          } else if (adDspProtocolType == DspProtocolType.HUAWEI.getType()) {
                            notifyUrl =
                                notifyUrl.replace(
                                    RtbConstants.HUAWEI_MEDIA_MACRO_AUCTION_LOSS, "102");
                            if (NumberUtils.isCreatable(replacePrice)) {
                              notifyUrl =
                                  notifyUrl.replace(
                                      RtbConstants.HUAWEI_MEDIA_MACRO_PRICE,
                                      new BigDecimal(replacePrice)
                                          .divide(new BigDecimal(100), 2, RoundingMode.FLOOR)
                                          .toString());
                            }
                          }
                          final String requestUrl = notifyUrl;

                          URI callbackUri;
                          try {
                            callbackUri =
                                UriComponentsBuilder.fromHttpUrl(notifyUrl).build(false).toUri();
                          } catch (Exception e) {
                            /// LogUtil.localError("{} parse callback url:{} error!", rid,
                            // notifyUrl, e);
                            try {
                              notifyUrl = URLDecoder.decode(notifyUrl, StandardCharsets.UTF_8);
                              callbackUri =
                                  UriComponentsBuilder.fromHttpUrl(notifyUrl).build(true).toUri();
                            } catch (Exception ie) {
                              //                              LogUtil.localError(  "urlDecode error!
                              // url:{}, exception:{}",
                              //                                  notifyUrl,
                              //                                  ie.getMessage());
                              notifyUrlMono.add(Mono.just(Boolean.FALSE));
                              continue;
                            }
                          }
                          notifyUrlMono.add(
                              webClient
                                  .get()
                                  .uri(callbackUri)
                                  .acceptCharset(StandardCharsets.UTF_8)
                                  .exchangeToMono(clientResponse -> Mono.just(Boolean.TRUE))
                                  .doOnError(
                                      e ->
                                          LogUtil.localError(
                                              "request({}) {} error!", rid, requestUrl, e))
                                  .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                                  .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
                        }
                      }

                      if (!CollectionUtils.isEmpty(notifyUrlMono)) {
                        return Mono.zip(
                                notifyUrlMono,
                                results -> {
                                  boolean finalResult = Boolean.TRUE;
                                  for (Object o : results) {
                                    Boolean notifyResult = (Boolean) o;
                                    if (Boolean.FALSE.equals(notifyResult)) {
                                      finalResult = Boolean.FALSE;
                                      break;
                                    }
                                  }
                                  if (finalResult) {
                                    return OK_RESPONSE;
                                  } else {
                                    return INTERNAL_SERVER_ERROR_RESPONSE;
                                  }
                                })
                            .flatMap(r -> r);
                      }
                      return OK_RESPONSE;
                    });
              }
              return OK_RESPONSE;
            })
        .onErrorResume(
            Exception.class,
            e -> {
              /// LogUtil.localError("standard dsp callback error", e);
              LogUtil.error(extraLogInfos, extraLoginFiled, e);
              sendThirtyFourDatalog(
                  new AcThirtyFourParam(
                      rid,
                      cid,
                      StringUtils.EMPTY,
                      RtbCallbackConstants.CallbackResult.ERROR.getCode(),
                      userId,
                      adxName,
                      appId,
                      adSlotId,
                      apiRequestId,
                      apiAppId,
                      apiAdSlotId,
                      priceForSsp,
                      developerId,
                      null,
                      null,
                      vid,
                      transAdType,
                      sspErrorCode,
                      uid,
                      taskId,
                      0,
                      adType,
                      currencyRate,
                      osType,
                      creativeId,
                      adxId,
                      dspSellType,
                      dspApiAppId,
                      dspApiPlacementId,
                      null));
              return INTERNAL_SERVER_ERROR_RESPONSE;
            });
  }

  /**
   * @param acThirtyFourParam
   * @param extraLogInfos
   * @param errorMsg
   * @param uri
   * @return
   */
  protected Mono<ServerResponse> badRequestResponse(
      AcThirtyFourParam acThirtyFourParam,
      //      String requestId,
      //      String adSourceChannel,
      //      String adxApi,
      //      String uid,
      //      String appId,
      //      String adSlotId,
      //      String apiRequestId,
      //      String apiAppId,
      //      String apiAdSlotId,
      //      String priceForSsp,
      //      String developerId,
      Map<String, Object> extraLogInfos,
      String errorMsg,
      String uri
      //          ,
      //      String impId,
      //      String transAdType
      ) {
    LogUtil.localWarn(errorMsg, uri);
    LogUtil.warn(extraLogInfos, null, new RtbCallbackException(errorMsg));
    sendThirtyFourDatalog(acThirtyFourParam);
    return BAD_REQUEST_RESPONSE;
  }

  /**
   * 发送34号点打点日志
   *
   * @param acParam`
   */
  protected final void sendThirtyFourDatalog(
      //      String requestId,
      //      String adSourceChannel,
      //      String auctionPrice,
      //      int callbackStatus,
      //      String userId,
      //      String adxApi,
      //      String appId,
      //      String adSlotId,
      //      String apiRequestId,
      //      String apiAppId,
      //      String apiAdSlotId,
      //      String priceForSsp,
      //      String deveLoperId
      AcThirtyFourParam acParam) {
    try {
      Ac534MessageBuilder messageBuilder = new Ac534MessageBuilder(acParam);
      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 534th Datalog error!", e);
    }
  }
}
