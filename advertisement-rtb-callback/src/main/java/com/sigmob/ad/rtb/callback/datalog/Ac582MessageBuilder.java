package com.sigmob.ad.rtb.callback.datalog;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.datalog.AcType;
import com.sigmob.ad.core.datalog.DataLogField;
import com.sigmob.ad.core.datalog.DataLogMessageBuilder;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class Ac582MessageBuilder extends DataLogMessageBuilder {

  private final String requestId;

  private final Integer appId;

  private final String placementId;

  private final String userId;

  private final String clientIp;

  private final String appCallbackUrl;

  private final int callbackCode;

  private final long sendCallbackTime;

  private final long finishCallbackTime;

  private static final AcType AC_TYPE = AcType.FIVE_EIGHT_TWO;

  public Ac582MessageBuilder(
      String requestId,
      Integer appId,
      String placementId,
      String userId,
      String clientIp,
      String appCallbackUrl,
      int callbackCode,
      long sendCallbackTime,
      long finishCallbackTime) {
    this.requestId = requestId;
    this.appId = appId;
    this.placementId = placementId;
    this.userId = userId;
    this.clientIp = clientIp;
    this.appCallbackUrl = appCallbackUrl;
    this.callbackCode = callbackCode;
    this.sendCallbackTime = sendCallbackTime;
    this.finishCallbackTime = finishCallbackTime;
  }

  @Override
  protected Map<String, Object> buildMessageBody() {

    Map<String, Object> dataLog = Maps.newHashMapWithExpectedSize(100);

    dataLog.put(DataLogField.REQUEST_ID, requestId);
    dataLog.put(DataLogField.APP_ID_NEW, null == appId ? StringUtils.EMPTY : appId.toString());
    dataLog.put(DataLogField.PLACEMENT_ID, placementId);
    dataLog.put(DataLogField.CLIENT_IP, clientIp);
    dataLog.put(
        DataLogField.APP_CALLBACK_URL_RV,
        URLEncoder.encode(Strings.nullToEmpty(appCallbackUrl), StandardCharsets.UTF_8));
    dataLog.put(DataLogField.APP_CALLBACK_CODE, Integer.toString(callbackCode));
    dataLog.put(DataLogField.IS_VALID_RV, Integer.toString(callbackCode == 0 ? 1 : 0));
    dataLog.put(DataLogField.CALLBACK_TIMESTAMP, Long.toString(sendCallbackTime));
    dataLog.put(DataLogField.VALID_TIMESTAMP, Long.toString(finishCallbackTime));
    dataLog.put(DataLogField.DEVICE_USER_ID, userId);
    return dataLog;
  }

  @Override
  protected int getOsType() {
    return 0;
  }

  @Override
  protected int getAppId() {
    return appId;
  }

  @Override
  protected AcType getAcType() {
    return AC_TYPE;
  }

  @Override
  protected String getUserId() {
    return userId;
  }
}
