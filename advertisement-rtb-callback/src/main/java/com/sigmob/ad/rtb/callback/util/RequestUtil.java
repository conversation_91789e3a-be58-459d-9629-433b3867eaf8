package com.sigmob.ad.rtb.callback.util;

import com.sigmob.ad.core.util.BaseRequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.reactive.function.server.ServerRequest;

/** 请求处理工具类 */
public class RequestUtil extends BaseRequestUtil {

  public static String getAesHeaderEcb(ServerRequest request) {
    return request.headers().firstHeader(AES_HEADER);
  }

  public static String getAesHeaderGcm(ServerRequest request) {
    return request.headers().firstHeader(AES_HEADER_AGN);
  }

  public static String getAesHeaderCbc(ServerRequest request) {
    return request.headers().firstHeader(AES_HEADER_CBC);
  }
}
