package com.sigmob.ad.rtb.callback.handler;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.exception.*;
import com.sigmob.ad.core.publishing.windmill.StrategyScene;
import com.sigmob.ad.core.security.crypto.AesUtils;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.MediationStrategyDao;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.dao.ToBidImpDao;
import com.sigmob.ad.rtb.callback.datalog.Ac682MessageBuilder;
import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * ToBid rv imp回调
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ToBidSsvImpHandler extends CallbackBaseHandler {

  @NonNull private final DataLogService dataLogService;

  @NonNull private final MediationStrategyDao mediationStrategyDao;

  @NonNull private final ToBidImpDao toBidImpDao;

  @NonNull
  private final ToBidRvCallbackRequest defaultToBidRvCallbackRequest = new ToBidRvCallbackRequest();

  /** 激励视频imp回调 */
  @NonNull
  public Mono<ServerResponse> impCallback(@NonNull ServerRequest request) {

    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String uri = httpRequest.getURI().toString();
    var appIdStr = request.queryParam(LogUtil.LOG_FIELD_APP_ID).orElse(StringUtils.EMPTY);
    var sdkVerStr = request.queryParam(LogUtil.LOG_FIELD_SDK_VERSION).orElse(StringUtils.EMPTY);
    Integer appId = !appIdStr.isBlank() ? NumberUtils.toInt(appIdStr) : null;

    Map<String, Object> extraLogInfoMap = Maps.newHashMapWithExpectedSize(16);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(16);

    ServerRequest.Headers headers = request.headers();
    extraLogInfoMap.put(LogUtil.LOG_FIELD_HEADER, JsonSerializationUtils.objToJson(headers));
    extraLoginFiled.put(RtbCallbackConstants.SsvParam.APP_ID.getName(), appIdStr);
    extraLoginFiled.put(LogUtil.LOG_FIELD_SDK_VERSION, sdkVerStr);

    Mono<ToBidRvCallbackRequest> requestMono = getToBidRvCallbackRequestMono(request, sdkVerStr);

    return requestMono
        .switchIfEmpty(Mono.just(defaultToBidRvCallbackRequest))
        .defaultIfEmpty(defaultToBidRvCallbackRequest)
        .publishOn(Schedulers.boundedElastic())
        .onErrorMap(
            Exception.class,
            e ->
                new SspBusinessException(
                    ErrorCode.INVALID_JSON_DATA, "can not get data from request body", e))
        .flatMap(
            toBidRvCallbackRequest -> {
              var placementId = toBidRvCallbackRequest.getPlacementId();
              var userId = toBidRvCallbackRequest.getUserId();

              var loadId = toBidRvCallbackRequest.getLoadId();
              String channelId = toBidRvCallbackRequest.getChannelId();
              String mask = toBidRvCallbackRequest.getMask();
              int checkMask = toBidRvCallbackRequest.getCheckMask();

              String bodyJson = JsonSerializationUtils.objToJson(toBidRvCallbackRequest);

              extraLogInfoMap.put(RtbCallbackConstants.SsvParam.USER_ID.getName(), userId);
              extraLogInfoMap.put(LogUtil.LOG_FIELD_REQ, uri);
              extraLogInfoMap.put(LogUtil.LOG_FIELD_BODY, bodyJson);

              extraLoginFiled.put(RtbCallbackConstants.SsvParam.LOAD_ID.getName(), loadId);
              extraLoginFiled.put(RtbCallbackConstants.SsvParam.USER_ID.getName(), userId);
              extraLoginFiled.put(RtbCallbackConstants.SsvParam.APP_ID.getName(), appIdStr);
              extraLoginFiled.put(
                  RtbCallbackConstants.SsvParam.PLACEMENT_ID.getName(), placementId);
              extraLoginFiled.put(
                  RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.TO_BID_IMP.getName());

              var currentTimeMillis = System.currentTimeMillis();
              if (Strings.isNullOrEmpty(loadId)
                  || null == appId
                  || Strings.isNullOrEmpty(placementId)
                  || Strings.isNullOrEmpty(userId)
                  || Strings.isNullOrEmpty(mask)
                  || Strings.isNullOrEmpty(channelId)) {

                return badRequestResponse(
                    loadId,
                    appId,
                    placementId,
                    channelId,
                    mask,
                    toBidRvCallbackRequest,
                    currentTimeMillis,
                    currentTimeMillis,
                    extraLogInfoMap,
                    extraLoginFiled);
              }

              if (checkMask != 1) {
                String errMsg = "checkMask is not 1";
                int errorCode = ErrorCode.ToBid.CHECK_MASK_ERR.getCode();
                extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
                LogUtil.error(
                    extraLogInfoMap, extraLoginFiled, new SsvException(errorCode, errMsg));
                send682DataLog(
                    appId, errorCode, toBidRvCallbackRequest, currentTimeMillis, currentTimeMillis);
                return OK_RESPONSE;
              }

              Mono<Optional<StrategyScene>> strategySceneMono =
                  mediationStrategyDao.getStrategyScene(placementId);
              Mono<Boolean> absentMono =
                  toBidImpDao.setMaskInfoIfAbsent(loadId, toBidRvCallbackRequest);
              return Mono.zip(strategySceneMono, absentMono)
                  .flatMap(
                      res -> {
                        var sceneOptional = res.getT1();
                        boolean impAbsent = res.getT2();

                        if (sceneOptional.isEmpty()) {
                          return badRewardConfigResponse(
                              loadId,
                              appId,
                              placementId,
                              channelId,
                              toBidRvCallbackRequest,
                              currentTimeMillis,
                              currentTimeMillis,
                              extraLogInfoMap,
                              extraLoginFiled,
                              "sceneId not exist");
                        }

                        StrategyScene strategyScene = sceneOptional.get();

                        String appSecurityKey = strategyScene.getAppSecurityKey();

                        if (ObjectUtils.anyNull(appSecurityKey)) {
                          return badRewardConfigResponse(
                              loadId,
                              appId,
                              placementId,
                              channelId,
                              toBidRvCallbackRequest,
                              currentTimeMillis,
                              currentTimeMillis,
                              extraLogInfoMap,
                              extraLoginFiled,
                              "sceneId reward config is error: SecurityKey is not exist. "
                                  + JsonSerializationUtils.objToJson(strategyScene));
                        }

                        int code = impAbsent ? 0 : ErrorCode.ToBid.IMP_REPEAT.getCode();

                        send682DataLog(
                            appId,
                            code,
                            toBidRvCallbackRequest,
                            System.currentTimeMillis(),
                            System.currentTimeMillis());
                        extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));

                        if (code == 0) {
                          String encrypt =
                              AesUtils.encrypt(
                                  AesUtils.AES_CRYPTO_KEY_CALLBACK.getBytes(StandardCharsets.UTF_8),
                                  loadId + ":" + toBidRvCallbackRequest.getThirdPlacementId());

                          // 校验通过才进行impCode下发处理
                          if (StringUtils.isNotBlank(encrypt)) {
                            extraLogInfoMap.put("response", encrypt);
                            LogUtil.info(extraLogInfoMap, extraLoginFiled);
                            return ok().body(BodyInserters.fromValue(encrypt));
                            // return ok().bodyValue(BodyInserters.fromValue(encrypt.getBytes()));
                          } else {
                            code = ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR;
                          }
                        }
                        extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(code));
                        LogUtil.error(extraLogInfoMap, extraLoginFiled, new SsvException(code));
                        return OK_RESPONSE;
                      });
            })
        .onErrorResume(
            Exception.class,
            e -> {
              LogUtil.error(extraLogInfoMap, extraLoginFiled, e);
              return OK_RESPONSE;
            });
  }

  private Mono<ServerResponse> badRequestResponse(
      String loadId,
      Integer appId,
      String placementId,
      String channelId,
      String mask,
      ToBidRvCallbackRequest request,
      long sendCallbackTime,
      long finishCallbackTime,
      Map<String, Object> extraLogInfoMap,
      Map<String, String> extraLogFields) {
    String errorMsg =
        "ssv param error! load id:"
            + loadId
            + ", aid:"
            + appId
            + ", pid:"
            + placementId
            + ", mask:"
            + mask
            + ", channelId:"
            + channelId;
    int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
    extraLogFields.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
    LogUtil.error(extraLogInfoMap, extraLogFields, new SsvException(errorCode, errorMsg));
    send682DataLog(appId, errorCode, request, sendCallbackTime, finishCallbackTime);

    return OK_RESPONSE;
  }

  private Mono<ServerResponse> badRewardConfigResponse(
      String loadId,
      Integer appId,
      String placementId,
      String channelId,
      ToBidRvCallbackRequest request,
      long sendCallbackTime,
      long finishCallbackTime,
      Map<String, Object> extraLogInfoMap,
      Map<String, String> extraLogFields,
      String msg) {
    String errorMsg =
        "ssv param error! load id:"
            + loadId
            + ", aid:"
            + appId
            + ", pid:"
            + placementId
            + ", channelId:"
            + channelId
            + ", errorMsg:"
            + msg;
    int errorCode = ErrorCode.SSV.PARAM_ERROR.getCode();
    extraLogFields.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errorCode));
    LogUtil.error(extraLogInfoMap, extraLogFields, new SsvException(errorCode, errorMsg));
    send682DataLog(appId, errorCode, request, sendCallbackTime, finishCallbackTime);

    return OK_RESPONSE;
  }

  /** 发送682号点打点日志 */
  private void send682DataLog(
      Integer appId,
      int callbackCode,
      ToBidRvCallbackRequest request,
      long sendCallbackTime,
      long finishCallbackTime) {
    try {
      var placementId = request.getPlacementId();
      var userId = request.getUserId();
      var loadId = request.getLoadId();
      Ac682MessageBuilder messageBuilder =
          new Ac682MessageBuilder(
              loadId,
              RtbCallbackConstants.SsvCategory.START.getType(),
              "",
              request,
              appId,
              placementId,
              userId,
              null,
              null,
              callbackCode,
              null,
              sendCallbackTime,
              finishCallbackTime);
      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 682 Datalog error!", e);
    }
  }
}
