package com.sigmob.ad.rtb.callback;

import com.sigmob.ad.dao.TailTrafficControlDao;
import com.sigmob.ad.service.TailTrafficControlService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/** 竞价成功回调通知处理服务 */
@SpringBootApplication
@ComponentScan(
    basePackages = "com.sigmob.ad",
    excludeFilters = {
      @ComponentScan.Filter(
          type = FilterType.ASSIGNABLE_TYPE,
          classes = {TailTrafficControlService.class, TailTrafficControlDao.class})
    })
public class RtbCallbackApplication {

  public static void main(String[] args) {
    try {
      SpringApplication.run(RtbCallbackApplication.class, args);
    } catch (Exception e) {
      e.printStackTrace();
      throw e;
    }
  }
}
