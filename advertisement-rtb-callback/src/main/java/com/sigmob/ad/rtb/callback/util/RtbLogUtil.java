package com.sigmob.ad.rtb.callback.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** ssp 上报回调服务日志 */
public class RtbLogUtil {

  /** ssv info 日志 */
  private static final Logger SSV_INFO_LOGGER = LoggerFactory.getLogger("ssvInfoLog");
  /** ssv warn 日志 */
  private static final Logger SSV_WARN_LOGGER = LoggerFactory.getLogger("ssvWarnLog");
  /** ssv error 日志 */
  private static final Logger SSV_ERROR_LOGGER = LoggerFactory.getLogger("ssvErrorLog");
  /** 302 redirect info 日志 */
  private static final Logger REDIRECT_INFO_LOGGER = LoggerFactory.getLogger("redirectInfoLog");
  /** 302 redirect warn 日志 */
  private static final Logger REDIRECT_WARN_LOGGER = LoggerFactory.getLogger("redirectWarnLog");
  /** 302 redirect error 日志 */
  private static final Logger REDIRECT_ERROR_LOGGER = LoggerFactory.getLogger("redirectErrorLog");
  /** header bidding callback info 日志 */
  private static final Logger HB_INFO_LOGGER = LoggerFactory.getLogger("hbInfoLog");
  /** header bidding callback warn 日志 */
  private static final Logger HB_WARN_LOGGER = LoggerFactory.getLogger("hbWarnLog");
  /** header bidding callback error 日志 */
  private static final Logger HB_ERROR_LOGGER = LoggerFactory.getLogger("hbErrorLog");

  public static void ssvInfo(String log) {
    SSV_INFO_LOGGER.info("{}", log);
  }

  public static void ssvWarn(String log) {
    SSV_WARN_LOGGER.warn("{}", log);
  }

  public static void ssvWarn(String log, Throwable e) {
    SSV_WARN_LOGGER.warn("{}", log, e);
  }

  public static final void ssvWarn(String format, Object... arguments) {
    SSV_WARN_LOGGER.warn(format, arguments);
  }

  public static void ssvError(String log) {
    SSV_ERROR_LOGGER.error("{}", log);
  }

  public static void ssvError(String log, Throwable e) {
    SSV_ERROR_LOGGER.error("{}", log, e);
  }

  public static final void ssvError(String format, Object... arguments) {
    SSV_ERROR_LOGGER.error(format, arguments);
  }

  public static void redirectInfo(String log) {
    REDIRECT_INFO_LOGGER.info("{}", log);
  }

  public static void redirectWarn(String log) {
    REDIRECT_WARN_LOGGER.warn("{}", log);
  }

  public static void redirectWarn(String log, Throwable e) {
    REDIRECT_WARN_LOGGER.warn("{}", log, e);
  }

  public static final void redirectWarn(String format, Object... arguments) {
    REDIRECT_WARN_LOGGER.warn(format, arguments);
  }

  public static void redirectError(String log) {
    REDIRECT_ERROR_LOGGER.error("{}", log);
  }

  public static void redirectError(String log, Throwable e) {
    REDIRECT_ERROR_LOGGER.error("{}", log, e);
  }

  public static final void redirectError(String format, Object... arguments) {
    REDIRECT_ERROR_LOGGER.error(format, arguments);
  }

  public static void hbInfo(String log) {
    HB_INFO_LOGGER.info("{}", log);
  }

  public static void hbWarn(String log) {
    HB_WARN_LOGGER.warn("{}", log);
  }

  public static void hbWarn(String log, Throwable e) {
    HB_WARN_LOGGER.warn("{}", log, e);
  }

  public static final void hbWarn(String format, Object... arguments) {
    HB_WARN_LOGGER.warn(format, arguments);
  }

  public static void hbError(String log) {
    HB_ERROR_LOGGER.error("{}", log);
  }

  public static void hbError(String log, Throwable e) {
    HB_ERROR_LOGGER.error("{}", log, e);
  }

  public static final void hbError(String format, Object... arguments) {
    HB_ERROR_LOGGER.error(format, arguments);
  }
}
