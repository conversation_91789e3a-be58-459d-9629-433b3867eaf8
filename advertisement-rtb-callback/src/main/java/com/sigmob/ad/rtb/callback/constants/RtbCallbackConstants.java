package com.sigmob.ad.rtb.callback.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 回调服务常量 */
public class RtbCallbackConstants {

  public static final String REQ_TYPE = "reqType";

  public static final String CALLBACK_RESULT = "result";

  public static final String PLACEMENT_ID = "pid";

  /** 业务请求类型 */
  @Getter
  @AllArgsConstructor
  public enum ReqType {
    /** 外部api竞价回调 */
    RTB_CALLBACK("rtbCallback"),
    /** 激励视频奖励验证 */
    SERVER_SIDE_VERIFICATION("ssv"),

    /** toBid激励视频奖励验证 */
    TO_BID_SSV("toBidSsv"),
    TO_BID_MMP("toBidMmp"),
    TO_BID_IMP("toBidImp"),
    /** track上报 */
    TRACK_REPORT("trackReport"),
    /** header bidding */
    HEADER_BIDDING("headerBidding");

    private final String name;
  }

  @Getter
  @AllArgsConstructor
  public enum CallbackResult {
    LOSE(0, "lose"),

    WIN(1, "win"),

    ERROR(2, "error");

    private final int code;
    private final String name;
  }

  /** oppo竞价成功回调接口参数 */
  @Getter
  @AllArgsConstructor
  public enum CallBackParam {
    /** 广告请求id */
    REQUEST_ID("rid"),
    /** 广告来源渠道 */
    AD_SOURCE_CHANNEL("cid"),
    /** 用户id(打点服务中消消乐使用) */
    USER_ID("uid"),
    /** 成交价格 */
    AUCTION_PRICE("ap"),
    /** sigmob媒体id */
    APP_ID("aid"),

    PLACEMENT_ID("pid"),
    /** api流量广告请求id */
    API_REQUEST_ID("arid"),
    /** api流量媒体id */
    API_APP_ID("aaid"),
    /** api流量媒体广告位id */
    API_PLACEMENT_ID("apid"),
    /** sigmob ssp出价 */
    PRICE_FOR_SSP("pfs"),
    /** sigmob开发者id */
    DEVELOPER_ID("devid"),
    API_TYPE("atype"),
    /** sigmob adx对接的三方dsp竞价成功通知 */
    DSP_NOTIFY_URL("durl"),
    SSP_ERROR_CODE("ec"),
    SETTLEMENT_MODE("sm"),
    HB_TYPE("hbt"),
    HB_PLATFORM("hbp"),
    VID("vid"),
    TRANS_AD_TYPE("tatype"),

    CUR("cur"),
    WIN_ADNID("winadnid"),
    HIGHEST_LOSS_PRICE("highestprice"),
    PRICE_FOR_SSP_ORI("pfsori"),

    /** 设备uid */
    DEVICE_UID("duid"),
    HAS_ENCODED_DEVICE_UID("haseduid"),
    TASK_ID("taskid"),

    /** 广告类型 */
    AD_TYPE("atp"),
    CURRENCY_RATE("cr"),
    OS_TYPE("ot"),
    AD_CREATIVE_ID("acid"),
    AD_DSP_PROTOCOL_TYPE("dpt"),
    // 标准ssp老协议ssp兼容sdk竞胜回调临时参数
    STANDARD_SSP_TEMP_WIN_URL("twurl"),
    // 标准ssp老协议ssp兼容sdk竞败回调临时参数
    STANDARD_SSP_TEMP_LOSE_URL("tlurl"),
    // adx中各个dsp的最大出价（max{  tba_bid_pirce,  bid_price }）
    ADX_MAX_PRICE("mp"),
    // 0-包装售卖；1-新售卖；2-Tobid Adx
    DSP_SELL_TYPE("dst"),

    ADX_ID("xid"),
    // dsp app信息在sigmob平台的id，数据库自增id
    DSP_APP_ID("daid"),
    DSP_API_APP_ID("daaid"),
    DSP_API_PLACEMENT_ID("dapid"),
    CALLBACK_DATA("cd"),
    // saas adx打点信息
    SAAS_ADX_POINT_DATA("saasp"),
    ;

    private final String name;
  }

  /** 服务端验证接口参数 */
  @Getter
  @AllArgsConstructor
  public enum SsvParam {
    /** 广告请求id */
    REQUEST_ID("rid"),
    /** 媒体应用id */
    APP_ID("aid"),
    /** 媒体广告位id */
    PLACEMENT_ID("pid"),
    /** UserID */
    USER_ID("uid"),
    /** loadID */
    LOAD_ID("loadId"),
    /** 开发者在平台填写的激励视频回调地址URL(encode后的值)。 */
    CALLBACK_URL("curl");

    private final String name;
  }

  /** 媒体服务端宏参数 */
  @Getter
  @AllArgsConstructor
  public enum ToBidThirdPartySsvParam {
    /** UserID */
    USER_ID("{{USER_ID}}"),
    /** loadID */
    LOAD_ID("{{TRANS_ID}}"),
    /** 奖励数量 */
    REWARD_AMOUNT("{{REWARD_AMOUNT}}"),
    /** 奖励的名称 */
    REWARD_NAME("{{REWARD_NAME}}"),
    /** sign */
    SIGN("{{SIGN}}"),
    /** extra */
    EXTRA_INFO("{{EXTRAINFO}}"),
    /** 聚合广告位id */
    PLACEMENT_ID("{{PLACEMENT_ID}}"),
    /** CHANNEL */
    CHANNEL_ID("{{NETWORK_ID}}"),
    /** thirdPlacementId */
    THIRD_PLACEMENT_ID("{{AGGR_PLACEMENT_ID}}"),
    THIRD_REAL_PLACEMENT_ID("{{ADN_PLACEMENT_ID}}"),
    THIRD_TRANS_ID("{{THIRD_TRANS_ID}}"),
    PRICE("{{PRICE}}"),
    THIRD_AD_TYPE("{{NETWORK_AD_TYPE}}"),
    REWARD_TIMING("{{REWARD_TIMING}}"),
    THIRD_CODE_PRICE("{{THIRD_CODE_PRICE}}"),
    APP_VERSION("{{APP_VERSION}}"),
    SDK_VERSION("{{SDK_VERSION}}"),
    ;

    private final String name;
  }

  /** 302接口参数 */
  @Getter
  @AllArgsConstructor
  public enum RedirectParam {

    /** 广告计划id */
    CAMPAIGN_ID("a1"),

    /** 广告请求id */
    REQUEST_ID("a2"),
    /** 操作系统 */
    OS("a3"),

    /** sigmob媒体id */
    APP_ID("a4"),

    /** sigmob媒体广告位id */
    SLOT_ID("a5"),

    /** 广告落地页链接 */
    LANDING_PAGE("sigmobu"),
    /** 触发时间戳 */
    TIMESTAMP("a6");

    private final String name;
  }

  @Getter
  @AllArgsConstructor
  public enum SsvCheckType {
    /** BLOCK回调 */
    BLOCK("1"),
    /** SKIP验证 */
    SKIP("2"),
    ;

    private final String type;
  }

  @Getter
  @AllArgsConstructor
  public enum SsvCategory {
    /** start */
    START("start"),
    /** reward */
    REWARD("reward"),
    ;

    private final String type;
  }
}
