package com.sigmob.ad.rtb.callback.handler;

import com.google.common.base.Strings;
import com.sigmob.ad.core.config.settingconfig.SettingConfig;
import com.sigmob.ad.core.constants.WindmillConstants;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SspBusinessException;
import com.sigmob.ad.core.security.crypto.AesUtils;
import com.sigmob.ad.core.util.ByteUtil;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import com.sigmob.ad.rtb.callback.util.RequestUtil;
import com.twofishes.config.manager.ConfigManager;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;
import static org.springframework.web.reactive.function.server.ServerResponse.status;

/**
 * <AUTHOR>
 */
public class CallbackBaseHandler {

  /** 参数错误响应 */
  protected static final Mono<ServerResponse> BAD_REQUEST_RESPONSE =
      status(HttpStatus.BAD_REQUEST).build();

  /** 服务内部错误响应 */
  protected static final Mono<ServerResponse> INTERNAL_SERVER_ERROR_RESPONSE =
      status(HttpStatus.INTERNAL_SERVER_ERROR).build();

  /** 正常响应 */
  protected static final Mono<ServerResponse> OK_RESPONSE = ok().build();

  @Nullable
  public static String getBase64Value(String uid, boolean isBase64) {
    if (!isBase64) {
      return uid;
    }
    return StringUtils.isEmpty(uid) || "null".equals(uid) || "0".equals(uid)
        ? uid
        : new String(org.apache.commons.codec.binary.Base64.decodeBase64(uid));
  }

  @NotNull
  public Mono<ToBidRvCallbackRequest> getClazzMonoFromAes(
      boolean hasGcm,
      String aesHeaderGcm,
      boolean supportAesCbc,
      ServerRequest request,
      String sdkVersion) {
    return request
        .bodyToMono(byte[].class)
        .switchIfEmpty(Mono.just(new byte[0]))
        .defaultIfEmpty(new byte[0])
        .flatMap(
            req -> {
              int length = req.length;
              if (length == 0) {
                return Mono.error(
                    Exceptions.propagate(
                        new SspBusinessException(
                            ErrorCode.SERVER_ERROR_PB_ERROR, "request body empty")));
              }
              String extBodyString = StringUtils.EMPTY;

              byte[] decrypt;
              if (supportAesCbc) {
                // 截取byte数组
                byte[] first32Bytes = Arrays.copyOf(req, 32);
                // 偏移回去
                byte[] caesar = ByteUtil.caesarDecrypt(first32Bytes);

                byte[] iv = ByteUtil.getIv(caesar, 0);

                byte[] newKey = getAesCbcKey(request.path(), sdkVersion);
                // 填充三个新字节
                ByteUtil.appendRandomAesKey(newKey, caesar);

                int value = ByteUtil.byteToInt(Arrays.copyOfRange(req, 32, 36));

                // 确认是整体加密
                byte[] body = Arrays.copyOfRange(req, 36, req.length);
                byte[] allBody = AesUtils.decryptByCbcWithCs5(newKey, iv, body);

                // 只有真正的pb部分
                decrypt = Arrays.copyOfRange(allBody, value, allBody.length);

                // 扩展信息
                byte[] extBody = Arrays.copyOfRange(allBody, 0, value);
                extBodyString = new String(extBody);
              } else {
                decrypt =
                    hasGcm
                        ? AesUtils.decryptWithGcm(
                            AesUtils.AES_CRYPTO_KEY,
                            req,
                            Base64.getDecoder()
                                .decode(aesHeaderGcm.getBytes(StandardCharsets.UTF_8)))
                        : AesUtils.decryptByEcbWithCs5(AesUtils.AES_CRYPTO_KEY, req);
              }

              return getToBidRvCallbackRequestMono(req, aesHeaderGcm, decrypt, extBodyString);
            });
  }

  private static @NotNull Mono<ToBidRvCallbackRequest> getToBidRvCallbackRequestMono(
      byte[] oriBody, String aesHeaderGcm, byte[] decrypt, String extBodyString) {
    if (decrypt == null || decrypt.length == 0) {
      return Mono.error(
          Exceptions.propagate(
              new SspBusinessException(
                  ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR,
                  "decrypt error, req body:"
                      + new String(Base64.getEncoder().encode((oriBody)))
                      + " ; gcm value:"
                      + aesHeaderGcm)));
    }
    String s = new String(decrypt);
    var clazz = JsonSerializationUtils.jsonToObj(s, ToBidRvCallbackRequest.class);
    if (null == clazz) {
      return Mono.error(
          Exceptions.propagate(
              new SspBusinessException(
                  ErrorCode.REQUEST_ERROR_INVALID_VALUE,
                  "invalid request data, req body:"
                      + new String(Base64.getEncoder().encode(oriBody))
                      + " ; gcm value:"
                      + aesHeaderGcm)));
    }

    // 设置扩展信息
    clazz.setExtBody(extBodyString);
    return Mono.just(clazz);
  }

  private static byte[] getAesCbcKey(String path, String sdkVersion) {
    SettingConfig settingConfig = ConfigManager.get(SettingConfig.class);
    HashMap<String, String> aesKey = settingConfig.getAesKey();
    String key;
    if (StringUtils.isNotEmpty(sdkVersion)
        && WindmillConstants.VER_4_2_22.getVersionStr().equals(sdkVersion)) {
      key = "/imp";
    } else {
      // aes key
      key = path.startsWith("/t/i") ? "/imp" : "/reward";
    }
    return aesKey.get(key).getBytes();
  }

  @NotNull
  public Mono<ToBidRvCallbackRequest> getToBidRvCallbackRequestMono(
      @NotNull ServerRequest request, String sdkVersion) {
    String aesHeaderEcb = RequestUtil.getAesHeaderEcb(request);
    String aesHeaderGcm = RequestUtil.getAesHeaderGcm(request);
    String aesHeaderCbc = RequestUtil.getAesHeaderCbc(request);
    boolean hasAes = StringUtils.isNotBlank(aesHeaderEcb);
    boolean hasGcm = StringUtils.isNotBlank(aesHeaderGcm);
    boolean supportAesCbc = StringUtils.isNotBlank(aesHeaderCbc);

    return (hasAes || hasGcm || supportAesCbc)
        ? getClazzMonoFromAes(hasGcm, aesHeaderGcm, supportAesCbc, request, sdkVersion)
        : request.bodyToMono(ToBidRvCallbackRequest.class);
  }

  public final String getClientIp(ServerHttpRequest request) {

    HttpHeaders httpHeaders = request.getHeaders();
    String ip = httpHeaders.getFirst("x-forwarded-for");
    if (!DeviceUtil.isValidIp(ip)) {
      ip = httpHeaders.getFirst("Proxy-Client-IP");
    }
    if (!DeviceUtil.isValidIp(ip)) {
      ip = httpHeaders.getFirst("WL-Proxy-Client-IP");
    }
    if (!DeviceUtil.isValidIp(ip)) {
      ip = request.getHeaders().getFirst("HTTP_CLIENT_IP");
    }
    if (!DeviceUtil.isValidIp(ip)) {
      ip = request.getHeaders().getFirst("HTTP_X_FORWARDED_FOR");
    }
    if (!DeviceUtil.isValidIp(ip)) {
      InetSocketAddress remoteAddress = request.getRemoteAddress();
      if (null != remoteAddress) {
        ip = remoteAddress.getAddress().getHostAddress();
      }
    }
    return !Strings.isNullOrEmpty(ip) && ip.indexOf(',') > 0 ? ip.split(",")[0].trim() : ip;
  }
}
