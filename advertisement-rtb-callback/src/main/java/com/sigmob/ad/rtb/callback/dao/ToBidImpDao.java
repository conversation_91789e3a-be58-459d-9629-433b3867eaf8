package com.sigmob.ad.rtb.callback.dao;

import com.sigmob.ad.core.constants.RedisConstants;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;

import java.time.Duration;
import java.util.Optional;

import com.sigmob.ad.rtb.callback.domain.ToBidRvCallbackRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * imp 相关信息
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class ToBidImpDao {

  private final ReactiveStringRedisTemplate toBidImpStringRedisTemplate;

  private String generateKey(String loadId) {
    return RedisConstants.WINDMILL_IMP + loadId;
  }

  private String generateRewardKey(String loadId) {
    return RedisConstants.WINDMILL_REWARD + loadId;
  }

  /**
   * 检查loadId是否缺席 <a>
   *
   * <p></a> redis> SETNX mykey "Hello" (整数) 1 redis> SETNX mykey "World" (整数) 0 redis> GET mykey
   * "Hello" redis>
   */
  public Mono<Boolean> setMaskInfoIfAbsent(String loadId, ToBidRvCallbackRequest mask) {

    String key = generateKey(loadId);
    return toBidImpStringRedisTemplate
        .opsForValue()
        .setIfAbsent(key, JsonSerializationUtils.objToJson(mask), Duration.ofDays(1))
        .defaultIfEmpty(Boolean.TRUE)
        .switchIfEmpty(Mono.just(Boolean.TRUE))
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "setMaskInfoIfAbsent error, key:" + key + ", errors:" + e.getMessage(), e);
              return Mono.just(Boolean.TRUE);
            });
  }

  public Mono<Optional<ToBidRvCallbackRequest>> getMaskInfo(String loadId) {
    String key = generateKey(loadId);
    return toBidImpStringRedisTemplate
        .opsForValue()
        .get(key)
        .defaultIfEmpty(StringUtils.EMPTY)
        .switchIfEmpty(Mono.just(StringUtils.EMPTY))
        .map(
            r -> {
              if (StringUtils.isEmpty(r)) {
                return Optional.<ToBidRvCallbackRequest>empty();
              } else {
                ToBidRvCallbackRequest toBidRvCallbackRequest =
                    JsonSerializationUtils.jsonToObj(r, ToBidRvCallbackRequest.class);
                return Optional.ofNullable(toBidRvCallbackRequest);
              }
            })
        .onErrorResume(
            e -> {
              LogUtil.localError("getMaskInfo error, key:" + key + ", errors:" + e.getMessage(), e);
              return Mono.just(Optional.empty());
            });
  }

  /**
   * redis> SETNX mykey "Hello" (整数) 1 redis> SETNX mykey "World" (整数) 0 redis> GET mykey "Hello"
   * redis>
   */
  public Mono<Boolean> checkReward(String loadId) {

    String key = generateRewardKey(loadId);
    return toBidImpStringRedisTemplate
        .opsForValue()
        .setIfAbsent(key, "1", Duration.ofDays(1))
        .defaultIfEmpty(Boolean.TRUE)
        .switchIfEmpty(Mono.just(Boolean.TRUE))
        .onErrorResume(
            e -> {
              LogUtil.localError("checkReward error, key:" + key + ", errors:" + e.getMessage(), e);
              return Mono.just(Boolean.TRUE);
            });
  }
}
