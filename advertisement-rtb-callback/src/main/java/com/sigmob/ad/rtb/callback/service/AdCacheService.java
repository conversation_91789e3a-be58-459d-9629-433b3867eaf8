package com.sigmob.ad.rtb.callback.service;

import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.rtb.callback.dao.CallbackAdCacheDao;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 广告缓存服务
 *
 * <AUTHOR> @Date 2021/6/22 3:41 下午 @Version 1.0 @Description
 */
@Service
@RequiredArgsConstructor
public class AdCacheService {

  private final CallbackAdCacheDao adCacheDao;

  /**
   * @param requestId
   * @return
   */
  public Mono<Long> deleteAdCache(String requestId) {
    return adCacheDao
        .deleteAdCache(requestId)
        .onErrorResume(
            e -> {
              RtbLogUtil.hbError("delete ad cache for requestId({}) error", requestId, e);
              return Mono.just(0L);
            });
  }

  /**
   * @param requestId
   * @param expireTimeInSecond
   * @return
   */
  public Mono<Boolean> expireAdCache(String requestId, int expireTimeInSecond) {
    return adCacheDao
        .expireAdCache(requestId, expireTimeInSecond)
        .onErrorResume(
            e -> {
              RtbLogUtil.hbError("expire ad cache for requestId({}) error", requestId, e);
              return Mono.just(Boolean.FALSE);
            });
  }

  /**
   * 查询缓存track
   *
   * @param requestId requestId
   * @return Mono<Optional<String>>
   */
  public Mono<Optional<String>> getCachedAdTrack(
      String requestId, String vid, int requestFlowType, String trackEventName) {
    return adCacheDao
        .getCachedTrack(requestFlowType, requestId, vid, trackEventName)
        .switchIfEmpty(Mono.just(Optional.empty()))
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "get cached track(request:{}, vid:{}, requestFlowType:{}, trackEventName:{}) error",
                  requestId,
                  vid,
                  requestFlowType,
                  trackEventName,
                  e);
              return Mono.just(Optional.empty());
            });
  }

  /** 设置需要过滤的广告 */
  public Mono<Boolean> setApiFilterAd(
      int requestFlowType, String dspId, String creativeId, int expireTimeInSecond) {
    return adCacheDao
        .setMediaFilterAd(requestFlowType, dspId, creativeId, expireTimeInSecond)
        .onErrorResume(
            e -> {
              String errorMsg =
                  "setApiFilterAd(requestFlowType:"
                      + requestFlowType
                      + ",dspId:"
                      + dspId
                      + ",creativeId:"
                      + creativeId
                      + ",expireTimeInSecond:"
                      + expireTimeInSecond
                      + ") error:"
                      + e.getMessage();
              LogUtil.localError(errorMsg);
              return Mono.just(Boolean.FALSE);
            });
  }
}
