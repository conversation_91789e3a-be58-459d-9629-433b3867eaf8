package com.sigmob.ad.rtb.callback.dao;

import com.google.common.base.Strings;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.RedisConstants;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.exception.SigmobRedisException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import java.time.Duration;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> @Date 2021/6/22 2:59 下午 @Version 1.0 @Description
 */
@Repository
@RequiredArgsConstructor
public class CallbackAdCacheDao {

  @NonNull private final ReactiveStringRedisTemplate adCacheStringRedisTemplate;

  private String generateKey(String requestId) {
    return RedisConstants.KEY_PREFIX_AD_CACHE + requestId;
  }

  private String generateApiFilterAdKey(int requestFlowType, String dspId, String creativeId) {
    return RedisConstants.KEY_PREFIX_API_FILTER_AD
        + requestFlowType
        + Constants.SYMBOL_UNDERSCORE
        + dspId
        + Constants.SYMBOL_UNDERSCORE
        + creativeId;
  }

  private String generateTrackKey(
      int requestFlowType, String requestId, String vid, String trackEventName) {
    return RedisConstants.KEY_PREFIX_AD_TRACK_CACHE
        + requestFlowType
        + Constants.SYMBOL_UNDERSCORE
        + requestId
        + Constants.SYMBOL_UNDERSCORE
        + vid
        + Constants.SYMBOL_UNDERSCORE
        + trackEventName;
  }

  /**
   * 删除广告缓存
   *
   * @param requestId
   * @return
   */
  @CircuitBreaker(name = "deleteAdCacheGrpc", fallbackMethod = "deleteAdCacheFallback")
  public Mono<Long> deleteAdCache(String requestId) {

    var key = generateKey(requestId);
    return adCacheStringRedisTemplate
        .delete(key)
        .onErrorMap(e -> new SigmobRedisException(ErrorCode.REDIS_WRITE_ERROR, e));
  }

  public Mono<Long> deleteAdCacheFallback(String requestId, CallNotPermittedException e) {
    return Mono.error(e);
  }

  @CircuitBreaker(name = "expireAdCacheGrpc", fallbackMethod = "expireAdCacheFallback")
  public Mono<Boolean> expireAdCache(String requestId, int expireTimeInSecond) {
    var key = generateKey(requestId);
    return adCacheStringRedisTemplate
        .expire(key, Duration.ofSeconds(expireTimeInSecond))
        .onErrorMap(e -> new SigmobRedisException(ErrorCode.REDIS_WRITE_ERROR, e));
  }

  public Mono<Boolean> expireAdCacheFallback(
      String requestId, int expireTimeInSecond, CallNotPermittedException e) {

    return Mono.error(e);
  }

  public Mono<Optional<String>> getCachedTrack(
      int requestFlowType, String requestId, String vid, String trackEventName) {
    String key = generateTrackKey(requestFlowType, requestId, vid, trackEventName);
    return adCacheStringRedisTemplate
        .opsForValue()
        .get(key)
        .map(
            result -> {
              if (Strings.isNullOrEmpty(result)) {
                return Optional.<String>empty();
              } else {
                return Optional.of(result);
              }
            })
        .defaultIfEmpty(Optional.empty())
        .onErrorMap(
            e -> {
              return new SigmobException(
                  ErrorCode.REDIS_READ_ERROR,
                  "get ad track cache error. key:" + key + ", error:" + e.getMessage(),
                  e);
            });
  }

  /** 设置需要过滤的广告 */
  @CircuitBreaker(name = "setMediaFilterAdGrpc", fallbackMethod = "setMediaFilterAdFallback")
  public Mono<Boolean> setMediaFilterAd(
      int requestFlowType, String dspId, String creativeId, int expireTimeInSecond) {
    var key = generateApiFilterAdKey(requestFlowType, dspId, creativeId);
    return adCacheStringRedisTemplate
        .opsForValue()
        .setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(expireTimeInSecond))
        .onErrorMap(e -> new SigmobRedisException(ErrorCode.REDIS_WRITE_ERROR, e));
  }

  public Mono<Boolean> setMediaFilterAdFallback(
      int requestFlowType,
      String dspId,
      String creativeId,
      int expireTimeInSecond,
      CallNotPermittedException e) {
    return Mono.error(e);
  }
}
