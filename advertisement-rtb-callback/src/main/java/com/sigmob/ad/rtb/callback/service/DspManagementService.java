package com.sigmob.ad.rtb.callback.service;

import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.dao.DspManagementDao;
import com.sigmob.ssp.pb.dspmanagement.DspApiAppInfo;
import com.sigmob.ssp.pb.dspmanagement.DspInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Optional;

/**
 * <AUTHOR> @Date 2024/12/23 19:16 @Description
 */
@Service
@AllArgsConstructor
public class DspManagementService {

  private final DspManagementDao dspManagementDao;

  /** dsp app信息 */
  public Mono<Optional<DspApiAppInfo>> getDspApiAppInfo(int dspId, int dspAppId) {
    return dspManagementDao
        .getDspApiInfo(dspId, dspAppId)
        .onErrorResume(
            SigmobException.class,
            e -> {
              LogUtil.localError(
                  "dsp:{} get dspApiAppInfo(dspAppId:{}) error:{}",
                  dspId,
                  dspAppId,
                  e.getMessage(),
                  e);
              return Mono.just(Optional.empty());
            });
  }

  public Mono<Optional<DspInfo>> getDspInfo(int dspId) {
    return dspManagementDao.getDspInfo(dspId).onErrorResume(e -> Mono.just(Optional.empty()));
  }
}
