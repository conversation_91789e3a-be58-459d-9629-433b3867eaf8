package com.sigmob.ad.rtb.callback.init.config;

import static org.springframework.web.reactive.function.server.RequestPredicates.*;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import com.sigmob.ad.rtb.callback.handler.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

@Configuration
public class RouterConfig {

  @Bean
  RouterFunction<ServerResponse> rtbRouterFunction(RtbCallbackHandler rtbCallbackHandler) {
    return nest(
        path("/rtb"),
        nest(
            accept(MediaType.ALL),
            route(GET("/callback/oppo/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/adxdata/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/standard/{result}"), rtbCallbackHandler::standardCallback)
                .andRoute(GET("/callback/ireader/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/moweather/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/dongqiudi/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/douguo/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/kingsoft/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/yidianzixun/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/vungle/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/maimai/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/huawei/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/dongman/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/ximalaya/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/sead/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/vivo/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/yueyou/{result}"), rtbCallbackHandler::callback)
                .andRoute(GET("/callback/yoyo/{result}"), rtbCallbackHandler::callback)
                .andRoute(HEAD("/callback/gromore/{result}"), rtbCallbackHandler::callback)));
  }

  @Bean
  RouterFunction<ServerResponse> ssvRouterFunction(SsvHandler ssvHandler) {
    return nest(
        path("/ssv"),
        nest(
            accept(MediaType.ALL).and(methods(HttpMethod.GET, HttpMethod.POST)),
            route(path("/rv"), ssvHandler::rvCallback)));
  }

  @Bean
  RouterFunction<ServerResponse> toBidRouterFunction(ToBidSsvHandler ssvHandler) {
    return nest(
        path("/tobid"),
        nest(
                accept(MediaType.APPLICATION_JSON).and(methods(HttpMethod.POST)),
                route(path("/rv").or(path("/r/{api}")), ssvHandler::rvCallback))
            .andRoute(path("/rv/test"), ssvHandler::test));
  }

  @Bean
  RouterFunction<ServerResponse> toBidMmpRouterFunction(ToBidMmpHandler mmpHandler) {
    return nest(
        path("/tobid"),
        nest(
            accept(MediaType.ALL).and(methods(HttpMethod.GET)),
            route(path("/mmp"), mmpHandler::mmpCallback)));
  }

  @Bean
  RouterFunction<ServerResponse> toBidImpRouterFunction(ToBidSsvImpHandler impHandler) {
    return nest(
        path("/t"),
        nest(
            accept(MediaType.APPLICATION_JSON).and(methods(HttpMethod.POST)),
            route(path("/m/abc").or(path("/i/{api}")), impHandler::impCallback)));
  }

  @Bean
  RouterFunction<ServerResponse> redirectFunction(RedirectHandler redirectHandler) {
    return nest(
        path("/r"),
        nest(
            accept(MediaType.ALL).and(methods(HttpMethod.GET, HttpMethod.POST)),
            route(path("/target"), redirectHandler::redirectTarget)));
  }

  @Bean
  RouterFunction<ServerResponse> hbRouterFunction(HeaderBiddingCallbackHandler hbCallbackHandler) {
    return nest(
        path("/hb"),
        nest(
            accept(MediaType.ALL).and(methods(HttpMethod.GET, HttpMethod.POST)),
            route(path("/{result}"), hbCallbackHandler::callback)));
  }

  @Bean
  RouterFunction<ServerResponse> trackReportRouterFunction(RedirectHandler redirectHandler) {
    return nest(
        path("/report"),
        nest(
            accept(MediaType.ALL).and(methods(HttpMethod.GET, HttpMethod.POST)),
            route(path("/at"), redirectHandler::reportTrack)));
  }
}
