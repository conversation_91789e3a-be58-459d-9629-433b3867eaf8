package com.sigmob.ad.rtb.callback.init.config;

import io.lettuce.core.ClientOptions;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;

/**
 * <AUTHOR> @Date 2021/6/24 4:01 下午 @Version 1.0 @Description
 */
@Configuration
@RequiredArgsConstructor
public class RedisConfig {

  @NonNull private final RedisProperties redisProperties;

  @Bean
  @Primary
  public GenericObjectPoolConfig redisPool() {
    RedisProperties.Pool pool = redisProperties.getLettuce().getPool();
    GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
    //        int coreSize = Runtime.getRuntime().availableProcessors();
    //    genericObjectPoolConfig.setMaxTotal(coreSize);
    //    genericObjectPoolConfig.setMaxIdle(coreSize);
    //    genericObjectPoolConfig.setMinIdle(coreSize);
    genericObjectPoolConfig.setMaxIdle(pool.getMaxIdle());
    genericObjectPoolConfig.setMinIdle(pool.getMinIdle());
    genericObjectPoolConfig.setMaxTotal(pool.getMaxActive());

    genericObjectPoolConfig.setTestOnCreate(true);
    //    genericObjectPoolConfig.setTestOnBorrow(true);
    genericObjectPoolConfig.setMaxWaitMillis(pool.getMaxWait().toMillis());
    return genericObjectPoolConfig;
  }

  /**
   * 配置settings数据源
   *
   * @return
   */
  @Bean("settingsRedisConfig")
  @Primary
  @ConfigurationProperties(prefix = "spring.settingsredis")
  public RedisStandaloneConfiguration settingsRedisConfig() {
    return new RedisStandaloneConfiguration();
  }

  /**
   * 配置realtime_data数据源
   *
   * @return
   */
  @Bean("adCacheRedisConfig")
  @ConfigurationProperties(prefix = "spring.adcacheredis")
  public RedisStandaloneConfiguration adCacheRedisConfig() {
    return new RedisStandaloneConfiguration();
  }

  @Bean("temporaryDataRedisConfig")
  @ConfigurationProperties(prefix = "spring.temporarydataredis")
  public RedisStandaloneConfiguration temporaryDataRedisConfig() {
    return new RedisStandaloneConfiguration();
  }

  /** ssp & adx 控量数据源 */
  @Bean("adControlRedisConfig")
  @ConditionalOnProperty(
      prefix = "spring.adcontroldataredis",
      name = "hostName",
      matchIfMissing = false)
  @ConfigurationProperties(prefix = "spring.adcontroldataredis")
  public RedisStandaloneConfiguration adControlRedisConfig() {
    return new RedisStandaloneConfiguration();
  }

  /** 聚合 数据源 */
  @Bean("toBidCacheRedis")
  @ConfigurationProperties(prefix = "spring.tobidcacheredis")
  public RedisStandaloneConfiguration tobidcacheredis() {
    return new RedisStandaloneConfiguration();
  }

  /** 聚合 IMP数据源 */
  @Bean("toBidImpRedis")
  @ConfigurationProperties(prefix = "spring.tobidimpcacheredis")
  public RedisStandaloneConfiguration toBidImpRedis() {
    return new RedisStandaloneConfiguration();
  }

  @Bean("settingsFactory")
  @Primary
  public LettuceConnectionFactory settingsFactory(
      GenericObjectPoolConfig config,
      @Qualifier("settingsRedisConfig") RedisStandaloneConfiguration settingsRedisConfig) {
    return createLettuceConnectionFactory(config, settingsRedisConfig);
  }

  @Bean("adCacheDataFactory")
  public LettuceConnectionFactory adCacheDataFactory(
      GenericObjectPoolConfig config,
      @Qualifier("adCacheRedisConfig") RedisStandaloneConfiguration adCacheRedisConfig) {
    return createLettuceConnectionFactory(config, adCacheRedisConfig);
  }

  @Bean("temporaryDataFactory")
  public LettuceConnectionFactory temporaryDataFactory(
      GenericObjectPoolConfig config,
      @Qualifier("temporaryDataRedisConfig")
          RedisStandaloneConfiguration temporaryDataRedisConfig) {
    return createLettuceConnectionFactory(config, temporaryDataRedisConfig);
  }

  @Bean("adControlDataFactory")
  @ConditionalOnProperty(
      prefix = "spring.adcontroldataredis",
      name = "hostName",
      matchIfMissing = false)
  public LettuceConnectionFactory adControlDataFactory(
      GenericObjectPoolConfig config,
      @Qualifier("adControlRedisConfig") RedisStandaloneConfiguration adControlRedisConfig) {
    return createLettuceConnectionFactory(config, adControlRedisConfig);
  }

  @Bean("toBidDataFactory")
  public LettuceConnectionFactory toBidDataFactory(
      GenericObjectPoolConfig config,
      @Qualifier("toBidCacheRedis") RedisStandaloneConfiguration toBidCacheRedis) {
    return createLettuceConnectionFactory(config, toBidCacheRedis);
  }

  @Bean("toBidImpDataFactory")
  public LettuceConnectionFactory toBidImpDataFactory(
      GenericObjectPoolConfig config,
      @Qualifier("toBidImpRedis") RedisStandaloneConfiguration toBidImpRedis) {
    return createLettuceConnectionFactory(config, toBidImpRedis);
  }

  private LettuceConnectionFactory createLettuceConnectionFactory(
      GenericObjectPoolConfig config, RedisStandaloneConfiguration redisConfig) {
    LettuceClientConfiguration clientConfiguration =
        LettucePoolingClientConfiguration.builder()
            .commandTimeout(redisProperties.getTimeout())
            .poolConfig(config)
            .clientOptions(ClientOptions.builder().publishOnScheduler(true).build())
            .build();
    if (redisProperties.isSsl()) {
      clientConfiguration.isUseSsl();
    }
    LettuceConnectionFactory lettuceConnectionFactory =
        new LettuceConnectionFactory(redisConfig, clientConfiguration);
    //    lettuceConnectionFactory.setShareNativeConnection(Boolean.FALSE);
    lettuceConnectionFactory.setEagerInitialization(Boolean.TRUE);
    return lettuceConnectionFactory;
  }

  @Bean("androidUidStringRedisTemplate")
  public ReactiveStringRedisTemplate androidUidStringRedisTemplate(
      @Qualifier("settingsFactory") LettuceConnectionFactory redisConnectionFactory) {

    return new ReactiveStringRedisTemplate(redisConnectionFactory);
  }

  @Bean("settingsStringRedisTemplate")
  public ReactiveStringRedisTemplate settingsStringRedisTemplate(
      @Qualifier("settingsFactory") LettuceConnectionFactory redisConnectionFactory) {
    return new ReactiveStringRedisTemplate(redisConnectionFactory);
  }

  @Bean("runtimeDataStringRedisTemplate")
  public ReactiveStringRedisTemplate runtimeDataStringRedisTemplate(
      @Qualifier("settingsFactory") LettuceConnectionFactory redisConnectionFactory) {
    return new ReactiveStringRedisTemplate(redisConnectionFactory);
  }

  @Bean("realtimeDataStringRedisTemplate")
  public ReactiveStringRedisTemplate realtimeDataStringRedisTemplate(
      @Qualifier("settingsFactory") LettuceConnectionFactory redisConnectionFactory) {
    return new ReactiveStringRedisTemplate(redisConnectionFactory);
  }

  @Bean("adCacheStringRedisTemplate")
  public ReactiveStringRedisTemplate adCacheStringRedisTemplate(
      @Qualifier("adCacheDataFactory") LettuceConnectionFactory redisConnectionFactory) {
    return new ReactiveStringRedisTemplate(redisConnectionFactory);
  }

  @Bean("temporaryDataStringRedisTemplate")
  public ReactiveStringRedisTemplate temporaryDataStringRedisTemplate(
      @Qualifier("temporaryDataFactory") LettuceConnectionFactory temporaryDataFactory) {
    return new ReactiveStringRedisTemplate(temporaryDataFactory);
  }

  @Bean("adControlDataStringRedisTemplate")
  @ConditionalOnProperty(
      prefix = "spring.adcontroldataredis",
      name = "hostName",
      matchIfMissing = false)
  public ReactiveStringRedisTemplate adControlDataStringRedisTemplate(
      @Qualifier("adControlDataFactory") LettuceConnectionFactory lettuceConnectionFactory) {
    return new ReactiveStringRedisTemplate(lettuceConnectionFactory);
  }

  @Bean("toBidCacheStringRedisTemplate")
  public ReactiveStringRedisTemplate toBidCacheStringRedisTemplate(
      @Qualifier("toBidDataFactory") LettuceConnectionFactory toBidDataFactory) {
    return new ReactiveStringRedisTemplate(toBidDataFactory);
  }

  @Bean("toBidImpStringRedisTemplate")
  public ReactiveStringRedisTemplate toBidImpStringRedisTemplate(
      @Qualifier("toBidImpDataFactory") LettuceConnectionFactory toBidImpDataFactory) {
    return new ReactiveStringRedisTemplate(toBidImpDataFactory);
  }
}
