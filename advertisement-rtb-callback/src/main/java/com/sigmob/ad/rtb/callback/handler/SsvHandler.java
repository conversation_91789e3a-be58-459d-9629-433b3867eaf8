package com.sigmob.ad.rtb.callback.handler;

import static org.springframework.web.reactive.function.server.ServerResponse.ok;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.enums.SsvUrlParam;
import com.sigmob.ad.rtb.callback.datalog.Ac582MessageBuilder;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.exception.DspBusinessException;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SsvException;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.core.util.SsvUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.dao.SsvRiskyDao;
import com.sigmob.ad.rtb.callback.domain.SsvCallbackResponse;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.Exceptions;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/** 服务端验证 */
@Component
@RequiredArgsConstructor
public class SsvHandler extends CallbackBaseHandler {

  @NonNull private final WebClient webClient;
  @NonNull private final SsvRiskyDao ssvRiskyDao;
  @NonNull private final DataLogService dataLogService;

  /** 激励视频奖励服务端验证回调 */
  public Mono<ServerResponse> rvCallback(ServerRequest request) {

    ServerHttpRequest httpRequest = request.exchange().getRequest();
    String clientIp = getClientIp(httpRequest);

    String uri = httpRequest.getURI().toString();
    var requestId =
        request
            .queryParam(RtbCallbackConstants.SsvParam.REQUEST_ID.getName())
            .orElse(StringUtils.EMPTY);
    var appIdStr =
        request
            .queryParam(RtbCallbackConstants.SsvParam.APP_ID.getName())
            .orElse(StringUtils.EMPTY);
    int appId = NumberUtils.toInt(appIdStr);

    var placementId =
        request
            .queryParam(RtbCallbackConstants.SsvParam.PLACEMENT_ID.getName())
            .orElse(StringUtils.EMPTY);
    var userId =
        request
            .queryParam(RtbCallbackConstants.SsvParam.USER_ID.getName())
            .orElse(StringUtils.EMPTY);

    var token = request.queryParam(SsvUrlParam.TOKEN.getName()).orElse(StringUtils.EMPTY);

    var callbackUrl =
        request
            .queryParam(RtbCallbackConstants.SsvParam.CALLBACK_URL.getName())
            .orElse(StringUtils.EMPTY);

    if (!Strings.isNullOrEmpty(callbackUrl) && !callbackUrl.startsWith("http")) {
      try {
        callbackUrl =
            Constants.AES_CRYPTO.decryptWithUrlSafe(
                Constants.SSV_CALLBACK_URL_ENCRYPT_KEY, callbackUrl);
      } catch (Exception ignored) {
      }
    }
    final String finalCallbackUrl = callbackUrl;

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(12);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(16);

    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);
    extraLogInfos.put(LogUtil.LOG_FIELD_SSV_SERVER_TOKEN, token);

    extraLoginFiled.put(RtbCallbackConstants.SsvParam.REQUEST_ID.getName(), requestId);
    extraLoginFiled.put(RtbCallbackConstants.SsvParam.APP_ID.getName(), appIdStr);
    extraLoginFiled.put(RtbCallbackConstants.SsvParam.PLACEMENT_ID.getName(), placementId);
    extraLoginFiled.put(
        RtbCallbackConstants.REQ_TYPE,
        RtbCallbackConstants.ReqType.SERVER_SIDE_VERIFICATION.getName());
    extraLoginFiled.put(RtbCallbackConstants.SsvParam.USER_ID.getName(), userId);

    if (Strings.isNullOrEmpty(requestId)
        || 0 == appId
        || Strings.isNullOrEmpty(placementId)
        || Strings.isNullOrEmpty(callbackUrl)
        || Strings.isNullOrEmpty(token)) {

      var currentTimeMillis = System.currentTimeMillis();
      return badRequestResponse(
          requestId,
          appId,
          placementId,
          userId,
          clientIp,
          token,
          callbackUrl,
          currentTimeMillis,
          currentTimeMillis,
          extraLogInfos,
          extraLoginFiled);
    }

    // check token
    boolean verifyToken = SsvUtil.verifyToken(token, placementId, requestId);
    if (!verifyToken) {
      var currentTimeMillis = System.currentTimeMillis();
      extraLogInfos.put("verifyToken", "verifyToken error");
      return badRequestResponse(
          requestId,
          appId,
          placementId,
          userId,
          clientIp,
          token + "-verifyToken error",
          callbackUrl,
          currentTimeMillis,
          currentTimeMillis,
          extraLogInfos,
          extraLoginFiled);
    }
    extraLogInfos.put(RtbCallbackConstants.SsvParam.CALLBACK_URL.getName(), callbackUrl);

    final var sendCallbackTime = System.currentTimeMillis();

    URI callbackUri;
    try {
      callbackUri =
          UriComponentsBuilder.fromHttpUrl(callbackUrl)
              .encode(StandardCharsets.UTF_8)
              .build()
              .toUri();
    } catch (Exception e) {
      RtbLogUtil.ssvError("{} parse callback url:{} error!", requestId, callbackUrl, e);
      LogUtil.error(extraLogInfos, extraLoginFiled, e);
      sendEightyTwoDataLog(
          requestId,
          appId,
          placementId,
          userId,
          clientIp,
          callbackUrl,
          ErrorCode.SSV.REQUEST_CALLBACK_URL_ERROR.getCode(),
          sendCallbackTime,
          System.currentTimeMillis());
      return OK_RESPONSE;
    }

    String finalCallbackUrl1 = callbackUrl;
    return ssvRiskyDao
        .getSigRiskyUserId(userId, appId)
        .flatMap(
            risky -> {
              if (risky) {
                ErrorCode.ToBid riskyUser = ErrorCode.ToBid.RISKY_USER;
                int errCode = riskyUser.getCode();
                extraLoginFiled.put(LogUtil.LOG_FIELD_ERROR_CODE, String.valueOf(errCode));
                LogUtil.error(
                    extraLogInfos, extraLoginFiled, new SsvException(errCode, riskyUser.getMsg()));
                sendEightyTwoDataLog(
                    requestId,
                    appId,
                    placementId,
                    userId,
                    clientIp,
                    finalCallbackUrl1,
                    errCode,
                    sendCallbackTime,
                    System.currentTimeMillis());
                return OK_RESPONSE;
              }

              return callBackMediaServer(
                  callbackUri,
                  finalCallbackUrl,
                  requestId,
                  appIdStr,
                  placementId,
                  extraLogInfos,
                  extraLoginFiled,
                  userId,
                  clientIp,
                  sendCallbackTime,
                  appId);
            })
        .onErrorResume(
            Exception.class,
            e -> {
              RtbLogUtil.ssvError(
                  "request({}) callback url:{} error!", requestId, finalCallbackUrl, e);
              LogUtil.error(extraLogInfos, extraLoginFiled, e);
              sendEightyTwoDataLog(
                  requestId,
                  appId,
                  placementId,
                  userId,
                  clientIp,
                  finalCallbackUrl,
                  ErrorCode.SSV.REQUEST_CALLBACK_URL_ERROR.getCode(),
                  sendCallbackTime,
                  System.currentTimeMillis());
              return OK_RESPONSE;
            });
  }

  @NotNull
  private Mono<ServerResponse> callBackMediaServer(
      URI callbackUri,
      String finalCallbackUrl,
      String requestId,
      String appIdStr,
      String placementId,
      Map<String, Object> extraLogInfos,
      Map<String, String> extraLoginFiled,
      String userId,
      String clientIp,
      long sendCallbackTime,
      int appId) {
    return webClient
        .get()
        .uri(callbackUri)
        .acceptCharset(StandardCharsets.UTF_8)
        .exchangeToMono(
            clientResponse -> {
              HttpStatus httpStatusCode = clientResponse.statusCode();
              if (!httpStatusCode.equals(HttpStatus.OK)
                  && !httpStatusCode.equals(HttpStatus.NO_CONTENT)) {
                return clientResponse
                    .bodyToMono(String.class)
                    .defaultIfEmpty(StringUtils.EMPTY)
                    .switchIfEmpty(Mono.just(StringUtils.EMPTY))
                    .map(
                        reason -> {
                          int httpCode = clientResponse.rawStatusCode();
                          throw Exceptions.propagate(
                              new DspBusinessException(
                                  httpCode,
                                  "request "
                                      + finalCallbackUrl
                                      + " error, return http code:"
                                      + httpCode
                                      + ",requestId:"
                                      + requestId
                                      + ",reason:"
                                      + Strings.nullToEmpty(reason)
                                      + " ,appId:"
                                      + appIdStr
                                      + ",adSlotId:"
                                      + placementId));
                        });
              } else {
                return clientResponse
                    .bodyToMono(String.class)
                    .defaultIfEmpty(StringUtils.EMPTY)
                    .flatMap(
                        responseBody -> {
                          int callbackCode = ErrorCode.SSV.CALLBACK_RESPONSE_FALSE.getCode();
                          if (httpStatusCode.equals(HttpStatus.NO_CONTENT)) {
                            extraLogInfos.put(
                                LogUtil.LOG_FIELD_CALLBACK_RESULT, HttpStatus.NO_CONTENT.value());
                            callbackCode = 0;
                          } else {
                            extraLogInfos.put(LogUtil.LOG_FIELD_CALLBACK_RESULT, responseBody);
                            SsvCallbackResponse ssvResponse =
                                JsonSerializationUtils.jsonToObj(
                                    responseBody, SsvCallbackResponse.class);
                            if (null == ssvResponse || ssvResponse.getIsValid() == null) {
                              return Mono.error(
                                  new SsvException(
                                      ErrorCode.SSV.CALLBACK_RESPONSE_ERROR.getCode(),
                                      ErrorCode.SSV.CALLBACK_RESPONSE_ERROR.getMessage()));
                            }
                            if (ssvResponse.getIsValid()) {
                              callbackCode = 0;
                            }
                          }
                          LogUtil.info(extraLogInfos, extraLoginFiled);

                          long currentTimeMillis = System.currentTimeMillis();

                          sendEightyTwoDataLog(
                              requestId,
                              appId,
                              placementId,
                              userId,
                              clientIp,
                              finalCallbackUrl,
                              callbackCode,
                              sendCallbackTime,
                              currentTimeMillis);
                          // 31587 特殊处理
                          if (appId == 31587 || appId == 33372) {
                            return Mono.just(currentTimeMillis)
                                .delayElement(Duration.ofMillis(950))
                                .flatMap(r -> ok().body(BodyInserters.fromValue(r + 950)));
                          }

                          return ok().body(BodyInserters.fromValue(currentTimeMillis));
                        });
              }
            })
        .timeout(Duration.ofMillis(1000L))
        .doOnError(e -> RtbLogUtil.ssvWarn("request({}) {} error!", requestId, finalCallbackUrl, e))
        .retryWhen(Retry.fixedDelay(3, Duration.ofMillis(200)))
        .onErrorResume(
            Exception.class,
            e -> {
              RtbLogUtil.ssvError(
                  "request({}) callback url:{} error!", requestId, finalCallbackUrl, e);
              LogUtil.error(extraLogInfos, extraLoginFiled, e);
              sendEightyTwoDataLog(
                  requestId,
                  appId,
                  placementId,
                  userId,
                  clientIp,
                  finalCallbackUrl,
                  ErrorCode.SSV.REQUEST_CALLBACK_URL_ERROR.getCode(),
                  sendCallbackTime,
                  System.currentTimeMillis());
              return OK_RESPONSE;
            });
  }

  private Mono<ServerResponse> badRequestResponse(
      String requestId,
      Integer appId,
      String placementId,
      String userId,
      String clientIp,
      String token,
      String appCallbackUrl,
      long sendCallbackTime,
      long finishCallbackTime,
      Map<String, Object> extraLogInfos,
      Map<String, String> extraLogFields) {
    String errorMsg =
        "ssv param error! rid:"
            + requestId
            + ", aid:"
            + appId
            + ", pid:"
            + placementId
            + ", token:"
            + token
            + ", curl:"
            + appCallbackUrl;
    LogUtil.localError(errorMsg + " info:{}", JsonSerializationUtils.objToJson(extraLogInfos));
    LogUtil.error(extraLogInfos, extraLogFields, new SsvException(errorMsg));
    sendEightyTwoDataLog(
        requestId,
        appId,
        placementId,
        userId,
        clientIp,
        appCallbackUrl,
        ErrorCode.SSV.PARAM_ERROR.getCode(),
        sendCallbackTime,
        finishCallbackTime);
    return OK_RESPONSE;
  }

  /** 发送82号点打点日志 */
  private void sendEightyTwoDataLog(
      String requestId,
      Integer appId,
      String placementId,
      String userId,
      String clientIp,
      String appCallbackUrl,
      int callbackCode,
      long sendCallbackTime,
      long finishCallbackTime) {
    try {
      Ac582MessageBuilder messageBuilder =
          new Ac582MessageBuilder(
              requestId,
              appId,
              placementId,
              userId,
              clientIp,
              appCallbackUrl,
              callbackCode,
              sendCallbackTime,
              finishCallbackTime);
      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 582th Datalog error!", e);
    }
  }
}
