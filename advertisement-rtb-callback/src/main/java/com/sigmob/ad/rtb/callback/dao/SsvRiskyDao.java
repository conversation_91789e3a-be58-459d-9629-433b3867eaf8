package com.sigmob.ad.rtb.callback.dao;

import com.google.common.collect.Lists;
import com.sigmob.ad.core.constants.RedisConstants;
import com.sigmob.ad.core.util.JsonSerializationUtils;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.rtb.callback.domain.RiskyCode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * ssv风控
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SsvRiskyDao {

  private final ReactiveStringRedisTemplate toBidCacheStringRedisTemplate;

  private String generateSigRiskyKey(String userId, Integer appId) {
    return RedisConstants.SIG_RISKY_USER_ID + appId + RedisConstants.KEY_PREFIX_SEPARATOR + userId;
  }

  //  private String generateKey(String userId, Integer appId) {
  //    return RedisConstants.WINDMILL_RISKY_USER_ID
  //        + appId
  //        + RedisConstants.KEY_PREFIX_SEPARATOR
  //        + userId;
  //  }

  //  private String generateClientIpKey(Integer appId, String clientIp) {
  //    return RedisConstants.WINDMILL_RISKY_IP
  //        + appId
  //        + RedisConstants.KEY_PREFIX_SEPARATOR
  //        + clientIp;
  //  }

  private String generateRiskyModeKey(int cMode, Integer appId, String userId) {
    return RedisConstants.WINDMILL_RISKY_USER
        + appId
        + RedisConstants.KEY_PREFIX_SEPARATOR
        + cMode
        + RedisConstants.KEY_PREFIX_SEPARATOR
        + userId;
  }

    /**
     * 生成新版本风控用户key（不包含cmode）
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 新版本key: TBAC_L1_{appId}_{userId}
     */
  private String generateRiskyModeKeyV2(Integer appId, String userId) {
    return RedisConstants.WINDMILL_RISKY_USER
        + appId
        + RedisConstants.KEY_PREFIX_SEPARATOR
        + userId;
  }

  public Mono<Boolean> getSigRiskyUserId(String userId, Integer appId) {

    String key = generateSigRiskyKey(userId, appId);
    return toBidCacheStringRedisTemplate
        .hasKey(key)
        .defaultIfEmpty(Boolean.FALSE)
        .switchIfEmpty(Mono.just(Boolean.FALSE))
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "getSigRiskyUserId error, kes:" + key + ", errors:" + e.getMessage(), e);
              return Mono.just(Boolean.FALSE);
            });
  }

  public Mono<Optional<RiskyCode>> getZipRiskyUserId(
      String userId, Integer appId, String channelId) {

    // 一期：先检查新版本key，如果没有则检查老版本key（兼容模式）
    return getUserIdRiskyCodeByChannelIdV2(appId, channelId, userId)
        .flatMap(
            result -> {
              if (result.isPresent()) {
                if (result.get().rt() == -1) {
                  return Mono.just(Optional.empty());
                }

                return Mono.just(result);
              }

              // 兼容老模式：检查老版本的key
              Mono<Duration> durationMono1 = getRiskyUserIdTtl(appId, userId, 1);
              Mono<Duration> durationMono2 = getRiskyUserIdTtl(appId, userId, 2);

              return Mono.zip(durationMono1, durationMono2)
                  .flatMap(
                      res -> {
                        Duration duration1 = res.getT1();
                        Duration duration2 = res.getT2();
                        if (Duration.ZERO.equals(duration1) && Duration.ZERO.equals(duration2)) {
                          return Mono.just(Optional.empty());
                        }

                        if (duration1.compareTo(duration2) > 0) {
                          return getUserIdRiskyCodeByChannelId(appId, channelId, userId, 1);
                        } else {
                          return getUserIdRiskyCodeByChannelId(appId, channelId, userId, 2);
                        }
                      });
            });
  }

  //  public Mono<Boolean> getRiskyUserId(String userId, Integer appId) {
  //
  //    String key = generateKey(userId, appId);
  //    return toBidCacheStringRedisTemplate
  //        .hasKey(key)
  //        .defaultIfEmpty(Boolean.FALSE)
  //        .switchIfEmpty(Mono.just(Boolean.FALSE))
  //        .onErrorResume(
  //            e -> {
  //              LogUtil.localError(
  //                  "getRiskyUserId error, kes:" + key + ", errors:" + e.getMessage(), e);
  //              return Mono.just(Boolean.FALSE);
  //            });
  //  }

  //  public Mono<Boolean> getRiskyUserIdClientIp(Integer appId, String clientIp) {
  //
  //    String key = generateClientIpKey(appId, clientIp);
  //    return toBidCacheStringRedisTemplate
  //        .hasKey(key)
  //        .defaultIfEmpty(Boolean.FALSE)
  //        .switchIfEmpty(Mono.just(Boolean.FALSE))
  //        .onErrorResume(
  //            e -> {
  //              LogUtil.localError(
  //                  "getRiskyUserIdClientIp error, kes:" + key + ", errors:" + e.getMessage(), e);
  //              return Mono.just(Boolean.FALSE);
  //            });
  //  }

  public record RiskyUserIdChannelId(List<Integer> channels, Long ttl) {}

  /**
   * 新版本方法：从新格式的Redis key中获取风控信息（cmode作为hash字段） key：TBAC_L1_{appId}_{userId} hash结构：
   *
   * <pre>
   * {
   *   "101": {
   *     "channels": [ 9, 13 ],
   *     "ttl": 1748923200000
   *   },
   *   "103": {
   *     "channels": [ 13 ],
   *     "ttl": 1748923400000
   *   },
   *   "cmode": 1
   * }
   * </pre>
   */
  private Mono<Optional<RiskyCode>> getUserIdRiskyCodeByChannelIdV2(
      Integer appId, String channelIdStr, String userId) {
    Integer channelId = NumberUtils.toInt(channelIdStr);
    String key = generateRiskyModeKeyV2(appId, userId);
    return toBidCacheStringRedisTemplate
        .opsForHash()
        .entries(key)
        .collectMap(Map.Entry::getKey, Map.Entry::getValue)
        .map(
            result -> {
              if (MapUtils.isEmpty(result) || result.size() < 2) {
                return Optional.<RiskyCode>empty();
              }

              List<String> code = Lists.newArrayListWithCapacity(10);
              // 获取cmode
              var cmodeObj = result.get("cmode");
              int cMode;
              if (Objects.isNull(cmodeObj)
                  || (cMode = NumberUtils.toInt(cmodeObj.toString(), 0)) == 0) {
                return Optional.of(new RiskyCode(-1, code));
              }

              long currentTimeMillis = System.currentTimeMillis();
              result.forEach(
                  (k, value) -> {
                    // 跳过cmode字段，只处理风控码
                    if (!"cmode".equals(k) && value != null) {
                      var channelInfo =
                          JsonSerializationUtils.jsonToObj(
                              value.toString(), RiskyUserIdChannelId.class);
                      if (null != channelInfo
                          && CollectionUtils.isNotEmpty(channelInfo.channels)
                          && channelInfo.ttl > currentTimeMillis) {
                        if (channelInfo.channels.contains(0)
                            || channelInfo.channels.contains(channelId)) {
                          code.add((String) k);
                        }
                      }
                    }
                  });

              if (CollectionUtils.isEmpty(code)) {
                  return Optional.of(new RiskyCode(-1, code));
              }

              return Optional.of(new RiskyCode(cMode, code));
            })
        .defaultIfEmpty(Optional.empty())
        .switchIfEmpty(Mono.just(Optional.empty()))
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "getUserIdRiskyCodeByChannelIdV2 error, key:" + key + ", error:" + e.getMessage(),
                  e);
              return Mono.just(Optional.empty());
            });
  }

  /**
   * 老版本方法（兼容模式）：从老格式的Redis key中获取风控信息
   * val：<Hash>
   *
   * <p>{ 101: "{"channels": [9, 13], "ttl": 1748923200000}", 103: "{"channels": [13], "ttl":
   * 1748923400000}" }
   */
  private Mono<Optional<RiskyCode>> getUserIdRiskyCodeByChannelId(
      Integer appId, String channelIdStr, String userId, int cMode) {
    Integer channelId = NumberUtils.toInt(channelIdStr);
    String key = generateRiskyModeKey(cMode, appId, userId);
    return toBidCacheStringRedisTemplate
        .opsForHash()
        .entries(key)
        .collectMap(Map.Entry::getKey, Map.Entry::getValue)
        .map(
            result -> {
              List<String> code = Lists.newArrayListWithCapacity(10);
              if (MapUtils.isNotEmpty(result)) {
                long currentTimeMillis = System.currentTimeMillis();
                result.forEach(
                    (k, value) -> {
                      if (value != null) {
                        var channelInfo =
                            JsonSerializationUtils.jsonToObj(
                                value.toString(), RiskyUserIdChannelId.class);
                        if (null != channelInfo
                            && CollectionUtils.isNotEmpty(channelInfo.channels)
                            && channelInfo.ttl > currentTimeMillis) {
                          if (channelInfo.channels.contains(0)
                              || channelInfo.channels.contains(channelId)) {
                            code.add((String) k);
                          }
                        }
                      }
                    });
                if (CollectionUtils.isEmpty(code)) {
                  return Optional.<RiskyCode>empty();
                }

                return Optional.of(new RiskyCode(cMode, code));
              }

              return Optional.<RiskyCode>empty();
            })
        .defaultIfEmpty(Optional.empty())
        .switchIfEmpty(Mono.just(Optional.empty()))
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "getUserIdRiskyCodeByChannelId error, key:" + key + ", error:" + e.getMessage(),
                  e);
              return Mono.just(Optional.empty());
            });
  }

  public Mono<Duration> getRiskyUserIdTtl(Integer appId, String userId, int cMode) {

    String key = generateRiskyModeKey(cMode, appId, userId);
    return toBidCacheStringRedisTemplate
        .hasKey(key)
        .defaultIfEmpty(Boolean.FALSE)
        .switchIfEmpty(Mono.just(Boolean.FALSE))
        .flatMap(
            hasKey -> {
              if (!hasKey) {
                return Mono.just(Duration.ZERO);
              }
              return toBidCacheStringRedisTemplate
                  .getExpire(key)
                  .defaultIfEmpty(Duration.ZERO)
                  .switchIfEmpty(Mono.just(Duration.ZERO))
                  .onErrorResume(
                      e -> {
                        LogUtil.localError(
                            "getRiskyUserIdTtl error, key:" + key + ", error:" + e.getMessage(), e);
                        return Mono.just(Duration.ZERO);
                      });
            })
        .onErrorResume(
            e -> {
              LogUtil.localError(
                  "getRiskyUserIdTtl error, key:" + key + ", error:" + e.getMessage(), e);
              return Mono.just(Duration.ZERO);
            });
  }
}
