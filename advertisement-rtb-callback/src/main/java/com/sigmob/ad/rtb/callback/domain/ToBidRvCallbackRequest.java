package com.sigmob.ad.rtb.callback.domain;

import lombok.Data;

import javax.annotation.Nullable;
import java.io.Serializable;

/**
 * rv callback body
 *
 * <AUTHOR>
 */
@Data
public class ToBidRvCallbackRequest implements Serializable {

  private String loadId;
  private String placementId;
  private String userId;
  @Nullable private String custom;
  private String channelId;
  private String thirdAppId;
  private String thirdPlacementId;

  /** sdk 3.0.0支持 */
  @Nullable private Integer ecpm;

  /** sdk 3.0.1支持 */
  @Nullable private String thirdRealPlacementId;

  /** sdk 3.3.0支持 */
  @Nullable private String uid;

  /** sdk 3.3.0支持 */
  @Nullable private String thirdTransId;

  /** sdk 3.3.0支持 */
  @Nullable private String sdkVersion;

  /** sdk 3.9.0支持 */
  @Nullable private Integer rewardType;

  @Nullable private Integer networkAdType;

  @Nullable private String thirdCodePrice;

  @Nullable private String mask;

  private int checkMask;

  private long timestamp;

  /** 开发者应用版本号 （ SDK4.1.0版本支持） */
  private String gameVersion;

  /** sdk扩展信息 */
  private String extBody;
}
