package com.sigmob.ad.rtb.callback.handler;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.thirdparty.rtb.VivoConstants;
import com.sigmob.ad.core.datalog.Ac502MessageBuilder;
import com.sigmob.ad.core.datalog.DataLogService;
import com.sigmob.ad.core.exception.RtbCallbackException;
import com.sigmob.ad.core.util.GzipUtil;
import com.sigmob.ad.core.util.LogUtil;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants;
import com.sigmob.ad.rtb.callback.constants.RtbCallbackConstants.RedirectParam;
import com.sigmob.ad.rtb.callback.service.AdCacheService;
import com.sigmob.ad.rtb.callback.util.RtbLogUtil;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import static com.sigmob.ad.rtb.callback.handler.CallbackBaseHandler.*;

/**
 * <AUTHOR> @Date 2021/6/9 5:02 下午 @Version 1.0 @Description
 */
@Component
@AllArgsConstructor
public class RedirectHandler {

  private final WebClient webClient;

  private final DataLogService dataLogService;

  private final AdCacheService adCacheService;

  /**
   * @param request
   * @return
   */
  public Mono<ServerResponse> redirectTarget(ServerRequest request) {

    //    ServerHttpRequest httpRequest = request.exchange().getRequest();
    //    String uri = httpRequest.getURI().toString();

    var campaignId =
        request.queryParam(RedirectParam.CAMPAIGN_ID.getName()).orElse(StringUtils.EMPTY);
    var requestId =
        request.queryParam(RedirectParam.REQUEST_ID.getName()).orElse(StringUtils.EMPTY);
    var os = request.queryParam(RedirectParam.OS.getName()).orElse(StringUtils.EMPTY);
    var appId = request.queryParam(RedirectParam.APP_ID.getName()).orElse(StringUtils.EMPTY);
    var slotId = request.queryParam(RedirectParam.SLOT_ID.getName()).orElse(StringUtils.EMPTY);

    var redirectUrl =
        request.queryParam(RedirectParam.LANDING_PAGE.getName()).orElse(StringUtils.EMPTY);
    var timeStamp = request.queryParam(RedirectParam.TIMESTAMP.getName()).orElse(StringUtils.EMPTY);

    //    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(1);
    //    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(7);
    //
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_CAMPAIGN_ID, campaignId);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_REQUEST_ID, requestId);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_OS, os);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_APP_ID, appId);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_ADSLOT_ID, slotId);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_REDIRECT_URL, redirectUrl);
    //    extraLoginFiled.put(LogUtil.LOG_FIELD_TIMESTAMP, timeStamp);
    //    extraLogInfos.put(LogUtil.LOG_FIELD_REQ, uri);

    //    RtbLogUtil.redirectInfo(JsonSerializationUtils.jacksonObjToJson(extraLoginFiled));

    sendThreeOTwoDataLog(requestId, appId, slotId, campaignId, redirectUrl, timeStamp, os);

    URI redirectUri;

    try {
      redirectUri = URI.create(redirectUrl);
      return ServerResponse.status(HttpStatus.MOVED_TEMPORARILY).location(redirectUri).build();
    } catch (Exception e) {
      RtbLogUtil.redirectError("parse redirectUrl({}) error", redirectUrl, e);
      return ServerResponse.status(HttpStatus.BAD_REQUEST).build();
    }
  }

  /** 广告track服务端上报 */
  public Mono<ServerResponse> reportTrack(ServerRequest request) {

    var encryptParams = request.queryParam("c").orElse(StringUtils.EMPTY);

    Map<String, Object> extraLogInfos = Maps.newHashMapWithExpectedSize(23);
    Map<String, String> extraLoginFiled = Maps.newHashMapWithExpectedSize(7);

    extraLogInfos.put("c", encryptParams);

    if (Strings.isNullOrEmpty(encryptParams)) {
      LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException("请求c参数为空"));
      return BAD_REQUEST_RESPONSE;
    } else {
      var reportParam =
          Constants.AES_CRYPTO.decryptWithUrlSafe(
              VivoConstants.TRACK_REPORT_TRACK_PARAM_ENCRYPT_KEY, encryptParams);
      extraLogInfos.put("dc", reportParam);
      if (!Strings.isNullOrEmpty(reportParam)) {
        var reportParams = reportParam.split(Constants.SYMBOL_AND);
        if (reportParams.length == 4) {
          var requestId = reportParams[0];
          var vid = reportParams[1];
          var requestFlowType = reportParams[2];
          var trackEventType = reportParams[3];
          extraLoginFiled.put(
              RtbCallbackConstants.REQ_TYPE, RtbCallbackConstants.ReqType.TRACK_REPORT.getName());
          extraLoginFiled.put(RtbCallbackConstants.CallBackParam.REQUEST_ID.getName(), requestId);
          extraLoginFiled.put(RtbCallbackConstants.CallBackParam.VID.getName(), vid);
          extraLoginFiled.put("ftype", requestFlowType);
          extraLoginFiled.put("te", trackEventType);

          if (!Strings.isNullOrEmpty(requestId)
              && !Strings.isNullOrEmpty(vid)
              && NumberUtils.isDigits(requestFlowType)
              && !Strings.isNullOrEmpty(trackEventType)) {
            return adCacheService
                .getCachedAdTrack(requestId, vid, Integer.parseInt(requestFlowType), trackEventType)
                .flatMap(
                    result -> {
                      if (result.isPresent()) {
                        String encryptTrackString = result.get();
                        String trackString =
                            new String(
                                GzipUtil.decompress(
                                    Base64.getDecoder().decode(encryptTrackString), 4096));
                        String[] trackList =
                            trackString.split(Constants.MULTI_DSP_TRACK_URL_DELIMITER);
                        List<Mono<Boolean>> notifyUrlMono =
                            Lists.newArrayListWithCapacity(trackList.length);
                        for (String track : trackList) {

                          URI callbackUri = null;
                          try {
                            callbackUri =
                                UriComponentsBuilder.fromHttpUrl(track).build(true).toUri();
                          } catch (Exception e) {
                            RtbLogUtil.hbError(
                                "requestId:{} vid:{} parse track({}) url:{} error:{}",
                                requestId,
                                vid,
                                trackEventType,
                                track,
                                e.getMessage());
                            callbackUri =
                                UriComponentsBuilder.fromHttpUrl(track).build(false).toUri();
                          }

                          notifyUrlMono.add(
                              webClient
                                  .get()
                                  .uri(callbackUri)
                                  .acceptCharset(StandardCharsets.UTF_8)
                                  .exchangeToMono(
                                      clientResponse -> {
                                        var statusCode = clientResponse.rawStatusCode();
                                        if (statusCode == 200 || statusCode == 204) {
                                          return Constants.MONO_TRUE;
                                        } else {
                                          return Constants.MONO_FALSE;
                                        }
                                      })
                                  .doOnError(
                                      e ->
                                          LogUtil.localError(
                                              "requestId:{} vid:{} requestFlowType:{} request track({}):{} error!",
                                              requestId,
                                              vid,
                                              requestFlowType,
                                              trackEventType,
                                              track,
                                              e))
                                  .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(200)))
                                  .onErrorResume(Exception.class, e -> Mono.just(Boolean.FALSE)));
                        }
                        if (!CollectionUtils.isEmpty(notifyUrlMono)) {
                          return Mono.zip(
                                  notifyUrlMono,
                                  results -> {
                                    List<Integer> errorNotifyUrlIdxList =
                                        Lists.newArrayListWithCapacity(trackList.length);
                                    for (int i = 0; i < results.length; i++) {
                                      Boolean notifyResult = (Boolean) results[i];
                                      if (Boolean.FALSE.equals(notifyResult)) {
                                        errorNotifyUrlIdxList.add(i);
                                      }
                                    }
                                    if (!CollectionUtils.isEmpty(errorNotifyUrlIdxList)) {
                                      LogUtil.error(
                                          extraLogInfos,
                                          extraLoginFiled,
                                          new Exception(
                                              "request No."
                                                  + errorNotifyUrlIdxList
                                                  + " track url error"));
                                      return INTERNAL_SERVER_ERROR_RESPONSE;
                                    }
                                    LogUtil.info(extraLogInfos, extraLoginFiled);
                                    return OK_RESPONSE;
                                  })
                              .flatMap(r -> r);
                        }
                      }

                      // 未找到缓存track
                      LogUtil.warn(
                          extraLogInfos, extraLoginFiled, new RtbCallbackException("未找到缓存track"));
                      return BAD_REQUEST_RESPONSE;
                    })
                .onErrorResume(
                    e -> {
                      LogUtil.warn(
                          extraLogInfos,
                          extraLoginFiled,
                          new RtbCallbackException(
                              "处理track提交请求错误。requestId:"
                                  + requestId
                                  + ", vid:"
                                  + vid
                                  + ", requestFlowType:"
                                  + requestFlowType
                                  + ", trackEventType:"
                                  + trackEventType
                                  + ", error:"
                                  + e.getMessage()));
                      return INTERNAL_SERVER_ERROR_RESPONSE;
                    });

          } else {
            LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException("请求c参数解密后参数错误"));
            return BAD_REQUEST_RESPONSE;
          }
        } else {
          LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException("请求c参数解密后参数个数错误"));
          return BAD_REQUEST_RESPONSE;
        }
      } else {
        LogUtil.warn(extraLogInfos, extraLoginFiled, new RtbCallbackException("请求c参数解密错误"));
        return BAD_REQUEST_RESPONSE;
      }
    }
  }

  /**
   * 发送302号点打点日志
   *
   * @param requestId
   * @param appId
   * @param placementId
   * @param campaignId
   * @param redirectUrl
   * @param timestamp
   * @oaram os
   */
  private void sendThreeOTwoDataLog(
      String requestId,
      String appId,
      String placementId,
      String campaignId,
      String redirectUrl,
      String timestamp,
      String os) {
    try {
      Ac502MessageBuilder messageBuilder =
          new Ac502MessageBuilder(
              requestId, appId, placementId, campaignId, redirectUrl, timestamp, os);
      var message = messageBuilder.build();
      if (null == message) {
        return;
      }
      dataLogService.send(message);
    } catch (Exception e) {
      LogUtil.localError("send 302th Datalog error!", e);
    }
  }
}
