# ADX竞价系统完整流程图

## 基础流程图

```mermaid
flowchart TD
    subgraph "请求入口层"
        HB1[Header Bidding V1<br/>POST /hb/bid]
        HB2[Header Bidding V2<br/>POST /hb/v2/bid]
        WM1[Windmill 策略 V5<br/>POST /strategy/v5]
        WM2[Windmill 策略 V6<br/>POST /strategy/v6]
        WF1[Windmill 瀑布流 V1<br/>POST /waterfall/v1]
        WF2[Windmill 瀑布流 V2<br/>POST /waterfall/v2]
        TRAD["传统广告请求<br/>GET|POST /ad/rv/v2"]
    end

    subgraph "请求处理层"
        VAL[请求验证<br/>- 签名验证<br/>- 参数校验<br/>- 黑名单检查]
        FIL[请求过滤<br/>- 流量控制<br/>- 设备过滤<br/>- 应用过滤]
        PRE[请求预处理<br/>- 广告位映射<br/>- 设备信息处理<br/>- 用户画像构建]
    end

    subgraph "竞价执行层"
        TB[任务构建<br/>- 并发任务规划<br/>- DSP选择<br/>- 策略应用]
        EXEC[任务执行<br/>- 并发请求<br/>- 超时控制<br/>- 错误处理]
    end

    subgraph "DSP交互层"
        DSP1[百度DSP<br/>BaiduMobAds]
        DSP2[腾讯DSP<br/>Tencent]
        DSP3[华为DSP<br/>Huawei]
        DSP4[OPPO DSP<br/>OPPO]
        DSP5[字节DSP<br/>Toutiao]
        DSP6[快手DSP<br/>Kuaishou]
        DSP7[京东DSP<br/>JD]
        DSP8[其他30+DSP<br/>VIVO/iQIYI等]
    end

    subgraph "价格决策层"
        PC[价格计算<br/>- 底价检查<br/>- 返点计算<br/>- 汇率转换]
        SORT[价格排序<br/>- 按价格排序<br/>- 按权重排序<br/>- 按质量排序]
        SEL[广告选择<br/>- 最优价格选择<br/>- 多维度评估<br/>- 胜出者判定]
    end

    subgraph "响应处理层"
        ENC[价格加密<br/>- AES加密<br/>- RSA加密<br/>- DSP特定加密]
        RES[结果封装<br/>- 响应格式化<br/>- 追踪链接生成<br/>- 宏替换]
        CACHE[结果缓存<br/>- 缓存策略<br/>- 过期时间<br/>- 缓存更新]
    end

    %% 主要流程连接
    HB1 & HB2 --> VAL
    WM1 & WM2 --> VAL
    WF1 & WF2 --> VAL
    TRAD --> VAL

    VAL --> FIL
    FIL --> PRE
    PRE --> TB
    TB --> EXEC

    EXEC --> DSP1
    EXEC --> DSP2
    EXEC --> DSP3
    EXEC --> DSP4
    EXEC --> DSP5
    EXEC --> DSP6
    EXEC --> DSP7
    EXEC --> DSP8

    DSP1 & DSP2 & DSP3 & DSP4 & DSP5 & DSP6 & DSP7 & DSP8 --> PC
    PC --> SORT
    SORT --> SEL
    SEL --> ENC
    ENC --> RES
    RES --> CACHE

    %% 决策点
    subgraph "关键决策点"
        D1{请求类型判断}
        D2{交易类型选择}
        D3{执行模式选择}
        D4{价格策略选择}
        D5{广告选择策略}
    end

    D1 --> VAL
    VAL --> D2
    PRE --> D3
    PC --> D4
    SORT --> D5

    %% 交易类型分支
    subgraph "交易类型处理"
        RTB[RTB实时竞价]
        PDB[PDB程序化购买]
        PD[优选交易]
    end

    D2 --> RTB
    D2 --> PDB
    D2 --> PD

    %% 执行模式分支
    subgraph "执行模式"
        PAR[并发执行]
        SEQ[串行执行]
        MIX[混合执行]
    end

    D3 --> PAR
    D3 --> SEQ
    D3 --> MIX
```

## 详细执行流程图

```mermaid
flowchart TD
    subgraph "1. 请求接收与验证"
        A1[接收HTTP请求]
        A2[解析请求参数]
        A3[验证请求签名]
        A4[检查请求频率限制]
        A5[黑名单/白名单检查]
    end

    subgraph "2. 业务逻辑处理"
        B1[获取应用信息]
        B2[获取广告位信息]
        B3[获取SDK设置]
        B4[设备信息处理]
        B5[地理位置定位]
        B6[用户画像构建]
    end

    subgraph "3. 过滤与控制"
        C1[广告位过滤]
        C2[设备频率控制]
        C3[应用交互过滤]
        C4[合规性检查]
        C5[流量策略应用]
    end

    subgraph "4. 竞价策略执行"
        D1[确定交易类型]
        D2[选择DSP列表]
        D3[构建竞价任务]
        D4[执行并发请求]
        D5[处理超时和错误]
    end

    subgraph "5. 价格计算与决策"
        E1[收集DSP响应]
        E2[价格解密验证]
        E3[底价检查]
        E4[返点计算]
        E5[汇率转换]
        E6[价格排序]
        E7[最优广告选择]
    end

    subgraph "6. 响应构建与返回"
        F1[价格加密]
        F2[构建响应对象]
        F3[生成追踪链接]
        F4[宏替换处理]
        F5[结果缓存]
        F6[返回响应]
    end

    %% 流程连接
    A1 --> A2 --> A3 --> A4 --> A5
    A5 --> B1 --> B2 --> B3 --> B4 --> B5 --> B6
    B6 --> C1 --> C2 --> C3 --> C4 --> C5
    C5 --> D1 --> D2 --> D3 --> D4 --> D5
    D5 --> E1 --> E2 --> E3 --> E4 --> E5 --> E6 --> E7
    E7 --> F1 --> F2 --> F3 --> F4 --> F5 --> F6

    %% 决策分支
    subgraph "关键决策"
        D6{交易类型?}
        D7{执行模式?}
        D8{有无广告?}
        D9{价格是否达标?}
    end

    D1 --> D6
    D3 --> D7
    E7 --> D8
    E3 --> D9

    D6 -->|RTB| D4
    D6 -->|PDB| D10[PDB处理]
    D6 -->|PD| D11[优选处理]

    D7 -->|并发| D12[并发执行]
    D7 -->|串行| D13[串行执行]

    D8 -->|有广告| E7
    D8 -->|无广告| F7[返回无广告]

    D9 -->|达标| E4
    D9 -->|不达标| E14[过滤掉]
```

## DSP并发请求流程图

```mermaid
flowchart TD
    subgraph "任务构建阶段"
        A1[分析请求参数]
        A2[获取DSP配置]
        A3[筛选可用的DSP]
        A4[构建DSP请求任务]
        A5[设置超时参数]
    end

    subgraph "并发执行阶段"
        B1[启动并发请求]
        B2[同时请求多个DSP]
        B3[监控请求状态]
        B4[处理请求响应]
        B5[处理超时和异常]
    end

    subgraph "结果收集阶段"
        C1[收集有效响应]
        C2[验证响应格式]
        C3[解密价格信息]
        C4[过滤无效响应]
        C5[合并响应结果]
    end

    subgraph "决策处理阶段"
        D1[按价格排序]
        D2[应用权重策略]
        D3[选择最优广告]
        D4[生成最终结果]
        D5[记录竞价日志]
    end

    %% 流程连接
    A1 --> A2 --> A3 --> A4 --> A5
    A5 --> B1 --> B2 --> B3 --> B4 --> B5
    B5 --> C1 --> C2 --> C3 --> C4 --> C5
    C5 --> D1 --> D2 --> D3 --> D4 --> D5

    %% 并发处理示意
    subgraph "DSP并发请求"
        DSP1[百度DSP<br/>请求]
        DSP2[腾讯DSP<br/>请求]
        DSP3[华为DSP<br/>请求]
        DSP4[OPPO DSP<br/>请求]
        DSP5[字节DSP<br/>请求]
    end

    B2 --> DSP1
    B2 --> DSP2
    B2 --> DSP3
    B2 --> DSP4
    B2 --> DSP5

    DSP1 & DSP2 & DSP3 & DSP4 & DSP5 --> B4
```

## 价格计算流程图

```mermaid
flowchart TD
    subgraph "价格获取与验证"
        A1[获取DSP竞价价格]
        A2[解密价格信息]
        A3[验证价格格式]
        A4[检查价格有效性]
        A5[记录原始价格]
    end

    subgraph "价格调整计算"
        B1[应用底价策略]
        B2[计算返点金额]
        B3[汇率转换]
        B4[计算分成比例]
        B5[应用价格系数]
    end

    subgraph "价格策略应用"
        C1[动态底价检查]
        C2[竞争价格分析]
        C3[历史价格对比]
        C4[机器学习价格优化]
        C5[最终价格确定]
    end

    subgraph "价格输出处理"
        D1[价格排序]
        D2[选择最优价格]
        D3[价格加密]
        D4[价格信息缓存]
        D5[价格审计记录]
    end

    %% 流程连接
    A1 --> A2 --> A3 --> A4 --> A5
    A5 --> B1 --> B2 --> B3 --> B4 --> B5
    B5 --> C1 --> C2 --> C3 --> C4 --> C5
    C5 --> D1 --> D2 --> D3 --> D4 --> D5

    %% 决策点
    subgraph "价格决策"
        D6{价格是否<br/>高于底价?}
        D7{是否需要<br/>价格调整?}
        D8{价格是否<br/>最优?}
    end

    B1 --> D6
    B4 --> D7
    D1 --> D8

    D6 -->|是| B2
    D6 -->|否| F1[过滤掉]

    D7 -->|是| B5
    D7 -->|否| C1

    D8 -->|是| D3
    D8 -->|否| F2[继续寻找]
```

## 完整系统架构图

```mermaid
flowchart TD
    subgraph "外部接入层"
        SSP[SSP平台<br/>供应方平台]
        SDK[SDK接入<br/>移动应用]
        API[API接入<br/>第三方平台]
        HB[Header Bidding<br/>网页竞价]
    end

    subgraph "请求处理层"
        GW[网关服务<br/>负载均衡]
        VAL[验证服务<br/>请求校验]
        FIL[过滤服务<br/>流量控制]
        PRE[预处理服务<br/>数据准备]
    end

    subgraph "业务服务层"
        AD_SVC[广告服务<br/>AdService]
        WM_SVC[Windmill服务<br/>竞价聚合]
        SIG_SVC[Sigmob服务<br/>自有广告]
        CTRL_SVC[控制服务<br/>策略管理]
    end

    subgraph "竞价执行层"
        TASK[任务管理<br/>并发控制]
        DSP_MGR[DSP管理<br/>平台接入]
        RTB[RTB引擎<br/>实时竞价]
        PRICE[价格引擎<br/>价格计算]
    end

    subgraph "数据处理层"
        CACHE[缓存服务<br/>Redis]
        DB[数据库<br/>业务数据]
        LOG[日志服务<br/>数据记录]
        MONITOR[监控服务<br/>性能监控]
    end

    subgraph "DSP平台层"
        Baidu[百度移动DSP]
        Tencent[腾讯广告DSP]
        Huawei[华为广告DSP]
        OPPO[OPPO广告DSP]
        Byte[字节跳动DSP]
        Kuaishou[快手广告DSP]
        JD[京东广告DSP]
        Others[其他30+DSP]
    end

    %% 连接关系
    SSP & SDK & API & HB --> GW
    GW --> VAL --> FIL --> PRE
    PRE --> AD_SVC & WM_SVC & SIG_SVC
    AD_SVC --> CTRL_SVC
    WM_SVC --> CTRL_SVC
    SIG_SVC --> CTRL_SVC

    CTRL_SVC --> TASK
    TASK --> DSP_MGR
    DSP_MGR --> RTB
    RTB --> PRICE

    TASK & DSP_MGR & RTB & PRICE --> CACHE
    TASK & DSP_MGR & RTB & PRICE --> DB
    TASK & DSP_MGR & RTB & PRICE --> LOG
    TASK & DSP_MGR & RTB & PRICE --> MONITOR

    DSP_MGR --> Baidu
    DSP_MGR --> Tencent
    DSP_MGR --> Huawei
    DSP_MGR --> OPPO
    DSP_MGR --> Byte
    DSP_MGR --> Kuaishou
    DSP_MGR --> JD
    DSP_MGR --> Others
```

这个完整的ADX竞价系统流程图展示了从请求接收到响应返回的完整流程，包括：

1. **多入口支持**：支持Header Bidding、Windmill策略、传统广告请求等多种接入方式
2. **完整的处理链路**：从验证、过滤、预处理到竞价执行、价格决策、响应处理
3. **丰富的DSP生态**：集成了百度、腾讯、华为、OPPO等30+主流DSP平台
4. **复杂的价格计算**：包含底价检查、返点计算、汇率转换等多重价格策略
5. **高并发处理**：支持并发请求多个DSP，提高竞价成功率
6. **完善的决策机制**：通过多个决策点实现智能化的广告选择

整个系统设计遵循高并发、低延迟、高可用性的原则，能够处理大规模的广告竞价请求。