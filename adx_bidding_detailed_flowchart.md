# ADX竞价系统详细流程图

## 竞价核心逻辑详细流程

```mermaid
flowchart TD
    %% 样式定义
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef parallel fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dsp fill:#e8f5e9,stroke:#1b5e20,stroke-width:2px
    classDef endNode fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    %% 主要流程
    A[接收竞价请求] --> B{请求类型判断}
    
    %% 请求类型分支
    B -->|Header Bidding| C[HeaderBiddingV2Handler]
    B -->|Windmill策略| D[WindmillBiddingService]
    B -->|传统广告请求| E[传统AdHandler]
    
    %% 统一进入SigBiddingService
    C --> F[SigBiddingService]
    D --> F
    E --> F
    
    %% SigBiddingService处理流程
    F --> G[获取SDK配置]
    G --> H[区域过滤检查]
    H --> I{是否通过过滤?}
    
    I -->|否| J[返回过滤错误]
    I -->|是| K[尾流量控制]
    
    K --> L[设备信息处理]
    L --> M[获取应用信息]
    M --> N[MIIT合规检查]
    N --> O{是否合规?}
    
    O -->|否| P[返回合规错误]
    O -->|是| Q[调用AdService.getAdV2]
    
    %% AdService核心竞价逻辑
    Q --> R[获取AB实验配置]
    R --> S{执行模式判断}
    
    S -->|并行模式| T[构建并行任务]
    S -->|串行模式| U[构建串行任务]
    S -->|混合模式| V[根据实验选择]
    
    %% 任务构建
    T --> W{交易类型判断}
    U --> W
    V --> W
    
    W -->|RTB| X[DspRtbRequestTask]
    W -->|PDB| Y[DspPdbRequestTask]
    W -->|Deal| Z[DspDealRequestTask]
    W -->|网盟| AA[AdNetworkTask]
    
    %% 并行请求执行
    X --> BB[构建DSP请求列表]
    BB --> CC[过滤不可执行DSP]
    CC --> DD[创建并行任务Mono.zip]
    
    %% DSP并行请求
    DD --> EE[并行请求多个DSP]
    
    %% DSP请求详情
    subgraph DSP请求详情
        EE --> FF{百度DSP}
        EE --> GG{腾讯DSP}
        EE --> HH{华为DSP}
        EE --> II{OPPO DSP}
        EE --> JJ{字节DSP}
        EE --> KK{快手DSP}
        EE --> LL{其他DSP...}
        
        FF --> MM[发送RTB请求]
        GG --> MM
        HH --> MM
        II --> MM
        JJ --> MM
        KK --> MM
        LL --> MM
        
        MM --> NN[接收响应/超时]
        NN --> OO{是否成功响应?}
        
        OO -->|是| PP[解析BidResponse]
        OO -->|否| QQ[记录错误日志]
        
        PP --> RR[验证响应有效性]
        RR --> SS{是否有效?}
        
        SS -->|是| TT[收集有效响应]
        SS -->|否| QQ
    end
    
    %% 价格决策
    TT --> UU[收集所有DSP响应]
    UU --> VV{是否有有效响应?}
    
    VV -->|否| WW[返回无广告]
    VV -->|是| XX[价格过滤]
    
    %% 价格计算流程
    XX --> YY[过滤低于底价的出价]
    YY --> ZZ{剩余广告数量?}
    
    ZZ -->|0| WW
    ZZ -->|1| AAA[单DSP处理]
    ZZ -->|>1| BBB[多DSP价格排序]
    
    %% 单DSP处理
    AAA --> CCC[结算价=底价<br/>v3.2.0起]
    CCC --> DDD[设置获胜广告]
    
    %% 多DSP处理
    BBB --> EEE[按价格从高到低排序]
    EEE --> FFF[价格相同?]
    
    FFF -->|是| GGG[随机选择胜者]
    FFF -->|否| HHH[选择最高价]
    
    GGG --> III["结算价=min(最高价, 次高价+1分)"]
    HHH --> III
    
    %% 实验价格处理
    III --> JJJ{是否有价格实验?}
    
    JJJ -->|是| KKK[应用实验策略]
    KKK --> LLL[STRATEGY_002: 扣除利润]
    KKK --> MMM[STRATEGY_003: 价格映射]
    
    JJJ -->|否| NNN[正常价格处理]
    
    %% 响应构建
    DDD --> OOO[构建RtbResponse]
    LLL --> OOO
    MMM --> OOO
    NNN --> OOO
    
    OOO --> PPP[设置结算价格]
    PPP --> QQQ[价格加密处理]
    QQQ --> RRR[生成回调URL]
    RRR --> SSS[添加追踪参数]
    SSS --> TTT[返回竞价结果]
    
    %% 样式应用
    class A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA process
    class BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO,PP,QQ,RR,SS,TT,UU,VV,WW,XX,YY,ZZ,AAA,BBB,CCC,DDD,EEE,FFF,GGG,HHH,III,JJJ,KKK,LLL,MMM,NNN,OOO,PPP,QQQ,RRR,SSS,TTT process
    class H,I,N,O,S,V,W,ZZ,FFF,JJJ decision
    class DD,EE parallel
    class FF,GG,HH,II,JJ,KK,LL dsp
    class J,WW endNode
```

## 价格计算详细逻辑

```mermaid
flowchart LR
    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef output fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px

    %% 输入
    A[DSP原始出价] --> B[获取底价配置]
    C[设备信息] --> B
    D[广告位信息] --> B
    E[用户信息] --> B
    
    %% 底价计算
    B --> F[计算底价]
    F --> G{底价策略}
    
    G -->|动态底价| H[根据时段调整]
    G -->|固定底价| I[使用配置底价]
    G -->|个性化底价| J[根据用户画像调整]
    
    H --> K[最终底价]
    I --> K
    J --> K
    
    %% 价格过滤
    L[DSP响应列表] --> M[遍历每个响应]
    M --> N{出价 >= 底价?}
    
    N -->|否| O[丢弃该响应]
    N -->|是| P[保留有效响应]
    
    %% 价格排序
    P --> Q[构建BidAd列表]
    Q --> R[设置比较价格]
    R --> S{是否有实验策略?}
    
    S -->|STRATEGY_003| T[使用实验价格映射]
    S -->|其他| U[使用原始价格]
    
    T --> V[按价格降序排序]
    U --> V
    
    %% 胜者选择
    V --> W{最高价数量}
    
    W -->|1个| X[直接选择为胜者]
    W -->|多个| Y[价格相同?]
    
    Y -->|是| Z[交易类型比较]
    Y -->|否| X
    
    Z --> AA[Deal > RTB > 网盟]
    AA --> BB[优先级比较]
    BB --> CC[随机选择]
    
    %% 结算价格计算
    X --> DD{DSP数量}
    DD -->|1个| EE[结算价 = 底价]
    DD -->|多个| FF[结算价 = min 最高价, 次高价+1分]
    
    EE --> GG[设置最终结算价]
    FF --> GG
    
    %% 输出
    GG --> HH[返回竞价结果]
    
    %% 样式应用
    class A,C,D,E,L input
    class B,F,H,I,J,K,M,N,O,P,Q,R,T,U,V,X,Z,AA,BB,CC,EE,FF process
    class G,S,W,Y,DD decision
    class O,HH output
```

## DSP并发请求时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant SSP as SSP服务
    participant ADX as ADX服务
    participant Task as 竞价任务
    participant DSP1 as DSP1
    participant DSP2 as DSP2
    participant DSP3 as DSP3
    participant Price as 价格决策
    
    Client->>SSP: 发送竞价请求
    SSP->>ADX: 转发RTB请求
    ADX->>Task: 创建竞价任务
    
    Note over Task: 构建并行请求
    Task->>DSP1: 发送BidRequest
    Task->>DSP2: 发送BidRequest
    Task->>DSP3: 发送BidRequest
    
    par 并行请求
        DSP1-->>Task: 返回BidResponse1
        DSP2-->>Task: 返回BidResponse2
        DSP3-->>Task: 超时/错误
    end
    
    Task->>Price: 收集有效响应
    Note over Price: 价格计算和排序
    Price->>Price: 过滤低价响应
    Price->>Price: 按价格排序
    Price->>Price: 选择获胜者
    Price->>Price: 计算结算价格
    
    Price-->>Task: 返回竞价结果
    Task-->>ADX: 返回RtbResponse
    ADX-->>SSP: 返回广告响应
    SSP-->>Client: 返回最终结果
```

## 关键特性说明

### 1. 多种竞价模式支持
- **Header Bidding**: 支持V1和V2版本
- **Windmill策略**: 支持瀑布流和并行竞价
- **传统请求**: 兼容旧的广告请求格式

### 2. 灵活的执行策略
- **并行模式**: 同时请求多个DSP，价格最高者获胜
- **串行模式**: 按优先级顺序请求，第一个成功即返回
- **混合模式**: 根据AB实验动态选择执行策略

### 3. 智能价格决策
- **底价策略**: 支持动态、固定、个性化底价
- **价格实验**: 支持多种价格调整策略
- **结算机制**: 第一价格和第二价格混合模式

### 4. 完善的容错机制
- **超时控制**: 每个DSP请求都有独立超时
- **错误隔离**: 单个DSP失败不影响整体流程
- **降级策略**: 无响应时返回默认或缓存结果

### 5. 高性能设计
- **异步非阻塞**: 基于Project Reactor响应式编程
- **真正并行**: 使用Mono.zip实现并发请求
- **资源优化**: 及时释放不可执行任务资源

这个详细的流程图展示了ADX竞价系统的完整工作流程，包括请求处理、竞价执行、价格决策和响应构建等各个环节，帮助理解系统的核心逻辑和设计思路。