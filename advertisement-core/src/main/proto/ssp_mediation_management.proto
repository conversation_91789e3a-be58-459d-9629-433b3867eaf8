/**
@description ssp 端管理sdk聚合渠道以及访问策略的相关对象
**/
syntax = "proto3";
package sigmob;
//import "sigmob_rtb.proto";
//import "sigmob_ssp_mediation.proto";

option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.mediation.management";


//聚合策略配置中的按尾号配置的渠道规则，多条规则形成一个策略
message MediationRule {
	uint32 lowerBound = 1;
	uint32 upperBound = 2;
	repeated uint64 ids = 3; //策略中包含的渠道，自增id
	uint32 maxParallelRequestNum = 4; //sdk最大并行请求数
}

message WhiteListedUser {
	uint32 appId = 1;
	/**
	*用户唯一标识,可能是idfa或userId(媒体的用户id）或udid（sigmob sdk生成），此字段将会作为用户白名单的唯一标识，
	*当用户请求广告时，根据用户的idfa，userID，udid的这样的顺序查找白明单，如果找到，则将白名单内设置的渠道返回给用户进行加载，
	*同时忽略聚合策略中的尾号和渠道的设置
	*/
	string identify = 2;
	string utype = 3;//用户唯一标识的类型，可能是idfa或userId(媒体的用户id）或udid（sigmob sdk生成)
	string adSlotId = 4; //广告单元id
	repeated uint32 channelIds=5;//渠道的id数组，形如形如：[123,134]，此处的id为策略渠道设置时的id，在同一个appid的同类型广告下，此id唯一，或者全局唯一也可
}

//account 聚合策略
message SdkStrategySetting {
	uint64 id = 1; //策略id
	uint32 gId = 2; //账户组id
	uint32 priority = 3; //优先级
	uint32 regionType = 4; //地区类型，0：全球，1:包含，2:排除
	uint32 adType = 5; //广告类型 1:激励视频
	uint32 osType = 6; //操作系统类型 1:ios，2:android
	uint32 state = 7; //状态
	map<string,string> regions = 8; //地域列表
	map<string,string> sigmobAdSlotIds = 9; //此策略包含的sigmob广告单元列表
}

message MediationRules {
	uint64 strategyId = 1; //策略id
	repeated MediationRule rules = 3;
}

message AdSlotSdkChannelArgs {
	uint64 strategyId = 1; //策略id
	string sigmobAdSlotId = 2; //sigmob广告单元id
	repeated SdkChannel channelArgs = 3; //渠道运行参数
}

//单个渠道具体参数配置信息
message SdkChannel {
	string name = 1;
	map<string,string> options = 2;
	string adapter = 3;
	uint32 channel_id = 4;
	uint64 id = 5; //数据库自增id，用来标识具体的渠道参数映射关系
}