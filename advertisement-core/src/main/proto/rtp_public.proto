// 实时预估服务相关数据交互协议

syntax = "proto3";

option go_package = "sigmob.com/common/entry/rtp";
option java_package = "com.sigmob.sigdsp.grpc";
option java_multiple_files = true;

// 实时推荐服务接口
service Rtp {
    // v2.2.0推荐请求
    rpc Serve (Request) returns (Response) {}
}

// 2.0版本以后，Model用来表示实验信息，而不是模型信息，模型信息在实验YAML中配置
message Model {
    string name = 1;    // 名称
    int64 version = 2;  // 版本
}

// 请求算法预估的广告
message Ad {
    uint64 creative_id = 1;         // 创意ID
    uint64 vedio_id = 2;            // 视频素材ID
    uint64 endcard_id = 3;          // 着陆页素材ID
    uint64 image_id = 4;            // 图片素材ID
    uint64 product_id = 5;          // 广告对应的全局产品ID，同global_id
    uint32 bid_type = 6;            // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
    uint32 bid_price = 7;           // 投放平台的出价价格，原价，单位：分
    double ctr = 8;                 // 广告CTR原值
    double cvr = 9;                 // 广告CVR原值
    uint32 ecpm = 10;               // 广告ECPM原值
    double pctr = 11;               // 预估CTR
    double pcvr = 12;               // 预估CVR
    uint32 pecpm = 13;              // 预估ECPM
    uint32 ad_type = 14;            // 广告类型。1:激励视频 2:开屏 4:全屏视频
    uint64 click_num = 15;          // 用户创意点击次数
    uint64 imp_num = 16;            // 用户创意曝光次数
    uint32 override_type = 17;      // override竞价类型
    uint32 override_price = 18;     // override竞价价格
    uint64 global_click_num = 19;   // 用户全局产品点击次数
    uint64 global_imp_num = 20;     // 用户全局产品曝光次数
    uint32 deep_target = 21;        // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
    uint32 deep_type = 22;          // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
    int64 deep_val = 23;            // 主要考核指标值
    uint32 max_price = 24;          // 最高出价
    uint32 min_price = 25;          // 最低出价
}

// 请求源信息
message Source {
    App app = 1;                // 必填！应用信息
    Device device = 2;          // 必填！设备信息
    Network network = 3;        // 必填！网络环境信息
}

// 应用信息
message App {
    string app_id = 1;              // 应用ID
    uint32 orientation = 2;         // app方向：0: MaskAll、1:portrait、2:landspace
    repeated AdSlot slots = 3;      // 广告位信息
    string package_name = 4;        // 媒体app包名
}

// 广告位信息
message AdSlot {
    string adslot_id = 1;               // 广告位ID
    string template_type = 2;           // 支持的模版类型，0表示没有伴随条
    string animate_type = 3;            // 支持的动画类型，0表示没有伴随条
    uint64 latest_crid = 4;             // 上一次播放的广告创意ID
    repeated uint64 cached_crids = 5;   // 缓存的广告创意ID
}

// 设备信息
message Device {
    uint32 device_type = 1;                 // 设备类型。0:unknown、1:iPhone、2:iPad、3:iPod
    uint32 os_type = 2;                     // 操作系统类型. 1=IOS；2=Android
    Version os_version = 3;                 // 必填！操作系统版本
    string vendor = 4;                      // 必填！设备厂商名称，中文需要UTF-8编码
    string model = 5;                       // 必填！设备型号，中文需要UTF-8编码
    Size screen_size = 6;                   // 必填！设备屏幕宽高
    Geo geo = 7;                            // 地理信息
    uint32 dpi = 8;                         // 屏幕密度
    bool is_root = 9;                       // 是否越狱（true：越狱）
    uint32 battery_state = 10;              // 电池充电的状态（0=UnKnow、1=Unplugged、2=Charging、3=Full）
    float battery_level = 11;               // 电池电量百分比
    bool battery_save_enabled = 12;         // 是否开启低电量模式
    uint64 mem_size = 13;				            // 系统内存大小，安卓必填（单位Byte）
    uint64 total_disk_size = 14;            // 手机磁盘总大小（单位Byte）
    uint64 free_disk_size = 15;             // 手机磁盘剩余大小（单位Byte）
    uint64 sd_total_disk_size = 16;         // 设备SD磁盘总大小（单位Byte）
    uint64 sd_free_disk_size = 17;          // 设备SD磁盘剩余大小（单位Byte）
}

// 版本
message Version {
    uint32 major = 1;           // 主版本号, 必填！
    uint32 minor = 2;           // 副版本号, 必填！
    uint32 micro = 3;           // 子版本号, 必填！
    string version_str = 4;     // 字符串表示的版本号,在客户端无法解析出三段式数字版本号信息时，用此字段表示
}

// 地理位置
message Geo {
    float lat = 1;             // 纬度
    float lon = 2;             // 经度
    string language = 3;       // 语言（大写）
    string time_zone = 4;      // 时区
    uint64 city_code = 5;      // 地域编码
    string country = 6;        // 国家
    string region_code = 7;    // ip库中识别出来的编码，可能是市级编码、省级编码
}

// 二维尺寸信息
message Size {
    uint32 width = 1;  // 宽度, 必填！
    uint32 height= 2;  // 高度, 必填！
}

// 网络环境信息
message Network {
    uint32 connection_type = 1;     // 必填！网络连接类型，用于判断网速。0=无法探测当前网络状态; 1=蜂窝数据接入，未知网络类型; 2=2G; 3=3G; 4=4G; 5=5G; 100=Wi-Fi网络接入; 101=以太网接入
    string operator = 2;            // 必填！移动运营商
}

// 推荐请求
message Request {
    string sid = 1;                             // required: SearchLog sid，用来追踪查看原始请求
    string uid = 2;                             // required: SearchLog uid，用户内部UID，用户在sigmob的唯一标识
    Source source = 3;                          // required: 请求源信息
    repeated uint32 abtest_layer = 4;           // required: AB框架层信息，在推荐内部没有实际意义，仅做日志记录用
    repeated uint32 abtest_exp = 5;             // required: AB框架实验信息，在推荐内部没有实际意义，仅做日志记录用
    repeated uint32 abtest_model = 6;           // required: AB框架模型信息，对应推荐内部实验YAML配置，如果某层没有试验，则不传
    repeated Ad ad = 7;                         // required: 进入推荐流程的广告列表
    uint32 request_flow_type = 8;               // 流量来源 0-sdk; 1-oppo; 2-adxData(美数)
    uint32 settlement_mode = 9;                 // 0-固定价格或分成，1-按竞价成交价格累计计费，2-开发者自设floor累计计费
    uint32 rta_type = 10;                       // 0-正常广告，1-RTA唤醒类广告
}

// 推荐的广告
message MagicAd {
    uint64 creative_id = 1;         // 创意ID
    double pctr = 2;                // 拟合后预估CTR
    double pcvr = 3;                // 拟合后预估CVR
    double score = 4;               // rank得分
    double p_retain_rate = 5;       // 预估次留率
    double p_pay_rate = 6;          // 预估付费率
    double predict_ctr = 7;         // 拟合前预估ctr值
    double predict_cvr = 8;         // 拟合前预估cvr值
    uint32 deep_ecpm_fee = 9;       // deep计费
    uint32 predict_price = 10;      // 预估计费价格
}

// 推荐返回
message Response {
    uint32 code = 1;                                // 返回状态码，默认表示成功
    string desc = 2;                                // 返回状态详细描述
    map<uint64, MagicAd> output = 3;                // 预估返回结果，用map格式方便查找，key为创意ID
}