/**
Copyright (c) 2021 Meituan Inc. All rights reserved.
@description 美团实时竞价交易协议
@版本: 1.0.3
**/
syntax = "proto2";
package meituan.ad.rtb;
option java_package = "com.sigmob.ad.adx.dsp.model.meituan";

// 推广方式
enum PromotionType {
  UNKNOWN = 0;  // 未知
  DEEPLINK = 1;  // deeplink方式
  DOWNLOAD = 2;  // 下载
  WEB = 3;  // 网页打开
}
// 设备网络类型
enum NetworkType {
  NETWORK_UNKNOWN = 0;  // 未知
  NETWORK_WIFI = 1;  // wifi
  NETWORK_2G = 2;  // 2G
  NETWORK_3G = 3;  // 3G
  NETWORK_4G = 4;  // 4G
  NETWORK_5G = 5;  // 5G
}
// 设备系统类型
enum DevicePlatformType {
  DPT_UNKNOWN = 0;  // 未知
  DPT_IOS = 1;  // iOS
  DPT_ANDROID = 2;  // Android
  DPT_OTHERS = 3;  // 其他
}
// 设备运营商类型
enum DeviceOperatorType {
  DOT_UNKNOWN = 0;  // 未知
  DOT_MOBILE = 1;  // 移动
  DOT_UNICOM = 2;  // 联通
  DOT_TELECOM = 3;  // 电信
}
// 经纬度来源
enum LatLonType {
  WGS84 = 0;  // 84坐标系
  GCJ02 = 1;  // 火星坐标系
}
// 性别
enum Gender {
  UNKNOWN_GENDER = 0;  // 未知
  MALE = 1;  // 男
  FEMALE = 2;  // 女
}
// 广告素材类型
enum AdPicType {
  PIC_UNKNOWN = 0;  // 未知
  PIC_LARGE = 1;  // 大图
  PIC_SMALL = 2;  // 小图
  PIC_GROUP = 3;  // 组图
  PIC_VIDEO = 4;  // 视频
}
// 视频素材类型
enum VideoType {
  VT_UNKNOWN = 0;  // 未知格式
  VT_FLV = 1;  // flv格式
  VT_MP4 = 2;  // mp4格式
  VT_AVI = 3;  // avi格式
}
// 监测事件
enum Event {
  START_DOWNLOAD = 1;  // 下载开始
  FINISH_DOWNLOAD = 2;  // 下载结束
  FINISH_INSTALL = 3;  // 安装结束
  VIDEO_PLAY_START = 4;  // 视频开始播放
  VIDEO_PLAY_END = 5;  // 视频播放结束
  VIDEO_PLAY_VALID = 6;  // 视频有效播放
}
// 设备id类型
enum DeviceIdType {
  IMEI = 1;  // Android设备, IMEI，如果是md5加密32位小写
  IDFA = 2;  // IOS IDFA（明文，大写），包含"-"
  OAID = 3;  // Android设备, oaid(明文)
  ANDROID_ID = 4;  // Android设备 android id（明文）
  MAC = 5;  // MAC地址
  CAID = 6;  // IOS14 CAID
}
// 加密方式
enum EncryptionType {
  PLAINTEXT = 1;  // 明文
  MD5 = 2;  // md5加密
}
// 用户年龄
enum UserAgeType {
  UNKNOWN_AGE_TYPE = 0;  // 未知年龄
  ZERO_AGE_TYPE = 1;  // 0-17
  TEN_AGE_TYPE = 2;  // 18-23
  TWENTY_AGE_TYPE = 3;  // 24-30
  THIRTY_AGE_TYPE = 4;  // 31-40
  FORTY_AGE_TYPE = 5;  // 41-49
  FIFTY_AGE_TYPE = 6;  // 50及以上
};
// 结算方式
enum PriceType {
  CPM_PRICE = 1;
  CPC_PRICE = 2;
};

// 图片或视频宽高
message Size {
  optional int32 width = 1;
  optional int32 height = 2;
}
// 设备id信息
message DeviceId {
  optional string id = 1;  // 必填，设备id
  optional DeviceIdType device_id_type = 2;  // 必填，设备id类型
  optional EncryptionType encryption_type = 3;  // 必填，加密方式
}
// 请求设备信息
message Device {
  repeated DeviceId device_ids = 1;  // 必填。设备Id信息
  optional DevicePlatformType plateform_type = 2;  // 必填，设备系统类型
  optional DeviceOperatorType operatior_type = 3;  // 运营商, 0-未知，1-移动，2-联通，3-电信
  optional NetworkType network_type = 4;  // 网络环境，0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g
  optional string brand = 5;  // 设备品牌 如nokia, samsumg
  optional string model = 6;  // 设备型号，如iPhone5s, Galaxy
  optional string os_version = 7;  // 操作系统版本，如7.0.2
  optional int32 device_pixel = 8;  // 设备像素
  optional Size screen_size = 9;  // 设备尺寸
  optional int32 device_screen_orientation = 10;  // 横竖屏，0-未知，1-竖屏，2-横屏
  optional string ua = 11;  // user_agent，用户代理
}
// 请求APP信息
message App {
  optional string app_name = 1;  // App应用名
  optional string package_name = 2;  // 必填，应用包名
  repeated string app_category = 3;  // ios apple store app_id eg.1234567890
  optional string version = 4;  // App应用的版本
}
// 请求位置信息
message LocationInfo {
  optional double latitude = 1;  // 纬度
  optional double longitude = 2;  // 经度
  optional string ipv4 = 3;  // 用户IPv4
  optional string ipv6 = 4;  // 用户IPv6
  optional LatLonType lat_lon_type = 5;  // 必填，默认84坐标系
}
// 请求用户信息
message UserInfo {
  optional string uid = 1;  // 用户在媒体上注册的ID
  optional Gender gender = 2;  // 用户的性别。0: Unknown, 1: Male, 2: Female
  optional UserAgeType age = 3;  // 用户的年龄，分段枚举
  optional string app_list = 4;  // 媒体传的已经安装的应用列表，英文逗号分隔
  repeated string user_tags = 5;  // 用户标签
}
// 视频素材要求
message Video {
  repeated VideoType video_types = 1;  // 视频素材类型。0：未知格式，1：flv格式，2：mp4格式，3：avi格式
  optional int32 minduration = 2;  // 视频最短时长，秒
  optional int32 maxduration = 3;  // 视频最长时长，秒
}
// 模板信息
message TemplateInfo {
  optional string template_id = 1;  // 必填。模板id，ADX与美团DSP提前约定模板信息，具体的素材要求不随请求发送，模板要求见"美团RTB接口文档"
  optional Video video = 2;  // 视频素材要求，如果模板id对应的模板为视频类型则需要填写
}

message Pmp {
  optional string deal_id = 1;  // PD、PDB流量的订单id
  optional uint64 deal_floor = 2;  // PB、PDB流量的订单价格，单位为cpm/分
  optional int32 deal_type = 3;  // 订单类型，3：PD，4：PDB
}

// 广告位信息
message AdSlot {
  optional string adslot_id = 1;  // 必填，广告位ID
  repeated TemplateInfo templates = 2;  // 模板信息
  optional uint64 floor = 3;  // 必填，广告cpm底价，单位为cpm/分
  optional bool is_https = 4;  // 推广位所在页面是否是https站点
  repeated Pmp pmps = 5;  // pmp 信息
  repeated PriceType price_types = 6;  // 支持的结算方式，1cpm、2cpc
  optional uint64 cpc_floor = 7;  // cpc底价，单位为分/千次点击，例如cpc底价为1元，则此字段为100,000
}

// 竞价请求
message RtbRequest {
  optional string request_id = 1;  // 必填，自定义的请求id，保证请求唯一性
  optional string adx_name = 2;  // 必填，adx名称，作为dsp识别adx唯一标识，只能包含小写字符和下划线，总长度不超过20个字符。
  optional Device device = 3 ;  // 必填，设备信息
  optional LocationInfo location = 4;  // 必填，位置信息
  optional UserInfo user = 5;  // 用户信息
  repeated AdSlot adslots = 6;  // 必填，广告位信息，至少填写一个
  optional App app = 7; // 请求App信息
  optional bool test = 8;  // 测试字段
  optional string token = 9;  // 必填，美团RTB分配给媒体的密钥，用于请求校验，与adx_name一一对应，如校验失败则停止竞价
}
// 视频素材
message VideoMaterial {
  optional string cover_url = 3;  // 视频播封面图或者播放结束图
  optional uint64 size = 5;  // 视频文件大小 单位byte eg.1591463
  optional float video_duration = 6;  // 视频时长，单位秒 eg.26.15
  optional string video_url = 7;  // 必填。视频链接
}
// 监测事件上报
message Tracking {
  optional Event event = 1;  // 监测事件，1：下载开始，2：下载结束，3：安装结束，4：视频播放开始，5：视频播放结束，6：视频播放有效
  repeated string urls = 2;  // 事件上报url
}
// 竞价广告
message Ad {
  optional string ad_id = 1;  // 必填，广告id
  optional int32 bid_price = 2;  // 必填，cpm出价，单位cpm/分
  optional string tempalte_id = 4;  // 对应请求中的template_id
  repeated PromotionType promotion_types = 5;  // 必填。推广方式，0：未知，1：deeplink，2：下载，3：网页打开
  optional Size image_size = 7;  // 必填，素材尺寸
  repeated string image_urls = 8;  // 必填，图片Url列表
  optional VideoMaterial video = 9;  // 视频广告必填，视频信息
  optional string target_url = 10;  // 必填，落地页url，如果交互类型为deeplink，此url为兜底页
  optional string deeplink_url = 11;  // deeplink推广类型必填，deeplink链接
  optional string download_url = 12;  // 下载推广类型必填，应用下载url
  optional string universal_link = 13;  // iOS唤起通用链接
  optional string title = 14;  // 必填，广告标题
  optional string description = 15;  // 必填，广告描述
  optional string icon = 16;  // 必填，icon_url
  optional string app_name = 17;  // 下载类必填，下载app名
  optional string package_name = 18;  // 下载推广类型必填，安卓：应用包名，iOS设备使用bundleid
  optional string market_id = 19;  // iOS应用下载app_id
  repeated string win_notice_urls = 20;  // 必填，竞胜url列表。
  repeated string imp_urls = 21;  // 必填，展现监测url列表。
  repeated string click_urls = 22;  // 必填，点击监测url列表。
  repeated Tracking tracking = 23;  // 其他监测事件
  optional PriceType price_type = 24;  // 出价方式，1cpm、2cpc，与bid_price出价对应
  optional int32 bid_cpc_price = 25;  // cpc出价，单位为分/千次点击，例如出价为1元时，此字段为100,000
  optional string deal_id = 26;  // 直接交易方式参竞时的订单id
}
// 对一个广告位的竞价信息
message Bid {
  repeated Ad ads = 1;  // 必填，广告信息，至少一个
}
// 竞价响应
message RtbResponse {
  optional string request_id = 1;  // 必填，RtbRequest中所带的request_id。
  optional int64 time_ms = 2;  // 非必填，从收到请求到返回响应所用的时间
  optional string advertiser_id = 3;  // 广告主id，双方约定后可用于ADX白名单设置
  repeated Bid bids = 4;  // 必填，竞价信息，至少一个
  optional bool block_device_id = 5;  // 为true时dsp短时屏蔽本请求的设备号，屏蔽时间与adx线下约定；为false时不屏蔽该设备
}
