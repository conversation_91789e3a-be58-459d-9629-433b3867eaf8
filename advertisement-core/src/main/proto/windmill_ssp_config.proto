syntax = "proto3";
import "sigmob_rtb.proto";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.config";
option objc_class_prefix = "Sigmob";

message WindMillSdkConfigRequest {
  App app = 1;                        // 必填！应用信息
  Device device = 2;                  // 必填！设备信息
  Network network = 3;                // 必填！网络环境信息
  Privacy privacy = 4;               // 用户隐私
  User user = 5;                     // 用户信息
}

message WindMillSdkConfigResponse {
  uint32 code = 1;
  string error_message = 2;
  WindMillSdkConfig config = 3;
}


//配置信息
message WindMillSdkConfig {
  WindMillCommon common_config = 1;
  WindMillIOS ios_config = 2;
  WindMillAndroid android_config = 3;
}

message WindMillCommon {
  WindMillCommonEndpointsConfig endpoints = 1;          //api配置
  uint32 configRefresh = 2;                     //config刷新间隔
  bool is_gdpr_region = 3;                      //是否gdpr地区，默认是false
  uint32 max_send_log_records = 4;              //每次上传打点日志的最大条数
  uint32 send_log_interval = 5;                 //打点日志上报时间间隔（单位：秒）
  repeated uint32 dclog_blacklist = 6;          //点号位黑明单，默认会将100，101号点自动加入黑名单，如果下发了黑明单，以下发为准。
  bool enable_debug_level = 7;                  //是否开启debug日志、初始化监测广告插件、监测结果打点和日志输出
  uint32 ready_expire_timestamp = 9;            //ready的有效期（单位：秒）default=900 // 平台无配置
  uint32 load_interval = 10;                    //重复发起请求的间隔时间，在非ready的情况下，用来防止重复发起请求
  bool disable_up_location = 11;                //是否禁止上传用户的位置信息。false: 不禁止上传；true: 禁止上传
  bool log_enc = 12;                            //  是否对打点数据做加密处理，false: 不加密；true: 加密
  uint32 tracking_expiration_time = 13;         // track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tracking_retry_interval = 14;          // track上报失败的重发时间间隔
}


message WindMillIOS {

}

message WindMillAndroid {
  uint32 report_log = 1;                      //是否要写log,控制最细颗粒度为设备 0:关、1:开
  uint32 disable_up_oaid = 2;                 //是否禁止获取oaid信息，其中在android10一下默认为不上传，Android10以上默认上传；1: 不禁止上传；2: 禁止上传; 其它走默认值
  bool enable_permission = 3;                 //允许获取权限的开关；默认为false: 表示不获取权限列表
  bool enable_report_crash = 4;               //允许获取崩溃信息的开关；默认为false: 表示不上报崩溃信息
  bool oaid_api_is_disable = 5;               //默认值 false :允许通过API模式采集oaid, true: 禁止通过API模式采集oaid，
  bool disable_boot_mark = 6;                 //禁止 boot_mark, update_mark 获取,default=false
  bool enable_reward_ndk = 7;                 //  开启开启 NDK加密,默认不开启
  bool enable_ndk_crash = 8;                  //  开启NDK 崩溃收集，默认不开启
  uint32 init_sync_timeout = 9;               //  同步初始化超时时间，默认2秒 (单位毫秒)
  uint32 gm_price_percent = 10;               // 当前默认100
}

//通用地址配置
message WindMillCommonEndpointsConfig {
  string log = 1;
  string strategy = 2;
  string bidding_url = 3;  // waterfall接口地址
  string config = 4;       // 备用config地址(sdk 3.7.3)
}

