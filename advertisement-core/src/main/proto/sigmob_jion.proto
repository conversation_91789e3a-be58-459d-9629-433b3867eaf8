/**
@description 内部数据对象集合，用于ssp和投放系统接口，和request、response日志打印。
<AUTHOR>
@版本: 1.1.0
**/
syntax = "proto3";

import "sigmob_rtb.proto";
import "sigmob_ad_operation.proto";
import "user_profile.proto";
import "rtp_public.proto";
import "abtest.proto";


option java_multiple_files = true;
option java_package = "com.sigmob.sigdsp.grpc";
option go_package = "sigmob.com/common/entry";

//广告竞价服务接口
service Rtb {
  rpc DebugTool(DebugReq) returns (DebugResp) {}

  // 新版sigmob dsp协议，支持原生信息流广告形式
  rpc GetSigmobAd(Req) returns (Resp) {}
  // new Ping api
  rpc SigmobDspPing(Req) returns (Resp) {}
}
message DebugReq {
  uint64 campaign_id = 1;                     // 待debug的计划id
  IndexRequest indexReq = 2;                  // index request
  profile.UserProfile user_profile = 3;       // 用户属性
  Req adxReq = 4;
}

message DebugResp {
  repeated string resp = 1;
}

message SearchLog {
  string sid = 1;                                 // 唯一标识一次竞价请求，内部生成。建议和sigmob.BidRequest.request_id一致
  int64  timestamp = 2;                           // 接收到请求的服务器unix时间戳，单位：秒
  uint32 time_interval = 3;                      // 请求时段
  string uid = 4;                                 // 用户内部UID，用户在sigmob的唯一标识。
  string channel_id = 5;                          // 必填！流量来源渠道编码：1=自有ssp流量
  sigmob.BidRequest request = 6;                  // sigmob sdk请求发起的pb格式的request对象
  sigmob.BidResponse response = 7;                // 投放系统和sdk约定的pb格式的response对象
  uint64 error_code = 8;                          // 错误码. 和sigmob.response中的error_code一致； 内部使用
  uint64 process_time_ms = 9;                     // 接收请求到返回广告的时间间隔。
  IndexRequest index_request = 10;                // 检索请求
  IndexResponse index_response = 11;              // 检索返回
  string server_ip = 12;                          // 请求接收server的ipv4地址
  profile.UserProfile user_profile = 13;          // 用户属性
  uint32 buctet_type = 14;                        // 投放类型。0：正常阶段；1：随机阶段；2：试投阶段；3：测试设备桶；
  int64 nanotime = 15;                            // 接收到请求的服务器unix时间戳的纳秒部分，单位：纳秒
  repeated uint64 layers_time_ms = 16;            // 各个层耗时，单位: 毫秒ms
  repeated AbTestLog logs = 18;                   // abtest算法框架打点信息（3号点）
  Bucket abtest_bucket = 19;                      // abtest算法框架当前桶信息，1: 实验桶; 2: 固化桶【v3.8.1版本开始弃用】
  uint32 is_test = 20;                            // 是否测试设备，1：为测试设备，0：为非测试设备
  bool is_ssp_retrieved = 21;                     // 是否SSP找回imei
  double compute_ctr = 22;                        // 投出广告创意ctr, 预估ctr值（若有拟合策略则存拟合后的值，没有存原始预估值）
  double compute_cvr = 23;                        // 投出广告创意cvr, 预估cvr值（若有拟合策略则存拟合后的值，没有存原始预估值）
  bool can_silent_user_last_ad = 24;              // 沉默用户是否使用上一次广告
  bool is_predict = 25;                           // 投出创意是否被算法预估（存在请求算法，但是没有被预估情况）
  bool is_into_abtest = 26;                       // 是否进入abtest实验（存在请求算法，但是没有被预估情况）
  uint32 ecpm = 27;                               // 投出广告创意eCPM, 单位:分
  string dsp_version = 28;                        // dsp version
  uint64 check_service_id = 29;                   // 审核服务id
  bool need_checked = 30;                         // 下发广告是否需要过审
  uint32 uid_type = 31;                           // uid取值来源类型：1-IMEI; 2-IDFA; 3-IDFV; 4-UDID; 5-Sigmob CAID; 6-OAID; 7-GAID; 8-ANDROID ID; 9-IMEI MD5; 10-IDFA MD5; 11-OAID MD5; 12-GAID MD5; 13-ANDROID ID MD5
  int64 risk_control_time_ms = 32;                // 风控耗时
  repeated ClosecardAd closecard_ads = 33;        // 关闭页广告列表
  bool disable_closecard = 34;                    // 是否禁用关闭页广告
  DspLog dsp_log = 35;                            // dsp打点数据
  map<uint64, ModelConfig> abtest_exp = 36;       // abtest框架各层模型
  uint64 market_channel_id = 37;                  // 应用市场渠道id（暂时与开发者id一致）
  bool need_market_channel_check = 38;            // 是否需要
  string product_market_id = 39;                  // 返回广告产品的市场id
  bool template_auto_click = 40;                  // 模板是否支持自动点击
  string sigmob_caid = 41;                        // 当前设备可使用的最老版本设备归因id，解决ios14广告无法归因问题
  uint32 cold_start_stage = 42;                   // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  uint32 cold_start_state = 43;                   // 最终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  uint32 request_flow_type = 44;                  // 流量来源 0-sdk; 1-oppo ... http://wiki.sigmob.cn/pages/viewpage.action?pageId=40079564
  repeated string ad_strategys = 45;              // 流量指定的广告策略数组
  uint32 settlement_mode = 46;                    // 0-固定价格或分成，1-按竞价成交价格累计计费，2-开发者自设floor累计计费(将废弃，请使用adslot中s_effective字段)
  uint32 supply_charging_mode = 47;               // 媒体侧计费方式，1 cpm, 2 cpc, 3 cpa
  double p_retain_rate = 48;                      // 投出广告预估次留率
  double p_pay_rate = 49;                         // 投出广告预估付费率
  uint32 request_timeout = 50;                    // 请求最大处理时间，单位(毫秒)
  double predict_ctr = 51;                        // 拟合前预估ctr值
  double predict_cvr = 52;                        // 拟合前预估cvr值
  uint32 deep_target = 53;                        // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
  uint32 deep_type = 54;                          // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
  int64 deep_val = 55;                            // 主要考核指标值
  uint32 deep_ecpm_fee = 56;                      // deep计费
  uint32 original_price = 57;                     // 广告主原始价格
  uint32 max_price = 58;                          // 最高出价
  uint32 min_price = 59;                          // 最低出价
}

message DspLog {
  string sid = 1;                                         // 唯一标识一次竞价请求，内部生成。建议和sigmob.BidRequest.request_id一致
  int64  timestamp = 2;                                   // 接收到请求的服务器unix时间戳，单位：秒
  string app_id = 3;                                      // app_id
  string uid = 4;                                         // 用户内部UID，用户在sigmob的唯一标识。
  uint64 error_code = 5;                                  // 错误码. 和sigmob.response中的error_code一致； 内部使用
  uint32 cold_start_stage = 6;                            // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  uint32 cold_start_state = 7;                            // 终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  repeated uint64 lookup_ad_list = 8;                     // 检索广告列表
  repeated DspDcUnvalidAd filter_unvalid_ads = 9;         // filter阶段过滤无效广告
  repeated DspDcUnvalidAd normal_unvalid_ads = 10;        // 冷启动正常阶段无效广告
  repeated DspDcUnvalidAd random_unvalid_ads = 11;        // 冷启动随机阶段无效广告
  repeated DspDcUnvalidAd trial_unvalid_ads = 12;         // 冷启动试投阶段无效广告
  repeated ValidCreative valid_creatives = 13;            // filter创意过滤后的创意信息列表, top200
  repeated uint64 valid_creative_ids = 14;                // 参与ranking的创意id列表
  bool is_into_abtest = 15;                               // 是否进入abtest实验（存在请求算法，但是没有被预估情况）
  repeated AbTestLog abtest_logs = 16;                    // abtest算法框架打点信息
  uint32 adslot_type = 17;                                // 广告位类型
  string adslot_id = 18;                                  // 广告位id
  uint32 bidfloor = 19;                                   // 地板价
  uint64 creative_id = 20;                                // 创意id
  uint64 campaign_id = 21;                                // 计划id
  bool is_predict = 22;                                   // 是否被预估
  uint32 override_type = 23;                              // override类型，1:cpm; 2:cpc; 3:cpa
  uint32 override_erpm = 24;                              // 人工设置出价，优先级最高。为0表示没设置。单位：分
  uint32 ecpm = 25;                                       // eCPM, 单位:分
  double old_ctr = 26;                                    // ctr真实值，当compute_ctr被预估值覆盖时，记录原真实值
  double old_cvr = 27;                                    // cvr真实值，当compute_cvr被预估值覆盖时，记录原真实值
  double compute_ctr = 28;                                // 最终计算时的ctr
  double compute_cvr = 29;                                // 最终计算时的cvr
  double selected_score = 30;                             // 选定的score最高的创意的score值
  uint32 serving_speed = 31;                              // 投放方式 1:正常投放 2:匀速投放v1 5:匀速投放v2
  uint64 video_id = 32;                                   // 视频素材ID
  uint64 endcard_id = 33;                                 // 着陆页素材ID
  uint64 image_id = 34;                                   // 图片素材ID
  uint32 ctype = 35;                                      // 广告交互类型。1=使用浏览器打开；2=下载应用
  uint64 product_id = 36;                                 // 广告对应的全局产品ID
  uint32 bid_type = 37;                                   // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
  uint32 bid_price = 38;                                  // 投放平台的出价价格，单位：分
  repeated uint64 index_os = 39;                          // 【索引-操作系统】后剩余计划全集
  repeated uint64 index_base = 40;                        // 【索引-运营商过滤】后剩余计划全集
  repeated uint64 index_dmp = 41;                         // 【索引-DMP人群定向排除】后剩余计划全集
  uint32 creative_type = 42;                              // 创意类型
  repeated ClosecardAd closecard_ads = 43;                // 关闭页广告列表
  bool disable_closecard = 44;                            // 是否禁用关闭页广告
  map<string, string> payload = 45;                       // 拓展字段
}

message DspDcUnvalidAd {
  uint64 campaign_id = 1;
  uint64 creative_id = 2;
  uint64 reason = 3;
  uint32 ecpm = 4;
}

message ClosecardAd {
  string sub_request_id = 1;          // 子请求id
  uint64 campaign_id = 2;             // 子广告计划id
  uint64 creative_id = 3;             // 子广告创意id
  uint64 product_id = 4;              // 子广告产品id，对应sig_dsp_adver_campaign.prodid
  uint32 bid_price = 5;               // 子广告价格, 分
  uint32 bid_type = 6;                // 子广告出价类型, 1 cpm, 2 cpc, 3 cpa
  uint32 override_type = 7;           // override类型，1:cpm; 2:cpc; 3:cpa
  uint32 override_erpm = 8;           // 人工设置出价，优先级最高。为0表示没设置。单位：分
  bool is_predict = 9;                // 是否预估
  double compute_ctr = 10;            // 子广告ctr
  double compute_cvr = 11;            // 子广告cvr
  string price_macro_name = 12;       // tracking URL price macro name, e.g. campaign_id=123, "_PRICE123_"
  double p_retain_rate = 13;          // 子广告预估次留率
  double p_pay_rate = 14;             // 子广告预估付费率
  uint32 deep_ecpm_fee = 15;          // deep计费
}

message AbTestLog {
  string layer_id = 1;                           // 分层id（弃用,改为使用code）
  string experiment_id = 2;                      // 实验id（弃用,改为使用code）
  Model model = 3;                               // 请求的模型信息，模型的版本会被反写（弃用,为了支持spp打点协议，model暂时保留，只做打点使用，逻辑改为使用model_code, model_version）
  int32 creative_count = 4;                      // 参与预估的合法创意数，默认值-1
  int32 return_creative_count = 5;               // 预估接口返回创意数，默认值-1
  int32 experiment_state = 6;                    // 预留字段，默认值-1
  int32 api_state = 7;                           // api正常返回为1，超时为2，DSP管理赋值，默认值-1
  uint64 layer_code = 8;                         // 分层code
  uint64 experiment_code = 9;                    // 实验code
  uint64 bucket_code = 10;                       // 桶code
  uint64 model_code = 11;                        // 模型code
  uint64 model_version = 12;                     // 模型version
  bool isMergeRequest = 13;                      // 是否合并请求
}

// 检索请求
message IndexRequest {
  repeated IndexTermGroup groups = 1;         //索引分组，多个分组之间为逻辑与(and)关系
  repeated string dmp_include_groups = 2;     // DMP人群定向 包含包 索引组
  repeated string dmp_except_groups = 3;      // DMP人群定向 排除包 索引组
}

// 检索返回
message IndexResponse {
  repeated string lookup_ad_list = 1;                     // 检索广告列表
  operation.AppSession app_session = 2;                   // App Session, SSP不使用
  repeated string optimise_unvalid_ads = 3;               // 优化桶无效广告【v3.6.4版本弃用】
  repeated string random_unvalid_ads = 4;                 // 冷启动随机阶段无效广告
  repeated ValidCreative valid_creatives = 5;             // filter创意过滤后的创意信息列表(非RTA)
  operation.AppSlotTargetCampIds app_slot_include = 6;    // app广告位定向包含
  operation.AppSlotTargetCampIds app_slot_except = 7;     // app广告位定向排除
  uint64 campaign_id = 8;                                 // 最终选取的广告计划
  uint64 creative_id = 9;                                 // 最终选取的广告创意
  int32 creative_index = 10;                              // 最终选取的广告序号
  repeated uint64 closecard_ads = 11;                     // Closecard Ad creativeIds【v3.7.3开始弃用】
  repeated string filter_unvalid_ads = 12;                // filter阶段过滤无效广告
  repeated string trial_unvalid_ads = 13;                 // 冷启动试投阶段无效广告
  repeated string normal_unvalid_ads = 14;                // 冷启动正常阶段无效广告
  repeated string test_unvalid_ads = 15;                  // 测试阶段无效广告
  uint64 stage = 16;                                      // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  uint64 creative_stage = 17;                             // 最终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  uint64 creative_count = 18;                             // 参与random/ranking的创意数【v3.6.4开始弃用】
  repeated ValidCreative valid_closecard_creatives = 19;  // valid closecard creative
  bool enable_base_ranking = 20;                          // 是否需要base ranking 公式排序【v4.1.2开始弃用】
  bool disable_override = 21;                             // abtest划分override是否生效实验的结果
  EcpmTargetParams ecpm_target_params = 22;               // ecpm target调节相关参数
  repeated ValidCreative valid_rta_creatives = 23;        // filter创意过滤后的创意信息列表（RTA广告）
  uint32 lastMaxEcpm = 24;                                // 最近一段时间内uid+adtype+appid最高ecpm
}

// ecpm target调节相关参数
message EcpmTargetParams {
  double w_base = 1;       // 判断广告是否返回的基线 Wbase
  double e_base = 2;       // 判断广告是否返回的基线 Ebase=Etarget*Wbase
  double w_predict = 3;    // 当前预估广告是否返回的概率 Wp
  uint32 e_target = 4;     // ecpm target
  uint32 e_predict = 5;    // ecpm final, Ep
}

//投放过程中的创意信息
message ValidCreative {
  uint64 id = 1;                          // 创意id
  uint64 campid = 2;                      // 计划id
  bool is_predict = 3;                    // 是否被预估
  uint32 override_type = 4;               // override类型，1:cpm; 2:cpc; 3:cpa
  uint32 override_erpm = 5;               // 人工设置出价，优先级最高。为0表示没设置。单位：分
  uint32 ecpm = 6;                        // eCPM, 单位:分，含override
  double old_ctr = 7;                     // ctr真实值，当compute_ctr被预估值覆盖时，记录原真实值
  double old_cvr = 8;                     // cvr真实值，当compute_cvr被预估值覆盖时，记录原真实值
  double compute_ctr = 9;                 // 最终计算时的ctr
  double compute_cvr = 10;                // 最终计算时的cvr
  double selected_score = 11;             // 选定的score最高的创意的score值
  uint32 serving_speed = 12;              // 投放方式 1:正常投放 2:匀速投放v1 5:匀速投放v2
  uint64 video_id = 13;                   // 视频素材ID
  uint64 endcard_id = 14;                 // 着陆页素材ID
  uint64 image_id = 15;                   // 图片素材ID
  uint64 product_id = 16;                 // 广告对应的全局产品ID
  uint32 bid_type = 17;                   // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
  uint32 bid_price = 18;                  // 投放平台的出价价格，单位：分
  uint64 material_id = 19;                // 创意素材id
  uint32 creative_type = 20;              // 创意类型
  uint32 ctype = 21;                      // 广告交互类型。1=使用浏览器打开；2=下载应用
  uint64 cold_start_state = 22;           // 冷启动状态
  uint64 prod_id = 23;                    // 产品id，对应sig_dsp_adver_campaign.prodid
  uint32 ecpm_non_override = 24;          // eCPM, 单位:分，不含override
  double p_retain_rate = 25;              // 投出广告预估次留率
  double p_pay_rate = 26;                 // 投出广告预估付费率
  uint32 rta_service_id = 27;             // RTA api 服务id
  double predict_ctr = 28;                // 拟合前预估ctr值
  double predict_cvr = 29;                // 拟合前预估cvr值
  uint32 bidding_price_weight = 30;       // 广告位多出价权重
  uint32 pri_ind = 31;                    // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
  uint32 pri_date = 32;                   // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
  int64 pri_val = 33;                     // 主要考核指标值
  uint32 deep_ecpm_fee = 34;              // deep计费
  uint32 original_price = 35;             // 广告主原始价格
  uint32 native_template_index = 36;      // 原生信息流模板数组下标
  uint32 max_price = 37;                  // 最高价
  uint32 min_price = 38;                  // 最低价
  bool is_ocpm = 39;                      // 是否支持ocpm出价
  uint32 rise_strategy = 40;              // 起量策略，1-普通起量 2-加速起量 0-常规投放，仅计费调整
  uint32 ocpm_price_factor = 41;          // ocpm出价系数
  string conversion_target = 42;          // 转化目标
  double global_ctr = 43;                 // 全局产品预估ctr
  int64 create_time = 44;                 // 创建时间戳
  double cold_start_radio = 45;           // 广告扶持系数
  int64 cold_start_budget_id = 46;        // 广告扶持预算id，-1：全局预算，其他为global_id
  uint32 cold_start_er = 47;               // 单次扶持eCPM，单位：分
  uint32 cold_start_de = 48;               // 返回的冷启创意的扶持价格，单位：分
  repeated uint32 recall_chan = 49;        // 多路召回渠道ID
  uint32 promotion_type = 50;              // 推广类型 1-普通推广 2-已安装促激活 3-已激活促后续
  uint32 top1_replace_de = 51;             // top1替换的扶持价格，单位：分
}

// 索引项分组
message IndexTermGroup {
  repeated string index_term = 1; // 索引项，多个索引项之间为逻辑或(or)的关系
}



/*===================================================================================================*/
/* 				                        	          	新协议		    										        				     */
/*===================================================================================================*/

message Req {
  sigmob.BidRequest request = 1;                  // sigmob sdk请求发起的pb格式的request对象
  bool is_ssp_retrieved = 2;                      // 是否SSP找回imei
  bool disable_closecard = 3;                     // 是否禁用关闭页广告
  uint32 request_flow_type = 4;                   // 流量来源 0-sdk; 1-oppo ... http://wiki.sigmob.cn/pages/viewpage.action?pageId=40079564
  uint32 is_test = 5;                             // 是否测试设备，1：为测试设备，0：为非测试设备
  uint32 settlement_mode = 6;                     // 0-固定价格或分成，1-按竞价成交价格累计计费，2-开发者自设floor累计计费(将废弃，请使用adslot中s_effective字段)
  uint32 request_timeout = 7;                     // 请求最大处理时间，单位(毫秒)
  uint64 check_service_id = 8;                    // 审核服务id
  bool need_checked = 9;                          // 下发广告是否需要过审
  bool need_market_channel_check = 10;            // 是否需要
  uint64 market_channel_id = 11;                  // 应用市场渠道id（暂时与开发者id一致）
  string sigmob_caid = 12;                        // 当前设备可使用的最老版本设备归因id，解决ios14广告无法归因问题
  sigmob.Version sdk_version = 13;                // SDK版本
  uint32 supply_charging_mode = 14;               // 媒体侧计费方式，1 cpm, 2 cpc, 3 cpa
  bool need_compliance = 15;                      // 是否要求合规
  repeated TemplateConfig template_config = 16;   // 模板格式
  int64 dev_account_id = 17;                      // 开发者账号id
  AppAdSetting app_ad_setting = 18;               // 媒体运营广告设置
  SearchInfo search_info = 19;                    //  搜索广告信息
  uint32 qingti_experiment_code = 20;             // 青提实验号
  uint32 user_ctr_level = 21;                     // 用户ctr档位
  string ssp_app_id = 22;                         // API接入方式的媒体自定义appid
  string ssp_placement_id = 23;                   // API接入方式的媒体自定义广告位id
  bool need_miit_compliance = 24;                 // 是否miit合规
  bool shield_material = 25;                      // 是否开启屏蔽素材
}

message TemplateConfig{
  uint32 ad_type = 1;     // 流量广告类型
  uint32 format_type = 2; // 广告位支持的format_type
  uint32 interaction_type = 3; // 广告交互类型
}

message Resp {
  uint64 error_code = 1;                         // 错误码. 和sigmob.response中的error_code一致； 内部使用
  sigmob.BidResponse response = 2;               // 投放系统和sdk约定的pb格式的response对象
  UserInfo user_profile = 3;                     // 用户属性
  string dsp_version = 4;                        // dsp version
  uint32 cold_start_stage = 5;                   // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  bool is_into_abtest = 6;                       // 是否进入abtest实验（存在请求算法，但是没有被预估情况）
  repeated AbTestLog logs = 7;                   // abtest算法框架打点信息（3号点）
  repeated DspAd ads = 8;                        // dsp ad 信息
  repeated ClosecardAd closecard_ads = 9;        // 关闭页广告列表
  string overfitting_flow_type = 10;             // 拟合流量标识
  Top1Replace top1_replace_info = 11;            // top1替换信息
  double platform_premium_ratio = 12;            // 直投平台所有产品（包括CPM，CPC, CPA）统一提价系数
  uint32 waterfall_placement_subsidy = 13;       // 瀑布流降低target_ecpm or floor
  bool random_ad_replace = 14;                   // 是否随机top1替换
}

message Top1Replace{
  uint32 top1_old_pecpm = 1;           // 被替换广告，原Top1预估ecpm
  double top1_old_pctr = 2;            // 被替换广告，原Top1预估ctr
  double top1_old_pcvr = 3;            // 被替换广告，原Top1预估cvr
  uint64 top1_old_creative_id = 4;     // 被替换广告，原Top1创意id
  uint32 top1_ratio = 5;               // 扶持系数
}

message DspAd {
  bool is_predict = 1;               // 投出创意是否被算法预估（存在请求算法，但是没有被预估情况）
  bool template_auto_click = 2;      // 模板是否支持自动点击
  uint32 ecpm = 3;                   // 投出广告创意eCPM, 单位:分
  uint32 cold_start_state = 4;       // 最终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  uint32 deep_target = 5;            // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
  uint32 deep_type = 6;              // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
  uint32 deep_ecpm_fee = 7;          // deep计费
  double compute_ctr = 8;            // 投出广告创意ctr, 预估ctr值（若有拟合策略则存拟合后的值，没有存原始预估值）
  double compute_cvr = 9;            // 投出广告创意cvr, 预估cvr值（若有拟合策略则存拟合后的值，没有存原始预估值）
  double p_retain_rate = 10;         // 投出广告预估次留率
  double p_pay_rate = 11;            // 投出广告预估付费率
  double predict_ctr = 12;           // 拟合前预估ctr值
  double predict_cvr = 13;           // 拟合前预估cvr值
  string product_market_id = 14;     // 返回广告产品的市场id
  int64 deep_val = 15;               // 主要考核指标值
  uint32 original_price = 16;        // 广告主原始价格
  uint32 max_price = 17;             // 最高出价
  uint32 min_price = 18;             // 最低出价
  string vid = 19;                   // imp id
  string advertiser_category = 20;   // 广告主分类
  bool income_is_ocpm = 21;          // 是否支持oCPM
  uint32 ocpm_cpa_price = 22;        // 经过运营出价策略后用于预估ecpm的cpa出价，单位：分
  string conversion_target = 23;     // oCPM广告转化目标
  double predict_ecpm_weight_network = 24;  // top1替换扶持系数
  bool is_joint_control = 25; // 是否是联合唤起频次控制
  bool is_premium = 26;                     // 是否bidding溢价
  uint32 before_premium_ecpm = 27;          // bidding溢价前ecpm
  double settlement_ratio = 28;             // bidding溢价系数
  float deep_prem = 29;                        // 深度转化溢价系数
  float p_pay_amount = 30;                  // 预估付费额
  uint32 cluster_code = 31;                 // 转化目标，17-电商订单
  uint32 widget_sensitivity = 32;           // 互动组件灵敏度，取值 [1 , 10]
  AppAdSetting app_ad_setting = 33;         // 广告设置
  int32 redirect_count = 34;                // 跳转次数，-1: 不允许跳转；0: 不限制跳转; >0: 跳转次数
  uint32 respect_user_ctr_level = 35;       // 青提user ctr level
  map<string,string> qingti_macros = 36;    // 青提宏参数
  string bidding_win_url = 37;              // 竞价成功回调地址
  string bidding_lose_url = 38;             // 竞价失败回调地址
}

message UserInfo {
  bool is_silent = 1; // 是否沉默用户
}

message DspLogV2 {
  string sid = 1;                                         // 唯一标识一次竞价请求，内部生成。建议和sigmob.BidRequest.request_id一致
  int64  timestamp = 2;                                   // 接收到请求的服务器unix时间戳，单位：秒
  string app_id = 3;                                      // app_id
  string uid = 4;                                         // 用户内部UID，用户在sigmob的唯一标识。
  uint64 error_code = 5;                                  // 错误码. 和sigmob.response中的error_code一致； 内部使用
  uint32 cold_start_stage = 6;                            // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  repeated uint64 lookup_ad_list = 7;                     // 检索广告列表
  repeated uint64 index_os = 8;                           // 【索引-操作系统】后剩余计划全集
  repeated uint64 index_base = 9;                         // 【索引-运营商过滤】后剩余计划全集
  repeated uint64 index_dmp = 10;                         // 【索引-DMP人群定向排除】后剩余计划全集
  repeated uint64 valid_creative_ids = 11;                // 参与ranking的创意id列表
  repeated DspDcUnvalidAd filter_unvalid_ads = 12;        // filter阶段过滤无效广告
  repeated DspDcUnvalidAd normal_unvalid_ads = 13;        // 冷启动正常阶段无效广告
  repeated DspDcUnvalidAd random_unvalid_ads = 14;        // 冷启动随机阶段无效广告
  repeated DspDcUnvalidAd trial_unvalid_ads = 15;         // 冷启动试投阶段无效广告
  repeated ValidCreative valid_creatives = 16;            // filter创意过滤后的创意信息列表, top200
  bool is_into_abtest = 17;                               // 是否进入abtest实验（存在请求算法，但是没有被预估情况）
  bool disable_closecard = 18;                            // 是否禁用关闭页广告
  string adslot_id = 19;                                  // 广告位id
  uint32 adslot_type = 20;                                // 广告位类型
  uint32 trans_adslot_type = 21;                          // 转换后广告位类型
  uint32 bidfloor = 22;                                   // 地板价
  repeated DspLogAd ads = 23;                             // 投出广告信息
}

message DspLogAd {
  uint64 campaign_id = 1;                                // 计划id
  uint64 creative_id = 2;                                // 创意id
  uint32 cold_start_state = 3;                           // 终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  string vid = 4;                                        // vid，同imp id
  bool is_predict = 5;                                   // 是否被预估
  uint32 override_type = 6;                              // override类型，1:cpm; 2:cpc; 3:cpa
  uint32 override_erpm = 7;                              // 人工设置出价，优先级最高。为0表示没设置。单位：分
  uint32 ecpm = 8;                                       // eCPM, 单位:分
  uint32 bid_type = 9;                                  // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
  uint32 bid_price = 10;                                 // 投放平台的出价价格，单位：分
  double compute_ctr = 11;                               // 最终计算时的ctr
  double compute_cvr = 12;                               // 最终计算时的cvr
  double selected_score = 13;                            // 选定创意的score值
  uint32 serving_speed = 14;                             // 投放方式 1:正常投放 2:匀速投放v1 5:匀速投放v2
  uint64 video_id = 15;                                  // 视频素材ID
  uint64 endcard_id = 16;                                // 着陆页素材ID
  uint64 image_id = 17;                                  // 图片素材ID
  uint32 ctype = 18;                                     // 广告交互类型。1=使用浏览器打开；2=下载应用
}

message IndexResponseV2 {
  repeated string lookup_ad_list = 1;                     // 检索广告列表
  operation.AppSession app_session = 2;                   // App Session, SSP不使用
  repeated string random_unvalid_ads = 4;                 // 冷启动随机阶段无效广告
  operation.AppSlotTargetCampIds app_slot_include = 5;    // app广告位定向包含
  operation.AppSlotTargetCampIds app_slot_except = 6;     // app广告位定向排除
  repeated ValidCreative valid_creatives = 7;             // filter创意过滤后的创意信息列表(非RTA)
  repeated ValidCreative valid_rta_creatives = 8;         // filter创意过滤后的创意信息列表（RTA广告）
  repeated string filter_unvalid_ads = 9;                 // filter阶段过滤无效广告
  repeated string trial_unvalid_ads = 10;                 // 冷启动试投阶段无效广告
  repeated string normal_unvalid_ads = 11;                // 冷启动正常阶段无效广告
  repeated string test_unvalid_ads = 12;                  // 测试阶段无效广告
  uint64 cold_start_stage = 13;                           // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  bool disable_override = 14;                             // abtest划分override是否生效实验的结果
  repeated ValidCreative valid_closecard_creatives = 15;  // valid closecard creative
  uint32 lastMaxEcpm = 16;                                // 最近一段时间内uid+adtype+appid最高ecpm
  repeated SelectedAd ads = 17;                           // 被选中的ad
  map<string, bool> rta_service_ids = 18;                 // 真实发起请求RTA列表
}

message SelectedAd{
  string vid = 1;                             // imp id
  uint64 campaign_id = 2;                     // 选取的广告计划
  uint64 creative_id = 3;                     // 选取的广告创意
  int32 creative_index = 4;                   // 选取的广告在valid_creatives的序号
  uint64 cold_start_state = 5;                // 选取的创意的冷启动状态 1-随机 3-试投 7-正常
  EcpmTargetParams ecpm_target_params = 6;    // 选取的广告ecpm target调节相关参数
}

// 媒体运营广告设置
message AppAdSetting {
  bool disable_full_click_on_video = 1;     // 禁用视频全屏可点：false-不禁用；true-禁用
  bool disable_full_click_on_endcard = 2;   // 禁用endcard全屏可点：false-不禁用；true-禁用
  bool disable_full_click_on_companion = 3; // 禁用伴随条全域可点：false-不禁用；true-禁用
  bool disable_full_click_on_ad = 4;        // 禁用广告全屏可点：false-不禁用；true-禁用（暂时只有插屏广告使用）
}

// 搜索广告对象
message SearchInfo {
  string key_words = 1;   // 搜索关键字
  string goal = 2;        // 搜索目标
}