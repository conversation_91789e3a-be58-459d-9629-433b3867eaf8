/**
@description 所有track事件日志集合
    add TrackLog.user_ip
<AUTHOR>
@版本: 1.1.1
@data 2018-03-21
**/

syntax = "proto3";
import "sigmob_rtb.proto";

option java_multiple_files = true;
option java_package = "com.sigmob.sigdsp.pb";
option go_package = "sigmob.com/common/entry/track";


message TrackLog {
    string request_id = 1;              // 唯一标识一次竞价请求，内部生成。
    string vid = 2;                     // 曝光ID
    int64 timestamp = 3;                // 接收到请求的服务器时间戳(毫秒),unix时间戳
    string channel_id = 4;              // 必填！流量来源渠道编码：1=自有ssp流量
    string adslot_id = 5;               // 广告位ID
    string tracking_event_type = 6;     // 事件类型:start, finish, show, click, active
    AdInfo adInfo = 7;                  // 广告相关信息
    System sys = 8;                     // 系统相关信息
    uint32 os_type = 9;                 // 操作系统类型. 1=IOS；2=Android
    uint32 ad_type = 10;                // 广告类型。1 奖励视频
    sigmob.DeviceId did = 11;           // 唯一设备标识，必需按要求填写
    string app_id = 12;                 // 应用ID
    string user_ip = 13;                // 用户的ipv4地址
    uint32 bucket_type = 14;            // 投放类型。0：正常阶段；1：随机阶段；2：试投阶段；3：测试设备桶；
    string urlParams = 15;              // 除了c,e,p以外所有trackurl中的参数
    uint32 is_test = 16;                // 是否测试设备，1：为测试设备，0：为非测试设备
    bool dont_billing_by_callback = 17; // true：callback不作为计费项， false：callback作为计费项
    uint64 stage = 18;                  // 投放冷启动算法状态，1-随机 3-试投 7-正常
    uint32 s_effective = 19;            // 是否参与分成
    uint32 s_type = 20;                 // 媒体侧计费方式，1 cpm, 2 cpc, 3 cpa
    uint32 f_type = 21;                 // 流量来源 0-sdk; 1-oppo; 2-adxData(美数)， 同SearchLog.request_flow_type
    uint32 alg_bid_type = 22;           // 0-代表走正常floor, 1-算法侧按target调节
    uint32 ecpm_target = 23;            // 平台计算出的ecpm target，单位：分
    uint32 settlement = 24;             // 平台settlement结算模式
    uint32 trans_ad_type =25;           // 转换后广告类型
    uint32 commer_ecpm_state = 26;      // 固定erpm结算模式状态：0、关闭，1:开启
    int64 req_timestamp = 27;           // 广告请求时间，unix时间戳
    string api_appid = 28;              // dsp媒体id
    string api_placement_id = 29;       // dsp广告位id
    uint32 adx_id = 40;                 // adx渠道ID
    uint32 re_transfer = 41;            // 是否补报
    bool is_closecard_ad = 42;          // 是否关闭页广告
    string additional_click_pkg = 43;   // 点击补报媒体包名
    bool is_search = 44;                // 是否关键字搜索广告
    uint32 layer910_model = 45;         // 910层model code
    uint64 iaa_allowance_pool = 46;     // IAA扶持资金池id
    uint32 iaa_allowance_de = 47;       // 返回的IAA扶持金额，单位：分
    string ssp_app_id = 48;             // API接入方式的媒体自定义appid
    string ssp_placement_id = 49;       // API接入方式的媒体自定义广告位id
    string dsp_request_id = 50;         // 唯一标识一次竞价请求dsp，内部生成
    int32 enable_dsp_req_id = 51;				// 是否开启差异化id
    string saas_load_id = 52;           // saas adx load_id
    string saas_channel_id = 53;        // saas adx channel id
    string saas_sub_channel_id = 54;    // saas adx sub channel id
    string saas_ad_channel_id = 55;     // saas adx ad channel id
}

//广告信息
message AdInfo {
    string cust_id = 1;                 // 广告主唯一标识
    string camp_id = 2;                 // 计划的系统id
    string creative_id = 3;             // 创意的系统id
    uint32 bid_type = 4;                // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
    uint32 advertiser_price = 5;        // 广告主在平台的出价价格
    uint32 bid_price = 6;               // 投放平台的出价价格
    uint32 settlement_price = 7;        // 媒体和投放平台的结算价格
    string settlement_price_enc = 8;    // 媒体和投放平台的结算价格密文
    string adver_app_id = 9;            // 下载类广告时有值，为app store id
    string material_id = 10;            // 创意素材id
    string product_id = 11;             // 全局产品id
    uint32 ad_source_channel=12;        // 渠道代码。sigmob：1000. 默认为0也代表1000
    double compute_ctr = 13;            // 参与ranking的ctr
    double compute_cvr = 14;            // 参与ranking的cvr
    bool is_predict = 15;               // 是否被算法预估
    uint64 prod_id = 16;                // 产品id，对应sig_dsp_adver_campaign.prodid
    uint64 video_id = 17;               // 素材视频id
    uint64 image_id = 18;               // 素材图片id
    uint64 endcard_id = 19;             // 素材endcard_id
    uint32 bid_price_weight=20;         // 广告位多出价权重，单位: 百分比%
    uint32 original_price = 21;         // 添加RTA label weight前广告主价格
    uint32 compute_ecpm = 22;           // 直投预估eCPM出价
    bool income_is_ocpm = 23;           // 是否支持oCPM
    uint32 ocpm_cpa_price = 24;         // 经过运营出价策略后用于预估ecpm的cpa出价，单位：分
    string conversion_target = 25;      // oCPM广告转化目标
    uint32 click_type = 26;             // 曝光上报点击属性: 0-正常点击, 1-含预估值, 2-随机值
    uint64 template_id = 27;            // 模板id
}

//请求的系统信息
message System {
    string client_ip = 1; // 发起track请求客户端的ipv4地址
    string useragent = 2; // 发起track请求客户端的UA信息
    string referer = 3;   // http referer
    string server_ip = 4; // 接收请求的server的ipv4地址
}

message DmiMaterialInfo {
  int64 developer_id = 1;                 // 开发者ID
  string preview_id = 2;                  // 预览ID
  string prod_name = 3;                   // 产品名称
  string video_url = 4;                   // 视频地址
  repeated string image_url_list = 5;     // 图片地址列表，升序排列（Ascending order）
  string title = 6;                       // endcard标题
  string desc = 7;                        // endcard描述
  string target_url = 8;                  // 目标地址
}


//message DspLog{
//    string request_id = 1;                        //唯一标识一次竞价请求，内部生成
//    string campaign_id = 2;                       //计划id
//    string creative_id = 3;                       //创意id
//    string app_id = 4;                            //应用id
//    int64  timestamp = 5;                         //接收到请求的服务器时间戳(毫秒),unix时间戳
//    uint32 bucket_type = 6;                       //分桶类型：0=优化桶，1=随机桶，2=随机桶无广告，走优化桶
//
//    uint32 no_creative_filter = 7;                //无创意计划过滤
//    uint32 camp_time_filter = 8;                  //计划投放时间段过滤
//    uint32 app_slot_filter = 9;                   //app广告位定向包含过滤
//    uint32 product_fatigue_filter = 10;           //产品疲劳期过滤器
//    uint32 active_product_filter = 11;            //产品已激活过滤
//    uint32 uniform_delivery_filter = 12;          //匀速投放过滤
//    uint32 bucket_filter = 13;                    //分桶过滤，随机桶过滤老创意，排序通过滤新创意
//    uint32 creative_state_filter = 14;            //创意状态过滤
//    uint32 orientation_filter = 15;               //横竖屏过滤
//    uint32 sdk_cached_filter = 16;                //sdk已经缓存过滤
//    uint32 last_creative_filter = 17;             //sdk上一次播放创意过滤
//    uint32 material_fatigue_filter = 18;          //素材疲劳期过滤器
//    uint32 creative_fatigue_filter = 19;          //素材疲劳期过滤器
//    uint32 bid_price_filter = 20;                 //地板价过滤
//    uint32 participate_optimise_ranking = 21;     //参与排序
//    uint32 participate_random = 22;               //参与随机
//    uint32 auto_bucket_creative_filter = 23;      //自动分桶比例创意weight过滤
//    uint32 app_target_filter = 24;                //媒体侧计划定向包含
//    uint32 app_target_except_filter = 25;         //媒体侧计划定向排除
//    uint32 creative_frequency_filter = 26;        //创意时间间隔过滤
//    uint32 no_prediction_cvr_ctr_filter = 27;     //离线无预估值创意过滤
//    uint32 app_slot_except_filter = 28;           //app广告位定向排除过滤
//}
