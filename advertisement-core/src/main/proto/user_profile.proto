/**
@description	用户属性。
	用户属性分为两部分：
	一部分为离线属性：比如人口属性、历史激活过的appid列表等
	另一部分为在线属性：比如历史观看过的创意计数、fatigue计数等
<AUTHOR>
@版本: 2.1.0
**/

syntax = "proto3";

package profile;

option java_package = "com.sigmob.sigdsp.grpc";
option go_package = "sigmob.com/common/entry/profile";

// user profile
message UserProfile {
	string uid = 1;							        // 用户在sigmob的唯一标识。
	string actived_product_ids = 2; 		        // 用户已激活产品列表。当前为iTunes ID列表
    map<string,ViewCampaign>  view_campaign = 3;  	// 用户观看过的广告信息, key:[campId]_[appId]_[adType]
	string gender = 4;								// 性别
	string age = 5;									// 年龄
	string career = 6;								// 职业
	string edu = 7;									// 教育程度
	string marriage = 8; 							// 婚姻状况
	PCTRAdList p_adList = 9;						// 预估出的适合投放的创意列表
    string interested = 10;				            // 兴趣爱好  参见sig_dsp_adver_target_template intersted
    string game = 11;                               // 游戏喜好
    string travel = 12;                             // 出行方式
    string shopping = 13;                           // 购物方式
    string pet = 14;					        	// 宠物
    map<string,ViewProduct> view_product = 15;  	// 用户观看过的广告信息, key:[productId]_[appId]_[adType]
    string payment_capacity = 16;                   // 付费能力
    string game_style = 17;                         // 游戏画风
    string game_theme = 18;                         // 游戏题材
    string game_strategy = 19;                      // 游戏玩法
    string game_scale = 20;                         // 游戏程度
    int32 is_emulator = 21;                         // 是否为模拟器
    repeated int64 package_codes = 22;              // 人群包编码数组
    bool is_silent = 23;                            // 是否沉默用户
}

message ViewCampaign {
    uint64 id = 1;
    uint64 imp_fatigue_period_counter = 2; 			// 疲劳期内曝光计数
    int64 imp_fatigue_time = 3;         			// 首次到达疲劳曝光的时间点（unix时间戳,格林威治时间1970年01月01日00时00分00秒起至现在的总秒数）
    map<uint64, ViewCreative> view_creative = 15; 	// 观看的创意信息
}

message ViewProduct {
    uint64 id = 1;                                  // 全局产品id
    int64 product_last_imp_time = 2;                // 上一次曝光时间戳，单位：秒
    uint64 product_fatigue_counter = 3; 			// 疲劳期内曝光计数
    map<uint64, ViewMaterial> view_material = 4; 	// 观看的素材信息
}

message ViewMaterial {
    uint64 id = 1;              			        // 视频素材id
    int64 material_last_imp_time = 2;               // 上一次曝光时间戳，单位：秒
    uint64 material_fatigue_counter = 3; 		    // 疲劳期内曝光计数
    map<uint64, ViewCreative> view_creative = 4; 	// 观看的创意信息
}

//用户适合投放的广告
message ViewCreative {
    uint64 id = 1;              				// 创意id
    uint64 imp_frequency_counter = 2;       	// 【废弃】
    int64 imp_frequency_latest_reset_time = 3;  // 【废弃】
    uint64 imp_total_num = 4;					// 总展现次数（疲劳期内曝光计数），creative_fatigue_counter
    uint64 click_total_num = 5;					// 总点击次数
    int64 imp_fatigue_time = 6;         		// 上一次曝光的时间戳, 单位：秒， creative_last_imp_time
}

// 预估提供的创意信息
message PCreative {
	uint64 id = 1;			// 创意local id
	double p_ctr = 2;     	// 预估的ctr
	double p_cvr = 3;     	// 预估的cvr
}

// 预估创意列表
message PCTRAdList {
	string version = 1;				 // 版本号
	int64 pctr_expire = 2;			 // 预估人群有效期（unix时间戳,格林威治时间1970年01月01日00时00分00秒起至现在的总秒数）
	repeated PCreative creatives =3; // 预估的创意信息
}