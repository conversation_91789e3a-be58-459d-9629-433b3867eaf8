syntax = "proto3";

package sigmob;

import "sigmob_rtb.proto";

option java_package = "com.sigmob.ad.uid.rpc.grpc";
option java_multiple_files = true;


//############################## uid基本数据结构 #####################################//
//设备uid信息
message UidInfo {
  string uid = 1;                 // 设备uid
  uint64 createTime = 2;          // 生成时间，单位纳秒
  enum UidType {
    NULL = 0; //无意义，不使用
    IMEI = 1; // imei
    IDFA = 2; // idfa
    IDFV = 3; // idfv
    UDID = 4; // udid
    SIGMOB_CAID = 5; //sigmob 属性id
    OAID = 6; // oaid
    GAID = 7; // gaid
    ANDOIRD_ID = 8; //android_id
    IMEI_MD5 = 9; //imei md5值
    IDFA_MD5 = 10; //idfa md5值
    OAID_MD5 = 11; //oaid md5值
    GAID_MD5 = 12; //gaid md5值
    ANDROID_ID_MD5 = 13; //android_id_md5;
  }
  UidType uidType = 3;               // uid类型
  string idfa = 4;                   // 设备idfa
  string idfa_md5 = 5;               // 设备idfa md5值
  map<string, IdfvUdids> idfv_udid_mapping = 6;      // idfv和所属udid映射关系,key为idfa
  DeviceAttribution device_attr = 7; // 设备属性
  string sigmob_caid = 8;            // 当前设备属性计算的属性id
  string sigmob_caid_old = 9;        // 旧sigmob 属性id
  string mainUid = 10;               // 对应主uid，用于设备合并。主id生成时间需要小于本uid生成时间
  repeated string subUids = 11;      // 关联子id列表
  map<string, string> sigmob_caids = 12;     // 属性id, key: 版本号_(old|new)标识新旧key；value: 该版本算法生成属性id
  string cdaCaid = 13;               // 广协caid接口返回设备caid
}

//idfv对应的udid集合
message IdfvUdids {
  //  string idfv = 1;                  // 设备idfv
  repeated string udid = 1;             // idfv对应的udid集合
}

//设备属性，用于生成sigmob caid
message DeviceAttribution {
  string model = 1;                 // 设备型号
  Version os_version = 2;           // 手机系统版本
  uint64 total_size = 3;            // 总存储容量
  string carrier_code = 4;          // 运营商代码
  string time_zone = 5;             // 时区
  string system_update_time = 6;    // 系统更新时间
  uint64 start_time = 7;            // 系统开机时间
  string device_name = 8;           // 设备名称
  uint64 mem_size = 9;              // 系统内存大小
  uint32 country_code = 10;         // 设备国家代码
}


// ################################### SSP 请求uid信息 ###################################
//ssp请求uid服务对象
message SspUidRequest {
  string request_id = 1;   // 请求id
  uint32 os_type = 2;      // 操作系统类型：1-iOS；2-Android
  Device device = 3;       // 设备信息
  Network network = 4;     // 设备网络信息
  string idfv = 5;         // 设备idfv
  uint32 country_code = 6; // 国家代码
  bool disable_update_uid = 7;     // 是否禁止新增/更新uid信息, false-不禁止; true-禁止
}

//uid服务响应ssp请求返回
message SspUidResponse {
  string uid = 1;                // 设备uid
  UidInfo.UidType uid_type = 2;  // 设备uid类型
  string sigmob_caid = 3;        // sigmob 属性id
}

service SspUidService {
  rpc getUid(SspUidRequest) returns(SspUidResponse);
}

// ################################### 数据请求uid服务 ######################################

//######################### 数据更新caid请求 #######################################
message CaidRequest {
  string request_id = 1;   // uid原始请求id
  uint32 os_type = 2;      // 操作系统类型：1-iOS；2-Android
  string uid = 3;          // 设备uid
  string cda_caid = 4;     // 广协caid
}

message CaidResponse {
  uint32 code = 1;        // 处理更新请求代码：
  string message = 2;     // 0-请求更新成功; 10000-服务内部错误; 10002-请求参数格式错误; 10004-请求缺少必要参数; 10005-不支持的设备os类型
}

service CaidUpdateService {
  rpc updateCaid(CaidRequest) returns(CaidResponse);
}

//message UidRequest {
//  string request_id = 1;     // 请求id
//  string device_id = 2;      // 设备id（UidInfo.uidType中的一种）
//  uint32 uid_type = 3;       // 设备id类型（具体值参见UidInfo.uidType）
//  uint32 os_type = 4;        // 设备操作系统类型：1-iOS; 2-Android
//}
//
//message UidResponse {
//  string uid = 1;            // 设备uid
//  uint32 uid_type = 2;       // 设备uid类型（具体值参见UidInfo.uidType）
//}
//
//service UidService {
//  rpc getUid(UidRequest) returns(UidResponse);
//}