/**
@description sigmob mock dsp
<AUTHOR>
**/

syntax = "proto3";
package sigmob;

import "sigmob_rtb.proto";
option go_package = "sigmob.com/common/entry/sigmob";
option java_package = "com.sigmob.mockdsp.pb";

// request
message MockBidRequest {
    string request_id = 1;           // 请求id
    repeated AdRule ad_rules = 2;    // 广告要求，可返回多个广告
    uint32 request_flow_type = 3;    // 流量来源 http://wiki.sigmob.cn/pages/viewpage.action?pageId=40079564
    string adslot_id = 4;            // 广告位id
    Version sdk_version = 5;         // sdk版本
}

message AdRule {
    string imp_id = 1;                   // imp id
    uint32 adslot_type = 2;              // 广告类型
    repeated uint32 creative_type = 3;   // 物料类型
    repeated uint32 support_action = 4;  // 广告行为动作
    uint32 os = 5;                       // 操作系统
    uint32 adslot_width = 6;             // 广告位宽
    uint32 adslot_height = 7;            // 广告位高
    repeated RequestNativeAdTemplate native_template = 8;     // native模版
    uint32 orientation = 9;              // app方向：0: MaskAll、1:portrait、2:landspace
    uint32 show_style = 10;                                     // 展现形式。0-全屏；1-半屏
    repeated uint32 material_type = 11;                         // 媒体支持的素材格式列表。插屏：0-视频；1-图片；2-图片视频混出
}

// response: sigmob.BidResponse