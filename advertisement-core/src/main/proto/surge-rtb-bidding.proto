syntax = "proto2";

package proto.adx.surge;
option java_package = "com.sigmob.ad.adx.dsp.model.surge";
option java_outer_classname = "SurgeBidding";

message BidRequest {
  required string id = 1; // 请求ID，唯一标识本次请求，明文字符串
  optional string api_version = 2; // 接口版本号, 目前传1.0.0
  optional uint32 test = 3 [default = 0]; // 如果不为0，那么这是一个测试请求。
  repeated Imp imp = 4; // 曝光机会列表，信息流类位置一次请求会有多个曝光机会
  optional App app = 5; // APP信息
  optional Device device = 6; // 设备信息
  optional Pmp pmp = 7; // 交易定义
  optional CustomizedUserTag customized_user_tag = 8; // 自定义标签信息
  optional int32 timeout = 9; // 要求DSP必须在timeout时间内返回，单位【ms】
  optional int64 media_time = 10; // 媒体下发的请求时间戳，单位【ms】
  optional int64 ssp_time = 11; // ssp下发的请求时间戳，单位是【ms】
  optional Others others = 12; // 其他可填信息
  message Device {
    optional string ua = 1; // UA信息
    optional string ip = 2; // 用户设备ip
    optional int32 device_type = 3; // 设备类型：0-未知 3-PC 4-手机Phone 5-平板Tablet 6-联网设备Connected Device 7-机顶盒Set Top Box
    optional string make = 4; // 手机品牌，如：iPhone，Xiaomi
    optional string model = 5; // 手机型号，如：iPhoneX, KNT-AL10
    optional string idfa = 6; // IDFA原始值
    optional string idfa_md5 = 7; // IDFA MD5值
    optional string oaid = 8; // Android OAID原始值
    optional string oaid_md5 = 9; // Android OAID MD5值
    optional string imei = 10; // Android IMEI原始值
    optional string imei_md5 = 11; // Android IMEI MD5值
    optional string os = 12; // 设备操作系统类型，android/ios
    optional string osv = 13; // 设备操作系统版本号buxian
    optional int32 carrier = 14; // 运营商 0：未知 1：中国移动 2：中国联通 3：中国电信
    optional int32 connection_type = 15; // 网络连接状态 0：未知 1：Ethernet以太网 2：WIFI 网络 3：蜂窝数据网络 - 未知 4：蜂窝数据网络 - 2G 5：蜂窝数据网络 - 3G 6：蜂窝数据网络 - 4G 7：蜂窝数据网络 - 5G
    optional string android_id = 16; // Android Id原始值
    optional string android_id_md5 = 17; // Android Id MD5值
    optional string mac = 18; // MAC地址原始值
    optional string mac_md5 = 19; // MAC地址MD5值，加密前先去除分隔符':'
    optional string caid_version = 20; // caid版本
    optional string caid = 21; // caid原始值
    optional string caid_md5 = 22; // caid md5值
    optional string boot_mark = 23; // 系统启动时间
    optional string update_mark = 24; // 系统更新时间
    repeated CaidInfo caid_infos = 25; // caid列表
    // 多版本CAID信息
    message CaidInfo {
      // CAID版本
      optional string version = 1;
      // CAID原值
      optional string caid = 2;
      // CAID md5值，caid原值进行md5后小写输出
      optional string caid_md5 = 3;
    }
  }
  message Imp {
    required string id = 1; // 区分于请求ID，标识唯一一次曝光机会
    optional string tag_id = 2; // 广告位标识ID
    optional string sub_tag_id = 3; // 明细广告位标识ID
    optional int32 ad_type = 4; // 广告类型，1:信息流、2:开屏、3：banner 、4：插屏、9：激励视频
    optional int32 bid_type = 5; // 出价类型, 0：cpm 1：cpc 默认为cpm
    optional int32 bid_floor = 6; // 竞价底价，单位：分/千次展现
    repeated Asset asset = 7; // 模版信息，详见模版资源映射表
    optional int64 cpc_bid_floor = 8; // cpc竞价底价，单位：分/点击
    optional int32 ads_count = 9; // 创意数量，默认为1
    optional float ad_ctr = 10;  // 广告位的历史n天（n >= 3)CTR统计值
    optional int32 ad_pv_level = 11;  // 广告位的历史n天 (n >= 3)PV取log10后的整数部分，如5表示曝光PV在区间[10^5, 10^6)
    repeated double ad_emb = 12;   // 广告位的多维向量
    repeated AdInfoMap ad_info_map = 13;
    repeated int32 ad_styles = 14;  //1:普通网址类，2:应用下载类，3:deeplink+普通网址类，4:deeplink+应用下载类，5:自定义h5+应用下载，6:快应用
    message Asset {
      optional string template_id = 1; //  广告位模版ID, 详见模版资源映射表
      optional int32 width = 2; // 广告位模版宽度
      optional int32 height = 3; // 广告位模版高度
      optional int32 priority = 4 [default = -1]; // 模版填充优先级，默认-1，值越小优先级越高
    }
    message AdInfoMap {
      // 广告位信息key，命名规则参考文档，如ad_ctr_7d、ad_ctr_15d
      required string key = 1;
      required string value = 2;
    }
  }
  message App {
    optional string name = 1; // 应用名称：例如：喜马拉雅FM
    optional string bundle = 2; // 应用程序包名：例如：com.ximalaya.iting
    optional string verion = 3; // 应用版本号: 例如: 2.3.5
  }
  message Pmp {
    required string deal_id = 1; // 直接交易的唯一ID
    required int32 pmp_bid_type = 2; // 包段出价类型, 0：cpm 1：cpc
    required int32 pmp_price = 3; // 包段价格, 单位：分/千次展现
    required bool support_quit = 4; // 是否支持退量
  }
  message CustomizedUserTag {
    // 用户已安装app列表字典：id可自定义，name为app的包名
    // 若SSP有汇总好的app字典，可以通过线上只传id，线下汇总统计
    message InstalledApp {
      optional uint32 id = 1;
      optional string name = 2;
    }
    repeated InstalledApp installed_app_list = 1;
    repeated string u_tags = 2;   // 用户自定义标签列表
    optional float u_ctr = 3;     // 用户的历史n天（n >= 15)CTR统计值
    repeated double u_emb = 4;    // 用户多维向量
    optional int32 u_pv_level = 5; // 用户的历史n天（n >= 15)PV取log2后的整数部分，如5表示曝光PV在区间[2^5, 2^6)
    optional int32 u_buy_level = 6; // 用户购买等级，当前用户的历史n天(n >= 15)购买单量取log2后的整数部分
    optional float u_wake2pay_rate = 7; // 用户的历史n天（n >= 15)唤端后购买的比例，即购买pv与唤端pv的比值
    optional int32 u_e_com_time_level = 8; // 用户电商活跃度等级
    repeated string u_e_com_category = 9; // 用户电商偏好类目
    optional int64 open_e_com_time = 11;  // 用户最近打开电商类app的时间戳，unix时间戳，单位是：【ms】
    optional float active_e_com_time = 12; // 用户最近一次电商类app的停留时长
    optional float active_device_time = 13; // 用户设备使用时长
    repeated string active_app_list = 14;  // 用户近期活跃app list
    repeated UserInfoMap user_info_map = 15;
    message UserInfoMap {
      // 用户信息key，命名规则参考文档，如u_ctr_7d、u_ctr_15d
      required string key = 1;
      required string value = 2;
    }
  }
  message Others {
    // 请求预估的CTR
    optional float pctr = 1;
  }
}

message BidResponse {
  optional string id = 1; // 请求ID，需与BidRequest.id一致
  optional string bid_id = 2; // surge侧竞价ID
  optional int32 nbr = 3; // 未出价原因
  repeated SeatBid seat_bid = 4; // DSP参与竞价的具体内容，目前只有一个
  message SeatBid {
    repeated Bid bid = 1; // DSP参与竞价的位置，与BidRequest.imp对应，每个imp最多只可返回一个bid
  }
  message Bid {
    optional string id = 1; // dsp侧针对这次竞价的ID
    optional string imp_id = 2; // 曝光ID，对应BidRequest.imp.id，必填！
    optional int64 price = 3; // DSP出价，单位：分/千次曝光
    optional string creative_id = 4; // 创意ID
    optional string nurl = 5; // 竞价成功通知地址
    optional string lurl = 6; // 竞价失败通知地址
    repeated string imp_trackers = 7; // 曝光监测地址
    repeated string clk_trackers = 8; // 点击监测地址
    optional Adm adm = 9; // 创意物料信息
    optional string deal_id = 10; // 直接交易的唯一ID
    optional string package_name = 11; // 安装包包名
    optional string app_name = 12; // 应用名称
    optional string icon_url = 13; // 应用icon url
    optional int32 user_score_level = 14; // 用户质量分
    optional int32 bid_type = 15; // 出价类型, 0 - CPM、1 — CPC、2 - CPA
    optional int32 origin_bid_type = 16; // 原始出价类型, 0 CPM、1 CPC: 被低价过滤 用cpa参竞、广告主的算法也有按照cpm或cpc给过出价，originBidType表示算法出价的方式
    optional int64 original_win_price = 17; // 原始竞胜价，结算价格，单位：分/cpm，即对于有补贴/扶持的出价，返回真实出价
    //物料信息
    message Adm {
      optional string template_id = 1; // 模版ID
      optional string title = 2; // 标题
      optional string desc = 3; // 描述
      repeated Image image = 4; // 图片信息
      optional Video video = 5; // 视频信息
      optional int32 ad_type = 6; // 推广物类型
      optional string deep_link = 7; // 客户端优先跳转deeplink链接,其次跳转普通网址类或应用下载类的落地页
      optional string universal_link = 8; // ios 唤起链接
      optional string landing_site = 9; // 落地页url
      optional string deal_id = 10; // 交易ID
      optional string cover_img_url = 11; //视频素材封面图片
      message Image {
        optional string url = 1; // 图片URL
        optional int32 width = 2; // 图片宽度
        optional int32 height = 3; // 图片高度
        optional string mime = 4; // 图片类型， 如：JPG/JPEG/PNG
      }
      message Video {
        optional string video_url = 1; // 视频URL
        optional int32 width = 2; // 视频宽度
        optional int32 height = 3; // 视频高度
        optional int32 duration = 4; // 视频素材的播放时长，单位：秒
        optional int32 bitrate = 5; // 视频文件的码率
        optional int32 size = 6; // 视频文件的大小，单位Byte
        optional string mime = 7; // 视频素材类型，以MIME类型表示，当前仅支持"video/mp4"
      }
    }
  }
  optional int64 cold_end_time = 5; // 设备冷却结束时间，unix时间戳，单位是：秒
  optional int64 bid_time = 6; // 返回的时间戳，单位是【ms】
}