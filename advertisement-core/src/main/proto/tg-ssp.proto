syntax = "proto2";
package tg;

// [START java_declaration]
option java_package = "com.sigmob.ad.adx.dsp.model.jdtg";

enum IdentifierType {
    OTHER = 99; // 未知
    IMEI = 0; // 安卓 imei
    OAID = 1; // 安卓 oaid
    IDFA = 2; // ios idfa
    MAC = 3; // 设备 mac
    CAID = 4; // 设备 caid
    OAID_MD5 = 5; // 安卓 oaid MD5
    CAID_MD5 = 6; // 设备caid MD5
}

message Request {

    // 资源位信息
    message Source  {

        // 天宫广告位id
        optional string sourceId = 1;

        // 媒体资源位id
        optional string impressionId = 2;

        // 媒体资源位的宽和高
        optional int32 width = 3;
        optional int32 height = 4;

        // 是否为多张素材资源位
        optional bool isMulti = 5;

        // 是否为全屏
        optional bool isFullscreen = 6;
        
        // 投放日期：为空表示请求当前日期广告，非空表示预加载指定日期广告
        optional string deliveryDate = 7;
    }
    optional Source source = 1;

    // 设备信息
    message Device {

        // ipv4 点分十进制, 必须为终端真实IP地址
        optional string ip = 1;

        // IOS6.0及以上的idfa号;安卓设备的imei号;OTT设备的mac地址【已废弃】
        optional string identifier = 2;

        // IOS设备，原始idfa的md5值;安卓设备，原始imei的md5值;OTT设备，原始 mac 地址去冒号转的 md5 值
        optional string deiviceId = 3;

        // android_id
        optional string android_id = 4;

        // 设备类型，0-手机;1-平板;2-PC;3-互联网电视
        optional int32 device_type = 5;

        // 设备品牌
        // 例如：nokia, samsung
        optional string brand = 6;

        // 设备型号
        // 例如：n70, galaxy
        optional string model = 7;

        // 操作系统
        // 例如：Android,iOS 
        optional string os = 8;

        // 操作系统版本
        // 例如：7.0.2
        optional string osv = 9;

        // 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g [ default = 1 ]
        optional int32 network  = 10;

        // 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
        optional int32 operator = 11;

        // 设备屏幕尺寸：宽
        optional int32 width = 12;

        // 设备屏幕尺寸：高
        optional int32 height = 13;

        // 设备密度，对应于pixel_ratio [default=1000]
        optional int32 pixel_ratio = 14;

        // 屏幕方向 0-未知, 1-竖屏, 2-横屏
        optional int32 orientation = 15;

        // 用户所处时区的分钟偏移量[ default = 480 ]
        // 例如：如果是东八区，则 timezone_offset = 60 * 8 = 480.
        optional int32 timezone_offset = 16;

        message Geo {
            // 纬度, 取值范围[-90.0 , +90.0]
            optional double lat = 1;
            // 经度, 取值范围[-180.0 , +180.0]
            optional double lon = 2;
        }
        optional Geo geo = 17;
        
        // 设备号类型
        optional IdentifierType identifierType = 18;

        // oaid 【已废弃】
        optional string oaid = 19;

        message CaidInfo {
            // caid版本
            optional string version = 1; 
            // caid值         
            optional string caid = 2;      
            // caid md5值         
            optional string caid_md5 = 3;        
        }
        // caid
        repeated CaidInfo caid_info = 20;
    }
    optional Device device = 2;
}

message Response {

    //对应Request中的impressionId
    optional string impressionId = 1;

    // 广告应答状态码，返回码：0 表示成功，非 0 表示失败,无广告返回
    optional int32 status = 2;

    // 若有错误，此字段为详细的错误信息
    optional string msg = 3;

    // 广告内容
    message Ad {

            // 创意标题
            optional string title = 1;            

            // 曝光监测地址
            optional string impressionUrl = 2;

            // 点击地址，在触发点击时，通过此地址到达落地页   
            optional string clickUrl = 3;

            // 创意地址域名
            optional string creativeDomain = 4;

            message Creative {

                    // 创意序号，从0开始  
                    optional int32  id = 1;

                    // 创意地址，与creativeDomain组装形成完整创意地址   
                    optional string creativeUrl = 2;

                    // 创意内容md5
                    optional string md5 = 3;
            }
            repeated Creative creative = 5;
            
            // 三方曝光监测地址
            optional string thirdImpUrl = 6;

            // 三方点击监测地址
            optional string thirdClickUrl = 7;

            // 京东曝光监测地址
            optional string jdImpUrl = 8;

            // 京东点击地址，在触发点击时，通过此地址到达落地页
            optional string jdClickUrl = 9;
            
            // （三方+京东）直呼协议链接
            optional string openUrl = 10;
            
            // 京东直呼协议链接
            optional string jdOpenUrl = 11;

            // 创意副标题
            optional string subTitle = 12;

            // 广告主名称
            optional string source = 13;

            // 广告主头像
            optional string sourceAvatar = 14;   

            // 创意id
            optional int32 uniqId = 15;
            
            // 扩展点击监测地址集合
            repeated string extendClickUrls = 16;

            // （三方+京东）ULink链接
            optional string uLinkUrl = 17;

            // 京东ULink链接
            optional string jdULinkUrl = 18;
        }
    optional Ad ad = 4;
    
    // 是否需要媒体额外填充素材：true 需要填充；false 不需要填充，直接播当前返回素材
    optional bool needFill = 5;

    // 用户分值
    message UserScore {
            // 场景
            optional string scene = 1;
            // 分值，分值越高用户优先级越高，分值范围为：[0,10]
            optional double score = 2;
    }
    repeated UserScore userScore = 6;
}