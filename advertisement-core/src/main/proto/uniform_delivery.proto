/**
@description	用于匀速投放的相关PB协议
<AUTHOR>
@版本: 2.0.0
**/

syntax = "proto3";

package uniform;

option java_package = "com.sigmob.sigdsp.grpc";
option go_package = "sigmob.com/common/entry/uniform";

message UniformDelivery{
    string campaign_id =1;      // 所属的计划ID
    double weight = 2;          // 投放权重
}


message HisImpressionData{
    uint64 daily_avg = 1;               // 历史7天(不含当天)平均每天的所有广告曝光数
    map<uint64,uint64> hourly_avg = 2;  // key[0,23]，历史7天key时刻小时内7天广告曝光平均值
}