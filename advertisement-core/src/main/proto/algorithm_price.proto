// ADX HB预估出价协议
syntax = "proto3";
package hbprice;

option java_package = "com.sigmob.ad.adx.model.algirithm";

service HBPrice {
  rpc PredictPrice(Request) returns (Price) {}
  rpc Echo(Msg) returns (Msg) {}
}

message Msg {
  string msg = 1;
}

message Request {
  string uid = 1;           // 用户UID
  uint32 channel_num = 2;   // 填充渠道个数
  uint32 win_channel = 3;   // adx内部赢价渠道id，信息流先不要
  uint64 bid_price = 4;     // 赢价渠道最终成交价(币种：RMB, 单位：分）
  uint64 floor_price = 5;   // 流量的底价(单位分， rmb)
  string model_id = 6;      // 模型id
  Env env = 7;              // 上下文环境信息
  string request_id = 8;    // 请求id
}

message Env {
  string appid = 1;         // 媒体id
  string placement_id = 2;  // 广告位id
  uint32 adype = 3;         // 流量类型
  string pkgname = 4;       // 流量包名
  uint32 os = 5;            // 操作系统: 1-iOS; 2-Android
  string clienttype = 6;    // 设备型号
  string clientversion = 7; // 操作系统版本
  string country = 8;       // 国家代码，例：1156000000
  string province = 9;      // 省份代码，例：1156130000
  string city = 10;         // 城市代码，例：1156130600
}

message Price {
  uint64 predict_price = 1; // 预估出价，单位分
  uint32 code = 2;          // 返回码，0表示成功
}

