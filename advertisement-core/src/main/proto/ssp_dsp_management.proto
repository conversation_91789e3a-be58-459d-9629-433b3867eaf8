/**
@description ssp 端管理dsp渠道以及访问策略的相关对象

for sdk 1.8:
add SplashConfig
CommonEndpointsConfig add ads and strategy

**/
syntax = "proto3";
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.dspmanagement";
message DspInfo {
  uint32 dspId = 1; //dsp id，1000 sigmob，2000 广点通实时api，2001youmob实时api
  string requestUrl = 2; //dsp 请求地址
  bool logoEnabled = 3; //是否启用logo，true使用dsp自有logo，false使用sigmob logo
  string logoUrl = 4; //logo地址
  bool trafficEnabled = 5; //流量开关，true开启，false关闭
  bool rtb = 6; //是否rtb竞价渠道，true位rtb竞价，false非rtb
  bool isStandard = 7; //是否标准化api渠道，默认true（2.5.2新增）
  string publicKey = 8;
  string secretKey = 9;
  bool winNoticeEncrypt = 10; //赢价通知价格是否加密(*******新增)
  uint32 examineType = 11;   //dsp的审核类型，0:免审，1:抽审（3.0.0添加）
  repeated uint32 dealType = 12;  //支持的交易类型. 2-DEAL; 3-RTB; 4-API;5:pdb
  DspTrafficFilter dspTrafficFilter = 13;//dsp渠道流量控制配置
  repeated uint32 supportOs = 14; //支持的操作系统定向，没有值表示全过滤，例如：[1,2](3.4.0添加）
  repeated uint32 supportAdType = 15; //支持的广告类型，没有值表示全过滤，例如：[1,2,4](3.4.0添加）
  map<uint32, DeviceFrequencySetting> deviceFrequencySettings = 16; //设备请求数频次控制配置，参见DeviceFrequencySetting 对象
  uint32 dspProtocolType = 17; //dsp渠道使用的对接协议的类型，参见wiki：http://wiki.sigmob.cn/pages/viewpage.action?pageId=32409877中关于dsp_protocol_type字段的说明
  uint32 interstitialTplId = 18;  //插屏模版id;
  uint32 strategyTargetType = 19; //流量策略维度：0: deal；1-dsp帐号
  bool dsp_expect_floor_switch = 20;//渠道广告位请求底价: false: 不启用, true：启用
  uint32 rtbFirstPriceResponseChange = 21; //rtb渠道一价成交流量返回出价控制：0-不削减，一价成交；1-削减，营造二价
  uint32 rtbFirstPriceResponseChangeMinRatio = 22; // rtb渠道一价成交流量返回出价削减系数下限
  uint32 rtbFirstPriceResponseChangeMaxRatio = 23; // rtb渠道一价成交流量返回出价削减系数上限
  uint32 dealIdType = 24; // dsp交易id类型：0-Sigmob中控平台生成的交易id；1-dsp三方交易id
  uint32 sellType = 25; // 0-包装售卖；1-新售卖；2-Tobid Adx
  repeated uint32 traffic_id = 26;  // 被关闭的组合id列表
  uint32 callback_type = 27;        // RTB竞价结果回传 0-adx；1-sdk
  string corp_id = 28;             // dsp渠道所属公司id
  repeated string device_brand_traffic_control = 29;  // 设备品牌请求dsp流量控制。为空表示不限制
  repeated string media_bidding_callback = 30;        // 媒体竞价会传。为空表示否, xiaomi\huawei\oppo\vivo
  string timeInterval = 31;													  // 衰减时段(每个数字代表1个小时) 包含7*24个数据 since: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135441067
  bool enable_app_category = 32;											// 是否开启应用分类控制(仅标准dsp渠道生效)
  bool enable_no_app_category_filter = 33;						// 是否开启无应用分类过滤(仅标准dsp渠道生效)
  bool enable_dsp_req_id = 34;						            // 是否开启差异化id
}

//废弃
//message AdSlotMapping {
//  uint32 dspId = 1;  //dsp id，参见dspinfo中的dspid
//  uint32 sigmobAppId = 2; //sigmob变现平台 App Id
//  string dspAppId = 3; //dsp平台对应的app id
//  string dspAppPackageName = 4; //发送给dsp时的应用包名
//  string marcketId = 5; //应用市场id
//  map<string, DspAdSlot> adSlots = 6;  //广告位映射列表
//  string dspAppName = 7; //应用在api所在平台的名称
//}

//废弃
//message DspAdSlot {
//  string adSlotId = 1; //sigmob 广告位id
//  string dspAdSlotId = 2; //对应的dsp的广告位id
//  AdSlotSize size = 3; // 广告位尺寸
//  uint32 floorPrice = 4;  //dsp api独立的底价，如果大于等于零，有效，如果小于零，则遵从上级
//}

message AdSlotSize {
  uint32 width = 1;
  uint32 height = 2;
}

//废弃
//message DspStrategy {
//  uint32 appId = 1; //sigmob变现平台 App Id
//  string adSlotId = 2; //sigmob 广告位id
//  repeated WeightStrategy strategies = 3; //基于权重的策略列表
//  int32 floorPrice = 4; //地板价
//  uint32 targetPrice = 5; //期望价格
//}

/**
* 基于权重的策略
**/
//废弃
//message WeightStrategy {
//  uint32 weight = 1; //权重
//  repeated uint32 dspIds = 2; //dsp列表
//}

//2.4新增
//dsp app信息
message DspApiAppInfo {
  uint32 id = 1;//dsp App 在sigmob平台的id，数据库自增id
  uint32 dspId = 2; //dsp渠道id，例如2002
  string app = 3; //dsp平台对应的app id
  string packageName = 4; //在dsp平台申请时填写的包名
  string name = 5; //dsp app名称
  map<uint32, DspApiAdSlot> adSlots = 6;
  uint32 state = 7; //可用状态，0:可用，1:禁用(3.2.0新增）
  string app_key = 8; //密钥(demand v4.4.1）
  uint32 deeplink_clear_ratio = 9; // deeplink清空比例
}

//dsp ad slot对象
message DspApiAdSlot {
  uint32 id = 1; //dsp 广告位在sigmob平台数据库自增id
  string adSlot = 2;
  AdSlotSize size = 3; // 广告位尺寸
  uint32 adSlotType = 4; //dsp原始广告类型
  uint32 expected_price = 5; //渠道广告期望价格，单位: 分
  uint32 daily_impression_count = 6; // 日曝光上限，整数，最小为 1
  uint32 qps = 7;            // 0则不触发 渠道广告位级QPS过滤
  uint32 expected_price_rule = 8;    // 期望底价规则: 优先:0、比较1 ；默认 = 优先(0)
  bool traffic_disabled = 9;          // 禁止流量请求，false - 允许流量请求，true - 禁止流量请求
}

//sigmob广告单元与dsp广告单元关联关系对象
message DspApiAdSlotMapping{
  uint32 sigmobAppId = 1;
  string sigmobAdSlotId = 2;
  uint32 dspId = 3;
  uint32 dspAppId = 4; //对应DspAppInfo中的id字段，数据库自增id
  uint32 dspAdSlotId = 5; //对应DspAdSlot中的id字段，数据库自增id
  uint32 state = 6; //0开启，1关闭
}

//message DspStrategyList {
//  repeated DspStrategy list = 1;
//}

//2.5.2新增
//dsp api的交易设置
message DspApiDeal {
  uint32 id = 1; //交易信息的数字id，数据库自增id
  uint32 dspId = 2; //dsp渠道id
  string dealId = 3; //系统生成的交易ID，字符串，与合作方以此id对账
  uint32 type = 4; //交易类型：1，联盟后台，2、出价，3固定价格
  uint32 ecpm = 5; //type为固定价格时的具体价格，整数，单位为（分）
  uint32 requestLimitType = 6; //日控量类型：0、不限；1、按请求量限制；2、按曝光限制
  uint64 limit = 7; //长整数，限制的阈值，当requestlimittype为1或2时，此值有效，表示requestlimittype指定的指标到达该值时，禁止访问。
  DateRange dateRange = 8; //交易有效期范围，参见下表DateRange对象，为空表示长期有效
  string period = 9; //交易时段，为168位的长字符串
  uint32 state = 10; //状态：0开启，1关闭
  repeated DealAppInfo dspApps = 11; //参与交易的媒体信息数组，指dsp的app及广告位，参见下表DspAppInfo
  uint32 probability = 12;//交易概率，1-100的整数
  repeated string regionCodes = 13; //交易地区列表，为空表示不限 ,此字段在与平台接口交互时有效，ssp保存时将此数据转换到regionCache字段，并清空此字段，使用时请使用regionCache
  map<string, string> regionCache = 14; //交易地区map，便于快速查询
  uint32 priority = 15;//deal优先级，0，1是普通deal，2是高优先级deal
  TargetCondition targetCondition = 16; //定向条件
  uint32 strategyTargetState = 17;//流量策略定向开关，0关闭，1开启
  string dspDealId = 18; // dsp第三方交易id
  uint32 tradeType = 19;          // 0||2:deal, 5:pdb
}

message DateRange {
  uint32 begin = 1; //开始日期，格式20190810
  uint32 end = 2; //结束日期，格式20190810
}

message DealAppInfo {
  uint32 dspAppId = 1; //参与交易的媒体信息数组，指dsp的app及广告位，参见下表DspAppInfo
  repeated uint32 dspAdSlotIds = 2; //参与交易的dsp的广告位数组，数组中元素为数据库中的自增id，例如[123，124，125]
}

//dsp渠道设备定向白名单
message DspDeviceWhiteList {
  //设备唯一标识，ios为idfa，android的可为imei>gaid>androidid
  string identify = 1;
  //dsp渠道id
  uint32 dsp_id = 2;
  //sigmob广告单元id
  string ad_slot_id = 3;
}

//广告单元后端交易策略
message AdSlotTradingSetting {
  uint32 appId = 1; //sigmob变现平台 App Id
  string adSlotId = 2; //sigmob 广告位id
  repeated TradingStrategyFlow flow = 3; //基于权重的交易策略列表
}
//基于权重的交易策略瀑布流
message TradingStrategyFlow {
  uint32 weight = 1; //权重，0-100之间的整数，为0表示不生效
  repeated TradingStrategy strategies = 2; //dsp列表
}
//策略瀑布流中具体的交易策略
message TradingStrategy {
  uint32 tradingType = 1; // 交易类型：2、Deal, 3、Rtb, 4、联盟
  int32 floorPrice = 2; // 底价
  uint32 dspId = 3; // 如果交易类型为4，则dsp_id为渠道id；其他则无效
  uint32 sellType = 4; // 0-包装售卖；1-新售卖
}


//dsp控量信息
message DspControlSetting {
  uint32 id = 1; //控量配置的数据库自增id
  uint32 dspId = 2; //dsp渠道id
  uint32 requestLimitType = 3; //日控量类型：0、不限；1、按请求量限制；2、按曝光限制
  uint64 limit = 4; //长整数，限制的阈值，当requestlimittype为1或2时，此值有效，表示requestlimittype指定的指标到达该值时，禁止访问。
  DateRange dateRange = 5; //交易有效期范围，参见下表DateRange对象，为空表示长期有效
  string period = 6; //交易时段，为168位的长字符串
  uint32 state = 7; //状态：0开启，1关闭
  repeated DealAppInfo dspApps = 8; //参与交易的媒体信息数组，指dsp的app及广告位，参见下表DspAppInfo
  repeated string regionCodes = 9; //交易地区列表，为空表示不限 ,此字段在与平台接口交互时有效，ssp保存时将此数据转换到regionCache字段，并清空此字段，使用时请使用regionCache
  map<string, string> regionCache = 10; //交易地区map，便于快速查询
}

//dsp媒体控制对象，可以执行对媒体的包含和排除设置
message DspTrafficFilter {
  repeated string includeAdSlotId = 1;  // 媒体包含
  repeated string excludeAdSlotId = 2 ; // 媒体排除
  bool adSlotFilterEnable = 3;          // 是否启用广告位过滤，true启用（全过滤），false不启用（不过滤）
}

//设备请求数频次控制配置
message DeviceFrequencySetting {
  uint32 interval = 1; //时间间隔（单位：分钟）
  uint32 frequency = 2; //最大请求次数，超过该次数，则该设备在该时间间隔内禁止准入。0表示不限制
}

message TargetCondition {
  repeated string brandTarget = 1;//品牌定向，空表示不限制，全小写
  repeated uint32 creative_type_target = 2;//创意类型过滤，目前仅video + htmlsrc（对应响应 attr  =5）、video + htmlurl（对应响应attr = 7）
}