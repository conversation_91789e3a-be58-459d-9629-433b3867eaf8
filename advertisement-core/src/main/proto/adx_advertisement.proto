syntax = "proto3";

package sigmob;

import "sigmob_rtb.proto";
import "ssp_dsp_management.proto";
import "user_profile.proto";
import "sigmob_jion.proto";
/// import "rtp_public.proto";
import "abtest.proto";

option java_multiple_files = true;
option java_package = "com.sigmob.ad.adx.rpc.grpc";

message RtbRequest {
  BidRequest bidRequest = 1;
  uint32 appId = 2;
  DspInfo dspInfo = 3;
  Version sdkVersion = 4;
  DspApiAppInfo dspApp = 5;
  DspApiAdSlot dspApiAdSlot = 6;
  bool isDebug = 7;
  bool isSspRetrievedDeviceId = 8; // ⚠️废弃
  DspApiDeal deal = 9;
  uint32 dspId = 10;
  uint32 whiteListDspId = 11;
  uint32 changeNetwork = 12; // 改变请求广告时设备网络类型：0-不改变；1-未知网络改为4g；
  uint32 respAdType = 13; // 响应广告类型:1-激励视频、2-开屏、4-全屏、5-原生、6-插屏
  uint32 originDspAdType = 14; // dsp原始广告位请求类型
  uint32 uid_type = 15; //uid类型；1-IMEI; 2-IDFA; 3-IDFV; 4-UDID; 5-Sigmob CAID; 6-OAID; 7-GAID; 8-ANDROID ID; 9-IMEI MD5; 10-IDFA MD5; 11-OAID MD5; 12-GAID MD5; 13-ANDROID ID MD5
  uint32 floor_price_strategy = 16; //0-广告位底价&瀑布流底价逻辑；1-流量售卖底价&瀑布流底价逻辑
  repeated string installed_app = 17; //设备已安装（激活）应用
  uint32 access_mode = 18;            //流量接入方式：1、sdk接入；2、api接入
  uint64 check_service_id = 19;           // 审核服务id
  bool need_checked = 20;                 // 下发广告是否需要过审
  uint32 dsp_ecpm = 21;      //网盟dsp ecpm
  float ecpm_weight = 22;    //网盟dsp 出价权重
  uint32 bid_price = 23;     //网盟dsp 最后出价
  bool disable_close_card = 24; //禁用关闭页广告，true表示禁用；默认false，表示开启关闭页广告
  int64 market_channel_id = 25; //外部流量应用市场id，暂时与开发者id一致
  bool need_market_channel_check = 26; //根据外部流量应用市场id判断是否需要定向市场id广告
  bool enableServerCheckReward = 27;//奖励发放设置  0表示无需服务器判断，true表示需要服务器判断（supply 3.9.1)
  uint32 reward_num = 28; // 奖励数量（supply 3.9.1)
  string reward_name = 29; // 奖励名称（supply 3.9.1)
  string reward_url = 30; // 奖励事件服务端回调地址（supply 3.9.1)
  string reward_key = 31; // 安全key（supply 3.9.1)
  string sigmob_caid = 32; // 当前设备可使用的最老版本设备归因id，解决ios14广告无法归因问题
  string api_request_id = 33; // API流量的原始请求id
  string api_app_id = 34;  // API流量在自己系统内部标识流量的app id
  string api_placement_id = 35; //API流量在自己系统内部标识流量的广告位id
  uint32 request_flow_type = 36;   //流量类型，0-sdk; 1-oppo; 2-adxData
  uint32 request_flow_price = 37;    // 流量底价出价
  repeated string ad_strategys = 38;    // 流量指定的广告策略数组
  uint32 request_timeout = 39;   // 请求超时时间，单位（毫秒）
  uint32 bid_execute_mode = 40;  // 竞价模式：1-并行竞价；2-串行竞价
  uint32 support_material_type = 41; // 广告位支持素材类型，临时需求 for 插屏图片
  repeated uint32 api_support_action = 42; //api流量支持的广告素材类型
  string api_developer_id = 43;  //ssp api开发者id
  uint32 api_settlement_mode = 44; //结算方式，api流量使用; 0-非竞价模式; 1-竞价模式
  repeated string server_ips = 45; //从服务器获取的客户端请求ip（从header的X-Forward-For获取）
  AdFilterConfig ad_app_filter_configs = 46;   // 媒体应用级广告过滤配置
  AdFilterConfig ad_slot_filter_configs = 47;  // 广告位级广告过滤配置
  uint32 ad_check_type = 48; // 0-非前审；1-前审
  uint32 origin_ad_type = 49; // 原始流量请求广告类型
  uint32 hb_type = 50;   //header bidding类型；0-非header bidding；1-服务端-服务端header bidding,2:c-s
  uint32 at = 51;        //竞价价格类型：0-未知；1-一价；2-二价
  string currency = 52;     // 请求价格币种
  float currency_rate = 53; // 请求币种转人民币汇率
  uint32 original_request_flow_price = 54;  //流量请求原始出价，不考虑币种
  int32 sdk_type = 55; // 0:默认， 1：windmill
  AbExperiment ab_experiment = 56; // 实验分组数据
  string hb_version = 57;          // 聚合sigmob hb版本号，目前有1.0&2.0
  bool disable_aliyun_log = 58;    // 禁止阿里云日志标识，false-不禁止；true-禁止
  AdxExperiment adx_experiment = 59; // adx实验参数
  bool disable_elk_log = 60;  // 禁止elk日志标识，false-不禁止；true-禁止
  string wxAppId = 61;        // 微信开放平台APPID
  string universalLink = 62;  //
  int32 dsp_rebate_ratio = 63; // dsp出价折扣系数
  double dsp_rtb_bid_price_ratio = 64; // dsp RTB渠道成交价格调整系数
  DspHtmlTemplate dsp_html_template_config = 65; //dsp渠道返回广告使用模版
  uint32 dspRebateWinPriceRule = 66; // 0 - 处理返点后价格；1-真实成交价格
  string api_app_package = 67; //API流量在自己系统内部标识流量的app package
  bool need_compliance = 68; // 是否要求合规
  repeated AbExperiment ab_experiments = 69; // 一系列ab实验参数记录
  ComplianceRule compliance_rule = 70; // 合规条件
  Version toBidSdkVersion = 71;         // ToBid SDK VERSION
  DspTrafficParamUsage dsp_traffic_param_usage = 72; // dsp 使用流量参数方式
  uint32 dsp_request_traffic_id = 73; // dsp 流量组合id
  bool disable_request_sig_dsp = 74;  // 是否禁止请求直投dsp：true 不请求
  AppPriceStrategy app_price_strategy = 75; // 媒体报价策略
  AppAdSetting app_ad_setting = 76;
  SearchInfo searchInfo = 77;               // 搜索广告信息
  repeated string strategy_mapping_packages = 78; // 根据ad_strategys找到的对应关联包名
  bool need_miit_compliance = 79;         // 是否中控和变现平台配置要求工信部合规
  uint32 expired_ad_price = 80;           // 客户端竞价过期广告的出价
  repeated string traffic_strategy_exclude_installed_packages = 81; // 厂商流量在中控配置了策略-包名映射关系，但是请求中没有对应的策略，则该包名不能作为设备已安装包向dsp传递
  bool has_traffic_strategy_package_mapping_config = 82; // 是否有流量策略包映射关系配置
  repeated uint32 filter_support_interaction = 83;       // 根据中控配置过滤的广告动作类型
  string media_source = 84;                              // API媒体请求中，传递的 媒体来源包名
  DebugDeviceSupportInfo debugDeviceSupportInfo = 85;    // 测试设备以及支持动作类型、素材类型
  DeveloperSupportInfo developerSupportInfo = 86;        // DeveloperSupportInfo
  bool replace_track_host = 88;                          // 替换监测链接域名 see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135445722
  uint32 deeplink_clear_ratio = 89;                      // oppo vivo 清空比例 see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135441843
  uint32 rv_media_direct_err_click_rate = 90;               // 激励媒体直投误点率，取值0-100，表示百分比整数部分
  uint32 rv_media_prog_err_click_rate = 91;                 // 激励媒体程序化误点率，取值0-100，表示百分比整数部分
  uint32 interstitial_media_direct_err_click_rate = 92;     // 插屏媒体直投误点率，取值0-100，表示百分比整数部分
  uint32 interstitial_media_prog_err_click_rate = 93;       // 插屏媒体程序化误点率，取值0-100，表示百分比整数部分
  LogoConfig logo = 94;                                  // logo相关配置 see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444838
  DspExtInfo DspExtInfo = 95;                               // dsp EXT信息
}

message LogoConfig {
  string toBid_logo_url = 1;                            // tobid log url see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444838
  string sigmob_log_url = 2;                            // sigmob log url see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444838
}

message DspExtInfo {
  string dsp_req_id = 1;                                    // 请求dsp时的requestId
}

message DeveloperSupportInfo {
  int64 developer_id = 1;                                 // ssp 开发者id
  bool shield_material = 2;                               // 是否开启屏蔽素材
}

message DebugDeviceSupportInfo {
  bool isDebug = 1;         // 是否测试设备，从rtbRequest.isDebug迁入
  int32 interactType = 2;   // 0 = 不限
  // int32 materialType = 3;   // 0-不限；1-视频；2-图片
}

message ComplianceRule {
  bool need_compliance = 1;  // 是否要求合规(旧)，从rtbRequest.need_compliance 迁入
  bool compliance_filter_ad = 2; // 根据2023.08.14需求决定
}

message HeaderBiddingResponse {
  map<int32, ThirdBiddingResponse> third_bidding_response = 1;
  repeated ThirdBiddingResponse fail_list = 2; // 出价后竞价失败bidder
}

message ThirdBiddingResponse {
  string response_str = 1;        // 第三方adn竞价返回的json string
  string win_url = 2;             // 竞价成功url
  string lose_url = 3;            // 竞价失败url
  int32 ecpm = 4;                 // 出价 单位:分
  int32 channel_id = 5;           // 渠道
  BidResponse bid_response = 6;   // sigmob 广告
  string p_id = 7;                // 竞价广告位
  uint32 code = 8;
  string errMsg = 9;
  /** sig bidFloor */
  uint32 bidFloor = 10;
  int32 ori_ecpm = 11;            // sig原始出价（可能sigmob改价） 单位:分
  int32 ecpm_2 = 12;              // 出价2 单位:分
}

message AbExperiment {
  string ab_group_name = 1;  //  ssp服务做的ab分组名
  float float_ab_rate = 2;    //  ssp服务ab分组所占比例，取值0～1之间的小数
  int32 cache_time = 4;     // 实验结果缓存时间，单位：秒
  bool is_effect = 5;       // 实验是否生效
  bool execute_flag = 6;    // 是否执行实验动作
  string experiment_id = 7; // 实验id
  uint32 max_time_interval = 8;// 每隔多久（秒，如600秒）必须正常请求一次；
  uint32 max_filter_interval = 9;// 每隔多少次请求必须正常请求一次；
  uint32 filter_req_threshold = 10;// 开始过滤请求最小的请求次数，uid+广告位+渠道请求次数小于Req_min时，不过滤该用户请求；
  double filter_res_threshold = 11;// 开启过滤填充率最小值，填充率高于Res_min的用户不过滤
  double short_term_coefficient = 12;// floor随时间的变化系数，短期控制系数
  double long_term_coefficient = 13;// floor随时间的变化系数，长期控制系数
  double variation_coefficient = 14;// floor变化系数，有返回后的控制系数
  bool filter_flag = 15; // 根据实验参数和历史请求数据计算是否符合过滤要求
  uint32 exp_dimension = 16; //实验维度，由各实验自定义枚举值
  double req_variation_coefficient = 17; // floor变化系数，请求时的控制系数
  map<string, string> param = 18;     // 实验具体配置参数，今后不再新增字段
}

message AppPriceStrategy {
  uint32 strategy = 1;      // 0-固定系数; 1-算法加持
  int32 profit_factor = 2;  // 利润系数 -500～100的整数
}

message ResponseImage {
  repeated string url = 1; // 多个图片地址
  uint32 width = 2;         // 宽
  uint32 height = 3;        // 高
  uint32 file_size = 4;     // 文件体积大小，单位：字节
}

message RtbResponse {
  BidResponse bidResponse = 1;
  enum InteractType {
    NULL = 0;
    WEB_VIEW = 1;//网页打开
    APP_DOWNLOAD = 2;//下载
    DEEP_LINK_AND_OPEN = 3;//deeplink，如果没有app，则下载并唤起app
    ONLY_IMP = 4;//纯曝光
    REDIRECT_302_DOWNLOAD = 5; // 点击302后下载
    REDIRECT_302_JUMP = 6;     // 点击302后跳转
    WX_PROGRAM = 7;            // 微信小程序
    QUICK_APP = 8;             // 快应用
    WX_CANVAS = 9;             // 微信原生(预留，SDK暂未支持)
    SYS_EXPLORE = 98;          // 系统浏览器打开
    DEEP_LINK = 99;            // deeplink
    UNKNOWN = 100;             // 未知
    SCHEMA_WX_PROGRAM = 1000;  // schema微信小程序（SDK 版本>=Android  4.3.0 及iOS ）
  }
  InteractType rawClickType = 2;
  uint32 actionType = 3;
  uint32 bucketType = 4;
  int64 errorCode = 5;
  profile.UserProfile userProfile = 6;
  repeated string winUrl = 7;
  uint32 settlementPrice = 8;
  SigmobAdditionalResponse sigAdditionalResponse = 9;
  MaterialInfo materialInfo = 10;
  AiLogMessage aiLogMessage = 11;
  string settlementPriceEncForDsp = 12;
  string sigDspVersion = 13;
  uint32 maxPrice = 14; //竞价时最高价
  uint32 secondPrice = 15;//竞价时次高价
  uint32 requestType = 16;// 请求类型
  uint32 ecpm = 17; // 投出广告创意eCPM, 单位:分(暂时只有sigmob dsp)
  string seat = 18; //曝光购买者(可以是渠道，也可以是渠道广告主标识)
  repeated ClosecardAd closecard_ads = 19;        // 关闭页广告列表
  uint32 dsp_protocol_type = 20; //dsp渠道使用的对接协议的类型，参见wiki：http://wiki.sigmob.cn/pages/viewpage.action?pageId=32409877中关于dsp_protocol_type字段的说明
  uint32 standard_dsp_resp_attr = 21; // 标准dsp返回广告attr，见StandardDspCreativeType.java定义
  string product_market_id = 22; //外部api流量返回广告市场id
  string final_win_price_enc = 23; //最终成交价格加密串
  bool is_multi_dsp_bid = 24; //是否多家dsp竞价结果
  bool template_auto_click = 25; // 模板是否支持自动点击
  uint32 cold_start_stage = 26; // 冷启动算法流程，0-测试 1-随机 2-试投 3-正常 4-随机无广告转试投 5-随机无广告转正常
  uint32 cold_start_state = 27;  // 最终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  uint32 trading_type = 28;      // 广告交易类型：2-deal; 3-rtb; 4-ad network
  map<string, AdditionalResponse> additional_Responses = 29; // sigmob dsp广告额外信息
  uint32 change_win_price_type = 30; // 修改成交价类型：0-未修改; 1-rtb提价
  AdxExperiment adx_experiment = 31; // adx实验参数
  string dsp_final_win_price_enc = 32; // dsp最终胜出打点加密价格
  repeated string loseUrl = 33; // dsp竞价失败回调链接
  uint32 dsp_rebate_settlement_price = 34; //经过返利计算后的渠道成交价（dsp出价/（1+返点系数））
  string dsp_rebate_settlement_price_enc = 35; //经过返利计算后的渠道成交价（dsp出价/（1+返点系数））并按渠道要求加密规则加密
  uint32 ori_settlementPrice = 36;             // 原始成交价 未经过rtb提价处理的原始价格
  uint32 tba_bid_price = 37;    // dsp出价调整前原始价格 Demand V5.11.3: http://wiki.sigmob.cn/pages/viewpage.action?pageId=72944156
  uint32 tba_bid_changed_price = 38; // dsp出价调整后价格 Demand V5.11.3: http://wiki.sigmob.cn/pages/viewpage.action?pageId=72944156
  uint32 final_win_price = 39; // dsp最终胜出打点价格
  uint32 return_ad_dsp_count = 40; // 返回广告的渠道数量
  map<string, string> sigmob_dsp_ext = 41; // 直投专属业务字段表，多用于透传打点
  uint32 settlement_price_for_dsp = 42;   // dsp结算价
  string response_id = 43;   //dsp渠道响应id
  uint32 dsp_notify_callback_type = 44; // RTB竞价结果回传 0-adx；1-sdk
  string tba_bid_changed_price_enc = 45; // dsp出价调整后加密价格 Demand V5.11.3: http://wiki.sigmob.cn/pages/viewpage.action?pageId=72944156
  string dsp_req_id = 64;                // 请求dsp时的requestId
}



// 缓存广告响应
message CachedResponse {
  BidResponse bidResponse = 1;
  string bid_adslot_id = 2;     // 请求竞价广告位id
  int64 cache_time_stamp = 3;   // 广告缓存时间戳
  int32 ecpm = 4;               // 对外广告出价（cny、usd）
  string cur = 5;               // 币种
}

// 广告响应额外信息
message AdditionalResponse {
  RtbResponse.InteractType raw_click_type = 1;
  repeated string win_url = 2;
  uint32 settlement_price = 3;
  string settlement_price_enc_for_dsp = 4;
  string final_win_price_enc = 5; // 最终成交价格加密串
  SigmobAdditionalResponse sigmob_additional_response = 6;
  MaterialInfo material_info = 7;
  string ad_category = 8;  //sigmob广告分类
  string traffic_ad_category = 9;  // 流量方广告分类
  string traffic_advertiser_id = 10; //流量方广告主id
  uint32 dsp_rebate_settlement_price = 11; //经过返利计算后的渠道成交价（dsp出价/（1+返点系数））
  string dsp_rebate_settlement_price_enc = 12; //经过返利计算后的渠道成交价（dsp出价/（1+返点系数））并按渠道要求加密规则加密
  uint32 trading_type = 13;      // 广告交易类型：2-deal; 3-rtb; 4-ad network
  uint32 dsp_rebate_ratio = 14; // dsp出价折扣系数
  uint32 ori_settlementPrice = 15; // 原始成交价 未经过rtb提价处理的原始价格
  uint32 tba_bid_price = 16;    // dsp出价调整前原始价格 Demand V5.11.3: http://wiki.sigmob.cn/pages/viewpage.action?pageId=72944156
  uint32 final_win_price = 17;  // 最终成交价格
  uint32 settlement_price_for_dsp = 18; // dsp成交价
  uint32 dsp_notify_callback_type = 19; // RTB竞价结果回传 0-adx；1-sdk
  AppAdSetting app_ad_setting = 20;     // 广告设置
  uint32 tba_bid_changed_price = 38; // dsp出价调整后价格 Demand V5.11.3: http://wiki.sigmob.cn/pages/viewpage.action?pageId=72944156
  repeated string lose_url = 39; // dsp竞价失败回调链接
  uint32 dsp_protocol_type = 40; // dsp渠道使用的对接协议的类型，参见wiki：http://wiki.sigmob.cn/pages/viewpage.action?pageId=32409877中关于dsp_protocol_type字段的说明
  bool is_high_ctr = 41;         // 是否高点击用户
  bool is_joint_control = 42;    // 是否是联合唤起频次控制
  uint32 standard_ad_attr = 43;  // 标准dsp广告返回attr
  uint32 dsp_sell_type = 44;     // 0-包装售卖；1-新售卖；2-Tobid Adx
  uint32 widget_sensitivity = 45;           // 互动组件灵敏度，取值 [1 , 10]
  string dsp_interaction_type = 46;  // dsp广告推广类型，暂时用于oppoRtb
  string dsp_task_id = 47;           // dsp广告的taskId， 暂时只有启航有
  uint32 dsp_rebate_win_price_rule = 48; // 0 - 处理返点后价格；1-真实成交价格
  uint32 dsp_app_id = 49; //dsp app信息在sigmob平台的id，数据库自增id
  string dsp_api_app_id = 50; //请求dsp时的媒体id
  string dsp_api_placement_id = 51; //请求dsp时的广告位id
  int32 redirect_count = 52; // 落地页跳转次数，0为不限制
  bool forbidden_quick_app = 53; // 是否屏蔽快应用
  uint32 action_id = 54; // 快手临时adOperationType
  uint32 cpt_id = 55;   // 广告模版互动组件（挂件）id
  string native_template_id = 56; // 原生广告模版id
  uint32 dsp_user_score = 57; // dsp渠道给的请求广告用户评分
  string ad_brand = 58;    // 广告品牌，暂时只有api流量用，如果sdk也用可以考虑移到MaterialMeta对象里
  ResponseImage images = 59; // 多图广告图片信息，以后sdk支持的话移到MaterialMeta中
  repeated uint32 filter_support_interaction = 60;       // 根据中控配置过滤的广告动作类型(rtbRequest中对应字段的冗余，用于多广告打点)
  uint32 re_bid_price = 61;     // 二次竞价价格，当第一次出价竞价失败时使用
  AdButton ad_button = 62;      // 广告按钮
  string hit_percent = 63;      // 误点率,供打点使用 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
}

message SigmobAdditionalResponse {
  double computeCtr = 1;
  double computeCvr = 2;
  bool predict = 3;
  double predict_ctr = 4;                         // 拟合前预估ctr值
  double predict_cvr = 5;                         // 拟合前预估cvr值
  uint32 deep_target = 6;                         // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
  uint32 deep_type = 7;                           // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
  int64 deep_val = 8;                             // 主要考核指标值
  uint32 ecpm_fee = 9;                            // deep计费
  uint32 original_price = 10;                     // 广告主原始价格
  uint32 max_price = 11;                          // 算法最高出价
  uint32 min_price = 12;                          // 算法最低出价
  double p_retain_rate = 13;                      // 投出广告预估次留率
  double p_pay_rate = 14;                         // 投出广告预估付费率
  uint32 ecpm = 15;                               // 广告创意ecpm, 单位:分(暂时只有sigmob dsp)
  bool template_auto_click = 16;                  // 模板是否支持自动点击
  uint32 cold_start_state = 17;                   // 最终投出的创意的冷启动状态 1-随机 3-试投 7-正常
  string product_market_id = 18;                  // 外部api流量返回广告市场id
  bool income_is_ocpm = 21;                       // 是否支持oCPM
  uint32 ocpm_cpa_price = 22;                     // 经过运营出价策略后用于预估ecpm的cpa出价，单位：分
  string conversion_target = 23;                  // oCPM广告转化目标
  string overfitting_flow_type = 24;              // 拟合流量标识
  double predict_ecpm_weight_network = 26;        // top1替换扶持系数
  bool is_premium = 27;                           // 是否bidding溢价
  double settlement_ratio = 28;                   // bidding溢价系数
  float deep_prem = 29;                           // 深度转化溢价系数
  float p_pay_amount = 30;                        // 预估付费额
}

message MaterialInfo {
  string imageUrlMd5 = 1;
  string videoUrlMd5 = 2;
  string htmlSrcMd5 = 3;
}

message AiLogMessage {
  //  RecLog reclog = 1;
  repeated AbTestLog logs = 2;
  Bucket abtestBucket = 3;
  double p_retain_rate = 48;                      // 投出广告预估次留率
  double p_pay_rate = 49;                         // 投出广告预估付费率
}


message DspHtmlTemplate {
  uint32 id = 1;                                // dsp广告位在sigmob平台数据库自增id --DspApiAdSlo-->id
  repeated TemplateConfig template_configs = 2; // 模板配置能力
  uint64 update_time = 3;                       // 配置更新时间
}

message DspTrafficParamUsage {
  uint32 param_app_name = 1;                     // 传递配置应用名称：0-透传; 1-渠道信息; 2-自定义
  string defined_app_name = 2;                   // 传递配置自定义应用名称
  uint32 param_app_id = 3;                       // 传递配置应用id：0-透传; 1-渠道信息; 2-自定义
  string defined_app_id = 4;                     // 传递配置自定义应用id
  uint32 param_app_bundle = 5;                   // 传递配置应用包名：0-透传; 1-渠道信息; 2-自定义
  string defined_app_bundle = 6;                 // 传递配置自定义应用包名
  uint32 param_placement_id = 7;                 // 传递配置广告位id：0-透传; 1-渠道信息; 2-自定义
  string defined_placement_id = 8;               // 传递配置自定义广告位id
  uint32 param_placement_name = 9;               // 传递配置广告位名称：0-透传; 1-渠道信息; 2-自定义
  string defined_placement_name = 10;            // 传递配置自定义广告位名称
  uint32 third_ad_type = 11;                     // 传递配置广告类型
}

message TemplateConfig {
  uint32 type = 1;                   // 类型 网页、小程序。
  uint32 template_id = 2;            // 模板id
  uint32 ratio = 3;                  // 比例
  map<string, string> macro = 4;     // macro宏
  uint32 format_type = 5;            // 模版对应formatType
  uint32 show_style = 6;             // 模版展现形式：0-全屏；1-半屏
  uint32 orientation = 7;            // 模版方向：0-横竖屏兼容；1-竖屏；2-横屏
  bool is_base_template = 8;         // 是否兜底模版：false-不是；true-是
  uint32 template_type = 9;          // 0-视频模版，1-图片模版
  bool global_click = 10;            // 全域点击
  string interactive_word = 11;      // 互动语，暂时开屏使用
  Text action_word = 12;             // 行动语，暂时开屏使用
  uint32 sensitivity = 13;           // 互动组件灵敏度
  uint32 id = 14;                    // 配置项数据库id
  uint32 ad_type = 15;               // 1-激励视频 2-开屏 4-全屏 5-原生 6-插屏 7-banner
}


//message RecLog {
//  Model model = 1;                                // 请求的模型信息，模型的版本会被反写
//  uint64 error_code = 2;                          // 错误码
//  uint64 process_time_ms = 3;                     // 请求整体处理时间，单位：毫秒
//  uint64 tfx_time_ms = 4;                         // tfx服务器处理时间，单位：毫秒
//  repeated uint64 indexes = 5;                    // 参与预估的创意ID列表
//  map<uint64, operation.Creative> creatives = 6;  // 参与预估的创意映射
//  string features = 7;                            // 构建的特征列表，字符串表示，记录日志用
//  map<string, string> tfx_output = 8;             // 字符串表示的tfx的计算输出，记录日志用
//  int32 experiment_state = 9;                   // 预留字段
//}

message AdFilterConfig {
  repeated string bundles = 1;     // 广告过滤bundle id集合
  repeated string keywords = 2;    // 广告过滤关键字集合
  bool disable_deeplink = 3;       // 是否禁止deeplink唤起类广告；false：不禁止；true：禁止
  bool disable_download = 4;       // 是否禁止下载类广告：false：不禁止；true：禁止
  repeated string categories = 5;  // 广告过滤行业集合
  repeated string advertisers = 6; // 广告过滤广告主集合
}

message AdxExperiment {
  uint32 layer_id = 1;                         // 实验层id
  string exp_id = 2;                           // 实验id
  string strategy_id = 3;                      // 策略id
  map<string, double> exp_ratio = 4;           // 广告实验系数
  map<string, uint32> dsp_second = 5;          // 计算对外出价时使用的dsp
  map<string, uint32> exp_bid_price = 6;       // 每个广告根据实验策略出价
  map<string, uint32> exp_win_price = 7;       // 每个广告根据实验策略比价后胜出价格
  map<string, uint32> bucket_id = 8;           // 每个广告命中桶号
  uint32 key_hash = 9;                         // key hash
  uint32 filter_price = 10;                    // 当dsp的出价小于filter_price（单位：分） 时， 不请求算法预估
  double sigmob_pre_max_ratio = 11;            // 当流量按strategy_005 策略出价时， 直投dsp渠道可以出价的最大上限系数
  double api_pre_max_ratio = 12;               // 当流量按strategy_005 策略出价时， 三方dsp渠道可以出价的最大上限系数
  uint32 keep_time = 13;                       // 用户预估赢价的缓存时间
  string model_id = 14;                        // 访问算法的模型id
  bool use_predict_price = 15;                 // 是否使用算法预估价出价（使用缓存预估也算, strategy_005使用）
  double default_price_ratio = 16;             // 默认价格系数
  double sigmob_pre_min_ratio = 17;            // 当流量按strategy_005 策略出价时， 直投dsp渠道可以出价的下限系数
  double api_pre_min_ratio = 18;               // 当流量按strategy_005 策略出价时， 三方dsp渠道可以出价的下限系数
  map<string, uint32> exp_predict_price = 19;  // 每个广告算法预估价格
  uint32 max_exp_price = 20;                   // 实验最大价格上限
}

message UserAdReqFilterRequest {
  string uid = 1;
  string ad_slot_id = 2;
  uint32 dsp_id = 3;
  uint32 exp_dimension = 4;
  uint32 ad_type = 5;
  uint32 app_id = 6;
}

message UserAdReqFilterResponse {
  map<string, string> infos = 1;
}

message AdButton {
  string bg_color = 1;                      // 按钮背景颜色
  string text = 2;                          // 按钮文字
}

service AdxService {
  rpc getAdV2 (RtbRequest) returns (BidResponse);
  //  rpc getWindmillBidding (RtbRequest) returns (HeaderBiddingResponse);
  rpc getWindmillSigBidding (RtbRequest) returns (ThirdBiddingResponse);
  rpc getOppoAd (RtbRequest) returns (RtbResponse);
  rpc getAdxDataAd (RtbRequest) returns (RtbResponse);
  rpc getStandardAd (RtbRequest) returns (RtbResponse);
  rpc getMoWeatherAd (RtbRequest) returns (RtbResponse);
  rpc getIReaderAd (RtbRequest) returns (RtbResponse);
  rpc getDongqiudiAd (RtbRequest) returns (RtbResponse);
  rpc getEastDayAd (RtbRequest) returns (RtbResponse);
  rpc getDouguoAd (RtbRequest) returns (RtbResponse);
  rpc getMaimaiAd (RtbRequest) returns (RtbResponse);
  rpc getKingSoftAd (RtbRequest) returns (RtbResponse);
  rpc getMgTvAd (RtbRequest) returns (RtbResponse);
  rpc getYdzxAd (RtbRequest) returns (RtbResponse);
  rpc getVungleAd (RtbRequest) returns (RtbResponse);
  rpc getHeaderBiddingAd (RtbRequest) returns (RtbResponse);
  rpc getHuaweiAd (RtbRequest) returns (RtbResponse);
  rpc getDongmanAd (RtbRequest) returns (RtbResponse);
  rpc getXimalayaAd (RtbRequest) returns (RtbResponse);
  rpc getHwSeadAd (RtbRequest) returns (RtbResponse);
  rpc getVivoAd (RtbRequest) returns (RtbResponse);
  rpc getYueyouAd (RtbRequest) returns (RtbResponse);
  rpc getUserAdReqFilter(UserAdReqFilterRequest) returns (UserAdReqFilterResponse);
  rpc getYoyoAd (RtbRequest) returns (RtbResponse);
  rpc getGromoreAd (RtbRequest) returns (RtbResponse);
}