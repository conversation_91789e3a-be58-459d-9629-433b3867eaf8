syntax = "proto3";

option go_package = "sigmob.com/common/entry/abtest";
option java_package = "com.sigmob.sigdsp.grpc";
option java_multiple_files = true;

// ABTest配置信息
message AbTestConfig {
    map<string, uint32> appid_weight_map = 1;               // key: appid  value: 该appid下流量进去ABTest框架的权重weight，千分比（0~1000）
    map<string, uint32> app_group_map = 2;                  // key: appid value: app group id
    repeated Layer layers = 3;                              // all layers
    map<string, Experiment> app_group_experiment_map = 4;   // key: [app_group_id]_[layer_code]_[ad_type]_[bucket_code]  value: 该app组下可用实验配置
}

//Bucket的权重顺序与桶信息映射
//message BucketMap{
//    repeated Bucket buckets=1;            // key:桶code
//}

//ABTest实验桶信息【v3.8.1版本开始弃用】
message Bucket {
    string id = 1;                              // 实验桶id (弃用)
    uint32 weight = 2;                          // 桶权重, 千分比（0~1000） (弃用)
    uint32 btype = 3;                           // 桶属性, 1: 实验桶; 2: 固化桶 (弃用)
    uint64 code = 4;                            // 实验桶code
    uint32 order = 5;                           // 各个桶权重顺序 (弃用)
    map<string, Experiment> expertiments = 6;   // key:[layer_code]_[ad_type], value: bucket_code下layer_code层的实验配置
}

//层信息
message Layer {
    uint64 code = 1;    // 层code
    uint32 prime = 2;   // 用于Hash的质数
}

//实验信息
message Experiment {
    uint64 code = 1;                            // 实验code
    Layer layer = 2;                            // 分层信息【v3.8.1版本开始弃用】
    map<uint32, ModelConfig> algorithms = 3;    // 实验下的算法, key: 算法order
    uint32 os_type = 4;                         // 操作系统类型. 1=IOS；2=Android
    uint32 prime = 5;                           // 用于Hash的质数
    uint32 ad_type = 6;                         // 广告类型
    uint64 bucket_code = 7;                     // 实验桶code
}

//算法模型信息
message ModelConfig{
    uint64 code = 1;        // ABTest算法模型code
    uint64 version = 2;     // 算法版本
    uint32 test_type = 3;   // abtest测试方式 1:api接口请求; 2:条件配置
    string api_url = 4;     // test_type为1时必填
    string target_args = 5; // 同sig_dsp_adver_target_args表val字段格式，test_type为2时必填
    uint32 weight = 6;      // 实验下各个算法的权重, 百分比（0~100）
    uint32 order = 7;       // 同实验下算法权重顺序
}