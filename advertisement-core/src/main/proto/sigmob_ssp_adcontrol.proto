/**
@description ssp ad server与sdk config接口的交互协议

for sdk 1.8:
add SplashConfig
CommonEndpointsConfig add ads and strategy

**/

syntax = "proto3";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.adcontrol";

message AppControlSetting {
  uint32 app_id = 1;
  RequestLimit request_limit = 2;
  string ad_slot_id = 3;
}

message RequestLimit {
  uint32 impression = 1; //广告单元总曝光控制。disableImpression为true时有效
  uint32 deviceFrequency = 2; //单设备曝光频次控制。disableDeviceFrequency为true时有效
  uint32 sigmobDspImpression = 3; //广告单元在sigmob Dsp渠道上的总曝光控制。disableSigmobDspImpression为true时有效(接口已删除)
  uint32 thirdPartyDspImpression = 4; //广告单元在所有三方dsp渠道上的总曝光控制，disableThirdPartyDspImpression为true时有效(接口已删除)
  uint32 sigmobDspDeviceFrequency = 5; //单设备在sigmob Dsp渠道上的曝光控制，disableSigmobDspDeviceFrequency为true时有效(接口已删除)
  uint32 thirdPartyDspFrequency = 6; //单设备在其他三方dsp渠道（除sigmob以外）上的曝光控制，disableThirdPartyDspFrequency为true时有效(接口已删除)
  bool disableImpression = 7; //是否禁用广告单元总曝光控制，默认false，不限制
  bool disableDeviceFrequency = 8; //是否禁用单设备曝光频次控制，默认false，不限制
  bool disableSigmobDspImpression = 9; //是否禁用广告单元在sigmob Dsp渠道上的总曝光控制，默认false，不限制(接口已删除)
  bool disableThirdPartyDspImpression = 10; //是否禁用广告单元在所有三方dsp渠道上的总曝光控制，默认false，不限制(接口已删除)
  bool disableSigmobDspDeviceFrequency = 11; //是否禁用单设备在sigmob Dsp渠道上的曝光控制，默认false，不限制(接口已删除)
  bool disableThirdPartyDspFrequency = 12; //是否禁用单设备在其他三方dsp渠道（除sigmob以外）上的曝光控制，默认false，不限制(接口已删除)
  uint32 requestRejectRatio = 13;          //请求丢弃比例0:不丢弃（supply-v5.2.1)
  uint32 requestDeviceFrequency = 14;      //单设备请求广告位频次上限 0:不限制supply-v5.2.1)
  uint32 requestFillRatio = 15;            //请求填充比例下限 0:不限制（demand-v6.2.4)
}

//message AppRequestForbidden{
//  uint32 app_id = 1;
//  string ad_slot_id = 2;
//}
