syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.sigmob.ad.core.callback.pb";
option java_outer_classname = "AdxStatisticsProto";
option objc_class_prefix = "SM";
//
//service AdxStatistics {
//  rpc AdxStatisticsApi (AdxStatisticsRequest) returns (AdxStatisticsResponse) {}
//}
//
//message AdxStatisticsRequest {
//  int32 channelId = 1;
//  int32 appId = 2;
//  string placementId = 3;
//  string request_id = 9;
//}
//
//message AdxStatisticsResponse {
//  double score = 1;
//  double ratio = 2;
//}

message CallbackData {
  string task_id = 1;
  repeated string dsp_urls = 2;
  string ad_creative_id = 3;
  string u_id = 4;
  string dspAppId = 5;
  string dspApiAppId = 6;
  string dspApiPlacementId = 7;
  int32 adx_id = 8;
  int32 sell_type = 9;
  string request_id = 10;
  string ssp_request_id = 11;
  string v_id = 12;
  string placement_id = 13;
  string ssp_app_id = 14;
  string ssp_placement_id = 15;
  int32 media_interaction_type = 16;
}

// saas adx 打点信息
message SaasAdxPointData {
  string ad_channel_id = 1;
  string load_id = 2;
  string channel_id = 3;
  string sub_channel_id = 4;
}