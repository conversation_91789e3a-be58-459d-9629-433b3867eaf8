/**
@description 
ssp与sdk相关的配置项，纯平台使用的对象，
下发给sdk的配置参见 sigmob_ssp_config.proto，以及sigmob_rtb.proto
**/

syntax = "proto3";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.sdksetting";

message SdkSettings {
  repeated SdkSetting settings = 1;
}

message SdkSetting {
  uint32 appId = 1; //appId,如果为零，表示是全局配置
  string adSlotId = 2; //当appId不为零时，并且该值不为空，则表示是广告单元层级的设置
  SdkCommonSetting common = 3; //sdk的公共配置
  SdkRewardedVideoSetting rv = 4; //sdk激励视频广告的配置
  SdkSplashSetting splash = 5; //sdk开屏广告的配置
  SdkFullScreenVideoSetting fsv = 6; //sdk全屏视频广告的配置
  SdkNativeSetting native = 7; //信息流广告配置（信息流广告版本添加）
  SdkInterstitialSetting interstitial = 8; // 插屏配置
}

message SdkCommonSetting {
  uint32 tracking_expiration_time = 2; //track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tracking_retry_interval = 3; //track上报失败的重发时间间隔
  bool ad_logo_enable = 4;  //是否显示广告logo
  int32 auto_load_interval = 5; //非0代表关闭自动加载；（单位：秒）
  bool disable_up_app_info = 6; //是否禁止上传用户已安装的app信息，默认是false，即可以上传
  bool disable_up_location = 7; //是否禁止上传用户的位置信息。false: 不禁止上传；true: 禁止上传
  repeated SdkChannelSetting sdkChannelSettings = 8; //sdk渠道配置信息，目前仅广告过期时间
  bool disable_close_card = 9; //禁用关闭页广告，true表示禁用；默认false，表示开启关闭页广告
  bool enable_without_idfa = 10; //是否允许无idfa的请求：不允许（默认）：false； 允许：true；
  uint32 native_cache_top = 11; //信息流广告的本地缓存广告上限（信息流广告版本添加）
  uint32 native_timeout = 12; //开发者要求的信息流广告最大响应时长（信息流广告版本添加）
  uint32 single_channel_timeout = 13; //聚合请求广告最大时长 (supply 4.10.1)
  uint32 tail_traffic_control_rate = 14; // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  uint32 tail_traffic_whitelist_discard_rate = 15; // 尾量控制广告位白名单需要丢弃的流量比例，默认0，即全部放行
  bool x_request = 16; // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  bool log_enc = 17;   // 是否对打点数据做加密处理，false: 不加密；true: 加密
  uint32 tobid_tracking_expiration_time = 18; // track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tobid_tracking_retry_interval = 19;  // track上报失败的重发时间间隔
  uint32 optimize_applist = 20;               // 0:获取，1:不获取，2:优化获取
  uint32 apk_download_type = 21;              // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 22;                // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 23;                         // 包体安装是否确认
  bool lock_play = 24;                        // 锁屏播放，默认不生效
  bool screen_keep = 25;                      // 广告播放保持屏幕常量，默认不生效
  bool disable_install_monitor = 26;          // 是否禁止安装监测
  uint32 log_interval_time = 27;              // 请求计数上报时间间隔（单位：秒）
  bool disable_scheme = 28;                   // 是否禁止scheme开关，true：禁止
  repeated string brands = 29;                // 跳转限定厂商商店
  bool enable_open_pkg_dir_list = 30;					// 通过文件路径嗅探应用程序是否存在，默认关闭且当enable_open_pkg_list 未开启 才生效。
  repeated string compliance_android_sdk_version = 31;            // 合规的sdk版本号,全部是all
  repeated string compliance_ios_sdk_version = 32;                // 合规的sdk版本号,全部是all
  repeated string compliance_harmony_sdk_version = 33;            // 合规的sdk版本号,全部是all
  uint32 expire_monitor_interval = 34;                            // 广告过期监测时间间隔（单位：秒），默认0秒；>0：秒开启定时器；=0：停止定时器
  map<string, uint32> adtype_shake_map = 35;                      // K: 广告类型 V: 震感次数 since: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135441613
  bool enable_auto_expire = 36;                                   // 是否可以主动过期 since: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135439154
  bool disable_small_window = 37;                                 // 是否禁用小窗 since: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135440718
  bool enable_ad_privacy = 38;                                    // 是否开启隐私六要素过滤
  uint32 sniffing_type = 39;                                      // deeplink 判断和 canopen 包名嗅探方式（Android），0-queryIntentActivitiesAsUser 1-getApplicationInfo（新）
}

//激励视频的配置
message SdkRewardedVideoSetting {
  //广告是否静音（0:静音、1:非静音。default=0）
  uint32 ifMute = 1;
  //广告在倒数第x秒显示关闭按钮（default=9999，代表不显示关闭按钮），此项属性在新版本的sdk中已废弃
  uint32 showClose = 2;
  //视频观看完成度定义(%)
  float finished = 3;
  //激励视频在ready情况下，播放前，检查的聚合策略过期时间（default=7200s)
  uint32 loadExpired = 4;
  //聚合超时时间(s) (聚合等待所有渠道返回的超时时间（default=45s))
  uint32 loadTimeout = 5;
  //视频关闭按钮位置，左上：1，右上：3
  uint32 videoClosePosition = 6;
  //endcard关闭按钮位置，左上：1，左下：2，右上：3，右下：4，
  uint32 endcardClosePosition = 7;
  //静音按钮位置， 左上：1，左下：2，右上：3，右下：4，
  uint32 mutePostion = 8;
  //跳过按钮，根据视频播放进度的百分比显示，取值范围[0,100]，取0表示一开始就可条，取30，表示播放到30%时显示跳过按钮
  uint32 skipPercentage = 9;
  //小于零表示此项不生效，大于等于零表示经过的指定的秒数后显示跳过按钮，如果次项配置大于等于零，则skipPercent配置项自动失效
  int32 skipSeconds = 10;
  //是否允许关闭视频时直接退出广告，而不再显示endcard
  bool enableExitOnVideoClose = 11;
  // 播放模式，0:预加载模式，1:预缓存及在线流式混合模式，边加载边播，2:在线流式播放，不预加载
  uint32 playMode = 12;
  //本地缓存的广告上限（default=4）
  uint32 cacheTop = 13;
  //视屏最大时长
  uint32 videoMaxDuration = 14;
  //重复发起聚合策略请求的间隔时间，在非ready的情况下，用来防止重复发起请求
  uint32 strategyLoadPeriodTime = 15;
  //是否限制时长: 1：表示不限制，2：表示按时关闭视频，3：表示限制视频素材时长（2.9.0新增）
  uint32 videoDurationType = 16;
  bool download_dialog = 17;  // 下载需要先弹四要素，默认false 下载不弹四要素，true为下载弹四要素弹框
  uint32 tail_traffic_control_rate = 18; // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  bool x_request = 19; // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  bool disable_auto_load = 20;            // 关闭广告播放中的自动加载逻辑
  uint32 sensitivity = 21;                // 互动灵敏度，1-低，2-中，3-高
  // 有效播放进度定义
  int32 charge_seconds = 22;              // 正数x秒 >=0生效  优先级=1
  int32 charge_percent = 23;              // 百分比 优先级=2
  // 奖励发放时机
  int32 reward_seconds = 24;              // 奖励发放时机 >=0生效  优先级=1
  int32 reward_percent = 25;              // 百分比 优先级=2

  int32 confirm_dialog = 26;              // 视频跳过是否展示挽留弹窗(未达到有效播放时) 0 or 1: 弹窗、2: 不弹窗
  int32 reward_style = 27;                // 奖励等待样式  0: 无； 1: 长驻提示
  uint32 apk_download_type = 28;          // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 29;            // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 30;                     // 包体安装是否确认
  uint32 new_sensitivity = 31;            // 互动灵敏度，1-10低-高
  bool video_error_reward = 32;                   // 异常播放跳过是否发放奖励；互斥单选，可选项=是、否；默认 = 否
  bool disable_full_click_on_video = 33;          // 视频是否全屏可点, 默认false 全屏可点
  bool disable_full_click_on_endcard = 34;        // endcard是否全屏可点, 默认false 全屏可点
  bool disable_full_click_on_companion = 35;      // companion是否全屏可点, 默认false 全屏可点
  uint32 disable_interaction = 36;                // 0：允许互动广告；1：屏蔽互动广告。
  // 激励 媒体直投误点率，取值0-100，表示百分比整数部分 @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
  uint32 media_direct_err_click_rate = 37;
  // 激励 媒体程序化误点率，取值0-100，表示百分比整数部分 @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
  uint32 media_prog_err_click_rate = 38;
  // 摇一摇操作时间阈值，默认2秒，只允许正整数 (单位：秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_operation_time_threshold = 39;
  // 摇一摇单双向触发，默认单向触发 (0: 单向, 1: 双向) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_single_direction_trigger = 40;
  // 互动组件跳转触发时间间隔(单位: 秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444341
  uint32 interactive_component_trigger_interval = 41;
}

//开屏配置
message SdkSplashSetting {
  uint32 showDuration = 1; //开屏倒计时时间
  int32 splashTemplateType = 2; // 1:跳过+广告合一+logo模版(广告字样在右上方）2:跳过+广告模版(广告字样在右下方）（2.5.1添加）
  //本地缓存的广告上限（default=4）
  uint32 cacheTop = 3;
  //自动下载配置
  AutoClickSetting click_setting = 4;
  //是否开启全屏点击模式（原点击区域），默认关闭（supply 4.8.3)
  bool enable_full_click = 5;
  // 开屏交互样式 (supply 4.10.1)
  repeated SplashTemplateTypeSetting splashTemplateTypeSetting = 6;
  bool use_floating_btn = 7;  //模版是否使用浮窗按钮（除摇一摇模版外）(supply 4.10.1)
  uint32 tail_traffic_control_rate = 8; // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  bool x_request = 9; // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  uint32 sensitivity = 10;                // 互动灵敏度，1-低，2-中，3-高
  uint32 apk_download_type = 11;          // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 12;            // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 13;                     // 包体安装是否确认
  uint32 click_type = 14;                 // 默认: 0（仅互动) 1（互动+互动组件点击）
  uint32 new_sensitivity = 15;            // 互动灵敏度，1-10低-高
  uint32 disable_interaction = 16;        // 0：允许互动广告；1：屏蔽互动广告。
  uint32 click_lose_rate = 17;            // 开屏广告点击概率隐藏配置（隐匿点击）
  // 摇一摇操作时间阈值，默认2秒，只允许正整数 (单位：秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_operation_time_threshold = 18;
  // 摇一摇单双向触发，默认单向触发 (0: 单向, 1: 双向) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_single_direction_trigger = 19;
  // 互动组件跳转触发时间间隔(单位: 秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444341
  uint32 interactive_component_trigger_interval = 20;
}

// 开屏交互样式 (supply 4.10.1)
message SplashTemplateTypeSetting {
  uint32 template_type = 1; //开屏交互样式 【4:普通交互、5:摇一摇模版】
  uint32 ratio = 2;//比例，0-100
}

//全屏视频
message SdkFullScreenVideoSetting {
  //本地缓存的广告上限（default=4）
  uint32 cacheTop = 1;
  //广告是否静音（0:静音、1:非静音。default=0）
  uint32 ifMute = 2;
  //视频关闭按钮位置，左上：1，右上：3
  uint32 videoClosePosition = 3;
  //endcard关闭按钮位置，左上：1，左下：2，右上：3，右下：4，
  uint32 endcardClosePosition = 4;
  //静音按钮位置， 左上：1，左下：2，右上：3，右下：4，
  uint32 mutePostion = 5;
  //跳过按钮，根据视频播放进度的百分比显示，取值范围[0,100]，取0表示一开始就可条，取30，表示播放到30%时显示跳过按钮
  uint32 skipPercentage = 6;
  //小于零表示此项不生效，大于等于零表示经过的指定的秒数后显示跳过按钮，如果次项配置大于等于零，则skipPercent配置项自动失效
  int32 skipSeconds = 7;
  //是否允许关闭视频时直接退出广告，而不再显示endcard
  bool enableExitOnVideoClose = 8;
  // 播放模式，0:预加载模式，1:在线流媒体播放模式
  uint32 play_mode = 9;
  //视屏最大时长
  uint32 videoMaxDuration = 14;
  //是否限制时长: 1：表示不限制，2：表示按时关闭视频，3：表示限制视频素材时长（2.9.0新增）
  uint32 videoDurationType = 15;
  //支持的素材类型，0、仅支持视频，默认值;1、仅支持图片;2、支持图片+视频(supply 4.1.3)
  uint32 materialType = 16;
  //自动下载配置
  AutoClickSetting click_setting = 17;
  uint32 tail_traffic_control_rate = 18; // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  bool x_request = 19; // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  bool disable_auto_load = 20;            // 关闭广告播放中的自动加载逻辑
  uint32 sensitivity = 21;                // 互动灵敏度，1-低，2-中，3-高
  uint32 apk_download_type = 22;          // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 23;            // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 24;                     // 包体安装是否确认
  uint32 new_sensitivity = 25;            // 互动灵敏度，1-10低-高
  // 摇一摇操作时间阈值，默认2秒，只允许正整数 (单位：秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_operation_time_threshold = 26;
  // 摇一摇单双向触发，默认单向触发 (0: 单向, 1: 双向) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_single_direction_trigger = 27;
}

// 插屏 Interstitial
message SdkInterstitialSetting {

  // 广告是否静音（0:非静音、1:静音。default=0）
  uint32 ifMute = 1;
  // 倒计时/跳过按钮 倒计时出现时间（图片）输入值，默认是0 s
  uint32 skipSecondsBtnPicShow = 2;
  // 倒计时/跳过按钮 倒计时出现时间（视频）输入值，默认是5 s
  uint32 skipSecondsBtnVideoShow = 3;
  // 广告跳过逻辑 打开endcard（默认值）直接关闭 是否直接关闭广告（默认false）
  bool enableExitOnVideoClose = 4;
  // 关闭按钮位置，左上：1，左下：2，右上（默认）：3，右下：4
  uint32 videoClosePosition = 5;
  // endcard关闭按钮位置，左上：1, 左下：2，右上（默认）：3，右下：4，
  uint32 endcardClosePosition = 6;
  // 静音按钮位置， 左上：1(默认），左下：2，右上：3，右下：4，
  uint32 mutePostion = 7;
  // 下载需要先弹四要素，默认false 下载不弹四要素，true为下载弹四要素弹框
  bool download_dialog = 8;
  // 用户摇一摇的力度 2:低 1:中 0:高 （默认值）
  uint32 shakeLevel = 9;
  // 视频素材的结算点的打点时间 默认 5s，取值范围[0,30]
  uint32 videoSettlementTime = 10;
  // 图片素材的结算点的打点时间 默认 1s,取值范围[0,30]
  uint32 picSettlementTime = 11;
  // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  uint32 tail_traffic_control_rate = 12;
  // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  bool x_request = 13;
  uint32 err_click_rate = 14;            // 误点率上限
  uint32 playMode = 15;                  // 播放模式，0:预加载模式，1:预缓存及在线流式混合模式，边加载边播，2:在线流式播放，不预加载
  bool disable_auto_load = 16;           // 关闭广告播放中的自动加载逻辑
  uint32 apk_download_type = 17;          // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 18;            // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 19;                     // 包体安装是否确认
  uint32 new_sensitivity = 20;            // 互动灵敏度，1-10低-高
  bool disable_full_click = 21;           // 全屏可点, 默认false 全屏可点
  uint32 disable_interaction = 22;        // 0：允许互动广告；1：屏蔽互动广告。
  // 是否点击后自动关闭 默认false @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135440224
  bool enable_click_auto_close = 23;
  // 倒计时n秒后自动关闭 默认值0  @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135440224
  uint32 countdown_auto_close_timer = 24;
  // 媒体直投误点率，取值0-100，表示百分比整数部分 @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
  uint32 media_direct_err_click_rate = 25;
  // 媒体程序化误点率，取值0-100，表示百分比整数部分 @see: http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
  uint32 media_prog_err_click_rate = 26;
  // 摇一摇操作时间阈值，默认2秒，只允许正整数 (单位：秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_operation_time_threshold = 27;
  // 摇一摇单双向触发，默认单向触发 (0: 单向, 1: 双向) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_single_direction_trigger = 28;
  // 互动组件跳转触发时间间隔(单位: 秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444341
  uint32 interactive_component_trigger_interval = 29;
}


message SdkChannelSetting {
  uint32 channel_id = 1; //渠道id，每个渠道的id是固定的
  uint32 adExpireTime = 2; //广告过期时间，默认为3600秒，0表示不设置过期时间，单位（秒）
}

//自动下载配置
message AutoClickSetting {
  //自动下载类型：0、wifi（默认）；1、全网；2、不自动下载；
  uint32 auto_click_mode = 1;
  //开放此配置的dsp渠道类型，1:直投，2:其他三方，3:全渠道
  uint32 support_dsp_mode = 2;
  //是否允许下载类自动点击
  bool enable_download_ad = 3;
  //广告展示到总播放时长的百分比时间后，执行自动跳转，取值1到100
  uint32 action_at_time_percentage = 4;
  //配置生效流量比例,1到100
  uint32 traffic_ratio = 5;
  bool download_dialog = 6;  // 下载需要先弹四要素，默认false 下载不弹四要素，true为下载弹四要素弹框
}


//中控信息流广告设置信息
message SdkNativeSetting {
  //请求时的素材条件设置
  repeated RequestAssetSetting request_assets = 1;
  //详情页播放器静音控制：0-不静音；1-静音
  uint32 detail_page_video_mute = 2;
  //广告有效曝光定义-曝光像素百分比
  uint32 impression_percent = 3;
  //广告有效曝光定义-曝光持续时间
  uint32 impression_time = 4;
  //单次请求最大返回的广告条数
  uint32 max_ad_num = 5;
  bool download_dialog = 6;  // 下载需要先弹四要素，默认false 下载不弹四要素，true为下载弹四要素弹框
  uint32 tail_traffic_control_rate = 7; // 需要控制请求的无填充广告尾量比例，默认0，即不控制
  bool x_request = 8; // 是否移除 x_requested_with请求头 默认false不移除，true为移除
  uint32 apk_download_type = 9;           // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumableDownload = 10;            // 支持断点续传下载, 默认false不使用，true为使用
  bool checkMd5 = 11;                     // 包体安装是否确认
  repeated WidgetSetting widgets = 12;    // 交互样式
  uint32 sensitivity = 13;                // 互动灵敏度，1-10 低-高
  int32 req_interval_time = 14;           // 配置间隔时间(单位：秒)
  int32 ad_pool_size = 15;                // 缓存的广告数量
  repeated string template_ids = 16;      // 元素映射模板id
  bool enable_developer_render = 17;      // 是否允许开发者渲染互动组件  false:SDK渲染  true：开发者渲染 默认false
  // 摇一摇操作时间阈值，默认2秒，只允许正整数 (单位: 毫秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_operation_time_threshold = 18;
  // 摇一摇单双向触发，默认单向触发 (0: 单向, 1: 双向) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_single_direction_trigger = 19;
  // 互动组件跳转触发时间间隔(单位: 毫秒) http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444341
  uint32 interactive_component_trigger_interval = 20;
}

message WidgetSetting {
  uint64 widget_id = 1;                   // 挂件id
  uint32 ratio = 2;                       // 比例，0-100

}

//请求时的素材设置
message RequestAssetSetting {
  //图片元素，参见RequestAssetImageSetting对象。RequestAssetImageSetting、RequestAssetTextSetting不共存
  RequestAssetImageSetting image = 1;
  //文字元素，参见RequestAssetText对象。RequestAssetImageSetting、RequestAssetTextSetting不共存
  RequestAssetTextSetting text = 2;
  //该元素是否必须返回，默认为否；1=必填；0=非必填
  uint32 required = 3;
}

//请求时图片元素
message RequestAssetImageSetting {
  //图片元素类型；1=视频封面图，2=icon
  uint32 type = 1;
  //素材宽度
  uint32 w = 2;
  //素材高度
  uint32 h = 3;
  //素材比例中的，宽度比值 （例如长宽比为9:16,那此处的值为16)
  uint32 w_ratio = 4;
  //素材比例中的，高度比值（例如长宽比为9:16,那此处的值为9)
  uint32 h_ratio = 5;
}

//请求时的文字元素
message RequestAssetTextSetting {
  //文字元素类型，1-标题；2-描述；3-评分；4-行动按钮文字；6-下载数量；7-星级
  uint32 type = 1;
  //文字元素最小长度
  uint32 min_length = 2;
  //文字元素最大长度
  uint32 max_length = 3 ;
}