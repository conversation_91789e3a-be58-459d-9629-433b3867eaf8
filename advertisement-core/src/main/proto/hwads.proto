syntax = "proto2";
option java_package = "com.sigmob.ad.ssp.model.huawei";

message BidRequest {
  optional string id = 1; //全局唯一的竞价请求ID
  repeated Imp imp = 2; //展现信息，Imp对象数组，至少包含一个Imp
  optional App app = 3; //移动应用详情信息
  optional Device device = 4; //承载曝光的用户设备信息
  optional int32 test = 5 [default = 0]; //标识是否为测试模式，测试模式不计费
  optional int32 at = 6 [default = 2]; //竞价类型
  optional int32 tmax = 7; //交易的最大超时时间（包含网络延迟），单位为毫秒
  repeated string wlang = 8; //创意的语言白名单
  repeated string wlocalecountry = 9; //创意的语言国家
  optional Regs regs = 10; // 指定针对此请求有效的任何行业、法律或政府法规
  optional Ext ext = 11; //扩展信息

  repeated string cachecrid = 52; //客户端（SDK或者应用）缓存的广告素材ID，可以含有多个素材ID
  optional Source source = 55; // 资源对象
  repeated string badv = 56; //广告主域名黑名单
  optional string consent = 57; //TCF 2.0 用户同意信息字符串, 遵循IAB TCF2.0标准中的Consent String Format格式
  repeated int64 cachecriddisptime = 60; //大屏开机广告请求，cachecriddisptime中存储广告的播放时间戳
  optional SearchInfo search = 61; //搜索信息：关键词，小艺语音接入PPS广告平台请求个性化广告

  // Extensions.
  extensions 100 to 9999;

  message Regs {
    optional string adRating = 1 [default = ""]; //广告内容分级
    optional int32 coppa = 2 [default = -1]; //面向儿童的设置
    optional Ext ext = 3; // Regs扩展属性

    // Extensions.
    extensions 100 to 9999;

    message Ext {
      optional int32 tfua = 1 [default = -1]; //对广告请求进行标记，确保针对未达到法定承诺年龄的欧洲经济区 (EEA) 用户投放的是合适的广告

      // Extensions.
      extensions 100 to 9999;
    }
  }

  message Ext {
    repeated string ail = 7; // App install list，设备已安装订单上关联的APP包名列表。
    repeated string anil = 8; // App not install list，设备未安装订单上关联的APP包名列表。
    optional int32 needCompliance = 12; //是否必须返回广告法要求的合规信息，跟complianceCountry对应使用
    optional string complianceCountry = 13; //要求合规信息的国家或者地区码，使用 ISO-3166-1-alpha-2两位编码

    // Extensions.
    extensions 100 to 9999;
  }

  message Source {
    optional int32 fd = 1;
    optional string tid = 2;

    // Extensions.
    extensions 100 to 9999;
  }

  message Imp {
    optional string id = 1; //广告位ID，由Ad Exchange分配
    optional Native native = 2;
    optional Pmp pmp = 3; //该次展示机会适用的私有市场交易
    optional float bidfloor = 4 [default = 0]; //竞价底价
    optional string bidfloorcur = 5; //竞价底价币种
    optional int32 adtype = 6; //请求的广告类型,定义参考枚举类型ADTYPE
    repeated string bcat = 7; //禁止投放的广告分类列表
    repeated string wcat = 8; //允许投放的广告分类列表
    repeated int32 winteractiontype = 9;  //推广类型白名单，定义参考枚举类型InteractionType
    repeated string bpkgname = 10;  //禁止投放的App包名
    repeated string cur = 11; //允许使用的币种
    optional int32 maxCount = 12 [default = 1]; //该广告位期望返回的最大广告数量，默认值为1；预加载请求表示，希望返回预加载素材总个数上限
    optional int32 trafficDistributeMode = 13 [default = 0]; //Ad Exchange给DSP的流量分发方式，默认值为0
    repeated ImpEX impEXs = 14; //扩展信息数组，数组定义见ImpEX
    optional Video video = 15; //三方SSP过来的请求时Video使用 视频贴片使用
    optional ImpExt ext = 16; //Imp扩展属性
    repeated string wapp = 17; //能够投放的App包名白名单
    repeated string bseat = 18; //禁止投放此广告主或代理商的创意；如果bseat为空，表示对广告主或代理商没有限制。
    repeated string bcrid = 19; //禁止投放创意
    optional int32 purpose = 56 [default = 1]; //请求目的，默认值为1正式广告请求 2纯缓存广告素材请求
    optional int32 supportChannelPkg = 71; //请求是否支持在响应中返回渠道包类型的素材。取值：0/空：不支持;1：支持;只有当wInteractionType中包含了应用类推广时才需要关注本字段
    optional SearchInfo searchInfo = 73; //大搜关键词信息
	optional float sugBidPrice = 79; // 出价建议

    // Extensions.
    extensions 100 to 9999;

    message Native {
      repeated Asset assets = 1; //Asset对象数组，Ad Exchange可以接受的广告样式
      repeated AssetLimit assetLimits = 2; //AssetLimit对象数组，Ad Exchange针对每个广告样式允许的最大数量

      // Extensions.
      extensions 100 to 9999;
      message Asset {
        optional int32 templateid = 1; //模板Id
        optional int32 w = 2; //允许的宽度，单位为像素
        optional int32 h = 3; //允许的高度，单位为像素
        // Extensions.
        extensions 100 to 9999;
      }

      message AssetLimit {
        optional int32 templateid = 1; //模板Id
        optional int32 maxcount = 2; //该模板对应的素材能够返回的最大广告数量

        // Extensions.
        extensions 100 to 9999;
      }
    }

    message Pmp {
      optional int32 private_auction = 1 [default = 1]; //除了私有交易，是否也接受公开竞价
      repeated Deal deals = 2; //表示本展示机会可以适用于哪些私有交易
      // Extensions.
      extensions 100 to 9999;

      message Deal {
        optional string id = 1; //Deal唯一的ID，Ad Exchange生成
        optional float bidfloor = 2; //底价
        optional string bidfloorcur = 3; //底价使用的币种
        optional int32 at = 4 [default = 3]; //交易类型 1 = First Price, 2 = Second Price Plus, 3 = Fixed Price
        repeated string cur = 5; //允许使用的币种
        repeated string wadomain = 6; //广告主域名白名单
        repeated int32 winteractiontype = 7; //推广类型白名单
        optional DealExt ext = 8; // Deal相关的扩展属性
        optional string transactiontype = 9; //订单交易模式，取值为PDB、PD、PA、RTB中的一种

        // Extensions.
        extensions 100 to 9999;

        message DealExt {
          repeated int32 durations = 1; //订单级的视频广告播放时长，单位是秒，当前仅用于视频贴片，视频贴片的时长有15s和30s两种。
          repeated string ail = 2; //App install list，设备已安装订单上关联的APP包名列表。
          repeated string anil = 3; //App not install list，设备未安装订单上关联的APP包名列表。
          repeated int64 displaytimes = 4; //订单排期日的开始时间戳，指从UTC时间1970年1月1日00:00:00以来的毫秒数，支持传递多值。
          repeated int32 maxCount = 5; //表示此订单对应周期需要的创意个数，与displaytimes数组对应。
        }
      }
    }

    message Video {
      optional int32 linearity = 3; //广告展现样式0: "未知";1：包括前贴中贴后贴;2：即视频播放中的悬浮广告;
      optional int32 startdelay = 4; //该字段仅在linearity=1时有效；线性贴片，0：前帖；-1：中贴；-2：后贴。
      optional VideoExt ext = 5; //Video扩展属性

      // Extensions.
      extensions 100 to 9999;
      message VideoExt {
        repeated int32 durations = 1; //视频广告播放时长，单位是秒，当前仅用于视频贴片，视频贴片的时长有15s和30s两种。

        // Extensions.
        extensions 100 to 9999;
      }
    }

    message ImpExt {
      optional int32 totalduration = 1; //视频广告总播放最大时长，单位是秒，当前仅用于视频贴片，视频贴片有多贴，表示的是多贴总播放的的最大时长。适用于音频广告。
      optional int32 adscore = 2; //广告位创意最低分数，先审后投-机审模式用到，取值：1、2、3、4，对应创意审核中创意评分。新接入DSP使用adscoreV2。
      optional string transUniqueId = 3;//唯一跟踪ID
      optional int32 adscoreV2 = 7; //广告位创意最低分数，先审后投-机审模式用到，取值例如： 5,10,20,30,40等，对应创意审核中创意评分。表示需要的创意评分应大于等于该分数。随着审核场景细分，分数枚举值会增加。
      optional int32 extraCount = 8; // 期望额外返回的广告个数
      optional string positionId = 10; // 订单对应的流量包id
      optional string callBack = 11; // base64 加密的（dealId^channelType） channelType:从端侧device.partnerChannel

      // Extensions.
      extensions 100 to 9999;
    }
  }

  message App {
    optional string id = 1; //应用ID，Ad Exchange分配
    optional string name = 2; //应用名称
    optional string bundle = 3; //应用的包名
    optional string ver = 4; //应用的版本号
    optional AppExt ext = 5; // 应用扩展字段
    optional Content content = 52; //媒体内容的详细信息
    repeated Industry industry = 56; //app行业分类

    // Extensions.
    extensions 100 to 9999;

    message Content {
      optional string id = 1; //影视剧ID
      optional int32 len = 2; //内容的时长，单位为秒
      optional string title = 3; //内容标题(电影、电视剧的名称)
      optional int64 displaytime = 4; //displaytime里面contentid对应的广告素材的播放时间戳(大屏开机广告，端侧会使用该字段决定广告素材的播放顺序)
      repeated Pair contentBundle = 5; //内容上下文，用于内容上下文定向
      // Extensions.
      extensions 100 to 9999;

      message Pair {
        optional string key = 1;
        optional string value = 2;
        // Extensions.
        extensions 100 to 9999;
      }
    }

    message Industry {
      optional string parentType = 1;
      optional string childType = 2;

      // Extensions.
      extensions 100 to 9999;
    }

    message AppExt {
      optional int32 linkAllowed = 1; //是否支持其他渠道或第三方检测渠道的链接 : 1:支持其他渠道或三方监测渠道的链接,0:不支持,默认值0
    }
  }

  message Device {
    optional string ua = 1; //浏览器user agent字符串，明文
    optional string ip = 2; //用户远程IPv4地址
    optional int32 devicetype = 3; //设备类型，定义参考枚举类型DeviceType
    optional string make = 4; //设备厂商
    optional string model = 5; //设备型号
    optional string os = 6; //操作系统
    optional string osv = 7; //设备操作系统版本号
    optional int32 h = 8; //屏幕的高度，单位为像素
    optional int32 w = 9; //屏幕的宽度，单位为像素
    optional int32 ppi = 10; //屏幕尺寸，单位是像素/英寸
    optional float pxratio = 11; //物理像素和设备独立像素的比值
    optional string language = 12; //系统语言，使用alpha-2/ISO 639-1语言表，例如zh、en
    optional string carrier = 13; //运营商，限国内使用
    optional int32 connectiontype = 14; //网络连接类型，定义参考枚举类型ConnectionType
    optional string dpidsha256 = 15; //Platform device ID(如Android ID)，SHA256哈希(64个字符，小写)，限国内使用
    optional string dpidmd5 = 16; //Platform device ID(如Android ID)，哈希(32个字符，小写)，限国内使用
    optional string oaid = 17; //Open Anonymous Device ID，是华为生成的匿名设备标识符
    optional string lmt = 18 [default = "0"]; //用户是否允许广告跟踪，默认值为0，0：用户对跟踪不限制 1：用户不允许跟踪
    optional string gaid = 19; //GAID(Google advertising ID)，海外使用
    optional Geo geo = 21; //增加地理位置信息
    optional string localeCountry = 22; //客户端系统语言的国家或者地区码，ISO 3166-1 alpha-2两位字母编码，例如CN、TW、HK、US、GB、JP
    optional Ext ext = 25;
    optional string agver = 59; //应用市场版本号
    optional string agcountry = 63; //应用市场App中设置里面的国家和地区

    // Extensions.
    extensions 100 to 9999;

    message Geo {
      optional string country = 1; //国家或者地区码，使用 ISO-3166-1-alpha-2.两位编码
      optional int32 utcoffset = 57; //当前时间和UTC时间的偏移+/-分钟数

      // Extensions.
      extensions 100 to 9999;
    }

    message Ext {
      optional string brand = 1;
      repeated string marketApps = 52; //设备已安装的应用市场包名列表。
      optional string bootMark = 54; //系统启动标识
      optional string updateMark = 55; //系统更新标识
      optional int32 appBrand = 56; //端侧app.brand

      // Extensions.
      extensions 100 to 9999;
    }
  }

  message SearchInfo {
    optional string qry = 1; //用户原始搜索词
    repeated Keyword kws = 2; //分词后的搜索关键词
    optional string chnl = 3; //搜索频道: 1:应用搜索, 2:视屏搜索, 3:图片搜索, 默认值: 空
    repeated int32 relevance = 4; // 搜索相关性, 0:不相关, 1：弱相关, 2:强相关

    // Extensions.
    extensions 100 to 9999;

    message Keyword {
      optional int32 type = 1; //关键词类型: 1: 应用名称, 2: 应用分类
      optional string kw = 51; //关键词
      // Extensions.
      extensions 100 to 9999;
    }
  }
}

message BidResponse {
  optional string id = 1; //竞价请求的ID
  repeated SeatBid seatbid = 2; //竞价席位对象数组
  optional string bidid = 3; //竞价者生成的响应ID
  optional int32 nbr = 4; //没有返回广告的原因，定义参考枚举类型NoBidReason

  // Extensions.
  extensions 100 to 9999;

  message SeatBid {
    repeated Bid bid = 1; //Bid对象数组。每个Bid对应一个曝光机会，多个Bid可以对应同一个曝光机会
    optional string seat = 2; //曝光购买者的ID。可以填写DSP平台给广告主分配的ID。
    optional Ext ext = 52;  //扩展信息

    // Extensions.
    extensions 100 to 9999;

    message Bid {
      optional string id = 1; //DSP生成的竞价ID，用于日志和跟踪
      optional string impid = 2; //对应竞价请求Imp中的id，是广告位ID
      optional double price = 3; //出价，如果CPM计费，单位为分/千次展示；如果CPT计费，单位为分/天
      optional string nurl = 4; //竞标成功通知URL
      optional string lurl = 5; //竞标失败通知URL
      optional string cid = 6; //Campaign ID，即广告活动ID
      optional string crid = 7; //创意ID（Creative ID）
      optional string dealid = 8; //对应于竞价请求中的deal.id
      optional NativeRsp nativersp = 9; //创意信息
      repeated string adomain = 10; //广告主域名
      repeated string cat = 15; //创意的内容类别
      optional string cur = 16; //响应的币种
      repeated ImpEX impEXs = 17; //扩展信息数组
      optional string language = 18; //创意的语言使用alpha-2/ISO 639-1语言表，例如zh、en
      optional string adRating = 19; //创意的内容分级
      optional string catText = 20; //创意行业分类(字符串)
      optional Ext ext = 84; //扩展信息
      repeated ContentExt contentExts = 87; // 和SEAD DSP新增字段
      optional int32 relevance = 90; //相关性
      optional string dwnParameter = 93; // 转换跟踪参数，AG DSP使用
      optional string seat = 94; // 曝光购买者（参与此次竞价的广告主或代理商）的ID，AG DSP使用，AG返回直客或者一代ID

      // Extensions.
      extensions 200 to 9999;

      message NativeRsp {
        optional int32 templateid = 1; //模板ID
        optional string title = 2; //标题
        optional string brand = 3; //品牌名称
        repeated Image images = 4; //image对象数组
        repeated string imptrackers = 5; //曝光跟踪URL数组，可以包含多个
        optional Link link = 6; //目标连接
        optional Video video = 7; //视频信息
        optional ShareInfo shareinfo = 8; //分享信息
        optional int64 expiretime = 9; //创意结束投放时间
        optional string summary = 11; //一句话描述
        optional Image icon = 12; //app图标
        repeated Permission permissions = 16; //被推广应用的权限信息说明，每条权限信息一条数据
        repeated Tracking eventtrackers = 17; //除曝光、点击外的其它跟踪事件
        repeated Measurement om = 18; //三方可视化监测提供商
        optional Audio audio = 19; //音频信息
        optional string label = 59; //创意标签
        optional int64 displaytime = 62; // 大屏开机广告：displaytime里面存储时间戳，表示crid对应素材的播放顺序
        repeated Data data = 65; //大搜响应素材子链信息，包括url，文字子链，按钮子链
        repeated NativeExts nativeExts = 66; //扩展信息

        // Extensions.
        extensions 100 to 9999;

        message NativeExts {
          optional string key = 1;
          optional string value = 2;

          // Extensions.
          extensions 100 to 9999;
        }

        message Data {
          optional string type = 1; //11: 展示url; 20: 文字子链; 21: 按钮子链
          optional string value = 2; //dispalyurl; 文字子链的文本; 按钮子链的文本
          optional string ext = 3; //文字子链的URL; 按钮子链的URL

          // Extensions.
          extensions 100 to 9999;
        }
      }

      message Ext{
        optional string compliance = 10; //投放客户的合规信息

        // Extensions.
        extensions 100 to 9999;
      }
    }

    message Ext {
      optional string compliance = 1; //投放客户的合规信息

      // Extensions.
      extensions 100 to 9999;
    }
  }

  message Image {
    optional string url = 1; //image文件的URL地址
    optional int32 w = 2; //image的宽，单位为像素
    optional int32 h = 3; //image的高，单位为像素
    optional string mime = 4; //image文件内容的MIME类型，如image/jpeg、image/png、image/gif
    optional string sha256 = 5; //图片文件的sha256摘要，客户端校验用到。
    optional int32 filesize = 6; //文件的大小，单位是字节
    optional string targetUrl = 55; // 图片跳转地址
    // Extensions.
    extensions 100 to 9999;
  }

  message Video{
    optional string url = 1; //视频文件的下载地址
    optional int32 duration = 2; //视频文件的播放时长，单位是毫秒
    optional int32 filesize = 3; //视频文件的大小，单位是字节
    optional string mime = 4; //视频文件内容的MIME类型，如video/mp4
    optional int32 w = 5; //视频的宽，单位为像素
    optional int32 h = 6; //视频的高，单位为像素
    optional string sha256 = 7; //视频文件的sha256摘要，客户端校验用到
    // Extensions.
    extensions 100 to 9999;
  }

  message ShareInfo{
    optional string iconurl = 1; //分享图标的URL地址。请提供https的地址。
    optional string title = 2; //分享标题。返回明文
    optional string description = 3; //分享描述。返回明文
    optional string shareurl = 4; //分享的链接URL地址
    // Extensions.
    extensions 100 to 9999;
  }

  message Link{
    optional int32 interactiontype = 1; //推广类型
    optional string url = 2; //落地页的地址。当先投后审的场景下，且推广类型为“点击打开网页”、“点击下载应用”时必填
    optional string deeplink = 3; //deeplink连接
    repeated string clicktrackers = 4; //点击跟踪URL数组，
    optional string pkgname = 5; //被推广App的包名。
    optional string channelinfo = 6; //渠道推广跟踪参数。
    optional Apk apk = 58; //Apk信息
    optional string appStorePkg = 63; //应用所在应用市场的包名
    optional string miniProgramLink = 69;  // 微信小程序链接
    optional string miniProgramId = 70;  // 微信小程序id
    optional string miniProgramName = 71;  // 微信小程序名称

    // Extensions.
    extensions 100 to 9999;
  }

  message Permission{
    optional string permissionLabel = 1; //权限名称
    optional string groupDesc = 2; //权限组描述
    // Extensions.
    extensions 100 to 9999;
  }

  message Tracking{
    optional string eventType = 1; //需要给第三方DSP、监测平台上报的其它跟踪事件类型（不包含曝光、点击）
    repeated string url = 2; //跟踪事件上报地址，可以包含多个。请提供https的地址
    // Extensions.
    extensions 100 to 9999;
  }

  message Measurement{
    optional string vendorKey = 1; //供应商标识
    optional string resourceUrl = 2; //托管供应商的验证脚本的网站
    optional string verificationParameters = 3; //向所下载的验证脚本传递的参数

    // Extensions.
    extensions 100 to 9999;
  }

  message Audio{
    optional string url = 1; //音频文件的下载地址
    optional int32 duration = 2; //音频文件的播放时长，单位是毫秒
    optional int32 filesize = 3; //音频文件的大小，单位是字节
    optional string mime = 4; //音频文件内容的MIME类型，如audio/mpeg,audio/x-wav
    optional string sha256 = 5; //音频文件的sha256摘要，客户端校验用到
    // Extensions.
    extensions 100 to 9999;
  }

  message Apk{
    optional string url = 1; //Apk文件的下载地址
    optional string securl = 2; //Second Url Apk文件的第二下载地址
    optional string ver = 3; //应用versionCode
    optional int32 filesize = 4; //APK文件大小，单位是字节。
    optional string sha256 = 5; //APK文件的sha256摘要。
    optional string name = 6; //应用名称，URL编码。
    optional string vername = 8; //APK的版本名称，例如8.0.0.300
    optional string iconurl = 12; //APP图标地址
    optional string pkgId = 14; //渠道包id
    optional int32 appSource = 15; //被推广应用应用市场来源 0：AG，1：Apkpure

    // Extensions.
    extensions 100 to 9999;
  }
}


message ImpEX {
  optional string key = 1; //Key值，该值为接口定义的扩展code
  optional string value = 2; ////Key code对应值
  // Extensions.
  extensions 100 to 9999;
}

message ContentExt {
  optional string key = 1; // Key值，该值为接口定义的扩展code
  optional string value = 2; // Key code对应值
  // Extensions.
  extensions 100 to 9999;
}

//网络连接类型
enum ConnectionType {
  CONNECTION_UNKNOWN = 0;
  ETHERNET = 1;
  WIFI = 2;
  CELL_UNKNOWN = 3;
  CELL_2G = 4;
  CELL_3G = 5;
  CELL_4G = 6;
  CELL_5G = 7;
}

//设备类型
enum DeviceType {

  UNKNOW_DEVICE = 0;

  // Phone.
  HIGHEND_PHONE = 4;

  // Tablet.
  TABLET = 5;

  // 大屏
  WISEDOM_SCREEN = 8;
}

//华为广告位类型
enum AdType {

  //开屏广告
  SPLASH_TYPE = 1;

  //原生广告
  NATIVE = 3;


  //视频贴片广告
  ROLL = 60;

  //激励视频
  INCENTIVE_VIDEO = 7;

  //横幅广告
  BANNER_TYPE = 8;

  //应用图标
  APPICON = 9;

  //插屏广告
  INTERSTITIAL_TYPE = 12;

  //原生信息流
  NATIVE_INFORMATION_FLOW = 31;
}


//推广类型
enum InteractionType {

  //纯展示，即点击后无反应
  //原生、原生信息流、开屏、手机锁屏、激励视频、Banner
  PURE_SHOW = 0;

  //点击打开网页
  //原生、原生信息流、开屏、手机锁屏、激励视频、Banner
  CLICK_OPEN_WEB = 1;

  //点击下载应用
  //原生、原生信息流、开屏、激励视频、Banner
  CLICK_DOWNLOAD_APP = 2;


  //点击进入应用内
  //备注: 如果请求体的推广类型白名单包含取值3和1，那么返回的推广类型为3时，可以携带Link.url(落地页地址)
  //原生、原生信息流、开屏、激励视频、Banner
  CLICK_IN_APP = 3;

  //快应用推广
  CLICK_DOWNLOAD_APP_SDK = 4;

  //应用推广（未安装则点击下载应用，已安装则点击进入应用内）
  //原生、原生信息流、开屏、手机锁屏、激励视频、Banner
  APPLICATION = 5;

  //拉起微信小程序
  WECHAT_MINI_APP = 10;

}

service BidReqService {
  rpc postReq (BidRequest) returns (BidResponse) {}
}