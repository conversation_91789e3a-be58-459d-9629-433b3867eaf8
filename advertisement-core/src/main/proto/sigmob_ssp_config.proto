/**
@description ssp ad server与sdk config接口的交互协议

for sdk 1.8:
add SplashConfig
CommonEndpointsConfig add ads and strategy

**/

syntax = "proto3";
import "sigmob_rtb.proto";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.config";
option objc_class_prefix = "Sigmob";

message SdkConfigRequest {
  string language = 1; //内部生成；请求唯一标识
  string idfa = 2; // 必填！iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}
  string idfv = 3; // 内部生成
  string country = 4;
  sigmob.Geo geo = 5;
  string android_id = 6;
  string imei = 7;
  string gaid = 8;
  Device device = 9; //设备信息
}

message SdkConfigResponse {
  uint32 code = 1;
  string error_message = 2;
  SdkConfig config = 3;
  SdkConfigV2 config_v2 = 4;
}

//配置信息
message SdkConfig {
  CommonEndpointsConfig endpoints = 1;
  uint32 configRefresh = 2;
  uint32 appOrientation = 3;
  RvConfig rv = 4;
  SplashConfig splash = 5;
  bool disableUpAppInfo = 6; //是否禁止上传用户已安装的app信息，默认是false，即可以上传
  uint32 report_log = 7; //是否要写log,控制最细颗粒度为设备 0:关、1:开
  AntiFraudLogConfig anti_fraud_log = 8; //反作弊信息采集配置
  bool is_gdpr_region = 9; //是否gdpr地区，默认是false
  uint32 tracking_expiration_time = 10; //track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tracking_retry_interval = 11; //track上报失败的重发时间间隔
  uint32 up_wifi_list_interval = 12; //上传wifi列表的时间间隔，单位秒，小于60秒则表示不上传
  uint32 max_send_log_records = 13; //每次上传打点日志的最大条数
  uint32 send_log_interval = 14; //打点日志上报时间间隔 （单位：秒）
  repeated uint32 dclog_blacklist = 15; //点号位黑明单，默认会将100，101号点自动加入黑名单，如果下发了黑明单，以下发为准。
  int32 auto_load_interval = 16; //非0代表关闭自动加载；[0, 30]统一为30（单位：秒）
  bool disable_up_location = 17;    //是否禁止上传用户的位置信息。false: 不禁止上传；true: 禁止上传
  uint32 disable_up_oaid = 18; //是否禁止获取oaid信息，其中在android10一下默认为不上传，Android10以上默认上传；1: 不禁止上传；2: 禁止上传; 其它走默认值
  bool enable_permission = 19;  //允许获取权限的开关；默认为false: 表示不获取权限列表
  uint32 apk_expired_time = 20; //下载的apk的过期时间，单位：s; 默认0: 表示永远过期；
  bool disable_up_ip = 21; //是否禁止取设备所有ip(仅iOS支持)
  repeated string ip_names = 22; //设备ip地址类型名(当disable_up_ip值为false时启用)
  bool enable_attribution_update = 23;  //是否开启在曝光是更新归因ID
  bool enable_report_crash = 24; //允许获取崩溃信息的开关；默认为false: 表示不上报崩溃信息
  bool oaid_api_is_disable = 25; //默认值 false :允许通过API模式采集oaid, true: 禁止通过API模式采集oaid，
  bool enable_debug_level = 26; //是否开启debug日志、初始化监测广告插件、监测结果打点和日志输出
  bool disable_boot_mark = 27;  //禁止 boot_mark, update_mark 获取,default=false
}

//配置信息
message SdkConfigV2 {
  Common common_config = 1;
  IOS ios_config = 2;
  Android android_config = 3;
}

message Common {
  CommonEndpointsConfig endpoints = 1;
  uint32 configRefresh = 2;
  RvConfig rv_config = 3;
  SplashConfig splash_config = 4;
  AntiFraudLogConfig anti_fraud_log = 5;      //反作弊信息采集配置
  bool is_gdpr_region = 6;                      //是否gdpr地区，默认是false
  uint32 tracking_expiration_time = 7; //track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tracking_retry_interval = 8;  //track上报失败的重发时间间隔
  uint32 max_send_log_records = 9;    //每次上传打点日志的最大条数
  uint32 send_log_interval = 10;       //打点日志上报时间间隔（单位：秒）
  repeated uint32 dclog_blacklist = 11;    //点号位黑明单，默认会将100，101号点自动加入黑名单，如果下发了黑明单，以下发为准。
  int32 auto_load_interval = 12; //非0代表关闭自动加载；[0, 30]统一为30（单位：秒）
  bool enable_attribution_update = 23;  //是否开启在曝光是更新归因ID
  bool enable_debug_level = 14; //是否开启debug日志、初始化监测广告插件、监测结果打点和日志输出
  NativeConfig native_config = 15;
  uint32 load_timeout = 16;//广告总超时
  uint32 load_periodTime = 17;//重复发起请求的间隔时间，在非ready的情况下，用来防止重复发起请求
  bool disable_up_location = 18;    //是否禁止上传用户的位置信息。false: 不禁止上传；true: 禁止上传
}

message IOS {
  bool disable_up_ip = 1; //是否禁止取设备所有ip(仅iOS支持)
  repeated string ip_names = 2; //设备ip地址类型名(当disable_up_ip值为false时启用)
}

message Android {
  bool disableUpAppInfo = 1;                    //是否禁止上传用户已安装的app信息，默认是false，即可以上传
  uint32 report_log = 2;                      //是否要写log,控制最细颗粒度为设备 0:关、1:开
  uint32 up_wifi_list_interval = 3;    //上传wiki列表的时间间隔，单位秒，小于60秒则表示不上传
  uint32 disable_up_oaid = 4; //是否禁止获取oaid信息，其中在android10一下默认为不上传，Android10以上默认上传；1: 不禁止上传；2: 禁止上传; 其它走默认值
  bool enable_permission = 5;  //允许获取权限的开关；默认为false: 表示不获取权限列表
  uint32 apk_expired_time = 6; //下载的apk的过期时间，单位：s; 默认0: 表示永远过期
  bool enable_report_crash = 7; //允许获取崩溃信息的开关；默认为false: 表示不上报崩溃信息
  bool oaid_api_is_disable = 8; //默认值 false :允许通过API模式采集oaid, true: 禁止通过API模式采集oaid，
  bool disable_boot_mark = 9;  //禁止 boot_mark, update_mark 获取,default=false
}

//传感器配置信息
message AntiFraudLogConfig {
  MotionConfig motion_config = 1; //传感器配置，如果不配置，则认为不采集传感器信息
  repeated string events = 2; //触发事件列表['load', 'start', 'click', 'close']
}


message MotionConfig {
  uint32 interval = 1; //单位毫秒；传感器获取的间隔时间
  uint32 queue_max = 2; //对列的最大值
  uint32 count = 3; //从队列中分别获取事件前后的条数
}

//通用地址配置
message CommonEndpointsConfig {
  string log = 1;
  string ads = 2;
  string strategy = 3;
  string hb_ads = 4;
}

//激励视频配置
message RvConfig {
  RvEndpointsConfig endpoints = 1;
  uint32 cacheTop = 2;
  uint32 ifMute = 3;
  uint32 showClose = 4;
  float finished = 5;
  uint32 loadExpired = 6;
  uint32 loadTimeout = 7;
  //视频关闭按钮位置，左上：1，右上：3
  uint32 videoClosePosition = 8;
  //endcard关闭按钮位置，左上：1，左下：2，右上：3，右下：4，
  uint32 endcardClosePosition = 9;
  //静音按钮位置， 左上：1，左下：2，右上：3，右下：4，
  uint32 mutePostion = 10;
  //重复发起请求的间隔时间，在非ready的情况下，用来防止重复发起请求
  uint32 loadPeriodTime = 11;
  //跳过按钮，根据视频播放进度的百分比显示，取值范围[0,100]，取0表示一开始就可条，取30，表示播放到30%时显示跳过按钮
  uint32 skipPercent = 12;
  //截屏开关：0:关、1:开 default=0
  uint32 screen_capture = 13;
  //需要截屏的时间集合
  repeated uint32 screen_capture_times = 14;
  //小于零表示此项不生效，大于等于零表示经过的指定的秒数后显示跳过按钮，如果次项配置大于等于零，则skipPercent配置项自动失效
  int32 skipSeconds = 15;
  //是否允许关闭视频时直接退出广告，而不再显示endcard
  bool enableExitOnVideoClose = 16;
  //激励视频关闭对话框配置(>=3.4.0支持)
  DialogSetting close_dialog_setting = 17;
}

//激励视频相关接口地址
message RvEndpointsConfig {
  string ads = 1;
  string strategy = 2;
}

//开屏配置
message SplashConfig {
  uint32 showDuration = 1; //开屏倒计时时间
  int32 cacheTop = 2; //开屏素材缓存上限，小于0，则清楚缓存
  int32 material_expired_time = 3; //素材的过期时间，单位天数，若小于0，则清楚缓存
}

//原生广告配置
message NativeConfig {
  int32 cacheTop = 1;//原生广告素材缓存上限，小于等于0，则清除缓存
}

message DialogSetting {
  string title = 1; //标题
  string body_text = 2; //正文，针对激励视频，需要在文案中带宏,表示视频剩余播放的秒数
  string cancel_button_text = 3; //取消按钮的文案
  string close_button_text = 4; //关闭按钮的文案
}

