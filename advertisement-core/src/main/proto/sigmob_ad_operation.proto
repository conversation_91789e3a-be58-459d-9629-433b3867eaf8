/**
@description 全局配置拆分为全局和App级别Session
 add AppSession

<AUTHOR>
@date 2018-09-27
@版本: 2.4.0
**/

syntax = "proto3";

package operation;

import "uniform_delivery.proto";
import "abtest.proto";
option java_package = "com.sigmob.sigdsp.grpc";
option go_package = "sigmob.com/common/entry/operation";

//广告竞价服务接口
service AdOperation {
    // 广告上线
    rpc OnlineCampaign (Campaign) returns (Mesg) {}
    // 广告下线(暂停)【v3.3.0开始弃用，改为使用OfflineBatchCampaigns，将于v3.4.0版本删除】
    rpc OfflineCampaign (Campaign) returns (Mesg) {}
    // 广告信息更新通知(新增、修改)
    rpc UpdateCampaignNotice (BatchCampaign) returns (Mesg) {}
    // 创意信息更新通知(新增、修改)
    rpc UpdateCreativeNotice (Creative) returns (Mesg) {}
    rpc Ping (Mesg) returns (Mesg) {}
    // 更新全局配置信息
    rpc UpdateConfigNotice (Session) returns (Mesg) {}
    // 更新app、adtype级别配置信息
    rpc UpdateAppConfigNotice (AppSession) returns (Mesg) {}
    // 更新一条白名单记录
    rpc UpdateWhiteListNotice (WhiteList) returns (Mesg) {}
    // 删除一条白名单记录
    rpc DeleteWhiteListNotice (WhiteList) returns (Mesg) {}
    // 更新app媒体侧定向包含、定向排除通知
    rpc UpdateAppSlotTargetNotice (AppSlots) returns (Mesg){}
    // 同步app信息
    rpc SyncProductInfo(Apps) returns (Mesg){}
    // 同步ABTest框架配置信息
    rpc SyncABTestConfig(Mesg) returns (Mesg){}
    // 同步App Slot 屏蔽规则
    rpc SyncAppSlotShieldRule(Mesg) returns (Mesg){}
    // 广告控量信息变更(campaign中只含有id)
    rpc UpdateCampaignAdControlNotice (Campaign) returns (Mesg) {}
    // 根据id列表同步EndCard模板source
    rpc SyncEndcardTemplateById(TemplateIdsReq) returns (Mesg){}
    // 广告计划控量到量处理通知
    rpc CampaignReachedLimit(ReachLimitInfo) returns (AdControlInfo) {}
    // 媒体状态更新通知 for 数据
    rpc SyncAppState(Mesg) returns (Mesg){}
    // 媒体控量规则修改 for 平台
    rpc UpdateAppControl(UpdateAppControlsReq) returns (Mesg){}
    // 同步产品级总预算(179天)通知 for 数据
    rpc SyncProductTotalIncome(Mesg) returns (Mesg){}
    // 产品级控量变更通知 for 平台
    rpc UpdateProductControlNotice(ProductControls) returns (Mesg) {}
    // 批量offline计划
    rpc OfflineBatchCampaigns (BatchCampaign) returns (Mesg) {}
    // 批量更改广告地址
    rpc UpdateBatchCampaignUrl (BatchCampaign) returns (Mesg) {}
    // 批量投放日期
    rpc UpdateBatchCampaignDate (UpdateBatchCampaignDateReq) returns (Mesg) {}
    // 批量投放时段
    rpc UpdateBatchCampaignTime (UpdateBatchCampaignTimeReq) returns (Mesg) {}
    // 更新vivo、oppo等广告审核状态
    rpc UpdateBatchCampaignCheckState(UpdateBatchCampaignCheckStateReq) returns (Mesg) {}
    // 创意出桶通知
    rpc CreativeOutBucketNotice (CreativeOutBucket) returns (Mesg) {}
    // 计划对应包体大小变更
    rpc UpdateProductPackageSizeNotice (ProductPackageSize) returns (Mesg) {}
    // 冷启动参数变更
    rpc UpdateColdStartInfoNotice (Mesg) returns (Mesg) {}
    // 素材跳过冷启动
    rpc ColdStartMaterialSkipNotice (ColdStartMaterialStateSkip) returns (Mesg) {}
    // IR极低广告位定向策略同步 for 数据
    rpc SyncLowIrSlots(LowIrSlotsReq) returns (Mesg){}
    // RTA标签定向
    rpc SyncRtaLabelTarget(Mesg) returns (Mesg){}
    // 电商商品更新同步
    rpc SyncMerchandiseInfo(Merchandise) returns(Mesg){}
    // 产品组更新同步
    rpc SyncLimitProdGroupInfo(LimitProdGroup) returns(Mesg){}
}

// IR极低广告位定向策略同步请求参数
message LowIrSlotsReq{
    repeated string solts = 1; // IR极低广告位列表
}

message ColdStartMaterialStateSkip{
    repeated uint64 material_id = 1;  // 素材id列表
    repeated uint64 adtype = 2;       // 广告类型列表
    repeated uint64 os = 3;           // 操作系统列表
}

message ProductPackageSize{
    uint64 package_size = 1;        // 产品包体大小，单位:Byte
    repeated uint64 camp_ids = 2;   // 对应计划id
}

message CreativeOutBucket{
    uint64 creative_id = 1;
    string app_id = 2;
    uint32 ad_type = 3;
}

message UpdateBatchCampaignCheckStateReq{
    repeated CampaignCheckState states = 1;
}

message CampaignCheckState{
    uint64 campaign_id = 1;
    uint64 creative_id = 2;
    uint64 check_service_id = 3;
    bool is_passed_check = 4;
}

message UpdateBatchCampaignDateReq{
    repeated CampaignDate camps = 1;
}

message CampaignDate{
    uint64 id = 1;          // campaign id
    string start_date = 2;  // 开始时间，yyyy-MM-dd
    string end_date = 3;    // 结束时间，yyyy-MM-dd
}

message UpdateBatchCampaignTimeReq{
    repeated uint64 ids = 1;  // campaign ids
    string time_interval = 2; // 对应sig_dsp_adver_campaign.time_interval
}

message ProductControls{
    repeated ProductControl prod_controls = 1;
}

message ProductControl{
    uint64 prod_id = 1;         // 产品id
    uint32 limit_type = 2;      // 1-日预算；2-日激活；3-总预算；4-设备曝光品控；5-设备点击频控；6-日DPBack
    uint32 limit_threshold = 3; // 控量阈值，预算单位：分，频控单位：次/天
}

message TemplateIdsReq{
    repeated uint32 ids = 1; // 需要更新的Endcard Template id列表
}

// 计划控量到量通知请求
message ReachLimitInfo{
    uint64 id = 1;              // campaign id
    uint32 limit_type = 2;      // 1:日曝光 2:日预算 3:总预算 4:日DpBack
    uint32 limit_threshold = 3; // 到量阈值
    uint32 limit_spend = 4;     // 当前统计值
    uint32 adver_bid_type = 5;  // 1:cpm; 2:cpc; 3:cpa
}

message Apps{
    repeated App apps = 1;
}

message App{
    string id = 1;        // ios：apple id，android：包名。 对应sigmob_web.sig_dsp_adver_campaign表中appid字段
    string icon_url = 2;  // 下载类app的icon（目前用在伴随广告）
    string title = 3;     // 广告标题（目前用在伴随广告）
    float score = 4;      // 下载类app的星际，小数，例如4.5表示4星半（目前用在伴随广告），score大于等于1才有效
    string desc = 5;      // 相关描述
}

message BatchCampaign{
    repeated Campaign campaigns=1;
}

//周期
message AdPeriod {
    int64 start_time = 1;
    int64 end_time = 2;
}

//广告计划(活动)
message Campaign {
    uint64 id = 1;                                      // 广告计划local id
    string campaign_code = 2;                           // 广告计划业务ID
    string name = 3;                                    // 广告计划名称
    string gid = 4;                                     // 用户组ID
    uint32 interaction_type = 5;                        // 广告交互类型。1=使用浏览器打开；2=下载应用
    string target_url = 6;
    uint32 track_active_type = 7;                       // 第三方激活监测平台。4：热云
    string track_active_url = 8;                        // 第三方点击监测地址
    uint32 adver_bid_type = 9;                          // 1:cpm; 2:cpc; 3:cpa
    uint32 adver_bid_price = 10;                        // 广告主出价价格。单位：cpm分
    uint32 bid_speed = 11;                              // 1: 匀速 2：快速
    uint32 day_budget = 12;                             // 日预算。单位：分
    string time_interval = 13;                          // 投放时段
    repeated AdPeriod period = 14;                      // 投放周期
    map<uint64,Creative> creatives = 15;                // 包含的创意信息
    uint32 state = 16;                                  // 广告开关。0:关闭 1:开启
    uint32 ad_type = 17;                                // 广告类型。1 奖励视频
    string adver_app_id = 18;                           // 下载类广告时，为app store id
    uint32 total_budget = 19;                           // 总预算。单位：分
    uint32 ad_state = 20;                               // 广告状态。20:计划未开始 21:投放中 22:计划到期 23:日预算到限 24:总预算到限 25:账户余额不足
    map<string, AdSlotIds> app_slot_ids = 21;           // 广告位定向包含(slot_id), {"0":["9999"]}表示不限
    string track_impress_url = 22;                      // 第三方曝光监测地址
    uint32 override_erpm = 23;                          // 人工设置出价，优先级最高。为0表示没设置。单位：分
    string deeplink_url = 24;                           //
    uint32 daily_impression = 25;                       // 日曝光上限
    uint32 serving_speed = 26;                          // 投放方式 1:正常投放 2:匀速投放
    map<string, AdSlotIds> app_slot_ids_except = 27;    // 广告位定向排除(slot_id), {"0":["9999"]}表示不限
    uint64 product_id = 28;                             // 全局产品id
    uint64 client_id = 29;                              // 客户Id
    App app = 30;                                       // 产品相关信息
    uint32 is_test = 31;                                // 是否测试广告计划
    int32 override_type = 32;                           // override类型，1:cpm; 2:cpc; 3:cpa
    uint64 ecpm = 33;                                   // 控量模块存储：计划真实ecpm值En。投放模块存储：计划下最大创意eCPM，Ecp=Max(Ep)
    bool support_oaid = 34;                             // 计划是否支持oaid
    bool support_gaid = 35;                             // 计划是否支持gaid
    bool support_http = 36;                             // 计划是否支持http
    uint64 prod_id = 37;                                // 产品id，对应sig_dsp_adver_campaign.prodid
    bool precheck = 38;                                 // 是否前审，true-已推送，false-未推送
    bool support_idfa = 39;                             // 计划是否支持idfa
    bool support_ip_ua = 40;                            // 计划是否支持ip+ua
    bool support_imei = 41;                             // 计划是否支持imei
    bool support_android_id = 42;                       // 计划是否支持androidId
    bool support_sk_ad_network = 43;                    // 计划是否支持apple SKAdNetwork
    uint64 package_size = 44;                           // 产品包体大小，单位:Byte
    bool closecard_ad = 45;                             // 是否关闭页推荐
    bool support_idfa_md5 = 46;                         // 计划是否支持idfa Md5
    bool support_imei_md5 = 47;                         // 计划是否支持imei Md5
    bool support_oaid_md5 = 48;                         // 计划是否支持oaid Md5
    bool support_gaid_md5 = 49;                         // 计划是否支持gaid Md5
    bool support_android_id_md5 = 50;                   // 计划是否支持androidId Md5
    bool support_caid = 51;                             // 计划是否支持caid
    uint32 rta_id = 52;                                 // rta service id
    bool is_muti_bid = 53;                              // 是否支持多出价
    string prod_version = 54;                           // 产品版本
    string prod_permission = 55;                        // 产品权限列表
    string prod_developer = 56;                         // 产品开发者名称
    string prod_privacy_url = 57;                       // 产品隐私协议地址
    uint32 pri_ind = 58;                                // 主要考核指标, 1-付费率，2-留存率，3-ROI，4-LTV
    uint32 pri_date = 59;                               // 主要考核时间选项, 1-首日，2-次日，3-7日，4-30日
    int64 pri_val = 60;                                 // 主要考核指标值
    bool support_native = 61;                           // 计划是否支持原生native流量
    repeated string target_type = 62;                   // 广告主行业分类
    bool support_rewarded = 63;                         // 计划是否支持激励视频流量
    uint32 merchandise_id = 64;                         // 电商关联商品id
    uint64 limit_prod_group_id = 65;                    // 控量产品组id
    bool only_huawei_chan_pkg = 66;                     // 仅支持华为渠道包流量
    string huawei_chan_pkg_id = 67;                     // 华为渠道包id
    uint32 rta_type = 68;                               // RTA 类型，1-普通网页 2-RTA促活 3-普通促活 4-在线支付
    uint32 day_dpback = 69;                             // 日dpback
}

message AdSlotIds{
    repeated string slot_id = 1;    // 广告位id
}

// 全局配置信息
message Session {
    uint64 score_param_b = 1;               // b参数，【已经弃用】
    double default_ctr = 2;                 // 系统全局配置的默认ctr【v2.3.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    double default_cvr = 3;                 // 系统全局配置的默认cvr【v2.3.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    int64 req_period = 4;                   // req计数清0周期。单位：秒。默认为10分钟，【已经弃用】
    uint32 random_bucket_ratio = 5;         // 随机投放流量比例。取值范围：[0-100]【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 optimize_bucket_ratio = 6;       // 正常投放流量比例。取值范围：[0-100]【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 new_creative_threshold = 7;      // 新Creative阈值【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint64 fatigue_times = 8;               // campaign层级疲劳值【v2.3.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    int64 fatigue_period = 9;               // 疲劳值清零周期。单位：天【v2.3.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint64 creative_fatigue_times = 10;     // 创意层级的疲劳值设置【v2.3.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 stat_level_one_latest_hours = 11;// 效果统计第一层计算规则：取最近n小时的效果，【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 stat_level_one_threshold = 12;   // 效果统计第一层计算规则阈值，比如大于1000次有效，【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 stat_level_two_latest_day = 13;  // 效果统计第二层计算规则：取最近n天，对应小时的累积效果，【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    uint32 stat_level_two_threshold = 14;   // 效果统计第二层计算规则阈值，比如大于1000次有效，【v1.7.0版本开始弃用，全局默认值与App级别配置存入AppSession中】
    double budget_threshold_cpm = 15;       // CPM 广告预算生效比例
    double budget_threshold_cpc = 16;       // CPC 广告预算生效比例
    double budget_threshold_cpa = 17;       // CPA 广告预算生效比例
}

message AppSessions {
    map<string, AppSession> app_session_map = 1;
}

// APP、AdType级别Session配置
message AppSession {
    string app_id = 1;                      //  应用ID
    uint32 ad_type = 2;                     //  广告类型。1 奖励视频，2 开屏广告，4 全屏视频
    uint32 random_bucket_ratio = 3;         //  随机投放流量比例。取值范围：[0-100]
    uint32 optimize_bucket_ratio = 4;       //  正常投放流量比例。取值范围：[0-100]
    uint32 new_creative_threshold_1 = 5;    //  新Creative 展示数阈值1
    uint32 new_creative_threshold_2 = 6;    //  新Creative 展示数阈值2
    double new_creative_threshold_ecpm = 7; //  新Creative eCPM阈值
    uint32 stat_level_one_latest_hours = 8; //  效果统计第一层计算规则：取最近n小时的效果
    uint32 stat_level_one_threshold = 9;    //  效果统计第一层计算规则阈值，比如大于1000次有效
    uint32 stat_level_two_latest_day = 10;  //  效果统计第二层计算规则：取最近n天，对应小时的累积效果
    uint32 stat_level_two_threshold = 11;   //  效果统计第二层计算规则阈值，比如大于1000次有效
    uint64 random_bucket_expect_time =12;   //  预期出桶时间(单位:min)
    uint64 random_ratio_max=13;             //  随机桶比例上限（0~100整数）
    uint64 random_ratio_min=14;             //  随机桶比例下限（0~100整数）
    uint32 is_auto_bucket=15;               //  是否自动分桶托管
    double default_ctr = 16;                //  系统全局配置的默认ctr
    double default_cvr = 17;                //  系统全局配置的默认ctr
    uint64 fatigue_times = 18;              //  campaign层级疲劳值【v2.3.1版本开始弃用】
    uint64 fatigue_period = 19;             //  疲劳值清零周期。单位：天【v2.3.1版本开始弃用】
    uint64 creative_fatigue_times = 20;     //  创意层级的疲劳值设置【v2.3.1版本开始弃用】
    FatigueArguments fatigue = 21;          //  疲劳期v2逻辑三层模型管理参数
}

message FatigueArguments{
    uint64 product_fatigue_times = 1;       // 全局产品层级的疲劳值设置
    uint64 material_fatigue_times = 2;      // 素材层级的疲劳值设置
    uint64 creative_fatigue_times = 3;      // 创意层级的疲劳值设置
    uint64 fatigue_period_days = 4;         // 疲劳期周期，单位：天
    uint64 creative_frequency_period = 5;   // 创意层级的频次周期，单位：小时
}

message AppSlotTargetFilter{
    map<string, AppSlotTargetCampIds> app_slot_target=1;           //app定向包含map, key: slot_id
    map<string, AppSlotTargetCampIds> app_slot_target_except=2;    //app定向排除map, key: slot_id
}

message AppSlotTargetCampIds {
    repeated uint64 camp_ids = 1;                       //【v2.5.2版本开始弃用】
    map<uint64, AppSlotTargetRule> campaign_rules = 2;  // key: camp_id
    bool is_effective = 3;                              // 当天是否生效
}

message AppSlotTargetRule {
    uint32 long_term_effective = 1; //长期有效 1:是 0:否
    int64 start_time = 2;           //开始时间戳
    int64 end_time = 3;             //结束时间戳
}

//广告创意
message Creative {
    uint64 id = 1;                          // 创意local id
    uint64 parent_id =2;                    // 所属的计划ID
    string creative_code=3;                 // 创意业务编码
    uint32 creative_type = 4;               // 创意类型：1=奖励视频广告的资源包形式(endcard为tgz包)，一般由video、endcard构成；2=纯静态图片广告，一般由单张image_src构成
    string video_url = 5;                   // 视频物料地址
    string endcard_url = 6;                 // Endcard地址
    uint32 state = 7;                       // 状态开关。0：关闭 1：开启
    double compute_ctr = 8;                 // 最终计算时的ctr
    double compute_cvr = 9;                 // 最终计算时的cvr
    DimRates dimRates = 10;                 // 不同维度的效果统计(v2.9.0弃用)
    uint64 req_accumulate = 11;             // req累积计数
    uint32 orientation =12;                 // 创意允许旋转方向：0: MaskAll、1:portrait、2:landspace
    uint32 ecpm = 13;                       // 当前创意的ecpm值。
    uint32 is_new = 14;                     // 是否为新创意。0:new 1:非new，【v1.8.0版本开始弃用】
    int64 imp_frequency_interval =15; 		// 限定的时间范围，比如24、48等。单位：小时。
    uint64 imp_frequency_times = 16;        // 创意疲劳值，在计划上设置的创意疲劳值；-1跟随全局设置 0:不限
    double selected_score = 17;             // 选定的score最高的创意的score值
    uint32 pctr_valid_check = 18;           // 是否使用预估ctr|cvr. 0：未查到预估数据；1：使用；2：过期；3：未过期，但无交集
    string image_url = 19;                  // 广告图片地址
    uint32 image_width = 20;                // 图片宽
    uint32 image_height = 21;               // 图片高
    uint32 image_type = 22;                 // 图片素材的格式编码, 0:unknown 1:jpeg,jpg 2:png 3:gif
    map<string, uint64> is_optimise = 23;   // 是否进入优化桶，0：随机桶，1：优化桶。key: "[app_id]_[ad_type]"
    uint64 abcWeight = 24;                  // 用于自动分桶比例调节创意曝光速度的weight
    uint64 material_id = 25;                // 创意素材id
    uint32 bid_type = 26;                   // 投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
    uint32 bid_price = 27;                  // 投放平台的出价价格
    uint64 vtime = 28;                      // 视频素材时长，单位：秒
    double old_ctr = 29;                    // ctr真实值，当compute_ctr被预估值覆盖时，记录原真实值
    double old_cvr = 30;                    // cvr真实值，当compute_cvr被预估值覆盖时，记录原真实值
    bool is_filter = 31;                    // 是否被过滤器过滤
    uint32 template_id = 32;                // endcard模板id
    uint32 flag = 33;                       // endcard新旧版标记 0:旧版资源包 1:新版Html Source
    string template_config = 34;            // endcard配置文件
    int64 imp_fatigue_times = 35;           // 创意疲劳值，在计划上设置的创意疲劳值；-1跟随全局设置 0:不限
    uint32 has_followline = 36;             // 开屏是否含有伴随条，1表示有伴随条，0表示无伴随条
    string followline = 37;                 // 开屏伴随条内容
    uint64 video_id = 38;                   // 视频素材ID
    uint64 endcard_id = 39;                 // 着陆页素材ID
    uint64 image_id = 40;                   // 广告图片ID
    uint32 apple_campaign_id = 41;          // 对应apple SKAdNetwork要求的campaignId (1~100)
    string preview_url = 42;                // endcard预览地址
    string slave_video_url = 43;            // 第二视频，用于单产品多视频模板
    string video_cover = 44;                // 主视频封面
    string slave_video_cover = 45;          // 第二视频封面，用于单产品多视频模板
    string video_mid_cover = 46;            // 主视频中间帧
    string slave_video_mid_cover = 47;      // 第二视频中间帧，用于单产品多视频模板
    int64 create_time = 48;                 // 创建时间
    uint32 video_width = 49;                // 主素材，视频宽
    uint32 video_height = 50;               // 主素材，视频高
    uint64 endcard_image_id = 51;           // endcard图片ID
    string endcard_image_url = 52;          // endcard图片url
    bool has_endcard = 53;                  // 是否存在endcard 0-无endcard 1-显示endcard
    string creative_title = 54;             // 创意title
    uint64 call_widget_id = 55;             // 号召挂件素材id
    string call_widget_url = 56;            // 号召挂件素材url
    uint64 atmosphere_widget_id = 57;       // 气氛挂件素材id
    string atmosphere_widget_url = 58;      // 气氛挂件素材url
}

// 图片素材
message Image{
    uint64 id = 1;                         // 图片id，目前对应sig_dsp_endcard_material.id
    string image_url = 2;                  // 广告图片地址
    uint32 image_width = 3;                // 图片宽
    uint32 image_height = 4;               // 图片高
    uint32 image_type = 5;                 // 图片素材的格式编码, 0:unknown 1:jpeg,jpg 2:png 3:gif
}

// 文本素材
message CreativeText{
    string endcard_btn_text = 1;    // endcard按钮文案
    string endcard_title = 2;       // endcard标题
    string endcard_desc = 3;        // endcard描述
}

// 维度广告效果统计
message DimRates {
    double ctr = 1; //弃用
    double cvr = 2; //弃用
    map<string, double> channel_ctr = 3; // 在指定媒体app上的历史点击率, key: "[app_id]_[ad_type]"
    map<string, double> channel_cvr = 4; // 在指定媒体app上的历史转化率, key: "[app_id]_[ad_type]"
}

//接口返回 定义
message Mesg {
    bool success = 1; //返回成功标识
    string code = 2;  //返回状态码(暂无)
    string desc = 3;  //错误详细信息
}

//广告变更信息。作为发布订阅的数据格式
message AdUpdate {
    AdIndexs ad_index = 1;                              // 索引信息
    Creative creative = 2;                              // 创意信息【v3.0.0版本开始弃用】
    Session session = 3;                                // 全局配置信息
    Campaign ad_stat = 4;                               // 历史效果统计信息【v3.0.0版本开始弃用,改为使用dim_rates】
    WhiteList white_list = 5;                           // 需要新增/更改的白名单信息
    AppSession app_session = 6;                         // App配置信息
    repeated AutoBucket autoBucket = 7;                 // 自动分桶
    uniform.UniformDelivery ud = 8;                     // 匀速投放
    AutoBucketCreativeWeight abcWeight = 9;             // 自动分桶比例各创意权重
    AppSlots app_slots = 10;                            // 媒体侧app过滤信息变更
    Apps apps = 11;                                     // 同步APP标题、描述、星星、icon信息
    bool sync_abtest = 12;                              // 同步abtest框架【v3.0.0版本开始弃用】
    bool sync_endcard_template = 13;                    // 同步endcard template src【v3.0.0版本开始弃用】
    bool sync_app_slot_shield_ad = 14;                  // 同步app/slot屏蔽广告【v3.0.0版本开始弃用】
    AdControlInfo adControl = 15;                       // 广告控量信息变更或状态变更
    map<uint64, DimRates> dim_rates = 16;               // 历史效果统计信息，key:creative_id
    repeated TemplateInfo templates = 17;               // 同步endcard template src, key:template_Id, value: endcard template
    AbTestConfig app_abtest_conf = 18;                  // key: appid   value:该appid下流量进去ABTest框架的权重weight，千分比（0~1000）
    map<string, bytes> app_slot_shield = 19;            // key:appId/slotId  value: bitmap Marshal bytes
    AppSlotTargetFilter app_slot_target = 20;           // appId/slotId 定向
    CreativeUpdate creativeUpdate = 21;                 // 创意更新消息
    bool sync_app_state = 22;                           // 同步app新/旧状态信息
    AppControlUpdate app_controls =23;                  // 同步app新/旧媒体控量条件
    LimitApp limit_app = 24;                            // app控量到限
    string open_app = 25;                               // 放开app控量到限
    repeated ProductControl prod_controls = 26;         // 产品级控量条件修改
    LimitProduct limit_prod = 27;                       // 产品控量到限
    repeated UpdateBatchCampaignUrl camp_urls = 28;     // 批量更新计划url
    UpdateBatchCampaignDateReq update_camp_date = 29;   // 批量更新计划日期
    UpdateBatchCampaignTimeReq update_camp_time = 30;   // 批量更新计划时间
    repeated CampaignCheckState check_state = 31;       // 计划创意审核状态
    CreativeOutBucket outBucket = 32;                   // 创意出桶通知
    ProductPackageSize package_info = 33;               // 产品包体大小变更
    UpdateColdStartInfo cold_start_info = 34;           // 冷启动信息变更消息
    repeated UpdateApiMarketInfo api_market_info = 35;  // api渠道应用市场信息
    LowIrSlotsReq low_ir_slots_req = 36;                // 极低IR广告位列表
    SlotEcpmWeightBase slot_ecpm_wbase = 37;            // 广告位开放ecpm模式调节参数wbase修改
    string open_app_prod = 38;                          // 放开app+prod控量到限
    repeated RtaLabelTarget rta_label_target = 39;      // RTA 标签定向mapping
    Merchandise merchandise = 40;                       // 二类电商商品信息
    LimitProdGroup prod_group = 41;                     // 控量产品组
    SlotEcpmWeightBase slot_ecpm_wp = 42;               // 广告位直投实时预估误差控制调节参数wp修改
    SlotEcpmWeightBase global_ecpm_wp = 43;             // global直投实时预估误差控制调节参数wp修改
}

message RtaLabelTarget{
    uint32 rta_id = 1;
    repeated string labels = 2;
}

message SlotEcpmWeightBase{
    string slot_id = 1;  // 广告位id
    double wbase = 2;    // 广告位开放ecpm模式调节参数wbase
}

message UpdateApiMarketInfo {
    uint64 campaign_id = 1;     // 计划id
    uint64 channel_id = 2;      // 渠道id
    string market_id = 3;       // 渠道对应appid
}

message TemplateInfo{
    uint32 id = 1;              // 模板id
    string src = 2;             // endcard template source code
    bool auto_click = 3;        // 是否支持自动点击
    string js_code_url = 4;     // js样式CDN地址
    string css_code_url = 5;    // css样式CDN地址
    uint32 show = 6;            // 展示概率百分比, 0~100
    string template_url = 7;    // 模板CDN地址
    uint32 ad_type = 8;         // 支持的媒体广告类型，0-不限
    uint32 orientation = 9;     // 1:横版、2:竖版、3:横竖兼容（插屏暂只有前两种）
    uint32 style = 10;          // 0:全屏、1:半屏
}

message UpdateColdStartInfo{
    map<string, uint64> params = 1;
    UpdateColdStartWeight weight = 2;
    UpdateColdStartState state =3;
}

message UpdateColdStartWeight{
    string weight_key = 1;
    double weight_value = 2;
}

message UpdateColdStartState{
    string state_key = 1;
    uint64 state_value = 2;
}

message UpdateBatchCampaignUrl{
    uint64 id = 1;                                      // campaign id
    bool support_oaid = 2;                              // 是否支持oaid
    bool support_gaid = 3;                              // 是否支持gaid
    bool support_http = 4;                              // 是否支持http
    string target_url = 5;                              // sig_dsp_adver_campaign表 targeturl
    string track_url = 6;                               // sig_dsp_adver_campaign表 trackurl
    string track_impress_url = 7;                       // sig_dsp_adver_campaign表 track_impress_url
    bool support_imei = 8;                              // 是否支持imei
    bool support_ip_ua = 9;                             // 是否支持ip+ua
    bool support_idfa = 10;                             // 是否支持idfa
    bool support_android_id = 11;                       // 是否支持android Id
    bool support_idfa_md5 = 12;                         // 计划是否支持idfa Md5
    bool support_imei_md5 = 13;                         // 计划是否支持imei Md5
    bool support_oaid_md5 = 14;                         // 计划是否支持oaid Md5
    bool support_gaid_md5 = 15;                         // 计划是否支持gaid Md5
    bool support_android_id_md5 = 16;                   // 计划是否支持androidId Md5
    bool support_caid = 17;                             // 计划是否支持caid
}

message LimitProduct{
    uint64 prod_id = 1;  // 产品id
    uint32 state = 2;    // 1-日预算到量；2-日激活到量；3-总预算到量
}

message LimitApp{
    uint64 appid = 1;    // app id
    uint32 ad_type = 2;  // ad type
    uint64 prod_id = 3;  // prod id
}

message AppControlUpdate{
    repeated uint64 appids = 1;
    map<string, AppControlInfo> rules = 2; // 同步app新/旧媒体控量条件，key: appid_adtype
}

message CreativeUpdate{
    uint64 creative_id = 1;
    Campaign camp = 2;
    repeated Image images = 3;
    CreativeText text = 4;
}

// 索引信息
message AdIndexs {
    uint64 ad_local_id = 1;                     // campaign系统id
    repeated string old_index_term = 2;         // 更新之前，广告包含的索引项
    repeated string new_index_term = 3;         // 更新后，广告包含的索引项
    repeated string appids_slotids = 4;         // app/slot屏蔽该计划列表
    Campaign camp = 5;                          // 计划
    repeated string label_codes = 6;            // app标签
    repeated string similar_pkgs = 7;           // 相似app
    repeated string strategy_keys = 8;          // app策略定向
    repeated string brand_codes = 9;            // 品牌定向
    map<uint64, Images> images = 10;            // key: 创意id，value: 图片素材列表
    RtaInfo rta = 11;                           // RTA相关信息
    map<uint64, CreativeText> texts = 12;       // key: 创意id，value: 图片素材列表
    map<string, uint32> bid_price_weights = 13; // 多出价权重，key: [campId]_[slotId]  value: weight
    repeated uint32 levels = 14;                // 计划分级定向
    map<uint32, float> gender_target = 15;      // 性别定向
    uint32 flow_target = 16;                    // 流量类型定向，0-SDK和API 1-SDK 2-API
    uint32 slot_include_or_except = 17;         // 投放侧定向包含/排除类型，0-不限 1-定向包含 2-定向排除
    repeated string slot_list = 18;             // 投放侧定向包含/排除广告位列表
    PackageTarget pkg_target = 19;              // 计划媒体包名定向
}

message RtaInfo{
    uint32 rta_id = 1;                          // rta id
    map<string, uint32> rta_label_price = 2;    // key: rta label, value: price
    map<string, string> params = 3;             // 自定义参数
}

message Images{
    repeated Image images = 1;   // 图片素材列表
}

// 获取creative效果统计的请求
message StatRequest {
    repeated uint64 creative_ids = 1;           // 创意唯一标识
    Session session = 2;                        // 全局配置信息，获取cvr、ctr默认值
    map<uint64, DimRates> dim_rates = 3;        // 历史效果统计信息， key:creative_id
    map<string, AppSession> app_session = 4;    // creative对应appid的配置信息
}

//白名单信息
message WhiteList{
    string uid = 1;               // 设备唯一标示信息 ios: idfa, android: uid 生成规则同 sigmob_rtb.proto DeviceId.uid
    uint64 camp_id = 2;           // 计划id
    uint64 creative_id = 3;       // 创意id
    string adslot_id = 4;         // 广告位id
    uint32 adslot_type = 5;       // 广告类型
}

message WhiteLists{
    map<string, string> white_list_map = 1;
}

message AutoBucket{
    string key=1;               //对应比例的key, dsp_random_ratio_%s_%d  appid,adtype
    uint64 randomRatio = 2;     //随机桶比例
}

message AutoBucketCreativeWeight{
    string creativeWeightKey=1; //对应创意weight的key, dsp_auto_bucket_creative_weight_%s_%d_%s  appid,adtype
    uint64 creativeWeight=2;    //对应创意的weight值，0~100
}

message AppSlots{
    repeated AppSlot app_slots=1;
}

message AppSlot{
    string appid=1;
    string slot_id=2;
    uint32 ad_type=3;
}

message DmpPackageCodes{
    repeated string codes = 1;
}

// 广告计划控量信息
message AdControlInfo {
    uint64 id = 1;                      // 计划id
    uint32 daily_impression = 2;        // 日曝光上限
    uint32 daily_budget = 3;            // 日预算。单位：分
    uint32 total_budget = 4;            // 总预算。单位：分
    uint32 state = 5;                   // 广告开关。0:关闭 1:开启
    uint32 ad_state = 6;                // 广告状态。20:计划未开始 21:投放中 22:计划到期 23:日预算到限 24:总预算到限 25:账户余额不足
    repeated string new_index_term = 7; // 更新后，广告包含的索引项
    uint32 daily_dpback = 8;            // 日dpback
}

// 媒体控量信息
message AppControlInfo {
    uint64 appid = 1;               // app id
    uint32 ad_type = 2;             // ad type
    uint32 daily_imp_num = 3;       // 日曝光上限
    uint32 daily_budget = 4;        // 日预算。单位：分
    uint32 prod_daily_imp_num = 5;  // 产品日曝光上限
    uint32 prod_daily_budget = 6;   // 产品日预算。单位：分
}

message AppState{
    uint64 appid = 1;               // app id
    bool is_new = 2;                // true:新媒体，false:其他所有状态的媒体都算老媒体
    int64 start_time = 3;           // 新媒体开始时间戳，单位：秒
}

// app daily imp spend
message AppDailyData{
    uint64 appid = 1;               // app id
    uint32 ad_type = 2;             // ad type
    uint32 daily_impression = 3;    // 日曝光
    double daily_income = 4;        // 日消耗，单位：分
    uint64 prod_id = 5;             // 产品id，对应sig_dsp_adver_campaign.prodid
}

// app daily imp spend api request
message AppDailyDataReq{
    repeated uint64 appids = 1;
    repeated uint32 adtypes=2;
}

// app daily imp spend api response
message AppDailyDataResp{
    repeated AppDailyData app = 1;
}

message UpdateAppControlsReq {
    repeated uint64 appids = 1;    // app id
}

message ProductControlRule{
    uint64 prod_id = 1;             // 产品id
    uint32 daily_budget = 2;        // 日预算。单位：分
    uint32 total_budget = 3;        // 总预算。单位：分
    uint32 daily_active = 4;        // 日激活
    uint32 daily_impression = 5;    // 设备日曝光频率
    uint32 daily_click = 6;         // 设备日点击频率
    uint32 daily_dpback = 7;        // 日deeplink callback
}

message ProdctTotalIncome{
    uint64 prod_id = 1;      // 产品id
    uint64 total_income = 2; // 产品总收入
    string data_date = 3;    // 产品数据日期，yyyy-MM-dd
}

message PlatformMsg{
    int64 time_ms = 1;  //消息时间戳，单位：ms
    LimitProduct limit_prod = 2;
}

message Rta {
    bool matched = 1;               // 是否匹配
    uint32 rta_id = 2;              // rta service id
    string imei_md5 = 3;            // imei md5
    string oaid_md5 = 4;            // oaid md5
    string idfa_md5 = 5;            // idfa md5
    repeated string label_code = 6; // rta label code
    string imei = 7;                // imei
    string oaid = 8;                // oaid
    string idfa = 9;                // idfa
    string android_id = 10;         // android id
    string android_id_md5 = 11;     // android id md5
    uint64 prod_id = 12;            // prod id 产品id
}

message PackageTarget{
    uint64 campaign_id = 1;                 // 计划id
    uint32 target_type = 2;                 // 包含or排除，1-包含，2-排除
    map<string, bool> package_names = 3;    // 媒体包名
}

message Merchandise{
    uint32 merchandise_id = 1;      // 商品id
    uint32 order_limit = 2;         // 单笔订单最大购买数
    uint64 unit_price = 3;          // 商品单价，单位：分
    string name = 4;                // 商品名称
    string detail = 5;              // 商品描述
    string weixin_mchid = 6;        // 微信商户号
    string checksum = 7;            // 校验hash
}

message LimitProdGroup{
    uint64 group_id = 1;                    // 控量产品组id
    uint32 limit_type = 2;                  // 控量类型，1-曝光频控 2-点击频控
    repeated uint64 prod_list = 3;          // 产品列表
    repeated uint64 old_camp_id_list = 4;   // 原控量产品组计划id列表
    repeated uint64 new_camp_id_list = 5;   // 新控量产品组计划id列表
}