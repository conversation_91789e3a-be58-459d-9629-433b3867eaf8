syntax = "proto2";
option java_package = "com.sigmob.ad.adx.dsp.model.eastday";

message AdRequest {
    required string requestid = 1; // 必填。自定义的请求id，长度为32位，保证其请求的唯一性。
    required string apiversion = 2; // 必填。此API的版本。
    required string sourcetype = 3; //流量类型：app或者wap
    optional User user = 4; // 可选。用户信息，用于人群定向。
    required App app = 5; // 必填。移动app的信息。
    required Device device = 6; // 必填。移动设备的信息。
    required string useragent = 7; // 必填。User-Agent
    required string ip = 8; // 必填。设备的ip，用于定位，地域定向
    repeated AdSlot adslots = 9; // 必填，至少一个。广告位的信息。
    optional string adxname = 10; // 判断哪家adx
    optional string adxpassword = 11; // 用来验证adx
    optional uint32 timeout = 12; // 超时时间

    message App {
        required string appid = 1; // 必填。app应用id，由DSP分配。
        optional string name = 2; // 可选。app应用名。
        optional string packagename = 3; // 可选。 包名
        optional string version = 4; // 可选。 app应用的版本。
        optional Geo geo = 5; // 可选。设备的地理位置信息
        optional string accid = 6; // 可选。App注册ID
        optional bool ispaidapp = 7; // 可选。 应用是否付费
    }

    message Device {
        required string deviceid = 1; // 必填。设备的唯一标识，安卓为IMEI, IOS为IDFA，TV为IDFA。
        required string imei = 2; //必填。设备IMEI（明文）
        optional string imeimd5 = 3; //设备IMEI的MD5值(32位⼩写)
        optional string aaid = 4; //可选。应用匿名设备标识符（AAID）是指第三方应用获取的匿名设备标识
        optional string oaid = 5; //可选。匿名设备标识符（OAID）是可以来链接所有应用数据的标识符
        optional string uuid = 6; //可选。安卓⼿机系统⽣成的设备ID
        optional string openudid = 7; //可选。IOS 软件⽣成替代UDID的标识
        optional string ssid = 8; //可选。⽆线⽹SSID名称，如获取不到可传空
        optional string wifimac = 9; //可选。WIFI路由器MAC地址，如获取不到可传空
        optional string phonename = 10; //可选。⼿机名称
        optional string dsid = 11; //可选。苹果账号（dsid）
        optional string powerontime = 12; //可选。打开⼿机到请求⼴告的时间⻓度
        optional string imsi = 13; //可选。IMSI（SIM卡串号）
        optional string romversion = 14; //可选。⼿机ROM的版本，如获取不到可传空
        optional string syscompilingtime = 15; //可选。系统编译时间，如获取不到可传空
        required DeviceType type = 16; //必填。设备类型：Unknown=0；Phone/⼿机=1；Tablet/平板=2；
        required string os = 17; //必填。操作系统，如：Android，IOS
        required string osversion = 18; //必填。操作系统版本，如：7.0
        required string brand = 19; //必填。设备品牌，如：HUAWEI，小米,OPPO等
        required string model = 20; //必填。设备型号，如:iPhone11,xiaomi9
        optional string language = 21; //必填。设备设置的语⾔：chinese、englist、other
        required ConnectionType network = 22; //必填。网络连接类型
        required string mac = 23; //必填。设备的网卡mac地址
        required uint32 screenwidth = 24; //必填。设备屏宽
        required uint32 screenheight = 25; //必填。设备屏⾼
        optional Orientation orientation = 26; //必填。屏幕⽅向
        required CarrierType operatortype = 27; //必填。运营商类型。

        // 设备类型
        enum DeviceType {
            DEVICE_UNKNOWN = 0;
            PHONE = 1; // 手机。
            TABLET = 2; // 平板。
        }

        // 网络类型
        enum ConnectionType {
            CONNECTQNJJNKNOWN = 0;
            CELL_UNKNOWN = 1;
            CELL_2G = 2;
            CELL_3G = 3;
            CELL_4G = 4;
            CELL_5G = 5;
            WIFKWi_Fi = 100;
            ETHERNET = 101;
            NEW_TYPE = 999;
        }
        //设备屏幕方向
        enum Orientation {
            UNKNOWN = 0;
            VERTICAL = 1; //垂直
            HORIZONTAL = 2; //水平
        }
        // 运营商类型
        enum CarrierType {
            UNKNOWN_OPERATOR = 0;
            CHINA_MOBILE = 1;
            CHINA_TELECOM = 2;
            CHINA_UNI8M = 3;
            OTHER_OP2ATOR = 99;
        }
    }

    message Geo {
        required float lat = 1; //必填。纬度
        required float lng = 2; //必填。经度
        required string city = 3; //必填。城市，中文即可(utf-8编码)
        required string province = 4; //必填。省份，中文即可(utf-8编码)
        optional string district = 5; //可选。区县，中文即可(utf-8编码)
    }

    message User {
        optional Gender gender = 1; // 可选。用户的性别。
        optional uint32 age = 2; // 可选。用户的年龄。
        optional string keywords = 3; // 可选。用户画像的关键词列表，以逗号分隔。

        enum Gender {
            UNKNOWN = -1;
            MALE = 1;
            FEMALE = 0;
        }

    }

    message AdSlot {
        required string id = 1; // 必填。广告位id。
        required AdType adtype = 2; // 必填。广告类型。
        required Position pos = 3; // 必填。广告展现位置。
        optional uint64 minimumcpm = 4; // 可选。最低的cpm出价, 单位为分/cpm。
        required uint32 adcount = 5; // 必填，单次请求的广告个数
        optional bool isoriginad = 6; // 可选，是否是原生广告
        required bool isdplink = 7; // 可选。是否支持deeplink
        optional uint32 videomin = 8; // 可选。期望视频的最⼩⻓度（秒）
        optional uint32 videomax = 9; // 可选。期望视频的最⼤⻓度（秒）
        required uint32 slotwidth = 10; // 可选。宽度，单位像素
        required uint32 slotheight = 11; // 可选。⾼度，单位像素
        repeated InteractionType interactiontype = 12; // 必填，至少一个。创意交互类型。

        // 广告的类型。
        enum AdType {
            BIG_IMG = 1; // 大图。
            SMALL_IMG = 2; // 小图。
            GROUP_IMG = 3; // 组图。
            OPEN_SCREEN = 4; // 开屏。
            INCENTIVE_VIDEO = 18; // 激励视频。
        }
        // 广告出现的位置。
        enum Position {
            TOP = 1; // 顶部。
            BOTTOM = 2; // 底部。
            FLOW = 3; // 信息流内。
            MIDDLE = 4; // 中部(插屏广告专用)。
            FULLSCREEN = 5; // 全屏。
        }

        // 创意交互类型。
        enum InteractionType {
            COMMON_LINK = 1; // 普通网页跳转。
            DEEPLINK = 2; // 应用内跳转。
            APP_DOWNLOAD = 3; // 应用下载。
        }

    }
}

message AdResponse {
    required string requestid = 1; // 必填。BidRequest中所带的request_id。
    repeated Ad ads = 2; // 可选。当竞价时必填，竞价广告列表，与adslots对应。
    optional uint32 processingtimems = 3; // 可选。从收到请求到返回响应所用的时间。
    optional uint32 statuscode = 4; // 可选。请求时的状态码。
    optional uint32 expirationtime = 5; // 可选。广告过期时间戳，单位为秒，针对预加载广告。
    optional string passback = 6; //可选。竞价成功后回传参数

    message Ad {
        required string advid = 1; // 必填。创意id。
        required MaterialMeta creative = 2; // 必填。素材。
        required double price = 3; // 出价

        message MaterialMeta {
            required AdType adstyle = 1; // 必填。该广告的创意类型。
            optional string source = 2;// 可选。广告来源，如广告公司名
            repeated Image imagesinfo = 3; // 图片素材物料：广告素材、大图、组图、背景图等
            optional Video videoinfo = 4; // 可选。视频
            optional string cornermark = 5;// 可选。DSP广告标示图地址，图片中包含文字和logo
            repeated Tracking trackingevent = 6;// 必填。打点上报url
            optional string deeplinkurl = 7; // 可选。⽤于调起预算的直达链接
            optional string targeturl = 8; // 可选。创意的落地⻚url
            optional string downloadurl = 9; // 可选。应⽤下载url
            required string title = 10; // 必填。⼴告标题
            required string description = 11; // 必填。⼴告副标题
            optional string landpageurl = 12; // 非必填。广告落地页链接
            optional string appname = 13; // 可选。针对应⽤下载类⼴告
            optional string packagename = 14; // 可选。应⽤下载包名
            repeated string winnoticeurl = 15; // 可选。获胜通知的url列表。
            optional string ext = 16; // 可选。扩展字段
            optional string phonenum = 17; // 可选。电话拨打⼴告，号码
            optional string buttontext = 18; // 可选。附加创意按钮上的名称，如⽴即下载、⻢上拨打、现在申请等
            optional string logourl = 20; // 可选。广告logo的url
            optional int32 productsize = 21; // 可选。Android下载类⼴告安装包⼤⼩
            optional string productmd5 = 22; // 可选。Android下载类⼴告安装包md5
            optional string creativeid = 23; // 可选。⼴告物料的唯⼀标识
            optional string channel = 24; // 可选。分包送审的渠道标识
            required InteractionType interactiontype = 25; // 必填。创意交互类型

            // 广告的类型。
            enum AdType {
                BIG_IMG = 1; // 大图。
                SMALL_IMG = 2; // 小图。
                GROUP_IMG = 3; // 组图。
                OPEN_SCREEN = 4; // 开屏。
                INCENTIVE_VIDEO = 18; // 激励视频。
            }

            // 图片素材信息。
            message Image {
                required string url = 1;// 必填。图⽚地址
                required uint32 width = 2;// 必填。图片宽
                required uint32 height = 3;// 必填。图片⾼
                required uint32 size = 4;// 必填。图片⼤⼩，单位byte
            }

            message Tracking {
                optional string event = 1;// 可选。事件类型
                optional string url = 2;// 可选。打点上报URL
            }

            // 视频
            message Video {
                required string url = 1;// 必填。视频地址
                required float time = 2;// 必填。视频时⻓，单位秒
                required uint32 size = 3;// 必填。视频⼤⼩，单位byte
                required string resolution = 4;// 必填。视频分辨率 如1280x720
                required string coverurl = 5;// 必填。视频封面图
                required uint32 coverwidth = 6;// 必填。封面宽，单位像素
                required uint32 coverheight = 7;// 必填。封面高，单位像素
                optional uint32 comments = 8;// 可选。激励视频广告评论数
                optional double rating = 9;// 可选。激励视频广告评分
            }

            // 创意交互类型。
            enum InteractionType {
                COMMON_LINK = 1; // 普通网页跳转。
                DEEPLINK = 2; // 应用内跳转。
                APP_DOWNLOAD = 3; // 应用下载。
            }
        }
    }
}
