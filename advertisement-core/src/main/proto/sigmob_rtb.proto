/**
@description sigmob程序化交易竞价协议
<AUTHOR>
//modifyby kai.huang
    add click_type                  //点击类型
    add version.version_str         //字符串版本号
    add device.mem_size             //硬件内存大小
    //eo kai.huang

2019-02-26 modify by kai.huang
	add WebEventHandle
	add request_scene_type
	add ad_is_expired
2019-04-29
    add request creative_type
@版本: 2.7.1
**/

syntax = "proto3";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.sigdsp.pb";
option go_package = "sigmob.com/common/entry/sigmob";
option objc_class_prefix = "Sigmob";
// 内部竞价请求
message BidRequest {
  string request_id = 1;      //内部生成；请求唯一标识，[a-zA-Z0-9]{32}
  Version api_version = 2;    //选填, API版本【废弃】
  App app = 3;                //必填！应用信息
  Device device = 4;          //必填！设备信息
  Network network = 5;        // 必填！网络环境信息
  repeated AdSlot slots = 6;  // 必填！广告位信息。 目前只支持单广告位
  int64 req_timestamp = 7;    // 广告请求时间，unix时间戳
  uint32 request_scene_type = 8;      // 发起请求时场景，1）前后台切换 2）视频播放中自动加载请求 3）初始化请求 4）其他被动请求
  bool ad_is_expired = 9;             //发起请求时已缓存的广告是否过期了
  Privacy privacy = 10;               //用户隐私
  bool disable_mediation = 11;    // false: 使用的聚合；true: 单接
  map<string, string> options = 15;    // s-s hb callback扩展字段
  HeaderBidding header_bidding = 16;   // header bidding信息
  User user = 17;                      // 用户信息
  repeated HeaderBidding hbs = 18;     // 第三方adn header bidding信息
  map<string, string> ext_options = 19;// 保留扩展字段 toBid使用的key:user_source、channel、sub_channel
  Version sdk_version = 20;            // sdk版本
  WXProgramReq wx_program_req = 21;
  StrategyReq strategy_req = 22;       // 策略请求
  bool disable_install_package = 23;   // 应用是否禁止安装行为，默认false
  bool widget_is_nonsupport = 24;      // iOS是否不支持互动组件
  bool so_enc = 25;                    // 开启so加密
  string origin_vid = 26;              // 过期在请求原始广告曝光id
  string origin_price = 27;            // 原始广告结算价格
}

message Version {
  uint32 major = 1;  // 主版本号, 必填！
  uint32 minor = 2;  // 副版本号, 必填！
  uint32 micro = 3;  // 子版本号, 必填！
  string version_str = 4;   //字符串表示的版本号,在客户端无法解析出三段式数字版本号信息时，用此字段表示
  uint32 build_version = 5;       // build版本号, 鸿蒙必填！
  string release_type = 6;        // 系统的发布类型，取值为：Canary2/Beta3/Release
  string version_name = 7;        // 系统版本名称，版本格式OpenHarmony-x.x.x.x(ReleaseType),x为数值
}

message App {
  string app_id = 1;          // 应用ID
  Version app_version = 2;    // 必填！应用版本
  string app_package = 3;     // 必填！应用包名。IOS设备为bundle id
  uint32 orientation = 4;      // app方向：0: MaskAll、1:portrait、2:landspace
  string name = 5;            // app名称
  string idfv = 6;            // Vendor标示符，适用于对内：例如分析用户在应用内的行为等。
  string channel_id = 7;      // 应用商店的渠道标识。字典同yomob的渠道字典，当前也只有yomob需要
  string product_id = 8;      // 媒体在投放系统中的apple id（iOS） 或 package name（Android）
  bool support_http = 9;      // app是否支持http
  repeated string ad_network_ids = 10;    //媒体在info.plist中配置的SKAdNetworkId列表
  string support_sk_version = 11;         // 如果为空，则不支持sk，填写当前支持的最大版本目前取值：1.0/2.0
  repeated uint32 sdk_ext_cap = 12;       //当前SDK版本所支持的扩展的功能1: overlay(仅iOS)；2:storekit(仅iOS)
  uint32 media_level = 13;    // 媒体分级
  string idfv_md5 = 14;       // Vendor标示符md5值
  int64 install_time = 15;    // 媒体应用安装时间，单位为秒
  string origin_sdk_orientation = 16; //sdk原始请求方向（服务端打点用）
  Version mraid1_version = 17;            // 必填！mraid1.x协议版本号
  Version mraid2_version = 18;            // 必填！mraid2.x协议版本号
  Permission permission = 19;             // 上报app配置的权限信息
  string ohos_app_id = 20;                // 鸿蒙的应用appId信息
  string app_sha256 = 21;                 // app 证书的 sha256
  repeated string label = 22;             // 应用标签
  string apk_sign = 23;                   // 应用包名的SHA1值
}

message Permission {
  bool accelerometer = 1;                 // 加速度传感器, 影响摇一摇
  bool store_persistent_data = 2;         // asset是否可以跨app
}

message Geo {
  float lat = 1;             // 纬度
  float lon = 2;             // 经度
  string language = 3;       // 语言（大写）
  string timeZone = 4;       // 时区
  uint64 city_code = 5;      // 地域编码
  string country = 6;        // 国家
  string region_code = 7;    // ip库中识别出来的编码，可能是市级编码、省级编码
  string secondsFromGMT = 8; // 当前时区距离隔离的秒数
  double accuracy = 9;       // 经纬度半径，单位：米
  string city = 10;          // 城市
  string province = 11;      // 省
  string district = 12;      // 区
}

message Device {
  uint32 device_type = 1;             // 设备类型。0:unknown、1:iPhone、2:iPad、3:iPod、4:Android Phone、5:Android Pad
  uint32 os_type = 2;                 // 操作系统类型. 1=IOS；2=Android
  Version os_version = 3;             // 必填！操作系统版本
  string vendor = 4;                  // 必填！设备厂商名称，中文需要UTF-8编码
  string model = 5;                   // 必填！设备型号，中文需要UTF-8编码
  DeviceId did = 6;                   // 必填！唯一设备标识，必需按要求填写
  Size screen_size = 7;               // 必填！设备屏幕宽高
  Geo geo = 8;                        // 地理信息
  uint32 dpi = 9;                     // 屏幕密度
  bool is_root = 10;                  // 是否越狱（true：越狱）
  uint64 disk_size = 11;              // 磁盘大小（单位Byte）【已4废弃】
  uint32 battery_state = 13;          // 电池充电的状态（0=UnKnow、1=Unplugged、2=Charging、3=Full）
  float battery_level = 14;           // 电池电量百分比
  bool battery_save_enabled = 15;     // 是否开启低电量模式
  string device_name = 16;            // 设备名称
  int64 start_timestamp = 17;         // 设备启动时间,unix时间戳 单位：ios:us，android: ns(从1970开始的时间戳)
  uint32 api_level = 18;              // Android API level (harmonyOs)
  uint64 mem_size = 19;        // 系统内存大小，安卓必填（单位Byte）
  uint64 total_disk_size = 20;          // 手机磁盘总大小（单位Byte）
  uint64 free_disk_size = 21;           // 手机磁盘剩余大小（单位Byte）
  uint64 sd_total_disk_size = 22;       // 设备SD磁盘总大小（单位Byte）
  uint64 sd_free_disk_size = 23;        // 设备SD磁盘剩余大小（单位Byte）
  Size resolution = 24;                 // 设备分辨率（单位px）
  string system_update_time = 25;     // 系统更新的时间
  string internal_name = 26;          // 手机mode编码
  string boot_mark = 27;              // 手机重启时间
  string update_mark = 28;            // 手机系统更新时间
  string fb_time = 29;                // 仅ios填充。设备初始化时间（拼多多版本）
  string file_mount_id = 30;          // ios caid生成参数 原mnt_id
  string device_name_md5 = 31;        // md5加密设备名称
  string rom_name = 32;               // 厂商定制化系统ROM名称(MIUI,EMUI等)
  Version rom_version = 33;           // 厂商定制化系统系统ROM版本号（非Android 版本号）
  int32  market_version = 34;         // 厂商应用市场market 版本号
  int32  hms_version = 35;            // 华为 HMS Core 版本号
  string wifi_mac = 36;               // wifi_mac
  string power_on_time = 37;          // 打开手机到请求广告的时间长度
  string sys_compiling_time = 38;     // 系统编译时间，手机ROM的编译时间，如获取不到可传空
}

// 唯一用户标识，优先使用明文，必需按要求填写，具体填写指导详见接口说明文档
message DeviceId {
  string idfa = 1;                // [必填]iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}
  string udid = 2;                // 内部生成
  string user_id = 3;             // 用户id
  string imei = 4;                // [必填]设备IMEI，IOS设备填空
  string android_id = 5;          // [必填]android设备的android_id，ios设备填空
  string android_uuid = 6;        // Android系统为开发者提供的用于标识手机设备的串号,会根据不同的手机设备返回IMEI，MEID或者ESN码
  string imsi = 7;                // Sim Serial Number
  string gaid = 8;                // 在Android设备上找到Google advertising ID (GAID)
  string uid = 9;                 // 用户在sigmob的唯一标识。
  string brand = 10;              // 手机品牌名称，安卓专属
  string imei1 = 11;
  string imei2 = 12;
  string oaid = 13;               // msa 联盟 oaid 匿名设备标识符(Open Anonymous Device Identifier)最长64位
  string vaid = 14;               // msa 联盟 vaid 开发者匿名设备标识符(Vender Anonymous Device Identifier)最长64位
  string aaid = 15;               // msa 联盟 aaid 应用匿名设备标识符(Application Anonymous Device Identifier)最长64位
  string msa_udid = 16;           // msa 联盟 udid
  string idfa_md5 = 17;           // iOS设备的IDFA MD5值
  string imei_md5 = 18;           // Android设备IMEI MD5值，iOS设备填空
  string android_id_md5 = 19;     // Android设备的android_id MD5值，iOS设备填空
  string gaid_md5 = 20;           // Android设备Google advertising ID (GAID) MD5值
  string oaid_md5 = 21;           // msa 联盟 oaid 匿名设备标识符(Open Anonymous Device Identifier) MD5值
  string imei1_md5 = 22;          // Android设备IMEI1 MD5值
  string imei2_md5 = 23;          // Android设备IMEI2 MD5值
  bool is_custom_imei = 24;       // false ：SDK 主动获取的设备id  true ：开发者设置的设备id
  bool is_custom_oaid = 25;
  bool is_custom_idfa = 26;
  bool is_custom_android_id = 27;
  string idfa_sha1 = 28;          // idfa sha1，为了海外adx添加
  string imei_sha1 = 29;          // imei sha1, 为了海外adx添加
  string android_id_sha1 = 30;    // androidId sha1, 为了海外adx添加
  repeated Caid caid = 31;
  string oaid_api = 32;           // 用api方式获取的oaid
  string odid = 33;               // 开发者匿名设备标识符
  bool is_custom_location = 35;   // 是否是自定义地理位置
  bool is_custom_idfv = 36;       // 是否是自定义idfv
}

// 二维尺寸信息
message Size {
  uint32 width = 1;  // 宽度, 必填！
  uint32 height = 2;  // 高度, 必填！
}

message Caid {
  string id = 1;    // caid的值
  uint64 generate_time = 2; // id生成时间，unix时间戳， 秒
  string version = 3; // id的版本
}

// 网络环境信息
message Network {
  string ipv4 = 1;                // 必填！用户设备的公网IPv4地址，格式要求：***************
  uint32 connection_type = 2;     // 必填！网络连接类型，用于判断网速。0=无法探测当前网络状态; 1=蜂窝数据接入，未知\网络类型; 2=2G; 3=3G; 4=4G; 5=5G; 100=Wi-Fi网络接入; 101=以太网接入
  uint32 operator_type = 3;       // 必填！移动运营商类型，用于运营商定向广告。0=未知的运营商；1=中国移动；2=中国联通；3=中国电信；
  string ua = 4;                  // 浏览器ua
  string operator = 5;            // 必填！移动运营商类型（将来会废弃operator_type）46011
  string mac = 6;                 // 设备的mac地址
  string wifi_mac = 7;            // wifi路由器的mac地址
  string wifi_id = 8;             // wifi ssid
  map<string, string> ips = 9;    // 设备各种ip取值集合（仅iOS使用）
  string carrier_name = 10;       // 运营商名称
  string ipv6 = 11;               // 用户设备的公网IPv6地址
  bool is_custom_operator_code = 12; // 是否是开发者传入的运营商代码，例：46011
  bool is_custom_operator_name = 13; // 是否是开发者传入的运营商名称，例：中国移动
}

// 广告位信息
message AdSlot {
  string adslot_id = 1;                           // 必填！广告位ID
  Size adslot_size = 2;                           // 必填！广告位尺寸。暂不填写，后续产品确定策略有再议
  repeated uint32 adslot_type = 3;                // 选填！支持的广告位类型（目前只支持一种）。 1=通用奖励视频 2=开屏
  uint32 bidfloor = 4;                            // 底价
  string vid = 5;                                 // 曝光ID，该广告位此次请求曝光唯一ID。可用requestid + adslot序列号生成
  string latest_camp_id = 6;                      // 上一次播放的campaign id
  string latest_crid = 7;                         // 上一次播放的Creative id
  repeated uint32 material_type = 8;              // 媒体支持的素材格式列表。开屏：0-未知；1-jpg、jpeg；2-png； 3-gif。激励视频广告先填空。插屏：0-图片；1-视频；2-图片视频混出
  uint32 sdk_strategy_index = 9;                  // sdk聚合策略位置。从1开始
  uint32 api_strategy_index = 10;                 // api聚合策略位置。从1开始
  map<string, string> sdk_cached_ads = 11;        // sdk已经缓存的广告，key：Creative Id，value：Campaign Id
  repeated uint32 creative_type = 12;             // 媒体支持的创意类型，与response中MaterialMeta的creative_type一致（sdk从2.10开始补传开屏支持的创意类型3和8）
  uint32 algorithm_floor = 13;                    // 期望价格，对应【算法 eCPM Floor】
  map<string, string> ext = 14;                   // key:template_type,表示模版类型，0表示没有伴随条；key:animate_type,表示动画类型，0表示没有伴随条
  map<string, AdCache> ad_caches = 15;            // key:广告位id
  Video video = 16;                               // 视频素材请求参数，非原生广告使用
  uint32 expected_floor = 17;                     // 流量售卖底价, 根据Supply端多重价格考虑因素计算得出
  uint32 auto_click_mode = 18;                    // 自动下载类型：0、wifi（默认）；1、全网；2、不自动下载；
  uint32 settlement_mode = 19;                    // 结算方式: 0-固定价格或分成，1-按竞价成交价格累计计费，2-开发者自设floor累计计费
  uint32 alg_bid_type = 20;                       // 0-代表走正常floor, 1-算法侧按target调节
  uint32 ecpm_target = 21;                        // 平台计算出的ecpm target，单位：分
  uint32 settlement_setting = 22;                 // 平台设置的结算模式，1:分成结算，2:固定eRPM结算，3:竞价计算，4:eCPM底价结算
  uint32 commer_ecpm = 23;                        // 商务ecpm，单位：分
  uint32 adx_ecpm = 24;                           // 程序化ecpm,固定erpm计算模式下且商务ecpm>0的情况下，expected_price=adx_ecpm
  uint32 commer_ecpm_state = 25;                  // 固定erpm结算模式状态：0、关闭，1:开启
  repeated RequestNativeAdTemplate native_template = 26;                 // 原生广告模版
  repeated string b_seat = 27;                    // 广告target_url域名黑名单
  uint32 ad_count = 28;                           // 请求的广告数
  Size changed_slot_size = 29;                    // 中控设置请求dsp渠道尺寸转换
  repeated string vid_list = 30;                  // 曝光id数组，数量应该与广告数ad_count一致
  uint32 trans_ad_type = 31;                      // 转换广告类型，用于请求dsp要求实际返回的广告类型
  repeated string app_white_list = 32;            // 能够投放的 App 包名白名单
  repeated string app_black_list = 33;            // 禁止投放的 App 包名黑名单
  Image image = 34;                               // 图片素材请求参数，非原生广告使用
  Image icon = 36;                                // icon请求参数，非原生广告使用
  repeated uint32 support_interaction_type_list = 37; // 广告位支持的广告动作类型：1-网页打开；2-下载；3-deeplink，如果没有app，则下载并唤起app；4-纯曝光；5-点击后302下载；6-点击后302跳转；7：wx 小程序；8：quick app, 98-系统浏览器打开；99-deeplink；100-未知
  repeated string app_category_white_list = 38;   // 允许投放的广告分类列表
  repeated string app_category_black_list = 39;   // 禁止投放的广告分类列表
  Text title = 40;                                // 广告标题要求
  Text description = 41;                          // 广告描述要求
  uint32 expected_fill_ratio = 42;                // 固定erpm结算方式下广告位的要求填充比例下限时，字段值为填充率百分数整数部分
  uint32 adjusted_erpm = 43;                      // 固定erpm结算方式下广告位的要求填充比例下限时，经过价格策略调整的erpm价格
  double price_wf = 44;                           // 固定erpm结算方式下广告位的要求填充比例下限时，价格调整系数
  bool in_app = 45;                               // 是否在应用内打开landing_page true:是
  repeated uint32 support_template_id = 46;       // sdk支持的原生模版ID列表
  uint32 show_style = 47;                         // 展现形式。0-全屏；1-半屏
  bool disable_mraid2 = 48;                       // 禁用mraid2.0模版广告。false-不禁用； true-禁用
  uint32 err_click_rate = 49;                     // 误点率上限
  bool is_hb = 50;                                // 是否hb广告。false:不是, true:是
  uint32 adx_id = 51;                             // 999:ToBid_adx
  uint32 media_request_count = 52;                // 上一轮开发者请求量
  uint32 media_ready_count = 53;                  // 上一轮给开发者的填充量
  int64 pre_req_time = 54;                        // 上一轮请求时间(unix时间戳，毫秒)
  int32 req_interval_time = 55;                   // 配置间隔时间
  uint32 cached_ad_size = 56;                     // 缓存的广告数量
  uint32 widget_sensitivity = 57;                 // 互动组件灵敏度，取值 [1 , 10]
  uint32 disable_interaction = 58;                // 0：允许互动广告 1：屏蔽互动广告
  repeated string native_template_id = 59;        // 原生模版id
  repeated string freq_hit_pkgs = 60;             // 频控包名
  bool enable_advanced_interaction = 61;          // 是否允许高级互动  false:不允许  true：允许  默认false
  uint32 pos = 62;                                //
  repeated string support_pkg = 63;               //
  uint32 easy_play_type = 64;
}


message AdCache {
  int32 ad_type = 1;            //广告类型
  repeated string crids = 2;    //缓存的创意列表
}

message Video {
  uint32 max_duration = 1;  // 视频最大时长，单位：秒
  uint32 min_duration = 2;  // 视频最小时长，单位：秒
  uint64 max_file_size = 3; // 视频最大文件大小，单位：字节
  uint64 min_file_size = 4; // 视频最小文件大小，单位：字节
  string codec_format = 5;  // 视频编码格式，例如：h.264
}

message Image {
  uint64 max_file_size = 1; // 图片最大文件大小，单位：字节
  uint64 min_file_size = 2; // 图片最小文件大小，单位：字节
}

message Text {
  uint32 min_length = 1; // 文本最小长度
  uint32 max_length = 2; // 文本最大长度
}

message HeaderBidding {
  string bid_token = 1;                   //竞价获取广告标识
  uint32 channel_id = 2;                  // 聚合渠道id
  map<string, string> options = 3;        // 外部渠道媒体属性
  string p_id = 4;                        // 竞价广告位 toBid 1.9.0支持
  string cur = 5;                         // 请求价格币种 CNY, USD
  int32 ad_type = 6;                      // 聚合的广告类型（注意sig 插屏就是4） sdk3.8.0support
  string thrmei = 7;                      // gdt thrmei 信息  【sdk4.3.0support】
  int32 tag = 8;                          // filter tag, 1:过滤  【android sdk4.6.0 support】
}

message WXProgramReq {
  uint32 wx_api_ver = 1;  //用户微信内SDK版本
  string opensdk_ver = 2; //微信openSDK版本 android: 620953856 ios: 1.8.6
  bool wx_installed = 3;  //用户是否已安装微信
}

message StrategyReq {
  bool strategy_init = 1;       // 略是否初始化请求标记
  string last_strategy_id = 2;  // 上一次的下发的策略分组id 按广告位区分
}


message User {
  uint32 yob = 1;     // 生日年份，例：1995
  string gender = 2;  // 性别，M-男；F-女；O-未知
  repeated string installed_app = 3;   // 用户已安装应用列表
  repeated string user_strategy = 4;   // 用户属性id，如：3342，表示改用户安装了com.weibo应用；1111，表示该用户是体育运动爱好者
  Version app_market_version = 5;      // 设备应用市场版本号
  string app_market_bundle = 6;        // 用户安装的应用市场包名
  bool is_minor = 7;                  // 是否未成年人，false-否；true-是
  bool disable_personalized_recommendation = 8;  //是否禁止个性化推荐，false-不禁止；true-禁止
  bool change_recommendation_state = 9; // disable_personalized_recommendation 个性化状态（实时状态）与sdk初始化个性化推荐状态是否一致。false-一致；true-不一致
  string uid = 10;                      // 美团uid
}

message RequestNativeAdTemplate {
  string id = 1;                      // 原生广告模版id
  uint32 type = 2;                    // 原生广告模版类型：1: 单视频、2: 单图、3: 多图(指3张图片)
  repeated RequestAsset assets = 3;   // native 对象不为空则必填，元素对象
  uint32 w = 4;                // 原生广告位宽度，原生对象下必填
  uint32 h = 5;                // 原生广告位高度；原生对象下必填
  uint32 render_type = 6;      // 渲染类型，取值为：0-开发者自渲染
  uint32 video_auto_play = 7;  // 自动播放控制：0、总是；1、wifi；2、不自动
  uint32 preview_page_video_mute = 8;  // 预览页自动播放静音控制：0-静音；1-不静音
}

message RequestAsset {
  uint32 id = 1;              // 原生元素id，通常从0开始递增
  uint32 required = 2;        // 该元素是否必须返回，默认为否；0-非必填；1-必填
  RequestAssetVideo video = 3;       // 视频元素
  RequestAssetImage image = 4;       // 图片元素
  RequestAssetText text = 5;         // 文字元素
}

message RequestAssetVideo {
  repeated string mimes = 1;     // 视频文件格式，目前仅支持MP4
  uint32 w = 2;                  // 视频素材宽度，原生广告，该字段必填
  uint32 h = 3;                  // 视频素材高度，原生广告，该字段必填
  uint32 min_duration = 4;       // 视频最小时长
  uint32 max_duration = 5;       // 视频最大时长
  Size changed_video_size = 6;   // 中控设置请求dsp渠道尺寸转换
  uint64 max_file_size = 7;      // 视频最大文件大小，单位：字节
  uint64 min_file_size = 8;      // 视频最小文件大小，单位：字节
  uint32 bitrate = 9;            // 视频码率, 单位：kbps
  string codec_format = 10;      // 视频编码格式，例如：h.264
  double min_ratio = 11;         // 最小比例
  double max_ratio = 12;         // 最大比例
}

message RequestAssetImage {
  uint32 type = 1;               // 图片元素类型；1=主要图片，2=icon，3=视频封面图
  repeated uint32 mimes = 2;     // 图片文件格式，0:unknown 1:jpeg,jpg 2:png 3:gif
  uint32 w = 3;                  // 原生广告位宽度，原生对象下必填
  uint32 h = 4;                  // 原生广告位高度；原生对象下必填
  Size changed_image_size = 5;   // 中控设置请求dsp渠道尺寸转换
  uint32 max_file_size = 6;      // 图片最大文件大小，单位：字节
  uint32 min_file_size = 7;      // 图片最小文件大小，单位：字节
  double min_ratio = 8;          // 最小比例
  double max_ratio = 9;          // 最大比例
}

message RequestAssetText {
  uint32 type = 1;          // 文字元素类型，1-标题；2-描述；3-评分；4-行动按钮文字；6-下载数量；7-星级
  uint32 min_length = 2;    // 文字元素长度最小值
  uint32 max_length = 3;    // 文字元素长度最大值
}

/****************************************************** Response *************************************************************************/
//内部广告返回
message BidResponse {
  string request_id = 1;              // 对应请的request_id(内部生成的)
  repeated Ad ads = 2;                // 广告信息
  uint64 error_code = 3;              // 请求出错时的错误码，用于问题排查
  uint64 process_time_ms_dsp = 4;     // 投放系统广告检索时长，ms
  uint64 process_time_ms_ssp = 5;     // ssp聚合平台广告检索时长，ms
  uint32 pctr_valid_check = 6;        // 预估人群是否有效. 0：未查到预估数据；1：使用；2：过期；3：未过期，但无交集【v2.5.2版本开始只有0:未使用预估，4:在线预估】
  string pctr_version = 7;            // 预估版本【v2.5.2开始弃用】
  string uid = 8;                     // 用户在sigmob的唯一标识。
  uint32 expiration_time = 9;         // 广告有效截止日期。Unix时间戳，单位秒。
  string ad_ua = 10;                  // 广告使用ua，优先从pb中取，当pb中ua为空时从请求header中取user-agent
  SlotAdSetting slot_ad_setting = 11; // 广告位上广告相关设置，对返回所有广告生效（sdk 3.4.0开始生效）
  string error_message = 12;
  Template scene = 13;                // 当前的主入口
  BiddingResponse bidding_response = 14;      // c-s 返回的出价以及url
  string rv_callback_url = 15;        // 激励回调url iOS 4.9.4 & Android 4.13.2 rv改版
  string adx_id = 16;                 // adx渠道ID
  bool noncompliance_mark = 17;       // 是否是非合规标记；fasle: 合规版本；true: 非合规版本 default：false
}


// 广告信息
message Ad {
  string adslot_id = 1;                   // 对应请求时填写的广告位ID
  string vid = 2;                         // 广告曝光id.
  string cust_id = 3;                     // 未用到! 客户ID
  string camp_id = 4;                     // 推广计划ID
  string crid = 5;                        // 创意ID
  repeated MaterialMeta materials = 6;    // 物料元数据组(素材)，目前只有一个
  repeated Tracking ad_tracking = 7;      // 广告监控信息
  uint32 bid_price = 8;                   // 最高竞价，单位cpm分
  string product_id = 9;                  // 下载类广告时，为product在app store的id
  string settlement_price_enc = 10;       // 媒体和投放平台的结算价格密文
  uint32 is_override = 11;                // 未用到! 0:没有；1：有
  string ad_source_logo = 12;             // 广告来源方的logo小图标地址
  string ad_source_channel = 13;          // 广告来源编码
  uint32 ad_type = 14;                    // 广告类型：1=奖励视频广告, 2=开屏
  map<string, string> options = 15;        // 未用到! 保留扩展字段
  uint32 expired_time = 16;               // 广告过期时间 单位为秒
  bool forbiden_parse_landingpage = 17;   // 禁止解析落地页
  uint32 display_orientation = 18;        // 广告播放方向：0 自动 默认， 1.竖屏，2.横屏，3. 横竖屏
  AdSetting ad_setting = 19;              // 广告设置
  uint32 bid_type = 20;                   // 未用到!  投放平台的竞价类型: 1 cpm, 2 cpc, 3 cpa
  string attribution_id = 21;             // 未用到! 广告的归因id
  SKAdNetwork sk_ad_network = 22;           //SKAdNetwork所需要的信息
  SKAdNetwork view_through_ad_network = 23;   //SKAdNetwork所需要的信息,针对View-Through Ad
  map<string, string> ad_track_macro = 24;    // 广告新监测链接宏
  uint64 error_code = 25;                     // 广告响应错误码
  uint32 change_win_price_type = 26;          // 修改成交价类型：0-未修改; 1-rtb提价
  WXProgramRes wx_program_res = 27;           // 微信小程序相关
  uint64 prod_id = 28;                        // adx未用到! 产品id，对应sig_dsp_adver_campaign.prodid
  BiddingResponse bidding_response = 29;      // c-s 返回的出价以及url
  repeated FrequencyControl frequency_control = 31;     // 频控
  bool enable_small_window = 32;                        // 是否通过小窗方式唤起应用，默认 false
}

message FrequencyControl {
  string strategy_id = 1;                 // 频控策略id
  string frequency_control_event = 2;     // 频控事件
  uint32 frequency_control_count = 3;     // 频控次数
  repeated uint32 ad_expire_type = 4;     // 频控广告类型
}

// 广告物料元数据信息
message MaterialMeta {
  uint32 creative_type = 1;              // 创意类型：1=奖励视频广告的资源包形式(endcard为tgz包)，一般由video、endcard资源包构成；3=纯静态图片广告，一般由单张image_src构成;4=video+html源代码的模式;5=预留;6=video定帧+htmlsrc;7=video+landingpage(相当于在线endcard)；8单个视频；9 MRAID(The Mobile Rich Media Ad Interface Definitions system)
  uint32 interaction_type = 2;           // 广告交互类型。1=使用浏览器打开；2=下载应用;3=deeplink唤醒失败，执行下载
  string landing_page = 3;               // 广告目标地址
  string video_url = 4;                  // 视频物料地址
  uint32 video_duration = 5;             // 视频物料时长
  Size   video_size = 6;                 // 视频尺寸
  bytes html_snippet = 7;                // HTML片段，中文需要UTF-8编码. 激励视频广告时,存放Endcard的H5代码.
  string endcard_url = 8;                // Endcard地址，sigmob为资源包的方式
  string video_md5 = 9;                  // 视频的md5
  string endcard_md5 = 10;               // Endcard的md5
  string deeplink_url = 11;              // deeplink url
  string image_src = 12;                 // 广告图片地址
  Size   image_size = 13;                // 广告图片尺寸
  string image_md5 = 14;                 // 图片的md5值
  uint32 image_type = 15;                // 图片素材的格式编码，0:unknown 1:jpeg,jpg 2:png 3:gif
  uint32 click_type = 16;                // 点击类型，1=按钮点击，2=全屏点击
  bool has_companion_endcard = 17;       // 是否包含伴随广告的素材
  CompanionEndcard companion = 18;        // 伴随广告的素材对象
  WebEventHandle web_event_handle = 19;   // html事件交互定义与sdk的事件交互对象
  uint32 template_type = 20 ;             // 开屏:{1:跳过+广告合一+logo模版,2:跳过+广告模版}, 激励视频:{1:星级模版,2:描述模版},新插屏:{0:全屏,1:半屏},新插屏:{0:全屏,1:半屏}
  int32 video_reciprocal_millisecond = 21;// 视频播放结束后，停留的倒数（99999999:首帧；-99999999:尾帧；正数：正数第几毫秒；负数：倒数第几毫秒；0:取默认值）
  string html_url = 22;                   // html素材地址(表单类投放可使用html_url+按钮点击+landing_page为空)
  bool enable_collapse_tool_bar = 23;     // 是否允许收起\展开工具条（通过单击视频)，false不允许，true允许
  uint32 open_market_mode = 24;           // 打开应用市场的模式， 0:appstore半屏打开，1:全屏打开（目前仅ios使用）
  uint32 play_mode = 25;                  // 播放模式，0:预加载模式，1:在线流媒体播放模式
  uint32 sub_interaction_type = 26;       // 子交互类型，0:无效, 1:广点通下载类型，2. 已安装调起应用，3. yyb deeplink投下载类应用
  string title = 27;                      // 广告标题(ssp v3.4.0开始加入，未来CompanionEndcard中字段将废弃)
  string desc = 28;                       // 广告描述(ssp v3.4.0开始加入，未来CompanionEndcard中字段将废弃)
  string icon_url = 29;                   // 广告icon图片(ssp v3.4.0开始加入，未来CompanionEndcard中字段将废弃)
  float score = 30;                       // 下载类app的星级，小数，例如4.5表示4星半（目前用在伴随广告），score大于等于1才有效(ssp v3.4.0开始加入，未来CompanionEndcard中字段将废弃)
  uint32 template_id = 31 ;               // 模板id
  bool disable_auto_deeplink = 32;        // 是否禁止deeplink自动唤起
  bytes closecard_html_snippet = 33;      // 关闭页推荐HTML片段
  string app_name = 34;                   // 广告应用名称
  string button_text = 35;                // 广告按钮文案
  AdPrivacy ad_privacy = 36;              // 广告隐私信息
  uint32 video_byte_size = 37;            // 视频大小，单位：字节
  string html_src = 38;                   // html代码片段
  ResponseNativeAd native_ad = 39;        // 原生广告
  bool download_dialog = 40;              // 下载需要先弹四要素，默认false 下载不弹四要素，true为下载弹四要素弹框
  AndroidMarket android_market = 41;      // 跳转应用市场信息
  Template main_template = 42;            // 当前广告的主模版
  Template sub_template = 43;             // 当前广告的字模版
  repeated ResponseAsset asset = 44;      // 素材资源
  uint32 theme_data = 45;                 // 0: 主题非半透明；1: 主题半透明
  bool in_app = 46;                       // 是否在应用内打开landing_page true:是
  string endcard_image_src = 47;          // endcard图片（暂时用于插屏兜底原生模版）
  string creative_title = 48;             // 创意标题
  bool has_endcard = 49;                  // 是否有endcard
  Color button_color = 50;                // 按钮颜色
  repeated Widget widget_list = 51;       // 挂件列表
  string apk_md5 = 52;                    // apk下载文件的md5值
  OHOSMarket ohos_market = 53;            // 鸿蒙应用市场信息
  uint32 video_bit_rate = 54;             // 视频码率，单位： kbps
  string video_codec_format = 55;         // 视频文件的编码格式。如：H.264
  uint32 image_file_size = 56;            // 图片文件大小，单位：字节
}

message Widget{
  uint64 widget_id = 1;   // 挂件id
  bool enable_developer_render = 2;        // 是否允许开发者渲染互动组件  false:SDK渲染  true：开发者渲染 默认false
}

message OHOSMarket {
  string url = 1;           // 跳转URL（元服务）
  string bundle_id = 2 ;    // 应用包名
  uint32 type = 3;          // 0:应用内拉起AppGallery; 1:跳转应用商店; 2:元服务
}

message AndroidMarket {
  string market_url = 1;                         //market跳转Url
  string app_package_name = 2;                  //下载的应用包名
  string appstore_package_name = 3;             //指定的应用市场包名
  uint32 type = 4;                              //商店下载类型:0 普通商店下载，1. 小米直投2.0 下载;
}

message CompanionEndcard {
  string icon_url = 1;                  // 下载类app的icon（目前用在伴随广告）
  string title = 2;                     // 广告标题（目前用在伴随广告）
  float score = 3;                      // 下载类app的星级，小数，例如4.5表示4星半（目前用在伴随广告），score大于等于1才有效
  string button_text = 4;               // 伴随广告的button的文字（目前用在伴随广告）
  Color button_color = 5;               // 按钮颜色（目前用在伴随广告）,color对象
  Color button_text_color = 6;          // 按钮字体的颜色（目前用在伴随广告）,color对象
  uint32 click_type = 7;                // 点击类型，1=按钮点击，2=伴随条点击
  Color bar_color = 8;                  // 伴随条的颜色,color对象
  string desc = 9;                      // 应用描述
  uint32 animate_type = 10;             // 动画类型,1：中心缩放+弹簧；2：自下而上;3:无动画；
  uint32 show_delay_secs = 11;          // 伴随条延迟出现的秒数
}

message Color {
  uint32 red = 1;          //红色值，整数 0-255
  uint32 green = 2;     //绿色值，整数 0-255
  uint32 blue = 3;     //蓝色值，整数 0-255
  float alpha = 4;         //透明度，小数 0.0-1.0
}

// 广告效果跟踪信息
message Tracking {
  string tracking_event_type = 1;         // track事件类型。事件类型见字典
  repeated string tracking_url = 2;       // track事件URL
}

message WebEventHandle {
  uint32 handle_type = 1;        //1)postmesage body handle 2)postmesage name handle
  repeated string handle_name = 2;        //sdk用来注册监听的handle的名称，
  repeated WebEvent events = 3;  //事件数组
}

message WebEvent {
  string event_type = 1;   // close,loaded,click
  string event_name = 2;   // 各个dsp的html事件的具体名称
}


message LinkAction {
  string scheme = 1;    // 跳转链接协议头
  string host = 2;      // 跳转链接域名
  int32 redirect_count = 3; // 跳转次数，0: 不允许跳转；>0: 跳转次数
}

message AdSetting {
  RvAdSetting rv_setting = 1; //激励视频相关广告设置
  SplashAdSetting splash_setting = 2; //开屏广告相关配置
  int32 retry_count = 3; //重试次数（0:不重试）
  SingleNativeAdSetting single_native_setting = 4; // 原生广告相关设置
  bool in_app = 5; //是否在应用内打开landing_page
  bool use_safari = 6; //在应用内打开是否使用safari，默认使用WKWebView
  bool disable_download_listener = 7; //是否禁止webView下载监听，当开关开启时，webView的下载和安装监听全部取消。
  repeated string scheme_white_list = 8;// 允许模版跳转的白名单（各app的协议头），*表示通配符
  uint32 sensitivity = 9;                           // 互动灵敏度，1-低，2-中，3-高
  repeated LinkAction link_actions = 10;            // 落地页最后一跳链接特征，多广告场景下每个广告对同一种规则可能有不同跳转次数控制
  bool can_expire_reload = 11;                      // 广告过期是否重新请求 true：重新请求；false：不请求  默认：false
  uint32 expire_reload_count = 12;                  // 广告过期重试次数 默认0；
  uint32 shake_count = 13;                          // 互动震动次数，默认0 不限制震动次数 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135441613
  uint32 shake_time_threshold = 14;                 // 摇一摇时间阈值 单位毫秒 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 shake_trigger_type = 15;                   // 摇一摇触发类型 默认0：双向触发；1：单向触发 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448610
  uint32 widget_interval_time = 16;                 // 互动组件触发间隔 单位毫秒 默认0：无时间间隔 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444341
  uint32 ad_reload_type = 17;                       // 广告过期是否重新请求 0:不可以再请求 1:时间戳过期再请求 2:主动过期再请求；3:可以同时进行主动被动过期 (只有满足广告为直投、过期时间为1800以内（含）的广告才下发3)
}

message SlotAdSetting {
  RvAdSetting rv_setting = 1; //激励视频相关广告设置
  SplashAdSetting splash_setting = 2; //开屏广告相关配置
  NativeAdSetting native_setting = 3; // 原生广告相关设置
  int32 retry_count = 4; //重试次数（0:不重试）
  bool disable_x_requested_with = 5; //禁止webview携带x-requested-with请求头，默认false不禁止，true为禁止
  InterstitialSetting interstitial_setting = 6; //新插屏的相关配置
  uint32 apk_download_type = 7;                 // apk 下载类型，0: 系统下载, 1 自定义下载器单任务单线下载, 2: 自定义下载器单任务多线程下载
  bool resumable_download = 8;                  // 支持断点续传下载, 默认false不使用，true为使用
  bool use_downloaded_apk = 9;                  // 使用已经下载完成的apk进行安装, 默认false不使用，true为使用
  bool enable_deeplink_and_landing_page = 10; //是否允许deeplink和落地页同时开启，默认false不使用，true为使用
  repeated string market_package_name = 11; // 强制使用的应用市场包名
  repeated LinkAction link_actions = 12;       // 落地页最后一跳链接特征，优先看AdSetting的定义，如果AdSetting没有匹配到再看SlotAdSetting
  // @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=135442392
  uint32 click_lose_rate = 13;                 // 点击是不回调开发者的概率[0, 100]
}

message RvAdSetting {
  //是否静音，负值表示未设置，由sdk决定，0、有声音，1、静音
  int32 if_mute = 1;
  //播放完成比例定义，从0到1的小数
  float finished = 2;
  //视频关闭按钮位置，左上：1，右上：3
  int32 video_close_position = 3;
  //endcard关闭按钮位置，左上：1，左下：2，右上：3，右下：4，
  int32 endcard_close_position = 4;
  //静音按钮位置， 左上：1，左下：2，右上：3，右下：4，
  int32 mute_postion = 5;
  //跳过按钮，根据视频播放进度的百分比显示，取值范围[0,100]，取0表示一开始就可条，取30，表示播放到30%时显示跳过按钮
  int32 skip_percent = 6;
  //小于零表示此项不生效，大于等于零表示经过的指定的秒数后显示跳过按钮，如果次项配置大于等于零，则skipPercent配置项自动失效
  int32 skip_seconds = 7;
  //是否允许关闭视频时直接退出广告，而不再显示endcard
  bool enable_exit_on_video_close = 8;
  //视频全屏点击开关
  bool full_click_on_video = 9;
  //点击区域配置
  ClickAreaSetting click_setting = 10;
  //是否不显示广告字样
  bool invisible_ad_label = 11;
  //视频播放结束时间（t>0）
  int32 end_time = 12;
  int32 endcard_close_image = 13; // endcard close按钮图片配置：0: 使用老图片；1: 使用新图片 default = 0
  int32 end_impression_time = 14; // 针对view-through-ad
  bool disable_auto_load = 15;    // 关闭广告播放中的自动加载逻辑
  // 有效播放，>=0生效，正数第x秒触发
  int32 charge_seconds = 16;
  // 有效播放，>=0生效，播放百分比触
  int32 charge_percent = 17;
  // 触达奖励，>=0生效，正数第x秒触发
  int32 reward_seconds = 18;
  // 触达奖励，>=0生效，播放百分比触发
  int32 reward_percent = 19;
  // 广告在跳过时且未达到奖励条件时，是否弹窗挽留；0 or 1: 弹窗、2: 不弹窗
  int32 confirm_dialog = 20;
  // 奖励等待样式 0: 无； 1: 长驻提示
  int32 reward_style = 21;
  bool video_error_reward = 22; //视频播放出错是否发送奖励, 默认是false
}

message SplashAdSetting {
  int32 show_duration = 1;//开屏倒计时时间,负值表示为定义，由sdk默认值决定（目前默认值是3）
  bool  enable_close_on_click = 2; //开屏点击关闭开关，（false 关闭，true 不关闭，默认值 false)
  uint32 auto_click_mode = 3; // 自动下载类型：0、wifi（默认）；1、全网；2、不自动下载；
  int32 auto_click_time_ratio = 4; // 自动点击时机，例如当开屏展示50%进度时自动点击，本字段传值50；
  bool enable_full_click = 5;         //是否开启全屏点击，default=false
  bool invisible_ad_label = 6;        //是否不显示广告字样
  bool use_floating_btn = 7;          //模版是否使用浮窗按钮（除摇一摇模版外）
}

// 广告位原生广告配置
message NativeAdSetting {
  uint32 video_auto_play = 1; // 播放器自动控制：0-总是；1-wifi；2-不自动
  uint32 preview_page_video_mute = 2; // 预览页播放器静音控制：0-静音；1-不静音
  uint32 detail_page_video_mute = 3; // 详情页播放器静音控制：0-不静音；1-静音
  uint32 impression_percent = 4; // 广告有效曝光定义-曝光像素百分比
  uint32 impression_time = 5; // 广告有效曝光定义-曝光持续时间
  int32 end_impression_time = 6; //针对view-through-ad
  int32 req_interval_time = 7;   // 配置间隔时间
  int32 ad_pool_size = 8;        // 缓存的广告数量
  uint32 media_expected_floor = 9; // 媒体请求底价
  uint32 log_interval_time = 10;  // 请求计数上报时间间隔（单位：秒）
  bool enable_advanced_interaction = 11;    // 是否允许高级互动  false:不允许  true：允许  默认false
}

// 插屏广告设置
message InterstitialSetting {
  // 广告是否静音（0:非静音、1:静音。default=0）
  uint32 if_mute = 1;
  // 倒计时/跳过按钮 倒计时出现时间输入值，默认是0 s
  uint32 show_skip_seconds = 2;
  // 点击跳过时，是否直接关闭广告（默认false）
  bool skip_close_ad = 3;
  // 跳过按钮+同页面的关闭按钮位置，左上：1，左下：2，右上（默认）：3，右下：4
  uint32 skip_position = 4;
  // endcard关闭按钮位置，左上：1, 左下：2，右上（默认）：3，右下：4，
  uint32 endcard_close_position = 5;
  // 静音按钮位置， 左上：1(默认），左下：2，右上：3，右下：4，
  uint32 mute_position = 6;
  // 用户摇一摇的力度 2:低 1:中 0:高 （默认值）
  uint32 shake_level = 7;
  // 视频素材的结算点的打点时间 默认 5s，取值范围[0,30]
  uint32 charge_time = 8;
  //关闭广告播放中的自动加载逻辑
  bool disable_auto_load = 9; //关闭广告播放中的自动加载逻辑
  bool click_close_ad = 10;       // 点击是否关闭广告 false：不关闭；true：关闭 default：false
  uint32 seconds_close_ad = 11;   // 倒计时多少秒关闭广告；>0生效 ；default： 0；
  // 倒计时/跳过按钮 倒计时出现时间（图片）输入值，默认是0 s
  uint32 skip_seconds_btn_pic_show = 12;
  // 倒计时/跳过按钮 倒计时出现时间（视频）输入值，默认是5 s
  uint32 skip_seconds_btn_video_show = 13;
}

// 单个原生广告配置
message SingleNativeAdSetting {
  bool use_na_video_component = 1; // 详情页是否使用NA视频组件
}

message Privacy {
  uint32 gdpr_consent = 1;        // gdpr 授权状态
  uint32 child_protection = 2;    //儿童保护
  uint32 age = 3;                 //用户年龄
}

message AdPrivacy {
  string privacy_info_url = 1;                   // 下载广告隐私四要素信息页面地址，直接使用无需宏替换；当此字段存在忽略privacy_template_url和privacy_template_info
  string privacy_template_url = 2;               // 下载广告隐私四要素信息页面模版地址，使用privacy_template_info中键值对进行宏替换
  map<string, string> privacy_template_info = 3; // 下载广告隐私四要素信息页面模版信息；K: 宏名，V: 值
  string privacy_info_withbtn_url = 4 ;          // 带按钮的下载广告隐私四要素信息页面地址，直接使用无需宏替换；当此字段存在忽略privacy_template_url和privacy_template_info
}

message ClickAreaSetting {
  float top = 1;      //上边距百分比[0, 1)
  float left = 2;     //左边距百分比[0, 1)
  float bottom = 3;   //下边距百分比[0, 1)
  float right = 4;    //右边距百分比[0, 1)
}

message SKAdNetwork {
  string version = 1; //广告网络的版本1.0 or 2.0
  string ad_network_id = 2; //广告网络的id
  int32 campaign_id = 3; //广告网络确定其自己的广告系列标识符，该标识符必须为1到100之间的整数
  string itunes_id = 4; //广告主媒体的iTunes ID
  string source_app_id = 5; //媒体的itunes id
  string nonce = 6; //唯一曝光id
  uint64 timestamp = 7; //时间戳 (ms)
  string sign = 8;    //广告网络创建用于签名广告的加密签名
  uint32 fidelity_type = 9;   //2.2版本及更高版本的签名所需。对于view-through广告fidelity_type=0；storekit类型广告fidelity_type=1
}

message ResponseNativeAd {
  repeated ResponseAsset assets = 1;   // native 对象不为空则必填，元素对象
  uint32 type = 2;                     // 1: 单视频、2: 单图、3: 多图(指3张图片)
  string template_id = 3;              // 模版id，与RequestNativeAdTemplate中id字段对应
}

message ResponseAsset {
  uint32 index = 1;              // 原生元素id，通常从0开始递增
  ResponseAssetVideo video = 2;       // 视频元素
  ResponseAssetImage image = 3;       // 图片元素
  ResponseAssetText text = 4;        // 文字元素
}

message ResponseAssetVideo {
  string url = 1;                // 视频url
  uint32 w = 2;                  // 视频素材宽度，原生广告，该字段必填
  uint32 h = 3;                  // 视频素材高度，原生广告，该字段必填
  uint32 duration = 4;           // 视频时长
  uint64 file_size = 5;          // 视频文件大小，单位：字节
  uint32 bitrate = 6;            // 视频码率, 单位：kbps
  string codec_format = 7;       // 视频编码格式，例如：h.264
}

message ResponseAssetImage {
  string url = 1;                // 图片url
  uint32 w = 2;                  // 原生广告位宽度，原生对象下必填
  uint32 h = 3;                  // 原生广告位高度；原生对象下必填
  uint32 file_size = 4;          // 图片文件大小，单位：字节
}

message ResponseAssetText {
  string context = 1;           // text文字内容
}

message Template {
  uint32 type = 1;        // 1: 在线加载URL方式；2: html source方式；3: tgz压缩包，加载根目录endcard.html
  bytes context = 2;     // 中文需要UTF-8编码，存放对应的html内容或者URL
}

message BiddingResponse {
  string win_url = 1;             // 竞价成功url
  string lose_url = 2;            // 竞价失败url
  uint32 ecpm = 3;                // 出价 (cny、 usd)
  uint32 price_for_ssp = 4;       // cny价格
  string price_for_ssp_enc = 5;   // 格式：“price_for_ssp字段值“加密串
}

message WXProgramRes {
  string wx_app_id = 1; //微信开放平台媒体id
  string wx_universal_link = 2; //微信开放平台媒体UniversalLink（仅iOS需要）
  string wx_app_username = 3; //小程序原始id
  string wx_app_path = 4; //拉起小程序页面的可带参路径
  string wx_ext_msg = 5;        //ext信息
  uint32 wx_business_type = 6;  //业务类型：0小程序  1原生
  string wx_app_name = 7; // 微信小程序名称
}