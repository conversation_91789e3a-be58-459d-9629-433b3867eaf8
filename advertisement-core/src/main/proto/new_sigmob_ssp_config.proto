syntax = "proto3";
import "sigmob_rtb.proto";

package sigmob;
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.config";
option objc_class_prefix = "Sigmob";

message SigmobSdkConfigRequest {
  App app = 1;                        // 必填！应用信息
  Device device = 2;                  // 必填！设备信息
  Network network = 3;                // 必填！网络环境信息
  Privacy privacy = 4;               // 用户隐私
  User user = 5;                     // 用户信息
}

message SigmobSdkConfigResponse {
  uint32 code = 1;
  string error_message = 2;
  SigmobSdkConfig config = 3;
}

//配置信息
message SigmobSdkConfig {
  SigmobCommon common_config = 1;
  SigmobIOS ios_config = 2;
  SigmobAndroid android_config = 3;

  HarmonyOs ohos_config = 5;
}

message SigmobCommon {
  SigmobCommonEndpointsConfig endpoints = 1;          //api配置
  SigmobRvConfig rv_config = 2;                       //激励视频配置
  SigmobSplashConfig splash_config = 3;               //开屏配置
  SigmobNativeConfig native_config = 4;               //信息流配置
  uint32 configRefresh = 6;                     //config刷新间隔
  SigmobAntiFraudLogConfig anti_fraud_log = 7;        //反作弊信息采集配置
  bool is_gdpr_region = 8;                      //是否gdpr地区，默认是false
  uint32 tracking_expiration_time = 9;          //track事件过期时间，过期后不执行补发操作，单位秒
  uint32 tracking_retry_interval = 10;          //track上报失败的重发时间间隔
  uint32 max_send_log_records = 11;             //每次上传打点日志的最大条数
  uint32 send_log_interval = 12;                //打点日志上报时间间隔（单位：秒）
  repeated uint32 dclog_blacklist = 13;         //点号位黑明单，默认会将100，101号点自动加入黑名单，如果下发了黑明单，以下发为准。
  bool enable_debug_level = 14;                 //是否开启debug日志、初始化监测广告插件、监测结果打点和日志输出
  uint32 load_interval = 15;                    //重复发起请求的间隔时间，在非ready的情况下，用来防止重复发起请求
  bool disable_up_location = 16;                //是否禁止上传用户的位置信息。false: 不禁止上传；true: 禁止上传
  bool log_enc = 17;                            // 是否对打点数据做加密处理，false: 不加密；true: 加密
  bool feedback_debug = 18;                     // false: 不开启；true: 开启
  bool enable_extra_dclog = 19;                 // 开启额外的dclog，比如show_callback等回调打点上报。
  bool noncompliance_mark = 21;                 // 是否是非合规标记；fasle: 合规版本；true: 非合规版本 default：false
  uint32 expire_monitor_interval = 22;          // 广告过期监测时间间隔（单位：秒），默认0秒；>0：秒开启定时器；=0：停止定时器
  bool enable_active_expire = 23;               // 是否主动过期，false：不主动过期；true：主动过期；default：false
  uint32 sniffing_type = 24;                    // deeplink 判断和 canopen 包名嗅探方式（Android），0-queryIntentActivitiesAsUser 1-getApplicationInfo（新）
}

message HarmonyOs {
  repeated string open_scheme_list = 1;         // 代验证的canOpen列表
  bool use_web_source_cache = 2;                // 开启MRAID1，MRAID2 Js及CSS 文件缓存
  uint32 web_source_cache_expiration_time = 3;  // MRAID2 的Js/CSS/图片 缓存过期时间，单位天。
  bool disable_install_monitor = 4;             // 是否禁止安装监测
}

message SigmobIOS {
  // http://wiki.sigmob.cn/pages/viewpage.action?pageId=86377178
  repeated string open_scheme_list = 1;         // 代验证的canOpen列表
  bool queried_url_schemes = 2;                 // 是否开启读取info.plist的白名单配置 【!=4.16.0版本生效】
}

message SigmobAndroid {
  bool disableUpAppInfo = 1;                    //是否禁止上传用户已安装的app信息，默认是false，即可以上传
  uint32 report_log = 2;                        //是否要写log,控制最细颗粒度为设备 0:关、1:开
  uint32 up_wifi_list_interval = 3;             //上传wiki列表的时间间隔，单位秒，小于60秒则表示不上传
  uint32 disable_up_oaid = 4;                   //是否禁止获取oaid信息，其中在android10一下默认为不上传，Android10以上默认上传；1: 不禁止上传；2: 禁止上传; 其它走默认值
  bool enable_permission = 5;                   //允许获取权限的开关；默认为false: 表示不获取权限列表
  uint32 apk_expired_time = 6;                  //下载的apk的过期时间，单位：s; 默认0: 表示永远过期
  bool enable_report_crash = 7;                 //允许获取崩溃信息的开关；默认为false: 表示不上报崩溃信息
  bool oaid_api_is_disable = 8;                 //默认值 false :允许通过API模式采集oaid, true: 禁止通过API模式采集oaid，
  bool disable_boot_mark = 9;                   //禁止 boot_mark, update_mark 获取,default=false
  bool lock_play = 12;                          // 锁屏播放，默认不生效
  bool screen_keep = 13;                        // 广告播放保持屏幕常量，默认不生效
  repeated string open_pkg_list = 14;           // 验证的canOpen包名列表
  bool use_web_source_cache = 15;               // 开启MRAID1，MRAID2 Js及CSS 文件缓存
  uint32 web_source_cache_expiration_time = 16; // MRAID1，MRAID2 的Js及CSS 缓存过期时间，单位天
  bool disable_install_monitor = 17;            // 是否禁止安装监测 false:获取, true:不获取
  bool enable_open_pkg_list = 18;               // 是否开启验证的canOpen包名列表，默认关闭 【大于4.20.0生效】
  bool enable_open_pkg_dir_list = 19;					  // 通过文件路径嗅探应用程序是否存在，默认关闭且当enable_open_pkg_list 未开启 才生效。
}



//传感器配置信息
message SigmobAntiFraudLogConfig {
  SigmobMotionConfig motion_config = 1;             //传感器配置，如果不配置，则认为不采集传感器信息
  repeated string events = 2;                 //触发事件列表['load', 'start', 'click', 'close']
}


message SigmobMotionConfig {
  uint32 interval = 1;    //单位毫秒；传感器获取的间隔时间
  uint32 queue_max = 2;    //对列的最大值
  uint32 count = 3;       //从队列中分别获取事件前后的条数
}


//通用地址配置
message SigmobCommonEndpointsConfig {
  string log = 1;
  string ads = 2;
  string hb_ads = 3;
  string debug_feedback = 4;  // 开发调试日志
  string config = 5;          // 备用config地址
  string native_ad = 6;       // native 信息流 ad url
}

//激励视频配置
message SigmobRvConfig {
  uint32 cacheTop = 1;                            //本地缓存上限
  uint32 ad_load_timeout = 2;                     //广告总超时                        
  SigmobDialogSetting close_dialog_setting = 3;         //激励视频关闭对话框配置
}


//开屏配置
message SigmobSplashConfig {
  int32 cacheTop = 1;                             //开屏素材缓存上限，小于0，则清楚缓存
  int32 material_expired_time = 2;                //素材的过期时间，单位天数，若小于0，则清楚缓存
  uint32 ad_load_timeout = 3;                     //广告总超时,默认5s
}

//激励视频相关接口地址
message SigmobNativeConfig {
  int32 cacheTop = 1;                             //原生广告素材缓存上限，小于等于0，则清除缓存
  uint32 ad_load_timeout = 2;                     //广告总超时
}

message SigmobDialogSetting {
  string title = 1;                             //标题
  string body_text = 2;                         //正文，针对激励视频，需要在文案中带宏,表示视频剩余播放的秒数
  string cancel_button_text = 3;                //取消按钮的文案
  string close_button_text = 4;                 //关闭按钮的文案
}


