/**
@description ssp 中控管理媒体端信息

v 0.1

v 0.2 add 测试设备
**/
syntax = "proto3";
import "sigmob_rtb.proto";
option java_multiple_files = true;
option java_package = "com.sigmob.ssp.pb.publishing.management";
message AdSlotInfo{
  string id = 1; //广告位id
  uint32 appId = 2;
  uint32 state = 3; //状态，0:关闭，1开启
  uint32 type = 4; //广告类型，1、激励视频，2、开屏
  uint32 adState = 5;
  uint32 orientation = 6;
  bool enableServerCheckReward = 7;//奖励发放设置  0表示无需服务器判断，true表示需要服务器判断（supply 3.9.1)
  uint32 rewardNum = 8; //奖励数量（supply 3.9.1)
  string rewardName = 9; //奖励名称（supply 3.9.1)
  string rewardUrl = 10; //奖励事件服务端回调地址（supply 3.9.1)
  string rewardKey = 11; //安全key（supply 3.9.1)
  uint32 developer_bid_type = 12; //开发者设置的广告单元的竞价类型 0-普通；1-header bidding
  NativeSetting native_setting = 13; //信息流广告位相关设置，参加NativeSetting对象
  uint32 materialType = 14;          // 素材形式  ONLY_VIDEO(0), ONLY_IMAGE(1), VIDEO_AND_IMAGE(2)
  uint32 showStyle = 15;         		 // 展现形式 0:全屏、1:半屏、2:优选
  uint32 adx_id = 16;         		   // 999 :ToBid_adx
  bool enable_advanced_interaction = 17;    // 是否允许高级互动  false:不允许  true：允许  默认false
}

message DebugDevice{
  uint32 gId = 1; //账户组id
  repeated uint32 app_id_list = 2; //设备的app列表，表示此设备在相关app上是测试设备，只能获取测试广告
  string identify = 3; //设备唯一标识符
  uint32 u_type = 4; //标识符的类型
  uint32 state = 5; //开关，0：开启，1：关闭
}

message AdSlotPriceList {
  repeated AdSlotPrice list = 1;
}
//广告单元的价格相关
message AdSlotPrice {
  //广告单元id
  string ad_slot_id = 1;
  //广告单元地板价
  uint32 floor_price = 2;
  //广告单元期望价格
  uint32 algorithm_price = 3 ;
  //期望价格，是通过一系列计算后得到的价格
  uint32 expected_price = 4 ;
  //结算方式, 0-非竞价模式; 1-竞价模式; 2-ecpm底价结算;等价于s_effective
  uint32 settlement_mode = 5;
  //根据开发者设置的底价计算出的直投算法优化价格
  uint32 ecpm_target = 6;
  //1 代表算法侧按target调节，alg_bid_type = 0代表走正常floor
  uint32 alg_bid_type = 7;
  //平台设置的结算模式，1:分成结算，2:固定eRPM结算，3:竞价计算，4:eCPM底价结算，
  //与settlement_mode字段的区别是，settlement_setting中的1和2都归类为非竞价结算，
  //settlement_mode字段是ssp-adx真实使用的字段，settlement_setting目前仅做记录和打点
  uint32 settlement_setting = 8;
  //商务ecpm，单位（分）
  uint32 commer_ecpm = 9;
  //程序化ecpm,固定erpm计算模式下且商务ecpm>0的情况下，expected_price=adx_ecpm
  uint32 adx_ecpm = 10;
  //固定erpm结算模式状态：0、关闭，1:开启
  uint32 commer_ecpm_state = 11;
}

message NativeTemplate {
  //主素材类型,取值为1、仅视频；2、仅图片
  uint32 material_type = 1;
  repeated AssetSetting assets = 2;
}

message NativeSetting {
  //渲染类型，取值为：0、开发者自渲染
  uint32 render_type = 1;
  repeated NativeTemplate templates = 2;
  //视频配置信息（如果主素材包含视频，该项必填）参见VideoOption对象
  VideoOption video_option = 3;
  repeated string template_ids = 4;      // 元素映射模板id
}

message AssetSetting {
  //素材方向,取值：1、竖屏，2、横屏
  uint32 orientation = 1;
  //图片尺寸（如果有），包含两个属性,width和height，表示宽高
  sigmob.Size image_size = 2;
  //视频尺寸（如果有），包含两个属性,width和height，表示宽高
  sigmob.Size video_size = 3;
}

message VideoOption {
  //自动播放控制：0、总是；1、wifi；2、不自动
  uint32 video_auto_play = 1;
  //视频最大时长，为0表示不限制
  uint32 max_duration = 2;
  //视频最小时长，为0表示不限制
  uint32 min_duration = 3;
  //预览页自动播放静音控制：0-静音；1-不静音
  uint32 preview_page_video_mute = 4;
}