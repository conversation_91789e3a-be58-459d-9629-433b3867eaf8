package com.sigmob.ad.core.security.crypto;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 加解密类，通过base64编解码，并使用aes cbc模式加解密
 *
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 */
@Component
public class CryptoByAesAndBase64 implements Crypto {

  private static final String SECRET_AES_MODE = "CBC";
  private static final String SECRET_AES_PADDING = "PKCS5Padding";
  private static final String SECRET_AES_IV = "windmillwindmill";

  @Override
  public String encrypt(String key, String plaintext) {
    return this.encrypt(key, plaintext, false);
  }

  @Override
  public String encryptWithUrlSafe(String key, String plaintext) {
    return this.encrypt(key, plaintext, true);
  }

  @Override
  public String encryptWithUrlAndPadding(String key, String plaintext) {
    return this.encrypt(key, plaintext, false);
  }

  @Override
  public String decryptWithUrlSafe(String key, String encryptText) {
    return this.decrypt(key, encryptText);
  }

  public String encrypt(String key, String plaintext, boolean urlSave) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_MODE,
            SECRET_AES_PADDING,
            StringUtils.getBytesUtf8(key),
            StringUtils.getBytesUtf8(SECRET_AES_IV),
            StringUtils.getBytesUtf8(plaintext));

    byte[] base64;
    if (urlSave) {
      base64 = Base64.encodeBase64URLSafe(encryptBytes);
    } else {
      base64 = Base64.encodeBase64(encryptBytes);
    }
    return StringUtils.newStringUtf8(base64);
  }

  @Override
  public String decrypt(String key, String ciphertext) {
    byte[] base64 = Base64.decodeBase64(ciphertext);
    return this.decryptByBase64(key, base64);
  }

  @Override
  public String decrypt(String key, byte[] cipher) {
    byte[] base64 = Base64.decodeBase64(cipher);
    return this.decryptByBase64(key, base64);
  }

  protected String decryptByBase64(String key, byte[] base64) {
    byte[] decryptBytes =
        AesUtils.decrypt(
            SECRET_AES_MODE,
            SECRET_AES_PADDING,
            StringUtils.getBytesUtf8(key),
            StringUtils.getBytesUtf8(SECRET_AES_IV),
            base64);
    return StringUtils.newStringUtf8(decryptBytes);
  }
}
