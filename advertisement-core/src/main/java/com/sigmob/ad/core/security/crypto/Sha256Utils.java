package com.sigmob.ad.core.security.crypto;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/** */
public class Sha256Utils {

  private static final String ALGORITHM = "HmacSHA256";

  /**
   * @param key
   * @param message
   * @return
   * @throws NoSuchAlgorithmException
   * @throws InvalidKeyException
   */
  public static byte[] hmac(String key, String message)
      throws NoSuchAlgorithmException, InvalidKeyException {
    var mac = Mac.getInstance(ALGORITHM);
    mac.init(new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM));
    return mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
  }

  /**
   * @param key
   * @param message
   * @return
   * @throws NoSuchAlgorithmException
   * @throws InvalidKeyException
   */
  public static byte[] hmac(byte[] key, byte[] message)
      throws NoSuchAlgorithmException, InvalidKeyException {
    var mac = Mac.getInstance(ALGORITHM);
    mac.init(new SecretKeySpec(key, ALGORITHM));
    return mac.doFinal(message);
  }

  /**
   * 利用java原生的类实现SHA256加密
   *
   * @param str 加密后的报文
   * @return
   */
  public static byte[] getSHA256(String str) throws NoSuchAlgorithmException {
    MessageDigest messageDigest;
    byte[] encodeStr = null;
    messageDigest = MessageDigest.getInstance("SHA-256");
    messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
    return messageDigest.digest();
  }

  public static byte[] HMACSHA256(String data, String key) throws Exception {

    Mac sha256_HMAC = Mac.getInstance("HmacSHA256");

    SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");

    sha256_HMAC.init(secret_key);

    byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
    return array;
  }
}
