package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.google.common.collect.Maps;
import com.sigmob.ad.core.advertisement.AdMacro;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2021/10/11 4:33 下午 @Version 1.0 @Description
 */
public class HuaweiConstants {

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  /** 支持的广告行业 */
  public static final List<String> SUPPORT_CAT = List.of("3201");

  public static final Map<String, String> APP_INDUSTRY_MAPPING =
      Maps.newHashMapWithExpectedSize(24);

  /** 支持的推广类型 */
  public static final List<Integer> SUPPORT_INTERACTION_TYPE =
      List.of(
          ActionType.WEB_VIEW.getCode(),
          ActionType.DOWNLOAD.getCode(),
          ActionType.DEEPLINK.getCode(),
          ActionType.FAST_APP.getCode(),
          ActionType.WX_PROGRAM.getCode(),
          ActionType.DEEPLINK_AND_DOWNLOAD.getCode());

  /** 开屏支持模版 */
  public static final List<Integer> SUPPORT_SPLASH_TEMPLATE =
      List.of(NativeTemplate.SPLASH_IMAGE.getCode(), NativeTemplate.SPLASH_VIDEO.getCode());

  /** 开屏支持模版 */
  public static final List<Integer> SUPPORT_REWARDED_VIDEO_TEMPLATE =
      List.of(NativeTemplate.REWARDED_VIDEO.getCode());

  /** 原生支持模版 */
  public static final List<Integer> SUPPORT_NATIVE_TEMPLATE =
      List.of(NativeTemplate.NATIVE_IMAGE.getCode(), NativeTemplate.APP_ICON.getCode());

  /** 插屏支持模版 */
  public static final List<Integer> SUPPORT_INTERSTITIAL_TEMPLATE =
      List.of(
          NativeTemplate.INTERSTITIAL_IMAGE.getCode(), NativeTemplate.INTERSTITIAL_VIDEO.getCode());

  /** 信息流支持模版 */
  public static final List<Integer> SUPPORT_FEED_TEMPLATE =
      List.of(
          NativeTemplate.FEED_IMAGE.getCode(),
          NativeTemplate.FEED_THREE_IMAGE.getCode(),
          NativeTemplate.FEED_SMALL_IMAGE.getCode(),
          NativeTemplate.FEED_VIDEO.getCode());

  /** 开屏大图片默认文件大小150KB */
  public static final int DEFAULT_SPLASH_IMAGE_FILE_SIZE = 150 * 1024;

  /** 开屏视频默认视频文件大小10240KB */
  public static final int DEFAULT_SPLASH_VIDEO_FILE_SIZE = 10240 * 1024;

  /** 激励视频默认视频文件大小10240KB */
  public static final int DEFAULT_REWARDED_VIDEO_FILE_SIZE = 10240 * 1024;

  /** 激励视频默认小图片文件大小80KB */
  public static final int DEFAULT_REWARDED_VIDEO_ICON_FILE_SIZE = 80 * 1024;

  //  /** 激励视频默认预览图文件大小150KB */
  //  public static final int DEFAULT_REWARDED_VIDEO_IMAGE_FILE_SIZE_KB = 150 * 1024;
  /** 信息流视频默认视频文件大小50MB */
  public static final int DEFAULT_FEED_VIDEO_FILE_SIZE = 50 * 1024 * 1024;

  /** 信息流视频默认预览图文件大小150KB */
  public static final int DEFAULT_FEED_VIDEO_IMAGE_FILE_SIZE = 150 * 1024;

  /** 信息流视频默认小图文件大小80KB */
  public static final int DEFAULT_FEED_VIDEO_ICON_FILE_SIZE = 80 * 1024;

  /** 信息流3图文件大小80KB */
  public static final int DEFAULT_FEED_THREE_IMAGE_FILE_SIZE = 80 * 1024;

  /** 信息流大图默认文件大小150KB */
  public static final int DEFAULT_FEED_IMAGE_FILE_SIZE = 150 * 1024;

  /** 应用图标文件大小80KB */
  public static final int DEFAULT_APP_ICON_FILE_SIZE = 80 * 1024;

  /** 原生单图(1080*607)默认文件大小150KB */
  public static final int DEFAULT_NATIVE_IMAGE_1080_607_FILE_SIZE = 150 * 1024;

  /** 原生单图(1080*170)默认文件大小80KB */
  public static final int DEFAULT_NATIVE_IMAGE_1080_170_FILE_SIZE = 80 * 1024;

  public static final int DEFAULT_NATIVE_IMAGE_984_422_FILE_SIZE = 500 * 1024;

  /** 插屏图片文件大小150KB */
  public static final int DEFAULT_INTERSTITIAL_IMAGE_SIZE = 150 * 1024;

  /** 插屏视频文件大小不5M */
  public static final int DEFAULT_INTERSTITIAL_VIDEO_SIZE = 5 * 1024 * 1024;

  public static final List<String> SIGMOB_TRACK_MACRO =
      List.of(
          "__WIDTH__",
          "__HEIGHT__",
          AdMacro.CLICK_DOWN_X,
          AdMacro.CLICK_DOWN_Y,
          AdMacro.CLICK_UP_X,
          AdMacro.CLICK_UP_Y);
  public static final List<String> HUAWEI_TRACK_MACRO =
      List.of(
          Macro.__HW_W__.getName(),
          Macro.__HW_H__.getName(),
          Macro.__HW_DOWN_X__.getName(),
          Macro.__HW_DOWN_Y__.getName(),
          Macro.__HW_UP_X__.getName(),
          Macro.__HW_UP_Y__.getName());

  static {
    APP_INDUSTRY_MAPPING.put("com.taobao.taobao", "3201");
    APP_INDUSTRY_MAPPING.put("com.qiyi.video", "1518");
    APP_INDUSTRY_MAPPING.put("com.sankuai.meituan", "1215");
    APP_INDUSTRY_MAPPING.put("com.moji.mjweather", "1508");
    APP_INDUSTRY_MAPPING.put("com.xueersi.parentsmeeting", "2406");
    APP_INDUSTRY_MAPPING.put("com.iflyrec.tjapp", "1504");
    APP_INDUSTRY_MAPPING.put("com.zhaopin.social", "1230");
    APP_INDUSTRY_MAPPING.put("com.duowan.mobile", "1502");
    APP_INDUSTRY_MAPPING.put("com.smile.gifmaker", "1519");
    APP_INDUSTRY_MAPPING.put("com.jingdong.app.mall", "3201");
    APP_INDUSTRY_MAPPING.put("com.smzdm.client.android", "3204");
    APP_INDUSTRY_MAPPING.put("com.alibaba.wireless", "3201");
    APP_INDUSTRY_MAPPING.put("com.xunmeng.pinduoduo", "3201");
    APP_INDUSTRY_MAPPING.put("com.kuaishou.nebula", "1519");
    APP_INDUSTRY_MAPPING.put("com.baidu.searchbox", "1515");
    APP_INDUSTRY_MAPPING.put("com.smile.gifmake", "1519");
    APP_INDUSTRY_MAPPING.put("com.eg.android.AlipayGphone", "2109");
    APP_INDUSTRY_MAPPING.put("com.baidu.netdisk", "1538");
    APP_INDUSTRY_MAPPING.put("me.ele", "1234");
    APP_INDUSTRY_MAPPING.put("com.yaya.zone", "1234");
    APP_INDUSTRY_MAPPING.put("com.ximalaya.ting.android", "1520");
    APP_INDUSTRY_MAPPING.put("trip.android.view", "1906");
    APP_INDUSTRY_MAPPING.put("com.quark.browser", "1538");
    APP_INDUSTRY_MAPPING.put("com.taobao.idlefish", "3205");
    APP_INDUSTRY_MAPPING.put("com.xingye.app", "1514");
    APP_INDUSTRY_MAPPING.put("com.youku.phone", "1518");
    APP_INDUSTRY_MAPPING.put("com.gotokeep.keep", "1513");
    APP_INDUSTRY_MAPPING.put("com.cainiao.wireless", "1212");
    APP_INDUSTRY_MAPPING.put("com.dianzhong.hmxs", "1516");
    APP_INDUSTRY_MAPPING.put("com.ss.android.ugc.aweme", "1519");
    APP_INDUSTRY_MAPPING.put("com.baidu.searchbox.lite", "1515");
    APP_INDUSTRY_MAPPING.put("com.kmxs.reader", "1516");
    APP_INDUSTRY_MAPPING.put("com.qq.ac.android", "1521");
    APP_INDUSTRY_MAPPING.put("cn.missevan", "1514");
    APP_INDUSTRY_MAPPING.put("com.taobao.trip", "1906");
    APP_INDUSTRY_MAPPING.put("com.shizhuang.duapp", "3201");
  }

  /** 未返回广告原因代码 */
  @AllArgsConstructor
  @Getter
  public enum AdType {
    SPLASH(1),

    NATIVE(3),

    REWARDED_VIDEO(7),

    BANNER(8),

    APP_ICON(9),

    INTERSTITIAL(12),

    AUDIO(17),

    BIG_SCREEN_SPLASH(18),

    NATIVE_FEED(31),

    VIDEO(60);

    final int code;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    UNKNOWN(0),
    PHONE(4),
    TABLET(5),
    LARGE_SCREEN(8);

    final int code;
  }

  /** 运营商类型 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN("Unknown"),
    MOBILE("China Mobile"),
    TELECOM("China Telecom"),
    UNICOM("China Unicom");

    final String name;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    ETHERNET(1),
    WIFI(2),
    CELLULAR_UNKNOWN(3),
    CELLULAR_2G(4),
    CELLULAR_3G(5),
    CELLULAR_4G(6),
    CELLULAR_5G(7);

    final int code;
  }

  /** 广告推广类型 */
  @AllArgsConstructor
  @Getter
  public enum ActionType {
    /** 纯展示，点击后无反应 */
    VIEW(0),
    /** 点击打开网页 */
    WEB_VIEW(1),
    /** 点击下载应用 */
    DOWNLOAD(2),
    /** 点击进入应用内 */
    DEEPLINK(3),

    /** 快应用 */
    FAST_APP(4),

    /** 应用推广(未安装则点击下载应用，已安装 则点击进入应用内) */
    DEEPLINK_AND_DOWNLOAD(5),

    /** 微信小程序 */
    WX_PROGRAM(10);

    final int code;
  }

  /** 广告推广类型 */
  @AllArgsConstructor
  @Getter
  public enum NativeTemplate {
    /** 信息流大图 */
    FEED_IMAGE(1),
    /** 信息流三图 */
    FEED_THREE_IMAGE(3),
    /** 信息流小图 */
    FEED_SMALL_IMAGE(4),
    /** 信息流视频 */
    FEED_VIDEO(6),
    /** 橱窗广告 */
    FEED_WINDOW_AD(29),
    /** 服务卡片 */
    FEED_SERVICE_CARD(30),

    /** 原生单图 */
    NATIVE_IMAGE(9),

    /** 应用图标 */
    APP_ICON(28),

    /** 原生文本-短链 */
    NATIVE_SHORT_LINK(31),
    /** 原生文本-长链 */
    NATIVE_LONG_LINK(32),

    /** 开屏大图片 */
    SPLASH_IMAGE(13),
    /** 开屏视频 */
    SPLASH_VIDEO(15),

    /** 开屏图片 AG */
    SPLASH_IMAGE_AG(37),
    /** 开屏视频 AG */
    SPLASH_VIDEO_AG(38),

    /** 激励视频 */
    REWARDED_VIDEO(17),

    /** Banner 图片 */
    BANNER(18),

    /** 插屏静图 */
    INTERSTITIAL_IMAGE(19),

    /** 插屏视频 */
    INTERSTITIAL_VIDEO(27);

    final int code;
  }

  /** 未返回广告原因代码 */
  @AllArgsConstructor
  @Getter
  public enum Nbr {
    /** 未知错误 */
    UNKNOWN(0),
    /** 无效请求 */
    INVALID_REQUEST(2),
    /** 可疑非人工流量 */
    FAKE_TRAFFIC(4),
    /** 不支持的设备 */
    UNSUPPORTED_DEVICE(6),
    /** 不参与该次竞价 */
    NO_BID(501),
    /** 无有效广告 */
    NO_AD(502);

    final int code;
  }

  /** 宏 */
  @AllArgsConstructor
  @Getter
  public enum Macro {
    /** 价格 */
    AUCTION_PRICE("${AUCTION_PRICE}"),
    /** 币种 */
    AUCTION_CURRENCY("${AUCTION_CURRENCY}"),
    /** 竞标失败原因 */
    AUCTION_LOSE("${AUCTION_LOSS}"),

    __HW_W__("__HW_W__"),

    __HW_H__("__ HW_H__"),

    __HW_DOWN_X__("__HW_DOWN_X__"),

    __HW_DOWN_Y__("__HW_DOWN_Y__"),

    __HW_UP_X__("__HW_UP_X__"),

    __HW_UP_Y__("__HW_UP_Y__");

    final String name;
  }

  /** 广告追踪事件 */
  @AllArgsConstructor
  @Getter
  public enum TrackEvent {
    /** 开始播放事件 */
    PLAY_START("playStart"),
    /** 暂停播放事件 */
    PLAY_PAUSE("playPause"),
    /** 继续播放事件 */
    PLAY_RESUME("playResume"),
    /** 播放结束事件 */
    PLAY_END("playEnd"),
    /** deeplink 跳转成功事件 */
    INTENT_SUCCESS("intentSuccess"),

    DOWNLOAD_START("downloadstart"),

    DOWNLOAD_FINISH("download"),

    INSTALL_FINISH("install"),

    /** 关闭广告事件(例如开屏中点击跳过， 原生广告用户点击关闭按钮，也即dislike 事件) */
    USER_CLOSE("userclose");

    final String name;
  }

  @AllArgsConstructor
  @Getter
  public enum HuaweiOsType {
    HARMONY_ANDROID("harmonyos,android", 4),
    HARMONY_OS("harmonyos", 5);

    final String name;
    final int code;
  }
}
