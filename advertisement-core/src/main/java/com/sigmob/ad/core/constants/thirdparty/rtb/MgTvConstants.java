package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/** <AUTHOR> @Date 2021/5/14 11:20 上午 @Version 1.0 @Description 芒果TV常量 */
public class MgTvConstants {

  /** API版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(3).setMinor(4).setMicro(1).setVersionStr("3.4.1").build();

  public static final String DEFAULT_APP_NAME = "mgtv";

  /** 默认最大请求时间 - 300毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 240;

  /** 默认支持动作类型 */
  public static final List<Integer> supportActionList =
      List.of(
          RtbResponse.InteractType.WEB_VIEW_VALUE,
          RtbResponse.InteractType.APP_DOWNLOAD_VALUE,
          RtbResponse.InteractType.DEEP_LINK_VALUE);

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {AdMacro.TIMESTAMP, AdMacro.TIMEMILLIS},
          Constants.MACRO_INFO_VALUES,
          new String[] {Macro.__TS__.value, Macro.__TS__.value});

  /** 请求流量类型 */
  @AllArgsConstructor
  @Getter
  public enum RequestType {
    /** 站内流量 */
    APP(0),
    /** 联盟流量 */
    UNION(1);

    final int code;
  }

  /** 广告素材类型 */
  @AllArgsConstructor
  @Getter
  public enum CType {
    IMAGE(1),
    VIDEO(2);

    final int code;
  }

  /** 设备平台类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    PC(1),
    ANDROID_TABLET_H5(21),
    ANDROID_PHONE_H5(22),
    IOS_TABLET_H5(23),
    IOS_PHONE_H5(24),
    ANDROID_TABLET_APP(31),
    ANDROID_PHONE_APP(32),
    IOS_TABLET_APP(33),
    IOS_PHONE_APP(34),
    MI_PHONE_APP_SDK(41),
    BAIDU_PHONE_APP_SDK(42),
    OTT(100),
    PC_OUT_SITE(101),
    PC_CLIENT(102);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    WIFI(1),
    CELL_2G(2),
    CELL_3G(3),
    CELL_4G(4),
    CELL_5G(5);

    final int code;
  }

  /** 设备网络类型 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN(-1),

    MOBILE_GSM(0),

    UNICOM_GSM(1),

    MOBILE_TD_S(2),

    TELECOM_CDMA(3),

    INTERNET_TV(4),

    TELECOM_CDMA_2(5),

    UNICOM_WCDMA(6),

    MOBILE_TD_S_2(7);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum TEvent {
    DOWNLOAD_START("dlSt"),
    DOWNLOAD_FINISH("dlFin"),
    INSTALL_START("insSt"),
    INSTALL_FINISH("insFin");

    final String name;
  }

  /** 设备网络类型 */
  @AllArgsConstructor
  @Getter
  public enum Macro {
    __OS__("__OS__"),

    __IP__("__IP__"),

    __UA__("__UA__"),

    __OPENUDID__("__OPENUDID__"),

    __ANDROIDID__("__ANDROIDID__"),

    __IMEI__("__IMEI__"),

    __MAC__("__MAC__"),

    __MAC1___("__MAC1___"),

    __IDFA__("__IDFA__"),

    __UDID__("__UDID__"),

    __ODIN__("__ODIN__"),

    __UID__("__UID__"),

    __BRANCH__("__BRANCH__"),

    __MN__("__MN__"),

    __RS__("__RS__"),

    __VERSION__("__VERSION__"),

    __TS__("__TS__"),

    __SYSTEM__("__SYSTEM__"),

    __M6O__("__M6O__"),

    __IPDX__("__IPDX__"),

    __CHAN__("__CHAN__"),

    __APP__("__APP__"),

    __OAID__("__OAID__");

    final String value;
  }

  /** 设备网络类型 */
  @AllArgsConstructor
  @Getter
  public enum ResponseCode {
    OK(200),

    NO_AD(204);

    final int code;
  }
}
