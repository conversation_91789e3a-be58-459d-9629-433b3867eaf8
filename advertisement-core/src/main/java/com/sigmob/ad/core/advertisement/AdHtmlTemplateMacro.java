package com.sigmob.ad.core.advertisement;

/**
 * <AUTHOR> @Date 2022/6/13 19:30 @Description
 */
public class AdHtmlTemplateMacro {

  public static final String MARCO_ENDCARD_CONFIG = "{{ENDCARD_CONFIG}}";

  /**
   * <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361">媒体维度误点概率配置</a>
   */
  public static final String MARCO_HIT_PERCENT = "{{HIT_PERCENT}}";

  public static final String MACRO_TEMPLATE_CONFIG = "{{TEMPLATE_CONFIG}}";

  public static final String MACRO_ICON_IMG = "{{ICON_IMG.val}}";

  public static final String MACRO_TITLE = "{{TITLE.val}}";

  public static final String MACRO_BTN_TXT = "{{BTN_TXT.val}}";

  public static final String MACRO_BTN_BG_COLOR = "{{BTN_BG_COLOR.val}}";

  public static final String MACRO_STAR = "{{STAR.val}}";

  public static final String MACRO_BTN_TXT_COLOR = "{{BTN_TXT_COLOR.val}}";

  public static final String MACRO_CONTENT = "{{CONTENT.val}}";

  /** mraid 2参数 */
  public enum Mraid2TemplateField {
    ICON,
    TITLE,
    DESC,
    STAR,
    PROD_NAME,
    IMG,
    VIDEO,
    ENDCARD_IMG,
    OPEN_URL,
    PRIVACY_INFO,
    PRIVACY_INFO_URL,
    MACRO_CONFIG,
    HIT_PERCENT,
    GLOBAL_CLICK,
    DECORATE,
    SENSITIVITY,
    BTN_TXT,
    BTN_BG_COLOR
  }

  public enum MraidTemplateField {
    BODY_BG_IMG,
    ICON_IMG,
    TITLE,
    STAR,
    DESCRIBE,
    BTN_TXT,
    IMG_WIDTH,
    IMG_HEIGHT,
    DECORATE,
    HIT_PERCENT,
    SENSITIVITY
  }
}
