package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全屏广告位支持素材类型
 *
 * <p>0: 仅支持视频(默认值)<br>
 * 1: 仅支持图片<br>
 * 2: 支持图片和视频
 */
@AllArgsConstructor
@Getter
public enum MaterialType {
  ONLY_VIDEO(0),
  ONLY_IMAGE(1),
  VIDEO_AND_IMAGE(2);
//  IMAGE_AND_TEXT(3),
//  HTML_URL(4),
//  HTML_SRC(5),
//  TEXT_LINK(6),
//  VIDEO_AND_HTML_URL(7),
//  VIDEO_AND_HTML_SRC(8),
//  NATIVE(9);

  final int code;
}
