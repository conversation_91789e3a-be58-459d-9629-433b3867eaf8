package com.sigmob.ad.core.advertisement.model;

import com.google.gson.annotations.Expose;
import java.util.List;
import lombok.Data;

@Data
public class DspTemplateShieldV2 {

  /** 模板id */
  @Expose private Integer templateId;

  private List<ShieldInfo> list;

  @Data
  public static class ShieldInfo {

    private MediaShield mediaShield;

    private SdkVersionShield sdkVersionShield;

    private GlobalShield globalShield;

    private List<String> regionCodeList;
  }

  @Data
  public static class MediaShield {

    private List<Integer> appSheildList;

    private List<String> slotSheildList;
  }

  @Data
  public static class SdkVersionShield {

    private List<VersionInfo> androidList;

    private List<VersionInfo> iosList;
  }

  @Data
  public static class VersionInfo {

    /** 类型：1:>= 2: =， 3<= */
    private Integer type;

    private String version;
  }

  @Data
  public static class GlobalShield {

    /** android ：2， ios ：1 */
    private List<Integer> globalList;
  }
}
