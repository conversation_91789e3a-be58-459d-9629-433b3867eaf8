package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.ssp.model.gromore.GromoreAdx;
import com.sigmob.ad.ssp.model.gromore.GromoreAdx.AdType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2024/4/25 下午5:21 @Description
 */
public class GromoreConstants {

  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  public static final int MAX_FEED_LARGE_IMAGE_FILE_SIZE = 500 * 1024;

  public static final int MAX_FEED_VIDEO_IMAGE_FILE_SIZE = 300 * 1024;

  public static final int MAX_ICON_IMAGE_FILE_SIZE = 100 * 1024;

  public static final int MAX_RV_VIDEO_FILE_SIZE = 100 * 1024 * 1024;

  public static final int MIN_VIDEO_DURATION = 4;

  public static final int MAX_VIDEO_DURATION = 30;

  public static final List<AdType> SPLASH_AD_TYPE =
      List.of(AdType.UNION_SPLASH_LP, AdType.UNION_SPLASH_APP);

  // UNION_FEED_LP_LARGE
  // UNION_LP_VIDEO
  // UNION_LP_VERTICAL_VIDEO
  public static final List<AdType> RV_AD_TYPE =
      List.of(
          AdType.UNION_LP_VIDEO,
          AdType.UNION_APP_VIDEO,
          AdType.UNION_LP_VERTICAL_VIDEO,
          AdType.UNION_APP_VERTICAL_VIDEO);

  public static final List<AdType> FEED_AD_TYPE =
      List.of(
          AdType.UNION_FEED_LP_LARGE,
          AdType.UNION_FEED_LP_SMALL,
          AdType.UNION_FEED_LP_GROUP,
          AdType.UNION_FEED_APP_LARGE,
          AdType.UNION_FEED_APP_SMALL,
          AdType.UNION_FEED_APP_GROUP,
          AdType.UNION_LP_VIDEO,
          AdType.UNION_APP_VIDEO,
          AdType.UNION_LP_VERTICAL_VIDEO,
          AdType.UNION_APP_VERTICAL_VIDEO);

  public static final List<AdType> INTERSTITIAL_AD_TYPE =
      List.of(
          AdType.UNION_FEED_LP_LARGE,
          AdType.UNION_FEED_APP_LARGE,
          AdType.UNION_LP_VIDEO,
          AdType.UNION_APP_VIDEO,
          AdType.UNION_LP_VERTICAL_VIDEO,
          AdType.UNION_APP_VERTICAL_VIDEO);

  public static final List<Integer> SUPPORT_UNION_AD_TYPE =
      List.of(
          UnionAdSlotType.INTERSTITIAL.getCode(),
          UnionAdSlotType.SPLASH.getCode(),
          UnionAdSlotType.PRE_CACHED_SPLASH.getCode(),
          UnionAdSlotType.FEED.getCode(),
          UnionAdSlotType.PRE_MOVIE.getCode(),
          UnionAdSlotType.REWARDED_VIDEO.getCode(),
          UnionAdSlotType.FULLSCREEN.getCode(),
          UnionAdSlotType.DRAW_VIDEO.getCode());

  public static final List<String> SIGMOB_TRACK_MACRO = List.of(AdMacro.TIMEMILLIS);

  public static final List<String> GROMORE_TRACK_MACRO = List.of(Macro.TIMESTAMP.getName());

  @AllArgsConstructor
  @Getter
  public enum UnionAdSlotType {
    BANNER(1),

    INTERSTITIAL(2),

    SPLASH(3),

    PRE_CACHED_SPLASH(4),

    FEED(5),

    PRE_MOVIE(6),

    REWARDED_VIDEO(7),

    FULLSCREEN(8),

    DRAW_VIDEO(9);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum AdTypeInfo {
    UNION_FEED_LP_LARGE(GromoreAdx.AdType.UNION_FEED_LP_LARGE, 1280, 720);

    final AdType adType;
    final int width;
    final int height;
  }

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN("Unknown"),
    MOBILE("China Mobile"),
    TELECOM("China Telecom"),
    UNICOM("China Unicom");

    final String name;
  }

  @AllArgsConstructor
  @Getter
  public enum Macro {
    TIMESTAMP("{timestamp}");

    final String name;
  }
}
