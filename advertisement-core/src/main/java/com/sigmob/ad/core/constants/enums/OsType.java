package com.sigmob.ad.core.constants.enums;

import lombok.Getter;

/**
 * 手机操作系统枚举类：1-ios, 2-android, 3-windows phone
 *
 * <AUTHOR>
 */
public enum OsType {
  /** iOS */
  IOS(1, "iOS"),
  ANDRO<PERSON>(2, "Android"),
  WP(3, "Windows Phone"),
  /** 鸿蒙harmonyos */
  HARMONY_OS(4, "harmonyos"),
  ;

  final int type;

  @Getter final String name;

  OsType(int type, String name) {
    this.type = type;
    this.name = name;
  }

  public static boolean isValidType(int typeNum) {
    OsType[] types = values();
    for (OsType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static OsType getType(int i) {
    OsType[] types = values();
    for (OsType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return null;
  }

  public int getTypeNum() {
    return this.type;
  }

  /** 是否鸿蒙系统 */
  public static boolean isHarmony(String os) {
    return (HARMONY_OS.getName().equalsIgnoreCase(os) || "harmonyos,android".equalsIgnoreCase(os));
  }
}
