package com.sigmob.ad.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2021/6/21 8:06 下午 @Version 1.0 @Description
 */
public class HeaderBiddingConstants {

  /** 聚合 track url结算价格宏 */
  public static final String WINDMILL_SIGMOB_BID_PRICE_MACRO = "_TO_BID_SIG_PRICE_";

  /** 是否测试流量 */
  @AllArgsConstructor
  @Getter
  public enum Test {
    /** 否 */
    NO(0),
    YES(1);

    private final int code;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    /** 手机 */
    PHONE(1),
    TABLET(2);

    private final int code;
  }

  /** 是否支持https */
  @AllArgsConstructor
  @Getter
  public enum SupportHttps {
    /** 否 */
    NO(0),
    YES(1);

    private final int code;
  }

  public enum CallBackMacro {
    // hb竞价失败原因
    _BIDLOSSCODE_,

    // hb我方竞价失败时，本次竞胜方adnid
    _WINADNID_,

    // win url竞胜 瀑布流次高价
    __HIGHEST_LOSS_PRICE__,

    // win & lose url 币种
    __CURRENCY__
  }

  /** hb version */
  @AllArgsConstructor
  @Getter
  public enum HbVersion {
    // 1.0 协议
    V_1_0("1.0", "server-api"),
    V_2_0("2.0", "server-api"),
    V_2_1("2.1", "server-api"),

    V_C_1_O("1.0", "client"),

    V_TO_BID_1_O("1.0", "server-tobid");

    private final String code;

    private final String source;
  }

  @AllArgsConstructor
  @Getter
  public enum Timing {
    //
    REQ("ad_request"),
    LOAD("ad_load");

    private final String code;
  }
}
