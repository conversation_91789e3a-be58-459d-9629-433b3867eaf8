package com.sigmob.ad.core.advertisement;

import com.google.common.base.Strings;
import com.sigmob.ad.adx.rpc.grpc.AbExperiment;
import com.sigmob.ad.adx.rpc.grpc.DspTrafficParamUsage;
import com.sigmob.ad.adx.rpc.grpc.RtbRequest;
import com.sigmob.ad.core.constants.ABTestConstants;
import com.sigmob.ad.core.constants.enums.AdxSellType;
import com.sigmob.ad.core.constants.enums.DspParamUsage;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.dspmanagement.TradingType;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.sigdsp.pb.AdSlot;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * Dsp处理相关工具方法
 *
 * <AUTHOR> @Date 2023/2/1 15:14 @Description
 */
public class DspUtil {

  /** 最大请求dsp时间 */
  private static final int MAX_DSP_REQUEST_TIME = 800;

  /**
   * dsp请求超时时间
   *
   * @param rtbRequest
   * @return int
   */
  public static int getDspReqTimeout(RtbRequest rtbRequest) {
    var requestFlowType = rtbRequest.getRequestFlowType();
    var requestTimeout = rtbRequest.getRequestTimeout();

    //    if (rtbRequest.getHbType() != HeaderBiddingType.NOT_HB.getCode()) {
    //      return rtbRequest.getRequestTimeout();
    //    }
    var isSdkFlowType = requestFlowType == RequestFlowType.SDK.getCode();
    if (requestTimeout == 0) {
      if (isSdkFlowType) {
        requestTimeout = RtbConstants.DEFAULT_DSP_REQUEST_TIME_MILLIS;
      } else {
        requestTimeout = 300;
      }
    }
    var reservedTimeout = requestTimeout - (isSdkFlowType ? 50 : 30);
    requestTimeout = reservedTimeout <= 10 ? requestTimeout : reservedTimeout;
    return Math.min(requestTimeout, MAX_DSP_REQUEST_TIME);
  }

  /** 是否使用用户dsp广告请求过滤策略 */
  public static boolean isUserReqFilter(RtbRequest rtbRequest) {

    return null != rtbRequest
        && rtbRequest.getAbExperimentsCount() > 0
        && rtbRequest.getAbExperimentsList().stream()
            .anyMatch(
                abe ->
                    abe.getIsEffect()
                        && ABTestConstants.AB_EXPERIMENT_USER_REQ_FILTER.equals(
                            abe.getAbGroupName()));
  }

  /**
   * 获取请求获取实验
   *
   * @param rtbRequest {@link RtbRequest}
   * @return {@link AbExperiment}
   */
  public static AbExperiment getReqFilterExperiment(RtbRequest rtbRequest) {
    return rtbRequest.getAbExperimentsList().stream()
        .filter(
            abe ->
                abe.getIsEffect()
                    && ABTestConstants.AB_EXPERIMENT_USER_REQ_FILTER.equals(abe.getAbGroupName()))
        .findFirst()
        .orElse(null);
  }

  public static boolean isPddStandardDsp(int dspId) {
    return dspId == 2111 || dspId == 2115 || dspId == 2116 || dspId == 2154;
  }

  /** 计算给dsp包含返货的成加价 */
  public static int getDspRebatePrice(
      int dspWinPrice, int dspRebateRatio, TradingType tradingType, int tbaBidPrice) {
    if (TradingType.DEAL.equals(tradingType) || TradingType.PDB.equals(tradingType)) {
      return tbaBidPrice;
    } else {
      return new BigDecimal(dspWinPrice)
          .divide(new BigDecimal(100 - dspRebateRatio), 2, RoundingMode.FLOOR)
          .multiply(new BigDecimal(100))
          .intValue();
    }
  }

  /** dsp使用流量参数配置 */
  public static Optional<DspTrafficParamUsage.Builder> getDspTrafficParamUsage(
      com.sigmob.ad.core.advertisement.model.DspTrafficParamUsage dspTrafficParamUsage,
      boolean isTobidAdx) {

    if (null != dspTrafficParamUsage) {
      var paramUsageBuilder = DspTrafficParamUsage.newBuilder();
      var defaultValue =
          isTobidAdx ? DspParamUsage.PASS_THROUGH.getCode() : DspParamUsage.DSP_CONFIG.getCode();

      var paramAppId = dspTrafficParamUsage.getParamAppId();
      paramUsageBuilder.setParamAppId(Objects.requireNonNullElse(paramAppId, defaultValue));
      if (paramUsageBuilder.getParamAppId() == DspParamUsage.DEFINED.getCode()) {
        var definedAppId = dspTrafficParamUsage.getDefinedAppId();
        if (!Strings.isNullOrEmpty(definedAppId)) {
          paramUsageBuilder.setDefinedAppId(definedAppId);
        }
      }

      var paramAppName = dspTrafficParamUsage.getParamAppName();
      paramUsageBuilder.setParamAppName(Objects.requireNonNullElse(paramAppName, defaultValue));
      if (paramUsageBuilder.getParamAppName() == DspParamUsage.DEFINED.getCode()) {
        var definedAppName = dspTrafficParamUsage.getDefinedAppName();
        if (!Strings.isNullOrEmpty(definedAppName)) {
          paramUsageBuilder.setDefinedAppName(definedAppName);
        }
      }

      var paramAppBundle = dspTrafficParamUsage.getParamAppBundle();
      paramUsageBuilder.setParamAppBundle(Objects.requireNonNullElse(paramAppBundle, defaultValue));
      if (paramUsageBuilder.getParamAppBundle() == DspParamUsage.DEFINED.getCode()) {
        var definedAppBundle = dspTrafficParamUsage.getDefinedAppBundle();
        if (!Strings.isNullOrEmpty(definedAppBundle)) {
          paramUsageBuilder.setDefinedAppBundle(definedAppBundle);
        }
      }

      var paramPlacementId = dspTrafficParamUsage.getParamPlacementId();
      paramUsageBuilder.setParamPlacementId(
          Objects.requireNonNullElse(paramPlacementId, defaultValue));
      if (paramUsageBuilder.getParamPlacementId() == DspParamUsage.DEFINED.getCode()) {
        var definedPlacementId = dspTrafficParamUsage.getDefinedPlacementId();
        if (!Strings.isNullOrEmpty(definedPlacementId)) {
          paramUsageBuilder.setDefinedPlacementId(definedPlacementId);
        }
      }

      var paramPlacementName = dspTrafficParamUsage.getParamPlacementName();
      paramUsageBuilder.setParamPlacementName(
          Objects.requireNonNullElse(paramPlacementName, defaultValue));
      if (paramUsageBuilder.getParamPlacementName() == DspParamUsage.DEFINED.getCode()) {
        var definedPlacementName = dspTrafficParamUsage.getDefinedPlacementName();
        if (!Strings.isNullOrEmpty(definedPlacementName)) {
          paramUsageBuilder.setDefinedPlacementName(definedPlacementName);
        }
      }

      var thirdAdType = dspTrafficParamUsage.getThirdAdType();
      paramUsageBuilder.setThirdAdType(Objects.requireNonNullElse(thirdAdType, 0));

      return Optional.of(paramUsageBuilder);
    }

    return Optional.empty();
  }

  /** dsp请求使用媒体媒体id */
  public static int dspRequestDspAppIdUsage(RtbRequest rtbRequest) {

    var sellType = rtbRequest.getDspInfo().getSellType();

    int paramUsage =
        sellType == AdxSellType.NORMAL.getCode()
            ? DspParamUsage.DSP_CONFIG.getCode()
            : DspParamUsage.PASS_THROUGH.getCode();

    return rtbRequest.hasDspTrafficParamUsage()
        ? rtbRequest.getDspTrafficParamUsage().getParamAppId()
        : paramUsage;
  }

  /** 根据dsp使用流量参数配置获取请求dsp的appid */
  public static String getRequestDspAppId(RtbRequest rtbRequest, int paramUsage) {

    return paramUsage == DspParamUsage.PASS_THROUGH.getCode()
        ? rtbRequest.getBidRequest().getApp().getAppId()
        : paramUsage == DspParamUsage.DEFINED.getCode()
            ? rtbRequest.getDspTrafficParamUsage().getDefinedAppId()
            : rtbRequest.getDspApp().getApp();
  }

  /** dsp请求使用媒体广告位id */
  public static int dspRequestDspPlacementIdUsage(RtbRequest rtbRequest) {

    var sellType = rtbRequest.getDspInfo().getSellType();

    int paramUsage =
        sellType == AdxSellType.NORMAL.getCode()
            ? DspParamUsage.DSP_CONFIG.getCode()
            : DspParamUsage.PASS_THROUGH.getCode();

    return rtbRequest.hasDspTrafficParamUsage()
        ? rtbRequest.getDspTrafficParamUsage().getParamPlacementId()
        : paramUsage;
  }

  /** 根据dsp使用流量参数配置获取请求dsp的placement id */
  public static String getRequestDspPlacementId(RtbRequest rtbRequest, int paramUsage) {

    return paramUsage == DspParamUsage.PASS_THROUGH.getCode()
        ? rtbRequest.getBidRequest().getSlots(0).getAdslotId()
        : paramUsage == DspParamUsage.DEFINED.getCode()
            ? rtbRequest.getDspTrafficParamUsage().getDefinedPlacementId()
            : rtbRequest.getDspApiAdSlot().getAdSlot();
  }

  /** 请求dsp广告类型 */
  public static int getRequestDspAdType(RtbRequest rtbRequest) {
    if (rtbRequest.hasDspTrafficParamUsage()) {
      var dspTrafficParamUsage = rtbRequest.getDspTrafficParamUsage();
      var thirdAdType = dspTrafficParamUsage.getThirdAdType();
      if (thirdAdType != 0) {
        return thirdAdType;
      }
    }
    if (rtbRequest.hasDspApiAdSlot()) {
      return rtbRequest.getDspApiAdSlot().getAdSlotType();
    }
    var adSlot = rtbRequest.getBidRequest().getSlots(0);
    var transAdType = adSlot.getTransAdType();
    if (transAdType > 0) {
      return transAdType;
    } else {
      return adSlot.getAdslotType(0);
    }
  }

  /** dsp请求使用媒体名称方式 */
  public static int dspRequestAppNameUsage(RtbRequest rtbRequest) {

    var sellType = rtbRequest.getDspInfo().getSellType();

    int paramUsage =
        sellType == AdxSellType.NORMAL.getCode()
            ? DspParamUsage.DSP_CONFIG.getCode()
            : DspParamUsage.PASS_THROUGH.getCode();

    return rtbRequest.hasDspTrafficParamUsage()
        ? rtbRequest.getDspTrafficParamUsage().getParamAppName()
        : paramUsage;
  }

  /** 根据dsp使用流量参数配置获取请求dsp的媒体名称 */
  public static String getRequestDspAppName(RtbRequest rtbRequest, int paramUsage) {

    return paramUsage == DspParamUsage.PASS_THROUGH.getCode()
        ? rtbRequest.getBidRequest().getApp().getName()
        : paramUsage == DspParamUsage.DEFINED.getCode()
            ? rtbRequest.getDspTrafficParamUsage().getDefinedAppName()
            : rtbRequest.getDspApp().getName();
  }

  /** dsp请求使用媒体包名方式 */
  public static int dspRequestAppBundleUsage(RtbRequest rtbRequest) {

    var sellType = rtbRequest.getDspInfo().getSellType();

    int defaultParamUsage =
        sellType == AdxSellType.NORMAL.getCode()
            ? DspParamUsage.DSP_CONFIG.getCode()
            : DspParamUsage.PASS_THROUGH.getCode();

    int paramUsage =
        rtbRequest.hasDspTrafficParamUsage()
            ? rtbRequest.getDspTrafficParamUsage().getParamAppBundle()
            : defaultParamUsage;
    if (rtbRequest.getDspInfo().getDspProtocolType() == DspProtocolType.YYB.getType()) {
      return paramUsage;
    }
    if (sellType == AdxSellType.NORMAL.getCode()) {
      var requestFlowType = rtbRequest.getRequestFlowType();
      var dspId = rtbRequest.getDspId();
      if (requestFlowType == RequestFlowType.HUAWEI.getCode()
          || requestFlowType == RequestFlowType.OPPO.getCode()
          || dspId == 2068) {
        return DspParamUsage.PASS_THROUGH.getCode();
      }
    }
    return paramUsage;
  }

  /** 根据dsp使用流量参数配置获取请求dsp的app bundle */
  public static String getRequestDspAppBundle(RtbRequest rtbRequest, int paramUsage) {

    return paramUsage == DspParamUsage.PASS_THROUGH.getCode()
        ? rtbRequest.getBidRequest().getApp().getAppPackage()
        : paramUsage == DspParamUsage.DEFINED.getCode()
            ? rtbRequest.getDspTrafficParamUsage().getDefinedAppBundle()
            : rtbRequest.getDspApp().getPackageName();
  }
}
