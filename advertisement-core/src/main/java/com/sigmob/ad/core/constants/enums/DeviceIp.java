package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** <AUTHOR> @Date 2021/7/19 10:21 上午 @Version 1.0 @Description */
@Getter
@AllArgsConstructor
public enum DeviceIp {
  EN0_IPV4("en0/ipv4"),
  EN0_IPV6("en0/ipv6"),
  EN2_IPV4("en2/ipv4"),
  EN2_IPV6("en2/ipv6"),
  PDP_IP0_IPV4("pdp_ip0/ipv4"),
  PDP_IP0_IPV6("pdp_ip0/ipv6"),
  PDP_IP1_IPV6("pdp_ip1/ipv6"),
  PDP_IP2_IPV6("pdp_ip2/ipv6"),
  UTUN0_IPV6("utun0/ipv6"),
  UTUN1_IPV6("utun1/ipv6"),
  IPSEC0_IPV6("ipsec0/ipv6"),
  IPSEC1_IPV6("ipsec1/ipv6"),
  IPSEC2_IPV6("ipsec2/ipv6"),
  IPSEC3_IPV6("ipsec3/ipv6");

  final String name;
}
