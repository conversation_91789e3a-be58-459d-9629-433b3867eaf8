package com.sigmob.ad.core.exception;

/** dsp请求业务异常 */
public class DspBusinessException extends AdException {

  public DspBusinessException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public DspBusinessException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public DspBusinessException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public DspBusinessException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public DspBusinessException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public DspBusinessException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public DspBusinessException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public DspBusinessException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public DspBusinessException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public DspBusinessException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
