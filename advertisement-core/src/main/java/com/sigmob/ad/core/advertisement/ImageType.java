package com.sigmob.ad.core.advertisement;

/**
 * 图片格式：0-未知；1-jpg、jpeg；2-png；3-gif
 *
 * <AUTHOR>
 */
public enum ImageType {
  /** unknown */
  UNKNOWN(0),
  /** 1=JPG JPEG； */
  JPG(1),
  JPEG(1),
  /** PNG */
  PNG(2),
  /** GIF */
  GIF(3);
  private final int type;

  ImageType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    ImageType[] types = values();
    for (ImageType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static ImageType getType(int i) {
    ImageType[] types = values();
    for (ImageType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return JPG;
  }
}
