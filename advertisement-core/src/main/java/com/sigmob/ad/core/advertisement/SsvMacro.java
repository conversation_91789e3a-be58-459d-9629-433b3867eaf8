package com.sigmob.ad.core.advertisement;

/** 激励视屏回调宏 */
public class SsvMacro {

  /** 奖励数量 */
  public static final String REWARD_AMOUNT = "{{REWARD_AMOUNT}}";

  /** 奖励名称 */
  public static final String REWARD_NAME = "{{REWARD_NAME}}";

  /** 交易的id */
  public static final String TRANS_ID = "{{TRANS_ID}}";

  /** 媒体应用的用户唯一标识符。Sigmob使用它来知道成功完成广告后要奖励的用 */
  public static final String USER_ID = "{{USER_ID}}";

  /** 签名 */
  public static final String SIGN = "{{SIGN}}";

  /** 额外信息 */
  public static final String EXTRA_INFO = "{{EXTRAINFO}}";

  /** sigmob广告位id */
  public static final String PLACEMENT_ID = "{{PLACEMENT_ID}}";

  public static final String CURRENCY = "{{CURRENCY}}";

  public static final String ECPM = "{{ECPM}}";
}
