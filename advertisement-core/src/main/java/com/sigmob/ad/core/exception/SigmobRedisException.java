package com.sigmob.ad.core.exception;

/**
 * <AUTHOR>
 */
public class SigmobRedisException extends AdException {

  public SigmobRedisException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public SigmobRedisException(int code) {
    super();
    this.code = code;
  }

  public SigmobRedisException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SigmobRedisException(int code, String message) {
    super(message);
    this.code = code;
  }

  public SigmobRedisException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SigmobRedisException(int code, String message, Throwable cause) {
    super(message, cause);
    this.code = code;
  }

  public SigmobRedisException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SigmobRedisException(int code, Throwable cause) {
    super(cause);
    this.code = code;
  }

  public SigmobRedisException(
      int code, String message, boolean enableSuppression, boolean writableStackTrace) {
    this(code, message, null, enableSuppression, writableStackTrace);
  }

  public SigmobRedisException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SigmobRedisException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
