package com.sigmob.ad.core.advertisement.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * 日志开关标识
 *
 * <AUTHOR> @Date 2022/8/24 16:09 @Description
 */
@Getter
@Setter
@AllArgsConstructor
public class LogSwitch {

  public static final LogSwitch ALL_ENABLE_LOG = new LogSwitch(true, true);

  public static final LogSwitch ALL_DISABLE_LOG = new LogSwitch(false, false);

  /** true: 阿里云日志开 */
  private boolean aliyunLog;

  /** true: elk日志开 */
  private boolean elkLog;
}
