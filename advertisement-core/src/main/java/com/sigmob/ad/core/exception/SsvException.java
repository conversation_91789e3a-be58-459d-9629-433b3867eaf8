package com.sigmob.ad.core.exception;

/** 服务端验证接口异常 */
public class SsvException extends AdException {

  public SsvException() {
    this(0);
  }

  public SsvException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public SsvException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SsvException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public SsvException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SsvException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public SsvException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SsvException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public SsvException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SsvException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
