package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse.InteractType;
import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/** 懂球帝常量定义 */
public class DongqiudiConstants {

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  /** 懂球帝 rtb api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(6).setMicro(0).setVersionStr("1.6.0").build();

  /** 结算货币 - 人民币 */
  public static final String CURRENCY = "CNY";

  /** iOS 开屏 app_loading 默认支持广告动作类型 */
  public static final List<Integer> IOS_DEFAULT_ACTION_LIST =
      List.of(
          InteractType.WEB_VIEW_VALUE,
          InteractType.APP_DOWNLOAD_VALUE,
          InteractType.DEEP_LINK_VALUE);

  /** Android 开屏 app_loading 默认支持广告动作类型 */
  public static final List<Integer> ANDROID_DEFAULT_ACTION_LIST =
      List.of(InteractType.WEB_VIEW_VALUE, InteractType.DEEP_LINK_VALUE);

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {
            AdMacro.CLICK_DOWN_X, AdMacro.CLICK_DOWN_Y, AdMacro.CLICK_UP_X, AdMacro.CLICK_UP_Y
          },
          Constants.MACRO_INFO_VALUES,
          new String[] {
            Macro.__DOWN_X__.getValue(),
            Macro.__DOWN_Y__.getValue(),
            Macro.__UP_X__.getValue(),
            Macro.__UP_Y__.getValue()
          });

  /** 响应代码 */
  @AllArgsConstructor
  @Getter
  public enum ResultCode {

    /** 未知错误 */
    UNKNOWN_ERROR(0),

    /** 技术原因 */
    TECHNICAL_REASON(1),

    /** 无效请求 */
    INVALID_REQUEST(2),

    /** 网络爬虫 */
    WEB_CRAWLER(3),

    /** 疑似非人为流量 */
    ILLEGAL_TRAFFIC(4),

    /** 云端,数据中心, 代理 IP */
    PROXY_IP(5),

    /** 设备不支持 */
    DEVICE_TYPE_NOT_SUPPORT(6),

    /** 被屏蔽的流量 */
    BLOCKED_TRAFFIC(7),

    /** 用户不匹配 */
    USER_NOT_MATCH(8),

    /** 达到每日上限 */
    DAILY_REQUEST_LIMIT(9),

    /** 每日域名上限 */
    DAILY_DOMAIN_REQUEST_LIMIT(10);

    private final int code;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    PHONE(3),
    PAD(4);

    final int code;
  }

  /** 运营商名称 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    MOBILE("mobile"),
    UNICOM("unicom"),
    TELECOM("telecom");

    final String name;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    WIFI(2),
    CELL_2G(4),
    CELL_3G(5),
    CELL_4G(6);

    final int code;
  }

  /** 广告位支持图片元素 ID */
  @AllArgsConstructor
  @Getter
  public enum ImgType {
    ICON(1),
    MAIN(3);

    final int code;
  }

  /** 图片格式 */
  @AllArgsConstructor
  @Getter
  public enum ImgFormat {
    JPG("image/jpg"),
    PNG("image/png"),
    GIF("image/gif");

    final String format;
  }

  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum AdType {
    APP_LOADING("app_loading"),
    BIG_PIC_1("big_picture_1"),
    BIG_PIC_2("big_picture_2"),
    THREE_PIC("three_picture"),
    PIC_TXT("picture_txt"),
    VIDEO("video");

    final String name;
  }

  /** 宏 */
  @AllArgsConstructor
  @Getter
  public enum Macro {
    __DOWN_X__("__DOWN_X__"),
    __DOWN_Y__("__DOWN_Y__"),
    __UP_X__("__UP_X__"),
    __UP_Y__("__UP_Y__"),
    __WIN_PRICE__("__WIN_PRICE__"),
    __PRICING__("__PRICING__");

    final String value;
  }
}
