package com.sigmob.ad.core.constants.enums;

import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BaseRvTemplate {
  BASE_ORIGIN_TEMPLATE_10(10),

  BASE_MRAID_TEMPLATE_50(50),

  BASE_MRAID_TEMPLATE_51(51),

  BASE_MRAID_TEMPLATE_52(52),

  BASE_ORIGIN_TEMPLATE_109(109),

  BASE_MRAID_TEMPLATE_100049(100049),

  BASE_MRAID_TEMPLATE_100115(100115),

  BASE_MRAID_TEMPLATE_100129(100129),

  BASE_MRAID_TEMPLATE_100203(100203),

  BASE_MRAID_TEMPLATE_100327(100327);

  public static List<Integer> idList =
      List.of(10, 50, 51, 52, 109, 100049, 100115, 100129, 100203, 100327);
  final int id;
}
