package com.sigmob.ad.core.publishing.mediation.sdkstrategy;

import com.sigmob.ssp.pb.mediation.Strategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * strategy业务对象，封装返回的策略列表和最大并行请求数配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StrategyBO {

    private Integer maxConcurrentCount;

    private List<Strategy> strategyList;
}
