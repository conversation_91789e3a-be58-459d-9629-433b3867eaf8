package com.sigmob.ad.core.network.cnaaip;

public class Constants {

//	public static final String DATA_PATH = "/Users/<USER>/Downloads/data";
	public static final String CNAA_BASE_IP_FILE = "cnaa_ip.csv";

	public static final String CNAA_BASE_IPV6_FILE = "cnaa_ipv6.csv";
//	public static final String COUNTY_REGION_FILE = "cnaa_county.csv";
	public static final String COUNTY_REGION_FILE = "cnaa_city.csv";
//	public static final String CNAA_UNIVERSITY_FILE = "cnaa_university.csv";
	public static final String COUNTRY_CODE_FILE = "country_iso3361.csv";
	/**
	 * 直辖市因为定位到了省级，为了统计方便，需要把直辖市加入到市级地域编码中
	 */
	public static final String[][] MUNICIPALITIES = { { "1156110000", "北京市", "中国大陆" }, { "1156120000", "天津市", "中国大陆" },
			{ "1156500000", "重庆市", "中国大陆" }, { "1156310000", "上海市", "中国大陆" } };
	
	/**
	 * 国家级别的代码（region code）以6个0结尾
	 */
	public static final String REGION_CODE_COUNTRY_SUFFIX="000000";
	/**
	 * 省级的代码以4个0结尾
	 */
	public static final String REGION_CODE_PROVINCE_SUFFIX="0000";
	
	/**
	 * 市级代码以2个0结尾
	 */
	public static final String REGION_CODE_CITY_SUFFIX="00";
	
}
