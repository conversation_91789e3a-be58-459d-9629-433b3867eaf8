package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 广告竞价出价类型
 *
 * <p>1-rtb渠道实时出价;<br>
 * 2-deal约定价格<br>
 * 3-中控的运营期望价格配置<br>
 * 4-ecpm算法预估<br>
 * 5-请求底价的保底配置<br>
 * 6-底价兜底配置<br>
 * 8-rtb提价系数提价<br>
 * 10-pdb<br>
 */
@Getter
@AllArgsConstructor
public enum BidPriceType {
  RTB(1),
  DEAL(2),
  OPERATOR_EXPECTED_PRICE(3),
  ESTIMATE_ECPM(4),
  REQUEST_FLOW_PRICE(5),
  BASE_BID_PRICE(6),
  // rtb 提价
  RTB_EXTRA_BID_PRICE(8),
  PDB(10);
  //  REBATE(9);

  final int code;
}
