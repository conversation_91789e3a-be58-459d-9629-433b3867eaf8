/**
 * 
 */
package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Strings;

/**
 * cnaa地址库中，ip范围和region code的对应关系
 * 
 * <AUTHOR>
 *
 */
public class IpV4Name extends IpName implements Comparable<IpV4Name> {

	private Long upperIpNum;
	private Long lowerIpNum;
	private String name;

	/**
	 * 如果lowerIp upperIp regionCode 任意参数为空，则返回null
	 *
	 * @param lowerIp
	 * @param upperIp
	 * @param regionCode
	 * @return
	 */
	public static IpV4Name build(String lowerIp, String upperIp, String regionCode) {
		if (Strings.isNullOrEmpty(lowerIp) || Strings.isNullOrEmpty(upperIp) || Strings.isNullOrEmpty(regionCode)) {
			return null;
		}
		Long lowerIpNum = Utils.ipStrToLong(lowerIp.trim());

		if (lowerIpNum == null) {
			return null;
		}
		Long upperIpNum = Utils.ipStrToLong(upperIp.trim());

		if (upperIpNum == null) {
			return null;
		}
		IpV4Name irc = new IpV4Name();
		irc.lowerIpNum = lowerIpNum;
		irc.upperIpNum = upperIpNum;
		irc.name = regionCode.trim();
		return irc;

	}

	@Override
	public int compareTo(IpV4Name o) {
		return upperIpNum.compareTo(o.upperIpNum);
	}

	public Long getUpperIpNum() {
		return upperIpNum;
	}

	public void setUpperIpNum(Long upperIpNum) {
		this.upperIpNum = upperIpNum;
	}

	public Long getLowerIpNum() {
		return lowerIpNum;
	}

	public void setLowerIpNum(Long lowerIpNum) {
		this.lowerIpNum = lowerIpNum;
	}

	@Override
	public String getName() {
		return name;
	}

	public void setName(String regionCode) {
		this.name = regionCode;
	}
}
