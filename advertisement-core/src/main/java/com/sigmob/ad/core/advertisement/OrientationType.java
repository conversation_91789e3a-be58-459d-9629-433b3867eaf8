package com.sigmob.ad.core.advertisement;

/**
 * <AUTHOR>
 */
public enum OrientationType {
  All(0),
  /** 竖屏 */
  Portrait(1),
  /** 横屏 */
  Landscape(2),

  /** 临时增加用于兼容平台代码值（等价于0）*/
  PORTRAIT_OR_LANDSCAPE(3);

  private final int type;

  OrientationType(int type) {
    this.type = type;
  }

  public static boolean isValidType(int typeNum) {
    OrientationType types[] = values();
    for (OrientationType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static OrientationType getType(int i) {
    OrientationType types[] = values();
    for (OrientationType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return Portrait;
  }

  public int getTypeNum() {
    return this.type;
  }
}
