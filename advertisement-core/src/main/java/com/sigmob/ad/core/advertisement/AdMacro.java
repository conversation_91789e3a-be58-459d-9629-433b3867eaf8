package com.sigmob.ad.core.advertisement;

/**
 * <AUTHOR>
 */
public class AdMacro {

  /** 视频总时长，单位为秒 */
  public static final String VIDEO_TIME = "_VIDEOTIME_";

  /** track url触发的unix时间戳（秒） */
  public static final String TIMESTAMP = "_TIMESTAMP_";

  /** unix时间戳（毫秒） */
  public static final String TIMEMILLIS = "_TIMEMILLIS_";

  /** 视频播放开始时间，单位为 秒。如果视频从头开始播放， 则为0。 */
  public static final String BEGIN_TIME = "_BEGINTIME_";

  /** tracker事件发生的时间 */
  public static final String END_TIME = "_ENDTIME_";

  /** 当前播放进度： 百分比 0-100 */
  public static final String VIDEO_PROGRESS = "_PROGRESS_";

  /** 视频素材地址 */
  public static final String VIDEO_URL = "_VURL_";

  /** 视频素材的md5 */
  public static final String VIDEO_MD5 = "_VMD5_";

  /** 用户点击时手指按下时相对于屏幕的横坐标 */
  public static final String CLICK_DOWN_X = "_DOWNX_";

  /** 用户点击时手指按下时相对于屏幕的纵坐标 */
  public static final String CLICK_DOWN_Y = "_DOWNY_";

  /** 用户点击时手指离开手机屏幕时的横坐标 */
  public static final String CLICK_UP_X = "_UPX_";

  /** 用户点击时手指离开手机屏幕时的纵坐标 */
  public static final String CLICK_UP_Y = "_UPY_";

  /** sdk信息流click事件使用 */
  public static final String CLICK_AREA = "_CLICKAREA_";

  /** sdk信息流click事件使用 */
  public static final String CLICK_SCENE = "_CLICKSCENE_";

  /** sdk信息流click事件使用 */
  public static final String AUTO_CLICK = "_CLICKSCENE_";

  /** sdk信息流click事件使用 */
  public static final String FINAL_CLICK = "_FINALCLICK_";

  /** 下发配置的视频秒数 */
  public static final String SET_CLOSE_TIME = "_SETCLOSETIME_";

  /** 为1:表示视频被截断，为0:表示未截断正常播放 */
  public static final String IS_TRUNCATION = "_ISTRUNCATION_";

  /** 视频是否从第一帧开始播放。从第一帧开始播放，则为1；否则，为0 */
  public static final String IS_FIRST_FRAME = "_PLAYFIRSTFRAME_";

  /** 视频是否播放到最后一帧。播放到最后一帧，则为1；否则，为0 */
  public static final String IS_LAST_FRAME = "_PLAYLASTFRAME_";

  /**
   * 播放类型<br>
   * 1 - 第一次播放； <br>
   * 2 - 暂停后继续播放； <br>
   * 3 - 重新开始播放。
   *
   * <p>根据实际情况判断，第一次播放为1，续播为2，重新（二次）播放为3
   */
  public static final String PLAY_TYPE = "_TYPE_";

  /**
   * 播放行为<br>
   * 1 - 自动播放（推荐联网方式为wifi或4G时，设置视频自动播放）;<br>
   * 2 - 点击播放。
   *
   * <p>信息流根据实际播放情况判断。
   */
  public static final String PLAY_BEHAVIOR = "_BEHAVIOR_";

  /**
   * 播放状态<br>
   * 0 - 正常播放；<br>
   * 1 - 视频加载中；<br>
   * 2 - 播放错误。
   *
   * <p>信息流写死为0
   */
  public static final String STATUS = "_STATUS_";

  /**
   * 视频播放场景<br>
   * 0 - 其它开发者自定义场景;<br>
   * 1 - 在广告曝光区域播放; <br>
   * 2 - 全屏竖屏、只展示视频;<br>
   * 3 - 全屏竖屏、屏幕上方展示 视频、下方展示广告推广目标 网页(仅适用于交互类型是打 开网页的广告;目标网页由点 击上报返回数据中取得;<br>
   * 4 - 全屏横屏、只展示视频;
   */
  public static final String SCENE = "_SCENE_";

  /** 广告展现位置 */
  public static final String AD_SCENE = "_ADSCENE_";

  public static final String AD_SLOT_WIDTH = "_SLOTWIDTH_";

  public static final String AD_SLOT_HEIGHT = "_SLOTHEIGHT_";

  public static final String CLICK_ID = "_CLICKID_";

  /** api流量媒体处理sigmob广告错误代码 */
  public static final String API_ERROR_CODE = "_ERRORCODE_";

  /** mraid 模版id */
  public static final String TRACK_TEMPLATE_ID = "_TEMPLATE_";

  /** 是否有挂件（兜底没有） */
  public static final String TRACK_HAS_CPT = "_HASCPT_";

  /** 号召挂件id列表（兜底没有） */
  public static final String TRACK_AC_CPT_IDS = "_ACCPTIDS_";

  /** 气氛挂件id列表（兜底没有） */
  public static final String TRACK_AT_CPT_IDS = "_ATCPTIDS_";

  /** 广告区域左上角坐标（相对于屏幕左上角） （兜底不做） */
  public static final String TRACK_AX = "_AX_";

  public static final String TRACK_AY = "_AY_";

  /** 广告区域左上角坐标（相对于屏幕左上角） （兜底不做） */
  public static final String TRACK_AW = "_AW_";

  public static final String TRACK_AH = "_AH_";

  /** 叉按钮中心点坐标（相对于屏幕左上角）（兜底不做） */
  public static final String TRACK_XX = "_XX_";

  public static final String TRACK_XY = "_XY_";

  /** 叉按钮半径（兜底不做） */
  public static final String TRACK_X_RADIUS = "_XR_";

  /** 返回广告id */
  public static final String TRACK_IMP_ID = "_IMPID_";

  /**
   * （兜底模板无，adx默认替0）
   *
   * <p>广告交互方式，可能取值： 0 - 常规触屏点击 1 - 滑动点击 2 - 摇一摇 3 - 自定义手势 5 - 扭一扭 6 - 擦除
   */
  public static final String TRACK_SLD = "_SLD_";

  /** 模板配置的误点概率 */
  public static final String TRACK_WRONG_CLICK_RATE = "_ECR_";

  /** 用户的点击是否为误点 */
  public static final String TRACK_IS_WRONG_CLICK = "_ISEC_";

  /** 挂件id */
  public static final String TRACK_CPT_ID = "_CPTIDS_";

  public static final String TRACK_X_MAX_ACC = "_XMAXACC_";

  public static final String TRACK_Y_MAX_ACC = "_YMAXACC_";

  public static final String TRACK_Z_MAX_ACC = "_ZMAXACC_";

  public static final String TRACK_TURN_X = "_TURNX_";

  public static final String TRACK_TURN_Y = "_TURNY_";

  public static final String TRACK_TURN_Z = "_TURNZ_";

  public static final String TRACK_TURN_TIME = "_TURNTIME_";

  /** 事件发生的时间戳（毫秒）。若为点击或滑动点击触发，则替换为点击（滑动）按下的时间戳（毫秒） */
  public static final String TRACK_DOWN_TIMESTAMP = "_DOWNTS_";

  /** 击（滑动）事件触发时，点击（滑动）抬起的时间戳（毫秒），UTC时间1970年1月1 日00:00:00 以来的毫秒数。 */
  public static final String TRACK_UP_TS = "_UPTS_";

  //  --------------------------track 通用宏 ------------------------------------
  /** track链接基地址宏 */
  public static final String TRACK_BASEURL = "_BASEURL_";

  /** track链接参数-c */
  public static final String TRACK_PARAM_C = "_COMM_";

  /** track 链接-vid */
  public static final String TRACK_PARAM_VID = "_VID_";

  public static final String TRACK_AUCTION_PRICE = "_AUCTIONPRICE_";

  public static final String TRACK_IS_NC = "_ISNC_";

  /** 美团 saas channel id 宏 */
  public static final String SAAS_CHANNEL_ID_MACRO = "&saas_channel_id=__CHANNEL__";

  /** 美团 saas sub channel id 宏 */
  public static final String SAAS_SUB_CHANNEL_ID_MACRO = "&saas_sub_channel_id=__SUBCHANNEL__";
}
