package com.sigmob.ad.core.advertisement.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * dsp请求传递配置
 *
 * <AUTHOR> @Date 2024/1/8 11:35 @Description
 */
@Getter
@Setter
@AllArgsConstructor
public class DspTrafficParamUsage {

  // 传递配置应用名称：0-透传；1-渠道信息；2-自定义
  private Integer paramAppName;

  // 传递配置自定义应用名称
  private String definedAppName;

  // 传递配置应用id：0-透传；1-渠道信息；2-自定义
  private Integer paramAppId;

  // 传递配置自定义应用id
  private String definedAppId;

  // 传递配置应用包名：0-透传；1-渠道信息；2-自定义
  private Integer paramAppBundle;

  // 传递配置自定义应用包名
  private String definedAppBundle;

  // 传递配置广告位id：0-透传；1-渠道信息；2-自定义
  private Integer paramPlacementId;

  // 传递配置自定义广告位id
  private String definedPlacementId;

  // 传递配置广告位名称：0-透传；1-渠道信息；2-自定义
  private Integer paramPlacementName;

  // 传递配置自定义广告位名称
  private String definedPlacementName;

  private Integer thirdAdType;
}
