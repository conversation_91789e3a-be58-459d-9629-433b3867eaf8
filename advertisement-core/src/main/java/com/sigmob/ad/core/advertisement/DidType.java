/** */
package com.sigmob.ad.core.advertisement;

/**
 * 设备id类型
 *
 * <AUTHOR>
 */
public enum DidType {
  IDFA(1),
  IMEI(2),
  GAID(3),
  OAID(4),
  ODID(5);
  private final int type;

  DidType(int type) {
    this.type = type;
  }

  public static boolean isValidType(int typeNum) {
    DidType[] types = values();
    for (DidType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static DidType getType(int i) {
    DidType[] types = values();
    for (DidType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return null;
  }

  public int getTypeNum() {
    return this.type;
  }
}
