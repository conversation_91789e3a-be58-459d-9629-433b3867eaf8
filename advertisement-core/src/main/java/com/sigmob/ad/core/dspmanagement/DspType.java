package com.sigmob.ad.core.dspmanagement;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;

import java.util.Map;

/** dsp类型 */
//@AllArgsConstructor
//public enum DspType {
//  STANDARD_DSP(0, "standard"),
//
//  SIG_DSP(1000, "sigmob"),
//
//  /** 后添加dsp复用广点通渠道 */
//  TENCENT_GDT(1001, "tencent_gdt"),
//
//  /** 京东 */
//  JD(1002, "jd"),
//
//  /** 京东-京准通 */
//  JD_JZT(1003, "jd_jzt"),
//
//  /** 淘宝-直播联盟 */
//  TAOBAO_LIVE(1004, "taobao_live"),
//
//  /** oneway新渠道 */
//  ONEWAY_2(1005, "oneway_2"),
//
//  /** 360 */
//  QIHU_360(1006, "360"),
//
//  /** 广点通 */
//  TENCENT_SOCIAL_ADS_API(2000, "tencent"),
//  /** yomob */
//  YOMOB_API(2001, "yomob"), // 废弃
//  /** 头条 */
//  TOUTIAO_ADS_API(2002, "toutiao"),
//  /** 和传媒 */
//  YU_CHANG_HE(2003, "yuchanghe"), // 和百度一个逻辑
//  /** 百度 */
//  BAIDU(2004, "baidu"),
//  /** 推啊 */
//  TUIA(2005, "tuia"), // 废弃
//  /** 新义 */
//  MOBRTB_API(2006, "mobrtb"),
//  /** INMOBI */
//  INMOBI_API(2007, "inmobi"),
//  /** oneway */
//  ONE_WAY_API(2008, "oneway"),
//  /** 聚量 */
//  UNI_PLAY(2009, "uniplay"),
//  /** 科大讯飞 */
//  VOICEADS(2010, "voiceads"),
//
//  /** 旺脉 */
//  WANGMAI_API(2011, "wangmai"), // 废弃
//  /** 椰子 */
//  COCO_API(2012, "coco"); // 废弃
//
//  private static Map<Integer, DspType> index;
//
//  static {
//    index = Maps.newHashMapWithExpectedSize(DspType.values().length);
//    for (DspType type : DspType.values()) {
//      index.put(type.getId(), type);
//    }
//  }
//
//  private int id;
//  private String name;
//
//  public int getId() {
//    return id;
//  }
//
//  public String getName() {
//    return name;
//  }
//
//  public static DspType valueById(Integer dspId) {
//
//    DspType dType = index.get(dspId);
//    if (dType != null) {
//      return dType;
//    }
//    return STANDARD_DSP;
//  }
//}
