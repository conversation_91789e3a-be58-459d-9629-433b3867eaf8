/** */
package com.sigmob.ad.core.advertisement;

/**
 * 广告类型枚举类 1、激励视频
 *
 * <AUTHOR>
 */
public enum CompanionTemplateType {

  /** 带星星的模版 */
  SCORE(1),
  /** 带描述的模版 */
  DESC(2);
  private final int type;

  CompanionTemplateType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    CompanionTemplateType[] types = values();
    for (CompanionTemplateType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }
}
