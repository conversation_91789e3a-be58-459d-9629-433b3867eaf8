package com.sigmob.ad.core.advertisement;

/**
 * 广告类型枚举类
 *
 * <AUTHOR>
 */
public enum AdType {
  REWARDED_VIDEO(1), // 激励视频
  SPLASH(2), // 开屏
  FLOATING(3), // 漂浮
  FULL_SCREEN_VIDEO(4), // 全屏
  NATIVE(5), // 原生
  INTERSTITIAL(6), // 插屏
  TO_BID_INTERSTITIAL(4), // 插屏
  BANNER(7),//banner
  DRAW_VIDEO(11); //draw 视频（暂时只有快手支持）
  final int type;

  AdType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    AdType[] types = values();
    for (AdType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static AdType getType(int i) {
    AdType[] types = values();
    for (AdType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return REWARDED_VIDEO;
  }
}
