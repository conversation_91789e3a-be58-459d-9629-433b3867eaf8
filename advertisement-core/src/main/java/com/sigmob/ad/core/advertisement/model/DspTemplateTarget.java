package com.sigmob.ad.core.advertisement.model;

import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
public class DspTemplateTarget {

  /** 模板id */
  private Integer templateId;

  private Integer adType;

  private List<Integer> supportActionList;

  private Integer osType;

  /** 2：直投程序化共用；1：仅支持程序化；0：仅支持直投 */
  private Integer useType;

  /** 1:横版、2:竖版、3:横竖兼容（插屏暂只有前两种） */
  private Integer orientation;

  /** 0:全屏、1:半屏 */
  private Integer style;

  /** 是否视频模版：1：是；0：否 */
  private boolean video;

  /** 模版是否支持图片(暂时只有开屏用) */
  private boolean image;

  private Integer formatType;

  /** 1：是；0：否 */
  private boolean overlay;

  /** 全域点击 */
  private boolean globalClick;

  /** 互动语 */
  private String interactiveWord;

  /** 行动语 */
  private Text actionWord;

  @Getter
  @Setter
  public static class Text {

    private Integer minLength;

    private Integer maxLength;
  }
}
