package com.sigmob.ad.core.dspmanagement;

import com.google.common.collect.Maps;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** dsp协议类型 */
@AllArgsConstructor
@Getter
public enum DspProtocolType {
  /** dsp协议类型 */
  STANDARD(1, "标准协议", "standard"),
  SIGMOB_DSP(2, "sigmob", "sigmob"),
  TENCENT_GDT(3, "广点通", "guangdiantong"),
  JD(4, "京东黑珑", "jd_heilong"), // 已废弃
  TAOBAO_LIVE(5, "淘宝直播", "taobaoLive"), // 已废弃
  QIHU360(6, "奇虎360", "360"), // 已废弃
  YOMOB(7, "yomob", "yomob"), // 已废弃
  TOUTIAO_Deprecated(8, "头条", "bytedance"), // 已废弃
  BAIDU(9, "百度", "baidu"), // 已废弃
  TUIA(10, "推啊", "tuia"), // 已废弃
  MOBRTB(11, "新义", "mobrtb"), // 已废弃
  INMOBI(12, "inmobi", "inmobi"), // 已废弃
  ONEWAY(13, "oneway", "oneway"), // 已废弃
  UNIPLAY(14, "聚量", "uniplay"), // 已废弃
  VOICEADS(15, "科大讯飞", "voiceads"),
  WANGMAI(16, "旺脉", "wangmai"), // 已废弃
  COCONUTS(17, "椰子", "coconuts"), // 已废弃
  PINGDUODUO(18, "拼多多", "pingduoduo"), // 已废弃
  JD_JZT(19, "京东京准通", "jd_jingzhuntong"),
  SOYOUNG(20, "新氧", "soyoung"), // 已废弃
  DEVELOPER_TEST(21, "测试DSP协议", "test"),
  IQIYI(22, "爱奇艺", "iqiyi"),
  DONGFENG(23, "东风", "dongfeng"), // 已废弃
  BAIDU_MOBADS(24, "百度百青藤", "baidu"), // 已废弃
  DEVELOPER_TEST_NEW(25, "新测试DSP协议", "new_test"),
  TANX(26, "阿里tanx", "tanx"),
  VUNGLE(27, "vungle", "vungle"), // 已废弃
  MAIMAI_OEM(28, "oem脉脉", "maimai"), // 已废弃

  VOICEADS_RTB(29, "科大讯飞RTB", "voiceads"),

  MEITUAN(30, "美团", "meituan"),

  BAIDU_MOBADS_PB(31, "百度百青藤pb", "baidu"),

  KUAI_SHOU(34, "快手", "kuaishou"),

  OPPO_RTB(35, "oppo", "oppo"),

  /** 巨浪 */
  SURGE(36, "启航", "surge"),

  VIVO(37, "vivo", "vivo"),

  HUICHUAN(38, "汇川", "huichuan"),

  /** 废弃 */
  SURGE_NEW_Deprecated(39, "新启航", "surge"),
  HUAWEI(40, "华为", "huawei"),
  JD_TG(41, "京东天宫", "jd_tiangong"),
  YYB(42, "YYB", "YYB"),
  SURGE_NEW(43, "平台口启航", "surge"),
  MT_BZ(44, "倍孜", "mtbeizi"),
  TOUTIAO(45, "穿山甲", "bytedance"),

  TO_BID_SAAS_ADX(997, "ToBid SaaS ADX", "ToBid_SaaS_ADX"),
  ;

  private static final Map<Integer, DspProtocolType> INDEX;

  static {
    INDEX = Maps.newHashMapWithExpectedSize(DspProtocolType.values().length);
    for (DspProtocolType type : DspProtocolType.values()) {
      INDEX.put(type.getType(), type);
    }
  }

  final int type;
  final String name;
  final String adnName;

  public static DspProtocolType valueById(Integer dspId) {

    return INDEX.get(dspId);
  }

  public static boolean isSurgeDsp(int dspProtocolType) {
    return dspProtocolType == DspProtocolType.SURGE.getType()
        || dspProtocolType == DspProtocolType.SURGE_NEW.getType();
  }
}
