package com.sigmob.ad.core.advertisement;

/**
 * 交易类型
 *
 * <AUTHOR>
 */
public enum DealType {

  /** API的形式，以对方的结算数据为准 */
  API(1),
  /** 按照每单出价为准 */
  BID(2),
  /** 私下协商的固定价格 */
  PRIVATE_PRICE(3);
  private final int type;

  DealType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    DealType[] types = values();
    for (DealType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static DealType getType(int i) {
    DealType[] types = values();
    for (DealType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return API;
  }
}
