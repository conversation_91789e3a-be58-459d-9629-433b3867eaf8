package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR> @Date 2022/6/29 20:04 @Description
 */
public class HwSeadConstants {

  /** 默认最大请求时间 - 1000毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 1000;

  public static final List<Integer> supportActionList =
      List.of(
          RtbResponse.InteractType.WEB_VIEW_VALUE,
          RtbResponse.InteractType.APP_DOWNLOAD_VALUE,
          RtbResponse.InteractType.SYS_EXPLORE_VALUE,
          RtbResponse.InteractType.DEEP_LINK_VALUE);

  public static final Version DEFAULT_ANDROID_OS_VERSION =
      Version.newBuilder().setMajor(10).setMinor(0).setMicro(0).setVersionStr("10.0.0").build();

  /** 广告图片最大体积 */
  public static final int MAX_AD_IMAGE_SIZE_IN_BYTE = 500 * 1024;
  /** 错误码 */
  @AllArgsConstructor
  @Getter
  public enum ErrorCode {
    OK("000000", 0),

    INVALID_TIMESTAMP("400005", 400005),

    FORBIDDEN("400031", 400031),

    INTERNAL_ERROR("500001", 500001);

    final String codeStr;
    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum AppType {
    ANDROID_APP(1),
    IOS_APP(2);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ContentPublisherType {
    APP("APP");

    final String name;
  }

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN(0),
    CUCC(1), // 中国联通
    CMCC(2), // 中国移动
    CTCC(3); // 中国电信

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),

    CELLULAR(1),

    WIFI(2),

    ETHERNET(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum CreativeStyle {
    // 0001:单图文

    // 纯图
    IMAGE_ONLY("00010009"),

    // 图标1：1
    LOGO("00010010"),

    // 上图下文16:9
    IMAGE_AND_TEXT("00010011"),

    // 悬浮小方图1:1
    FLOAT_IMAGE("00010012");

    final String code;
  }

  /** track事件 */
  public enum AdEvent {
    imp,
    click
  }

  @AllArgsConstructor
  @Getter
  public enum AdProductType {

    // General creative for landing page of website or app
    WEB_VIEW("1");

    final String code;
  }

  @AllArgsConstructor
  @Getter
  public enum CampaignType {

    // General display ad series
    DISPLAY_AD("10");

    final String code;
  }
}
