package com.sigmob.ad.core.exception;

/**
 * <AUTHOR>
 */
public class CircuitBreakFallbackException extends AdException {

  public CircuitBreakFallbackException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public CircuitBreakFallbackException(int code) {
    super();
    this.code = code;
  }

  public CircuitBreakFallbackException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public CircuitBreakFallbackException(int code, String message) {
    super(message);
    this.code = code;
  }

  public CircuitBreakFallbackException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public CircuitBreakFallbackException(int code, String message, Throwable cause) {
    super(message, cause);
    this.code = code;
  }

  public CircuitBreakFallbackException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public CircuitBreakFallbackException(int code, Throwable cause) {
    super(cause);
    this.code = code;
  }

  public CircuitBreakFallbackException(
      int code, String message, boolean enableSuppression, boolean writableStackTrace) {
    this(code, message, null, enableSuppression, writableStackTrace);
  }

  public CircuitBreakFallbackException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public CircuitBreakFallbackException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
