package com.sigmob.ad.core.advertisement;

/**
 * 广告结算类型
 *
 * <p>1: CPM<br>
 * 2: CPC<br>
 * 3: CPA
 *
 * <AUTHOR>
 */
public enum BidType {
  CPM(1),
  CPC(2),
  CPA(3);
  private final int type;

  BidType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    BidType[] types = values();
    for (BidType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static BidType getType(int i) {
    BidType[] types = values();
    for (BidType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return null;
  }
}
