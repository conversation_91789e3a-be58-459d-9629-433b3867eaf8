package com.sigmob.ad.core.security;

import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.security.crypto.Crypto;
import com.sigmob.ad.core.security.crypto.CryptoByAesAndBase64;
import org.springframework.stereotype.Service;

@Service
public class SecurityService {

  private final Crypto crypto;

  private final String DEFAULT_ZERO_ENCRYPT_PRICE;

  public SecurityService(CryptoByAesAndBase64 cryptoByAesAndBase64) {
    this.crypto = cryptoByAesAndBase64;
    DEFAULT_ZERO_ENCRYPT_PRICE = encodeBase64URLSafe(Constants.TRACKING_SECURITY_KEY, "0");
  }

  public static void main(String[] args) {
    var cryptoByAesAndBase64 = new CryptoByAesAndBase64();

    //    System.out.println(cryptoByAesAndBase64.encryptWithUrlSafe("Pr2df@*sgm&^b+k%","0"));
    //
    //

    //    System.out.println(cryptoByAesAndBase64.decrypt("A7o0y-maskn51cfNm2LCJw",
    // "RjQ4OEY5MEEtQjBDMS00NTJDLUE1NkEtMDhDRDFGMkNEMTExUfTMaW84JOFc%2BGnE"));
    System.out.println(
        cryptoByAesAndBase64.decrypt("Pr2df@*sgm&^b+k%", "Sd-gJCKJgZiPFzNVm8CwDA"));

//    System.out.println("Pr2df@*sgm&^b+k%".length());
//    System.out.println(cryptoByAesAndBase64.encryptWithUrlSafe("9249fd8e782db19834e2e2efecced59f", "612"));
//    System.out.println(cryptoByAesAndBase64.decryptWithUrlSafe("9249fd8e782db19834e2e2efecced59f","ypBRlSIVlP0AQB9z85AVZw"));

//    AesCrypto aesCrypto = new AesCrypto();
//    System.out.println(aesCrypto.encryptWithUrlSafe("aaaatestbbbbcccc", "zhaopeng"));

    //
    // System.out.println(cryptoByAesAndBase64.encryptWithUrlSafe("891cca875dc863aee2dab4c7036f6ba4","998"));
    //
    //
    // System.out.println(cryptoByAesAndBase64.decrypt("891cca875dc863aee2dab4c7036f6ba4","HN3LAL8j8RghAG0msvR2QA"));
  }

  public String encrypt(String key, String plaintext) {
    return crypto.encrypt(key, plaintext);
  }

  public String decrypt(String key, String ciphertext) {
    return crypto.decrypt(key, ciphertext);
  }

  public String decrypt(String key, byte[] cipher) {
    return crypto.decrypt(key, cipher);
  }

  public String encodeBase64URLSafe(String key, String plaintext) {
    return crypto.encryptWithUrlSafe(key, plaintext);
  }

  public String decodeBase64URLSafe(String key, String ciphertext) {
    return crypto.decryptWithUrlSafe(key, ciphertext);
  }

  /**
   * 价格为0的加密
   *
   * @return
   */
  public String getDefaultZeroEncryptPrice() {
    return DEFAULT_ZERO_ENCRYPT_PRICE;
  }
}
