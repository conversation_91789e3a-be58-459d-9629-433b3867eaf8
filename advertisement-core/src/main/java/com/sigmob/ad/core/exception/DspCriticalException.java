package com.sigmob.ad.core.exception;

/**
 * Dsp关键异常，需要降级、熔断
 */
public class DspCriticalException  extends AdException {

  public DspCriticalException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public DspCriticalException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public DspCriticalException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public DspCriticalException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public DspCriticalException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public DspCriticalException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public DspCriticalException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public DspCriticalException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public DspCriticalException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public DspCriticalException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
