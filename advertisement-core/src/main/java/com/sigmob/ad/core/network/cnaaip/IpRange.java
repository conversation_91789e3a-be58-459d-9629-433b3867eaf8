package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.io.ByteSource;
import com.google.common.io.Files;
import com.google.common.io.LineProcessor;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystemNotFoundException;
import java.util.Collections;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.io.ClassPathResource;

/**
 * 基于中国广告协会的ip库， 建立ip地址库与region code的映射表 按照地址范围的下界建立的有序数组（正序） 例如： 将以下地址列表
 * **********,************,1156150300 **********,************,1156150200
 * 建立数组时取地址范围的下界（分别是************和************），转换为长整数，再进行排序，形成数组
 *
 * <AUTHOR>
 */
public class IpRange {

  /** FakeRegionCode用来构建伪造的IpRegionCode对象，以便使用Collections.binarySearch进行查找 */
  private static final String FakeRegionCode = "fake";

  private boolean initialized = false;
  private List<IpV4Name> ipRegionCodeList;

  public synchronized void init(File ipFile) {
    synchronized (IpRange.class) {
      if (initialized) {
        return;
      }
      if (!ipFile.exists()) {
        throw new FileSystemNotFoundException("can not find file:" + ipFile.getPath());
      }

      ReadIP rIp = new ReadIP();
      try {
        Files.readLines(ipFile, Charsets.UTF_8, rIp);
        ipRegionCodeList = rIp.getResult();
        Collections.sort(ipRegionCodeList);
        initialized = true;
      } catch (IOException e) {
        throw new RuntimeException("can not initiallize cnaa_ip!", e);
      }
    }
  }

  public synchronized void init(String filePath) {
    synchronized (IpRange.class) {
      if (initialized) {
        return;
      }

      ClassPathResource resource = new ClassPathResource(filePath);
      if (!resource.exists()) {
        throw new FileSystemNotFoundException("can not find file:" + resource.getPath());
      }

      ReadIP rIp = new ReadIP();
      try(InputStream inputStream = resource.getInputStream()) {
        ByteSource byteSource =
            new ByteSource() {
              @Override
              public InputStream openStream() throws IOException {
                return inputStream;
              }
            };
        byteSource.asCharSource(Charsets.UTF_8).readLines(rIp);
        //        Files.readLines(inputStream, Charsets.UTF_8, rIp);
        ipRegionCodeList = rIp.getResult();
        Collections.sort(ipRegionCodeList);
        initialized = true;
      } catch (IOException e) {
        throw new RuntimeException("can not initiallize cnaa_ip!", e);
      }
    }
  }

  /**
   * 根据ip地址获取该ip地址所在的ip范围对象
   *
   * @param ip
   * @return
   */
  public IpV4Name getIpName(String ip) {
    if (!initialized) {
      return null;
    }
    if (Strings.isNullOrEmpty(ip)) {
      return null;
    }

    // 这里偷个懒，就不自己写二分查找算法了，利用Collections.binarySearch方法来实现
    // 这样的实现可能有点让人迷惑，请参看IpRangeCompartor的注释
    IpV4Name key = IpV4Name.build(ip, ip, FakeRegionCode);
    if (key == null) {
      return null;
    }
    int index = Collections.binarySearch(ipRegionCodeList, key, new IpRangeComparator());
    if (index >= 0) {
      return ipRegionCodeList.get(index);
    }
    return null;
  }

  static class ReadIP implements LineProcessor<List<IpV4Name>> {
    private final List<IpV4Name> ipRegionCodeList = Lists.newArrayList();
    private boolean firstLine = true;

    @Override
    public boolean processLine(String line) throws IOException {

      if (line != null) {
        String[] contents;
        if (firstLine) {
          contents = line.trim().replace("\uFEFF", "").split(",");
        } else {
          contents = line.trim().split(",");
        }
        if (contents != null && contents.length == 3) {

          IpV4Name irc = IpV4Name.build(contents[0], contents[1], contents[2]);
          if (irc == null) {
            throw new IOException("load file error,invalid content: " + line);
          }
          ipRegionCodeList.add(irc);
        }
      }
      firstLine = false;
      return true;
    }

    @Override
    public List<IpV4Name> getResult() {
      return ipRegionCodeList;
    }
  }
}
