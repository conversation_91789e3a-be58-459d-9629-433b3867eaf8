package com.sigmob.ad.core.exception;

import lombok.Getter;
import lombok.Setter;

/** ssp请求准出异常 */
@Getter
@Setter
public class SspOutException extends AdException {

  /** 返回给请求方的错误码（通常由请求方定义，如果没有则默认使用sigmob定义错误码） */
  private int returnCode;

  public SspOutException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public SspOutException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public SspOutException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SspOutException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public SspOutException(int code, int returnCode, String message) {
    this(code, message, null, false, false);
    this.code = code;
    this.returnCode = returnCode;
  }

  public SspOutException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SspOutException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public SspOutException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SspOutException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public SspOutException(int code, int returnCode, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
    this.returnCode = returnCode;
  }

  public SspOutException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SspOutException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
