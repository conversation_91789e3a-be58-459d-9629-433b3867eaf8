package com.sigmob.ad.core.constants.enums;

/**
 * 标准dsp返回广告创意类型
 *
 * <AUTHOR>
 */
public enum StandardDspCreativeType {

  /** 纯静态图片广告，一般由单张image_src构成 */
  SPLASH_IMAGE(3),
  /** video + 素材模版(实际上是video + image) */
  VIDEO_TEMPLATE(4),
  /** video+html源代码的模式 */
  VIDEO_HTML_SNIPPET(5),
  /** 单个视频, 用在视频开屏 */
  ONLY_VIDEO(6),
  /** video + html url */
  VIDEO_HTML_URL(7),
  /** 原生广告素材 */
  NATIVE(8);

  private final int type;

  StandardDspCreativeType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    StandardDspCreativeType[] types = values();
    for (StandardDspCreativeType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static StandardDspCreativeType getType(int i) {
    StandardDspCreativeType[] types = values();
    for (StandardDspCreativeType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return VIDEO_TEMPLATE;
  }
}
