package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.google.common.base.Strings;
import com.sigmob.ad.core.advertisement.AdType;
import com.sigmob.ad.core.advertisement.CreativeType;
import com.sigmob.ad.core.constants.enums.RequestFlowType;
import com.sigmob.ad.core.constants.enums.StandardDspCreativeType;
import com.sigmob.ad.core.exception.DspBusinessException;
import com.sigmob.ad.core.exception.ErrorCode;
import java.util.Optional;

/** api流量广告响应过滤 */
public class ApiAdFilter {

  /**
   * Api流量请求标准DSP返回广告过滤
   *
   * @param requestFlowType
   * @param adAttr
   * @param adType
   * @param videoUrl
   * @param imageUrl
   * @return
   */
  public static Optional<DspBusinessException> filterStandardDspAd(
      int requestFlowType, int adAttr, int adType, String videoUrl, String imageUrl) {

    // api激励视频流量过滤标准dsp协议返回，需求 http://wiki.sigmob.cn/pages/viewpage.action?pageId=40079763
    // http://wiki.sigmob.cn/pages/viewpage.action?pageId=40077112
    if ((adType == AdType.REWARDED_VIDEO.getTypeNum()
            || adType == AdType.FULL_SCREEN_VIDEO.getTypeNum())
        && (adAttr == StandardDspCreativeType.VIDEO_HTML_SNIPPET.getTypeNum()
            || adAttr == StandardDspCreativeType.VIDEO_HTML_URL.getTypeNum())) {
      return Optional.of(
          new DspBusinessException(
              ErrorCode.DSP_API_RESPONSE_ATTR_NOT_SUPPORT,
              "Api(" + requestFlowType + ") ad response not support attr(" + adAttr + ")"));
    }

    if (adAttr == StandardDspCreativeType.VIDEO_TEMPLATE.getTypeNum()) {
      if (Strings.isNullOrEmpty(imageUrl)) {
        return Optional.of(
            new DspBusinessException(
                ErrorCode.RTB_SIG_DSP_RESPONSE_NEED_IMAGE_URL,
                "Api("
                    + requestFlowType
                    + ") response ad missing image_url for attr("
                    + adAttr
                    + ")"));
      }

      if (Strings.isNullOrEmpty(videoUrl)) {
        return Optional.of(
            new DspBusinessException(
                ErrorCode.RTB_SIG_DSP_RESPONSE_NEED_VIDEO_URL,
                "Api("
                    + requestFlowType
                    + ") response ad missing video_url for attr("
                    + adAttr
                    + ")"));
      }
    }

    return Optional.empty();
  }

  /**
   * Api流量请求三方dsp广告过滤
   *
   * @return
   */
  public static Optional<DspBusinessException> filterThirdPartyDspAd(
      int requestFlowType, int requestCreativeType, String videoUrl, String imageUrl) {
    if (requestFlowType != RequestFlowType.SDK.getCode()
        && requestCreativeType == CreativeType.VIDEO_AND_IMAGE.getTypeNum()) {
      if (Strings.isNullOrEmpty(imageUrl)) {
        return Optional.of(
            new DspBusinessException(
                ErrorCode.RTB_SIG_DSP_RESPONSE_NEED_IMAGE_URL,
                "Api("
                    + requestFlowType
                    + ") response ad missing image_url for request creativeType("
                    + requestCreativeType
                    + ")"));
      }

      if (Strings.isNullOrEmpty(videoUrl)) {
        return Optional.of(
            new DspBusinessException(
                ErrorCode.RTB_SIG_DSP_RESPONSE_NEED_VIDEO_URL,
                "Api("
                    + requestFlowType
                    + ") response ad missing video_url for creativeType("
                    + requestCreativeType
                    + ")"));
      }
    }
    return Optional.empty();
  }

  /**
   * @param adType
   * @param requestFlowType
   * @param requestCreativeType
   * @param adCreativeType
   * @return
   */
  public boolean filterCreativeType(
      int adType, int requestFlowType, int requestCreativeType, int adCreativeType) {
    return false;
  }
}
