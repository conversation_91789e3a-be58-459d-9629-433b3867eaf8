package com.sigmob.ad.core.exception;

/**
 * <AUTHOR> @Date 2022/3/31 4:01 PM @Description
 */
public class DspTimeOutException extends AdException {

  public DspTimeOutException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public DspTimeOutException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public DspTimeOutException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public DspTimeOutException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public DspTimeOutException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public DspTimeOutException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public DspTimeOutException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public DspTimeOutException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public DspTimeOutException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public DspTimeOutException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
