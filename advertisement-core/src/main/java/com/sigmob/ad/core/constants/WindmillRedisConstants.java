package com.sigmob.ad.core.constants;

public class WindmillRedisConstants {

    /** 场景 */
    public static final String KEY_PREFIX_MEDIATION_STRATEGY_SCENE = "MS_SCENE_";

    /** 聚合策略渠道 */
    public static final String KEY_MEDIATION_STRATEGY_CHANNELS = "MS_CHANNELS";

    /** 聚合策略 */
    public static final String KEY_PREFIX_MEDIATION_STRATEGY = "MS_STRATEGY_";

    /** 聚合三方广告位 */
    public static final String KEY_PREFIX_MEDIATION_ELEMENT = "MS_ELEMENT_";

    /** 聚合应用 */
    public static final String KEY_PREFIX_MEDIATION_APP = "MS_TP_APP_";

    /** 聚合测试设备 */
    public static final String KEY_PREFIX_MEDIATION_DEBUG_DEVICE = "MS_DD_";

    // #############windmill start##############

    /** 聚合app信息前缀 */
    public static final String WINDMILL_MEDIATION_APP_ID = "WINDMILL_MEDIATION_APP_ID_";

    /** 聚合三方广告单元前缀 */
    public static final String WINDMILL_MEDIATION_ELEMENT_ID = "WINDMILL_MEDIATION_ELEMENT_ID_";

    /** 聚合流量分组策略 */
    public static String WINDMILL_STRATEGY = "WINDMILL_STRATEGY_";

    public static String WINDMILL_USE_MEDIATION = "WINDMILL_useMediation_";

    public static String WINDMILL_CUSTOM_SETTING = "WINDMILL_CUSTOM_SETTINGS_";

    /** 聚合流量分组对应的瀑布流 */
    public static String WINDMILL_WATERFALL = "WINDMILL_WATERFALL_";

    /** 聚合广告位曝光频次是否计算完成 */
    public static String WINDMILL_SCENE_CALCULATE_STATUS = "pid_calculate_status_";

    /** 聚合测试设备 */
    public static String WINDMILL_DEBUG_DEVICE_V2 = "WINDMILL_DEBUG_V_2_";

    /** 聚合测试设备策略 */
    public static String WINDMILL_DEBUG_DEVICE_STRATEGY_MAPPER = "STRATEGY_MAPPER_";

    public static String SIG_RISKY_USER_ID = "SIGRISKY_USER_ID_";

    public static String WINDMILL_RISKY_USER_ID = "RISKY_USER_ID_";

    /**
     * 激励回调时机：TBAC_L1_{appid}_{app_user_id}，cmode作为hash字段存储
     * 老格式（兼容）：TBAC_L1_{appid}_{cmode}_{gid}
     * @since <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135448260">风控策略升级</a>
     */
    public static String WINDMILL_RISKY_USER = "TBAC_L1_";

    public static String WINDMILL_CHANNEL_FILTER = "TBAC_L2_";

    public static String WINDMILL_SO_APP_UDID = "SO_CONFIG";

    public static String WINDMILL_RISKY_IP = "RISKY_IP_";

    public static String WINDMILL_IMP = "T_IMP_";

    public static String WINDMILL_REWARD = "T_REWARD_";

    /** 聚合自定义渠道 */
    public static String WINDMILL_CUSTOM_CHANNEL = "WINDMILL_C_C";

    public static String WINDMILL_CONFIG = "WINDMILL_CONFIG_";

    public static String WINDMILL_DEVELOPER_CONFIG = "WINDMILL_DEVELOPER_CONFIG_";

    public static String WINDMILL_GLOBAL_CONFIG = "WINDMILL_GLOBAL_CONFIG_";

  /**
   * 使用新key
   *
   * @since <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443443">SSP-关闭个性化设置过滤渠道</a>
   */
  public static String WINDMILL_GLOBAL_CONFIG_NEW = "WINDMILL_GLOBAL_CONFIG_NEW";

    public static String WINDMILL_CONFIG_STRATEGY_SETTING = "WINDMILL_CONFIG_STRATEGY_";

    public static String WINDMILL_ROI_APP_CONFIG = "WINDMILL_ROI_APP_ID_CONFIG_";

    /** 非hb广告源id（不分渠道）按收益排序列表（降序) */
    public static String WINDMILL_ALGORITHM_COST_RANK_LIST = "pid_cost_rank_list_";

    /** 聚合广告位的该分组B智能组新建，最近2天新建的广告源id列表 */
    public static String WINDMILL_ALGORITHM_NEW_CREATE_LIST = "new_pid_list_";

    /**
     * wait_time1: 该分组下瀑布流的超时时长，（单位：毫秒， 计算到0.1s, 并转换为毫秒存储）, wait_time1=0时代表无效值，不可用 t_prob:
     * 超时时长使用wait_time1的用户比例，范围[0~100]闭区间，比如t_prob=82, 该分组桶号小于82的用户使用wait_time1, t_prob=-1 代表无效值，不可用。
     */
    public static String WINDMILL_ALGORITHM_WAIT_TIME = "windmill_wftime_";

    public static String WINDMILL_ALGORITHM_FILL_TIME = "windmill_avg_fill_time_";
    public static String WINDMILL_ALGORITHM_ERR_FILTER = "wffiltererr_";

    // #############windmill end##############
}
