package com.sigmob.ad.core.advertisement;

/**
 * 网络连接方式
 *
 * <AUTHOR>
 */
public enum ConnectionType {

  // 必填！网络连接类型，用于判断网速。0=无法探测当前网络状态; 1=蜂窝数据接入，未知网络类型; 2=2G; 3=3G; 4=4G; 5=5G;
  // 100=Wi-Fi网络接入; 101=以太网接入

  UNKNOWN(0),
  CELLULAR(1),
  CELL_2_G(2),
  CELL_3_G(3),
  CELL_4_G(4),
  CELL_5_G(5),
  WIFI(100),
  ETHERNET(101);
  private final int type;

  ConnectionType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    ConnectionType[] types = values();
    for (ConnectionType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static ConnectionType getType(int i) {
    ConnectionType[] types = values();
    for (ConnectionType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return UNKNOWN;
  }
}
