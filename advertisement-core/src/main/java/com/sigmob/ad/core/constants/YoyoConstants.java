package com.sigmob.ad.core.constants;

import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2023/9/1 10:41 @Description
 */
public class YoyoConstants {

  public static final Version API_VERSION =
          Version.newBuilder().setMajor(1).setMinor(0).setMicro(1).setVersionStr("1.0.1").build();

  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  public static final int MAX_FILE_SIZE_100K = 100*1024;


  /** 支持的出价类型 */
  @AllArgsConstructor
  @Getter
  public enum BillingType {
    CPM(1),
    CPC(2),
    CPD(4);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Os {
    UNKNOWN(0),
    IOS(1),
    ANDROID(2),
    WINDOWS(3),
    MACOS(4),
    LINUX(5),
    WPHONE(6);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN(0),
    MOBILE(1),
    UNICOM(2),
    TELECOM(3),
    NETCOM(4);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    UNKNOWN(0),

    PHONE(1),

    TABLET(2),

    TV(4);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    ETHERNET(-1),
    UNKNOWN(0),
    WIFI(1),
    CELL_2G(2),
    CELL_3G(3),
    CELL_4G(4),
    CELL_5G(5);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum TargetType {
    LANDING_PAGE(1),

    APP_DOWNLOAD(2),

    DP_AND_LANDING_PAGE(3),

    DP_AND_APP_DOWNLOAD(4),

    WX_PROGRAM(5),

    WX_GAME(6),

    QUICK_APP(7);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Template {
    REWARDVIDEO_5_1_2("5.1.2", 1280, 720,2),

    REWARDVIDEO_5_2_2("5.2.2", 720, 1280,2),

    SPLASH_IMG_3_1_1("3.1.1", 1080, 1920,1),

    SPLASH_IMG_3_1_2("3.1.2", 1080, 2160,1),

    SPLASH_IMG_3_1_3("3.1.3", 1080, 1920,1),

    SPLASH_IMG_3_1_4("3.1.4", 1080, 1620,1),

    SPLASH_IMG_3_1_6("3.1.6", 2160, 1080,1),

    SPLASH_IMG_3_1_7("3.1.7", 1080, 1880,1),

    SPLASH_VIDEO_3_2_1("3.2.1", 1080, 1920,2),

    SPLASH_VIDEO_3_2_2("3.2.2", 1080, 1620,2),

    INTERSTITIAL_4_1_1("4.1.1", 660, 370,0),

    INTERSTITIAL_4_1_2("4.1.2", 660, 370,1),

    INTERSTITIAL_4_2_1("4.2.1", 1280, 720,1),

    INTERSTITIAL_4_2_2("4.2.2", 1080, 1920,1),

    INTERSTITIAL_4_3_1("4.3.1", 780, 800,1),
    NATIVE_1_2_2("1.2.2", 1280, 720,1),
    NATIVE_1_2_7("1.2.7", 720, 1280,1),
    NATIVE_1_2_8("1.2.8", 1280, 720,1),
    NATIVE_1_2_9("1.2.9", 1080, 1920,1),
    NATIVE_1_6_2("1.6.2", 1280, 720,1);

    final String id;
    final int width;
    final int height;
    /**1-图片，2-视频*/
    final int type;
    public static Template getTemplate(String id) {
      for (Template value : values()) {
        if (value.getId().equals(id)) {
          return value;
        }
      }
      return null;
    }
  }
}
