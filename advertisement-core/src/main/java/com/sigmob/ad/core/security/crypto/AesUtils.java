package com.sigmob.ad.core.security.crypto;

import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.exception.SspBusinessException;
import com.sigmob.ad.core.util.LogUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0
 * @since 1.0
 */
public class AesUtils {
  public static final String ALGORITHM = "AES";
  public static final String SECRET_AES_ECB_MODE = "ECB";
  public static final String SECRET_AES_CBC_MODE = "CBC";

  public static final String SECRET_AES_PKCS5_PADDING = "PKCS5Padding";

  public static final String AES_CRYPTO_KEY = "KGpfzbYsn4T9Jyuq";

  public static final String AES_CRYPTO_KEY_CALLBACK = "ZGxfzbYs4h9LJcve";

  public static final String AES_KEY_SO = "SbsfzbYs4h9LJcve";
  public static final String AES_IV_SO = "sdhhEmbxX7fkl3bn";

  public static String encrypt(String secret, String text) {
    try {
      Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
      cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret.getBytes(), ALGORITHM));
      return new String(Base64.getEncoder().encode(cipher.doFinal(text.getBytes())));
    } catch (Exception e) {
      LogUtil.localError(
          "encrypt(String, String) text:{} encrypt with secret:{} error:{}",
          text,
          secret,
          e.getMessage());
      return null;
    }
  }

  public static byte[] encryptByEcbWithCs5(byte[] secret, byte[] text) {
    try {
      Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
      cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret, ALGORITHM));
      return cipher.doFinal(text);
    } catch (Exception e) {
      LogUtil.localError(
          "encryptByEcbWithCs5() text:{} encrypt with secret:{} error:{}",
          text,
          secret,
          e.getMessage());
      return null;
    }
  }

  public static byte[] encryptWithoutBase64(String secret, byte[] text) {
    try {
      Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
      cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret.getBytes(), ALGORITHM));
      return cipher.doFinal(text);
    } catch (Exception e) {
      LogUtil.localError("text:{} encrypt with secret:{} error:{}", text, secret, e.getMessage());
      throw new SigmobException(
          ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR,
          "encryptWithoutBase64 error" + e.getMessage());
    }
  }

  public static String encryptWithGcm(String secret, String text, byte[] iv) {
    try {
      // 创建 AES-GCM 加密器
      Cipher encryptCipher = Cipher.getInstance("AES/GCM/NoPadding");
      SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "AES");
      GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
      encryptCipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);

      var plaintextBytes = text.getBytes();
      // 加密数据
      byte[] ciphertext = encryptCipher.doFinal(plaintextBytes);

      return new String(Base64.getEncoder().encode(ciphertext));
    } catch (Exception e) {
      LogUtil.localError(
          "encryptWithGcm() text:{} with secret:{} error:{}", text, secret, e.getMessage());
      return null;
    }
  }

  public static byte[] encryptWithGcm(String secret, byte[] plaintextBytes, byte[] iv) {
    try {
      // 创建 AES-GCM 加密器
      Cipher encryptCipher = Cipher.getInstance("AES/GCM/NoPadding");
      SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "AES");
      GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
      encryptCipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);

      /// var plaintextBytes = text.getBytes();
      // 加密数据
      return encryptCipher.doFinal(plaintextBytes);
    } catch (Exception e) {
      LogUtil.localError("encryptWithGcm() with secret:{} error:{}", secret, e.getMessage());
      throw new SspBusinessException(
          ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR, "response encrypt error");
    }
  }

  public static String encryptGcmAppendIvAndBase64(
      String secret, byte[] plaintextBytes, byte[] iv) {
    try {
      byte[] cipherText = encryptWithGcm(secret, plaintextBytes, iv);
      // store as IV || ciphertext (cipherText already contains tag at the end)
      byte[] out = new byte[iv.length + cipherText.length];
      System.arraycopy(iv, 0, out, 0, iv.length);
      System.arraycopy(cipherText, 0, out, iv.length, cipherText.length);
      return Base64.getUrlEncoder().encodeToString(out);
    } catch (Exception e) {
      LogUtil.localError(
          "encryptGcmAppendIvAndBase64 with secret:{} error:{}", secret, e.getMessage());
      return StringUtils.EMPTY;
    }
  }

  public static byte[] decryptWithGcm(String secret, byte[] ciphertext, byte[] iv) {
    try {

      // 创建 AES-GCM 解密器
      Cipher decryptCipher = Cipher.getInstance("AES/GCM/NoPadding");
      SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "AES");
      GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);

      decryptCipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);

      // 解密数据
      return decryptCipher.doFinal(ciphertext);
    } catch (Exception e) {
      LogUtil.localError(
          "decryptWithGcm() with ciphertext.size:{} error:{}",
          null != ciphertext ? ciphertext.length : 0,
          e.getMessage());
      return null;
    }
  }

  public static String encryptOrDefault(String secret, String text, String defaultValue) {
    try {
      Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
      cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret.getBytes(), ALGORITHM));
      return new String(Base64.getEncoder().encode(cipher.doFinal(text.getBytes())));
    } catch (Exception e) {
      LogUtil.localError(
          "encryptOrDefault() text:{} encrypt with secret:{} error:{}",
          text,
          secret,
          e.getMessage());
      return defaultValue;
    }
  }

  public static String encrypt(byte[] secret, String text) {
    try {
      Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
      cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secret, ALGORITHM));
      return new String(Base64.getEncoder().encode(cipher.doFinal(text.getBytes())));
    } catch (Exception e) {
      LogUtil.localError(
          "encrypt(byte[], String) text:{} encrypt with secret:{} error:{}",
          text,
          secret,
          e.getMessage());
      return null;
    }
  }

  public static byte[] encryptByCbcWithCs5(byte[] key, byte[] iv, byte[] plaintext) {
    return doBytes(
        SECRET_AES_CBC_MODE, SECRET_AES_PKCS5_PADDING, key, iv, plaintext, Cipher.ENCRYPT_MODE);
  }

  /**
   * 加密
   *
   * <p>byte[] -> byte[]
   *
   * @param mode 模式
   * @param padding 填充
   * @param key 密钥
   * @param iv 向量，如果模式不用向量，可传递null
   * @param plaintext 明文
   * @return 密文
   */
  public static byte[] encrypt(
      String mode, String padding, byte[] key, byte[] iv, byte[] plaintext) {
    return doBytes(mode, padding, key, iv, plaintext, Cipher.ENCRYPT_MODE);
  }

  /**
   * 解密
   *
   * <p>byte[] -> byte[]
   *
   * @param mode 模式
   * @param padding 填充
   * @param key 密钥
   * @param iv 向量，如果模式不用向量，可传递null
   * @param ciphertext 密文
   * @return 明文
   */
  public static byte[] decrypt(
      String mode, String padding, byte[] key, byte[] iv, byte[] ciphertext) {
    return doBytes(mode, padding, key, iv, ciphertext, Cipher.DECRYPT_MODE);
  }

  /**
   * 解密
   *
   * <p>byte[] -> byte[]
   *
   * @param key 密钥
   * @param ciphertext Base64.decodeBase64()后密文
   */
  public static byte[] decryptByEcbWithCs5(String key, byte[] ciphertext) {
    return doBytes(
        SECRET_AES_ECB_MODE,
        SECRET_AES_PKCS5_PADDING,
        key.getBytes(StandardCharsets.UTF_8),
        null,
        ciphertext,
        Cipher.DECRYPT_MODE);
  }

  public static byte[] decryptByCbcWithCs5(byte[] key, byte[] iv, byte[] ciphertext) {
    return doBytes(
        SECRET_AES_CBC_MODE, SECRET_AES_PKCS5_PADDING, key, iv, ciphertext, Cipher.DECRYPT_MODE);
  }

  private static byte[] doBytes(
      String mode, String padding, byte[] key, byte[] iv, byte[] input, int opMode) {
    Cipher cipher = createCipher(mode, padding, key, iv, opMode);
    try {
      return cipher.doFinal(input);
    } catch (IllegalBlockSizeException | BadPaddingException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 加密
   *
   * <p>InputStream -> OutputStream
   *
   * @param mode 模式
   * @param padding 填充
   * @param key 密钥
   * @param iv 向量，如果模式不用向量，可传递null
   * @param plaintextIn 明文的输入流
   * @param ciphertextOut 密文的输出流
   */
  public static void encrypt(
      String mode,
      String padding,
      byte[] key,
      byte[] iv,
      InputStream plaintextIn,
      OutputStream ciphertextOut) {
    doStream(mode, padding, key, iv, plaintextIn, ciphertextOut, Cipher.ENCRYPT_MODE);
  }

  /**
   * 解密
   *
   * <p>InputStream -> OutputStream
   *
   * @param mode 模式
   * @param padding 填充
   * @param key 密钥
   * @param iv 向量，如果模式不用向量，可传递null
   * @param ciphertextIn 密文输入流
   * @param plaintextOut 明文输出流
   */
  public static void decrypt(
      String mode,
      String padding,
      byte[] key,
      byte[] iv,
      InputStream ciphertextIn,
      OutputStream plaintextOut) {
    doStream(mode, padding, key, iv, ciphertextIn, plaintextOut, Cipher.DECRYPT_MODE);
  }

  /**
   * 解密
   *
   * <p>InputStream -> byte[]
   *
   * @param mode 模式
   * @param padding 填充
   * @param key 密钥
   * @param iv 向量，如果模式不用向量，可传递null
   * @param ciphertextIn 密文输入流
   * @return 明文
   */
  public static byte[] decrypt(
      String mode, String padding, byte[] key, byte[] iv, InputStream ciphertextIn) {
    ByteArrayOutputStream output = new ByteArrayOutputStream();
    doStream(mode, padding, key, iv, ciphertextIn, output, Cipher.DECRYPT_MODE);
    return output.toByteArray();
  }

  private static void doStream(
      String mode,
      String padding,
      byte[] key,
      byte[] iv,
      InputStream input,
      OutputStream output,
      int opMode) {
    Cipher cipher = createCipher(mode, padding, key, iv, opMode);
    try {
      byte[] buf = new byte[1024];
      int numRead;
      byte[] obuf;
      while ((numRead = input.read(buf)) >= 0) {
        obuf = cipher.update(buf, 0, numRead);
        if (obuf != null) {
          output.write(obuf);
        }
      }
      obuf = cipher.doFinal();
      if (obuf != null) {
        output.write(obuf);
      }
    } catch (IOException | IllegalBlockSizeException | BadPaddingException e) {
      throw new RuntimeException(e);
    }
  }

  private static Cipher createCipher(
      String mode, String padding, byte[] key, byte[] iv, int opMode) {
    SecretKeySpec keySpec = new SecretKeySpec(key, ALGORITHM);
    IvParameterSpec ivSpec = null;
    if (iv != null) {
      ivSpec = new IvParameterSpec(iv);
    }

    String transformation = ALGORITHM + "/" + mode + "/" + padding;
    Cipher cipher;
    try {
      cipher = Cipher.getInstance(transformation);
      cipher.init(opMode, keySpec, ivSpec);
      return cipher;
    } catch (NoSuchAlgorithmException
        | NoSuchPaddingException
        | InvalidKeyException
        | InvalidAlgorithmParameterException e) {
      throw new RuntimeException(e);
    }
  }
}
