package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse.InteractType;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2023/6/5 17:31 @Description
 */
public class YueyouConstants {

  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(4).setMicro(0).setVersionStr("1.4.0").build();

  public static final int DEFAULT_MAX_REQUEST_TIME = 100;

  public static final List<Integer> SUPPORT_INTERACTION_TYPE =
      List.of(
          ActionType.WEB_VIEW.getCode(),
          ActionType.DOWNLOAD.getCode(),
          ActionType.DEEPLINK.getCode(),
          ActionType.GDT_DOWNLOAD.getCode(),
          ActionType.WX_PROGRAM.getCode());

  public static final List<Integer> SUPPORT_ACTION_TYPE =
      List.of(
          InteractType.WEB_VIEW_VALUE,
          InteractType.SYS_EXPLORE_VALUE,
          InteractType.APP_DOWNLOAD_VALUE,
          InteractType.DEEP_LINK_VALUE,
          InteractType.WX_PROGRAM_VALUE);

  /** 广告推广类型 */
  @AllArgsConstructor
  @Getter
  public enum ActionType {
    /** 点击打开网页 */
    WEB_VIEW(1),
    /** 点击下载应用 */
    DOWNLOAD(2),
    /** 点击进入应用内 */
    DEEPLINK(3),

    /** 快应用 */
    WX_PROGRAM(4),

    /** 拨打电话 */
    TELEPHONE(5),

    /** 微信小程序 */
    GDT_DOWNLOAD(6);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    PHONE(0),
    PAD(1),
    PC(2),
    OTT(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    ETHERNET(1),
    WIFI(2),
    CELLULAR(3),
    CELL_2G(4),
    CELL_3G(5),
    CELL_4G(6),
    CELL_5G(7);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN("0"),
    MOBILE("1"),
    UNICOM("2"),
    TELECOM("3"),
    TIETONG("4");

    final String code;
  }

  @AllArgsConstructor
  @Getter
  public enum AdType {
    IMAGE(1),
    VIDEO(2),
    APP(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum BidType {
    FIX_PRICE(0),
    RTB(1),
    PD(2),
    PD_AND_RTB(3);

    final int code;
  }
}
