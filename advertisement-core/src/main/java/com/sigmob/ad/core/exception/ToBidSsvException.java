package com.sigmob.ad.core.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * ToBid服务端验证接口异常
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ToBidSsvException extends AdException {

  private int processType;

  public ToBidSsvException(int code, String message) {
    super(message);
    this.code = code;
  }

  public ToBidSsvException(int code, String message, int processType) {
    super(message);
    this.code = code;
    this.processType = processType;
  }
}
