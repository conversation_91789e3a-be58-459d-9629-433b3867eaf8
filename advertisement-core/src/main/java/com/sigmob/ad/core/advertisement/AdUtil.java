package com.sigmob.ad.core.advertisement;

import static com.sigmob.ad.core.device.DeviceUtil.compareVersion;

import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.ByteString;
import com.happyelements.rdcenter.commons.util.MD5Util;
import com.sigmob.ad.adx.rpc.grpc.AdditionalResponse;
import com.sigmob.ad.adx.rpc.grpc.DspExtInfo;
import com.sigmob.ad.adx.rpc.grpc.RtbRequest;
import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.ad.adx.rpc.grpc.TemplateConfig;
import com.sigmob.ad.core.advertisement.AdHtmlTemplateMacro.Mraid2TemplateField;
import com.sigmob.ad.core.advertisement.AdHtmlTemplateMacro.MraidTemplateField;
import com.sigmob.ad.core.advertisement.model.AdMaterial;
import com.sigmob.ad.core.advertisement.model.AdPrivacyModel;
import com.sigmob.ad.core.advertisement.model.DspTemplateInfo;
import com.sigmob.ad.core.advertisement.model.Mraid2TemplateType;
import com.sigmob.ad.core.config.ConstantConfig;
import com.sigmob.ad.core.config.MediaPriceStrategyConfig;
import com.sigmob.ad.core.config.dsp.*;
import com.sigmob.ad.core.config.headerbidding.HeaderBiddingConfig;
import com.sigmob.ad.core.config.mediation.AppMiscellaneousConfig;
import com.sigmob.ad.core.config.settingconfig.SettingConfig;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.ad.core.constants.HeaderBiddingConstants;
import com.sigmob.ad.core.constants.enums.*;
import com.sigmob.ad.core.constants.enums.Currency;
import com.sigmob.ad.core.constants.enums.OsType;
import com.sigmob.ad.core.datalog.DataLogField;
import com.sigmob.ad.core.datalog.DataLogMessageBuilder;
import com.sigmob.ad.core.device.DeviceUtil;
import com.sigmob.ad.core.dspmanagement.DspProtocolType;
import com.sigmob.ad.core.dspmanagement.TradingType;
import com.sigmob.ad.core.exception.DspBusinessException;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.network.cnaaip.IpService;
import com.sigmob.ad.core.network.cnaaip.RegionType;
import com.sigmob.ad.core.publishing.app.AppInfo;
import com.sigmob.ad.core.publishing.developer.Developer;
import com.sigmob.ad.core.rtb.RtbConstants;
import com.sigmob.ad.core.rtb.model.HtmlTemplateParam;
import com.sigmob.ad.core.rtb.utils.AdTempActivityHelper;
import com.sigmob.ad.core.security.SecurityService;
import com.sigmob.ad.core.util.*;
import com.sigmob.ad.core.util.ApiAdBizHelper;
import com.sigmob.sigdsp.pb.*;
import com.sigmob.ssp.pb.dspmanagement.DspApiAdSlot;
import com.sigmob.ssp.pb.dspmanagement.DspInfo;
import com.sigmob.ssp.pb.publishing.management.AdSlotInfo;
import com.sigmob.ssp.pb.sdksetting.SdkCommonSetting;
import com.sigmob.ssp.pb.sdksetting.SdkNativeSetting;
import com.sigmob.ssp.pb.sdksetting.SdkSetting;
import com.twofishes.config.manager.ConfigManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;

/** 广告工具类 */
public class AdUtil {

  /**
   * 下发广告是否生成endCard，暂时只有sdk流量需要
   *
   * @param requestFlowType
   */
  public static boolean needEndcard(int requestFlowType, int adType) {
    return requestFlowType == RequestFlowType.SDK.getCode()
        && (adType == AdType.REWARDED_VIDEO.getTypeNum()
            || adType == AdType.FULL_SCREEN_VIDEO.getTypeNum());
  }

  /**
   * 下发广告是否需要伴随条，暂时只有sdk流量需要
   *
   * @param rtbRequest
   * @param adType
   * @return
   */
  public static boolean needCompanionBar(RtbRequest rtbRequest, int adType) {

    var companionBarConfig = ConfigManager.get(CompanionBarConfig.class);

    if (null != companionBarConfig) {
      return rtbRequest.getRequestFlowType() == RequestFlowType.SDK.getCode()
          && adType != AdType.SPLASH.getTypeNum()
          && adType != AdType.NATIVE.getTypeNum()
          && adType != AdType.INTERSTITIAL.getTypeNum()
          && companionBarConfig.allow(
              rtbRequest.getDspId(),
              rtbRequest.getSdkVersion(),
              rtbRequest.getBidRequest().getDevice().getDid().getUdid());
    }
    return false;
  }

  public static boolean needFullVideoClick(
      int osType, Version sdkVersion, int dspId, int adType, boolean needMiitCompliance) {
    if ((adType == AdType.REWARDED_VIDEO.getTypeNum()
            || adType == AdType.FULL_SCREEN_VIDEO.getTypeNum())
        && !needMiitCompliance) {
      return Optional.ofNullable(ConfigManager.get(DspMiscellaneousConfig.class))
          .map(dmConfig -> dmConfig.getDspMiscellaneous(dspId))
          .map(DspMiscellaneousConfig.DspMiscellaneous::isFullClickOnVideo)
          .orElse(false);
    }

    return false;
  }

  /**
   * 判断是否sigmob trackUrl
   *
   * @param trackUrl
   * @return
   */
  public static boolean isSigmobTrackUrl(String trackUrl) {
    if (!Strings.isNullOrEmpty(trackUrl)) {
      return trackUrl.contains("://track.sigmob.cn/")
          || trackUrl.contains("://trackstage.sigmob.cn/")
          || trackUrl.contains("://tracktp.sigmob.cn/")
          || trackUrl.contains("://tracktptest.sigmob.cn/")
          || trackUrl.contains("://ttp.sigmob.com/")
          || trackUrl.contains("://tracktp11.sigmob.cn/")
          || trackUrl.startsWith(AdMacro.TRACK_BASEURL);
    }
    return false;
  }

  public static Optional<AdPrivacy.Builder> buildAdPrivacy(
      AdPrivacyModel adPrivacyModel, Version sdkVersion) {

    if (null != adPrivacyModel && !adPrivacyModel.isEmpty()) {
      var adPrivacyBuilder = AdPrivacy.newBuilder();

      HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
      Optional.ofNullable(config)
          .flatMap(
              cf -> {
                if (compareVersion(sdkVersion, Constants.SDK_VERSION_4_16_0) == -1) {
                  return Optional.ofNullable(cf.getAdPrivacyTemplateUrl());
                } else {
                  return Optional.ofNullable(cf.getAdPrivacyTemplateUrlV2());
                }
              })
          .ifPresent(adPrivacyBuilder::setPrivacyTemplateUrl);

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppName())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_NAME.getName(), adPrivacyModel.getAppName());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppSize())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_SIZE.getName(), adPrivacyModel.getAppSize());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppVersion())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_VERSION.getName(), adPrivacyModel.getAppVersion());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppCompany())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_COMPANY.getName(), adPrivacyModel.getAppCompany());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppPrivacyUrl())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_PRIVACY_URL.getName(), adPrivacyModel.getAppPrivacyUrl());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppPrivacyText())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_PRIVACY_TEXT.getName(), adPrivacyModel.getAppPrivacyText());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppPermissionUrl())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_PERMISSION_URL.getName(),
            adPrivacyModel.getAppPermissionUrl());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppPermission())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_PERMISSION.getName(),
            adPrivacyModel.getAppPermission().replace("\n", StringUtils.EMPTY));
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppLandingpage())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_LANDING_PAGE_URL.getName(),
            adPrivacyModel.getAppLandingpage());
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppDesc())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_FUNCTION.getName(),
            TextUtils.toSafeString(adPrivacyModel.getAppDesc()));
      }

      if (!Strings.isNullOrEmpty(adPrivacyModel.getAppDescUrl())) {
        adPrivacyBuilder.putPrivacyTemplateInfo(
            AdPrivacyTemplateInfo.APP_FUNCTION_URL.getName(), adPrivacyModel.getAppDescUrl());
      }
      return Optional.of(adPrivacyBuilder);
    }

    return Optional.empty();
  }

  /**
   * 得到请求广告时广告位尺寸
   *
   * @param adSlot
   * @param dspApiAdSlot
   * @return
   */
  public static Size getRequestAdSlotSize(AdSlot adSlot, DspApiAdSlot dspApiAdSlot) {
    return null != dspApiAdSlot && dspApiAdSlot.hasSize()
        ? Size.newBuilder()
            .setWidth(dspApiAdSlot.getSize().getWidth())
            .setHeight(dspApiAdSlot.getSize().getHeight())
            .build()
        : adSlot.hasChangedSlotSize() ? adSlot.getChangedSlotSize() : adSlot.getAdslotSize();
  }

  /**
   * 得到请求广告时广告位尺寸
   *
   * @param adSlotBuilder
   * @param dspApiAdSlot
   * @return
   */
  public static Size getRequestAdSlotSize(AdSlot.Builder adSlotBuilder, DspApiAdSlot dspApiAdSlot) {
    return null != dspApiAdSlot && dspApiAdSlot.hasSize()
        ? Size.newBuilder()
            .setWidth(dspApiAdSlot.getSize().getWidth())
            .setHeight(dspApiAdSlot.getSize().getHeight())
            .build()
        : adSlotBuilder.hasChangedSlotSize()
            ? adSlotBuilder.getChangedSlotSize()
            : adSlotBuilder.getAdslotSize();
  }

  public static Size getRequestNativeAssetSize(RequestNativeAdTemplate nativeAdTemplate) {
    int nativeAdTemplateType = nativeAdTemplate.getType();
    List<RequestAsset> assetsList = nativeAdTemplate.getAssetsList();
    Size size = Size.getDefaultInstance();
    for (RequestAsset asset : assetsList) {
      if ((nativeAdTemplateType == NativeAdTemplateType.IMAGE.getCode()
              || nativeAdTemplateType == NativeAdTemplateType.THREE_IMAGE.getCode())
          && asset.hasImage()) {
        RequestAssetImage image = asset.getImage();
        if (image.getType() == NativeImageAssetType.MAIN.getCode()) {
          int imageH = image.getH();
          int imageW = image.getW();
          if (imageH == 0 && imageW == 0) {
            imageH = nativeAdTemplate.getH();
            imageW = nativeAdTemplate.getW();
          }
          size = Size.newBuilder().setHeight(imageH).setWidth(imageW).build();
          break;
        }
      } else if (nativeAdTemplateType == NativeAdTemplateType.VIDEO.getCode() && asset.hasVideo()) {
        RequestAssetVideo video = asset.getVideo();
        int videoH = video.getH();
        int videoW = video.getW();
        if (videoH == 0 && videoW == 0) {
          videoH = nativeAdTemplate.getH();
          videoW = nativeAdTemplate.getW();
        }
        size = Size.newBuilder().setHeight(videoH).setWidth(videoW).build();
        break;
      }
    }
    return size;
  }

  /**
   * 广告对应事件
   *
   * @param isVideoAd 是否视频广告
   * @param isDownloadAd 是否下载类广告
   * @param isDeepLinkAd 是否deeplink唤起类广告
   * @param hasEndcard 是否有endcard
   * @param hasCompanion 是否有伴随条
   * @param adType 广告类型
   * @return
   */
  public static Set<TrackingEvent> getTrackEventSet(
      boolean isVideoAd,
      boolean isDownloadAd,
      boolean isDeepLinkAd,
      boolean hasEndcard,
      boolean hasCompanion,
      int adType,
      int osType,
      Version sdkVersion,
      int dspId,
      boolean needMiitCompliance) {

    // 非标准协议dsp暂时不支持吊起应用商店页面广告，所以流量类型默认传sdk、isAppMarket默认false
    return getTrackEventSet(
        isVideoAd,
        isDownloadAd,
        isDeepLinkAd,
        hasEndcard,
        hasCompanion,
        adType,
        osType,
        sdkVersion,
        RequestFlowType.SDK.getCode(),
        false,
        false,
        dspId,
        needMiitCompliance);
  }

  public static Set<TrackingEvent> getTrackEventSet(
      boolean isVideoAd,
      boolean isDownloadAd,
      boolean isDeepLinkAd,
      boolean hasEndcard,
      boolean hasCompanion,
      int adType,
      int osType,
      Version sdkVersion,
      int requestFlowType,
      boolean isAppMarket,
      boolean useOpenPkg,
      int dspId,
      boolean needMiitCompliance) {
    Set<TrackingEvent> trackEventSet = Sets.newHashSet();
    trackEventSet.add(TrackingEvent.start);
    trackEventSet.add(TrackingEvent.click);
    trackEventSet.add(TrackingEvent.skip);

    var isSplashAd = adType == AdType.SPLASH.getTypeNum();
    var isFullScreenVideoAd = adType == AdType.FULL_SCREEN_VIDEO.getTypeNum();
    var isInterstitialAd = adType == AdType.INTERSTITIAL.getTypeNum();
    var isNativeAd = adType == AdType.NATIVE.getTypeNum();
    var isRewardedVideoAd = adType == AdType.REWARDED_VIDEO.getTypeNum();
    var isSdkRequestFlow = requestFlowType == RequestFlowType.SDK.getCode();
    var isViVoRequestFlow = requestFlowType == RequestFlowType.VIVO.getCode();

    for (TrackingEvent event : TrackingEvent.values()) {
      if (isVideoAd) {
        if (event.equals(TrackingEvent.play_quarter)
            || event.equals(TrackingEvent.play_two_quarters)
            || event.equals(TrackingEvent.play_three_quarters)
            || event.equals(TrackingEvent.complete)) {
          trackEventSet.add(event);
        }
        if ((isViVoRequestFlow || isNativeAd) && event.equals(TrackingEvent.video_start)) {
          trackEventSet.add(event);
        }

        if ((isNativeAd || isInterstitialAd) && event.equals(TrackingEvent.video_pause)) {
          trackEventSet.add(event);
        }

        if (isNativeAd && event.equals(TrackingEvent.video_restart)) {
          trackEventSet.add(event);
        }
      }
      if (event.equals(TrackingEvent.full_video_click)
          && needFullVideoClick(osType, sdkVersion, dspId, adType, needMiitCompliance)) {
        trackEventSet.add(event);
      }

      if (isRewardedVideoAd) {
        if (event.equals(TrackingEvent.finish)) {
          trackEventSet.add(event);
        }
      }

      if (isFullScreenVideoAd || isRewardedVideoAd) {
        if (event.equals(TrackingEvent.show_skip)) {
          trackEventSet.add(event);
        }
      }

      if (event.equals(TrackingEvent.ad_close)
          && (isSplashAd || isInterstitialAd || isRewardedVideoAd || isFullScreenVideoAd)) {
        trackEventSet.add(event);
      }

      if (isNativeAd) {
        if (event.equals(TrackingEvent.ad_show) || event.equals(TrackingEvent.ad_hide)) {
          trackEventSet.add(event);
        }
      }

      if (isInterstitialAd) {
        if (event.equals(TrackingEvent.charge)) {
          trackEventSet.add(event);
        }
      }
      if (isDownloadAd) {
        if (event.equals(TrackingEvent.download_start)
            || event.equals(TrackingEvent.download_finish)
            || event.equals(TrackingEvent.install_start)
            || event.equals(TrackingEvent.install_finish)) {
          trackEventSet.add(event);
        }
      } else if (isSdkRequestFlow && isAppMarket) {
        if (event.equals(TrackingEvent.install_start)
            || event.equals(TrackingEvent.install_finish)) {
          trackEventSet.add(event);
        }
      }

      if (isDeepLinkAd) {
        if (event.equals(TrackingEvent.open_deeplink)
            || event.equals(TrackingEvent.open_deeplink_failed)) {
          trackEventSet.add(event);
        }
        if (isSdkRequestFlow
            && (osType == OsType.IOS.getTypeNum()
                    && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_19_0) >= 0
                || osType == OsType.ANDROID.getTypeNum()
                    && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_23_0) >= 0)
            && (event.equals(TrackingEvent.can_dp) || event.equals(TrackingEvent.no_can_dp))) {
          trackEventSet.add(event);
        }
      }

      if (event.equals(TrackingEvent.companion_click)) {
        trackEventSet.add(event);
      }

      if (hasEndcard && event.equals(TrackingEvent.show)) {
        trackEventSet.add(event);
      }

      if (useOpenPkg && event.equals(TrackingEvent.open_pkg)) {
        trackEventSet.add(event);
      }
    }

    return trackEventSet;
  }

  /**
   * ssp打点版本
   *
   * @param rtbRequest
   * @return
   */
  public static String getSspVersion(RtbRequest rtbRequest) {

    if (null == rtbRequest
        || rtbRequest.equals(RtbRequest.getDefaultInstance())
        || !rtbRequest.hasBidRequest()
        || rtbRequest.getBidRequest().getSlotsCount() <= 0) {
      return DataLogField.SSP_VERSION_1_0_0;
    }

    var adSlot = rtbRequest.getBidRequest().getSlots(0);
    // 只有sdk流量信息流广告使用多广告格式打点
    if (adSlot.getAdslotType(0) == AdType.NATIVE.getTypeNum()) {
      return DataLogField.SSP_VERSION_2_0_0;
    } else {
      return DataLogField.SSP_VERSION_1_0_0;
    }
  }

  /**
   * 外部dsp的sigmob track基准url
   *
   * @return
   * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135445722">标准api媒体替换监测链接域名</a>
   */
  public static String getThirdPartyDspSigmobTrackBaseUrl(boolean modifyTrackURL) {
    SettingConfig settingConfig = ConfigManager.get(SettingConfig.class);
    return modifyTrackURL
        ? settingConfig.getCommonUrls().get(Constants.URL_TYPE_THIRD_PARTY_TRACK_V2)
        : settingConfig.getCommonUrls().get(Constants.URL_TYPE_THIRD_PARTY_TRACK);
  }

  /**
   * <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135445722">标准api媒体替换监测链接域名</a>
   *
   * @return url
   */
  public static String getStandardMediaTrackUrl() {
    SettingConfig settingConfig = ConfigManager.get(SettingConfig.class);
    return settingConfig.getCommonUrls().get(Constants.STANDARD_MEDIA_HOST);
  }

  /**
   * 获取sigmob track url参数c
   *
   * @param productId
   * @param campId
   * @param creativeId
   * @param rtbRequest
   * @param settlementPriceEnc
   * @return
   */
  public static String getSigmobTrackParamCommon(
      String productId,
      String campId,
      String creativeId,
      RtbRequest rtbRequest,
      String settlementPriceEnc,
      String vid) {
    BidRequest bidRequest = rtbRequest.getBidRequest();
    DspInfo dspInfo = rtbRequest.getDspInfo();
    TrackLog.Builder trackLogBuilder = TrackLog.newBuilder();

    int bidType = 0;
    boolean enableDspReqId = false;
    if (!rtbRequest.equals(RtbRequest.getDefaultInstance())
        && !DspInfo.getDefaultInstance().equals(dspInfo)) {
      if (dspInfo.getRtb()) {
        bidType = BidType.CPM.getTypeNum();
      }
      enableDspReqId = dspInfo.getEnableDspReqId();
    }
    String dspReqId = StringUtils.EMPTY;
    if (enableDspReqId && rtbRequest.hasDspExtInfo()) {
      DspExtInfo dspExtInfo = rtbRequest.getDspExtInfo();
      dspReqId = dspExtInfo.getDspReqId();
    }
    AdInfo.Builder adBuilder =
        AdInfo.newBuilder()
            .setAdSourceChannel(dspInfo.getDspId())
            .setAdverAppId(productId) // 下载类广告，安卓填包名，ios填store id
            .setAdvertiserPrice(0)
            .setBidPrice(0)
            .setBidType(bidType)
            .setCampId(campId)
            .setCreativeId(creativeId)
            .setMaterialId(StringUtils.EMPTY)
            .setProductId(StringUtils.EMPTY)
            .setSettlementPrice(0)
            .setSettlementPriceEnc(settlementPriceEnc);

    DeviceId bidDeviceId = bidRequest.getDevice().getDid();
    DeviceId.Builder trackDeviceIdBuilder = DeviceId.newBuilder();
    trackDeviceIdBuilder
        .setAndroidId(bidDeviceId.getAndroidId())
        .setAndroidUuid(bidDeviceId.getAndroidUuid())
        .setBrand(bidDeviceId.getBrand())
        .setGaid(bidDeviceId.getGaid())
        .setIdfa(bidDeviceId.getIdfa())
        .setImei(bidDeviceId.getImei())
        .setImsi(bidDeviceId.getImsi())
        .setUdid(bidDeviceId.getUdid())
        .setUserId(bidDeviceId.getUserId())
        .setUid(bidDeviceId.getUid());

    com.sigmob.sigdsp.pb.System.Builder bidSystem = com.sigmob.sigdsp.pb.System.newBuilder();
    bidSystem
        .setClientIp(bidRequest.getNetwork().getIpv4())
        .setServerIp(SystemUtils.getLocalIp())
        .setUseragent(bidRequest.getNetwork().getUa());

    var adSlot = bidRequest.getSlots(0);
    var requestFlowType = rtbRequest.getRequestFlowType();
    trackLogBuilder
        .setDid(trackDeviceIdBuilder)
        .setVid(vid)
        .setSys(bidSystem)
        .setTimestamp(bidRequest.getReqTimestamp())
        .setUserIp(bidRequest.getNetwork().getIpv4())
        .setOsType(bidRequest.getDevice().getOsType())
        .setRequestId(bidRequest.getRequestId())
        .setEnableDspReqId(enableDspReqId ? 1 : 0)
        .setDspRequestId(dspReqId)
        .setAdInfo(adBuilder)
        .setAdslotId(adSlot.getAdslotId())
        .setAdType(adSlot.getAdslotType(0))
        .setAppId(Integer.toString(rtbRequest.getAppId()))
        .setChannelId("1")
        .setSEffective(adSlot.getSettlementMode())
        .setSType(BidType.CPM.getTypeNum())
        .setFType(requestFlowType)
        .setSettlement(adSlot.getSettlementSetting())
        .setCommerEcpmState(adSlot.getCommerEcpmState())
        .setTransAdType(adSlot.getTransAdType())
        .setReqTimestamp(bidRequest.getReqTimestamp());
    // 下发给8号点打点使用
    Map<String, String> optionsMap = rtbRequest.getBidRequest().getExtOptionsMap();
    if (MapUtils.isNotEmpty(optionsMap)) {
      String channelId = optionsMap.get(DataLogField.SAAS_CHANNEL_ID);
      trackLogBuilder
          .setAdxId(Integer.parseInt(channelId))
          .setSaasLoadId(optionsMap.get(DataLogField.SAAS_LOAD_ID))
          .setSaasChannelId(optionsMap.get(DataLogField.SAAS_CHANNEL_ID))
          .setSaasSubChannelId(optionsMap.get(DataLogField.SAAS_SUB_CHANNEL_ID))
          .setSaasAdChannelId(optionsMap.get(DataLogField.SAAS_AD_CHANNEL_ID));
    }

    trackLogBuilder.setApiAppid(
        DspUtil.getRequestDspAppId(rtbRequest, DspUtil.dspRequestDspAppIdUsage(rtbRequest)));

    trackLogBuilder.setApiPlacementId(
        DspUtil.getRequestDspPlacementId(
            rtbRequest, DspUtil.dspRequestDspPlacementIdUsage(rtbRequest)));

    if (requestFlowType != RequestFlowType.SDK.getCode()) {
      trackLogBuilder
          .setSspAppId(rtbRequest.getApiAppId())
          .setSspPlacementId(rtbRequest.getApiPlacementId());
    }
    var sdkVersion = rtbRequest.getSdkVersion();
    var paramC = Base64.getUrlEncoder().encodeToString(trackLogBuilder.build().toByteArray());
    if (DeviceUtil.isGt3_4_0VersionAndSdkType(
        sdkVersion, rtbRequest.getSdkType(), bidRequest.getDevice().getOsType())) {
      return URLEncoder.encode(paramC, StandardCharsets.UTF_8);
    } else {
      return paramC;
    }
  }

  public static String buildSigmobTrackingUrl(
      String baseUrl,
      String c,
      String vid,
      String settlementPriceEnc,
      String eventName,
      RtbRequest rtbRequest,
      int adType,
      boolean needBid) {

    return buildSigmobTrackingUrl(
        baseUrl, c, vid, settlementPriceEnc, eventName, rtbRequest, adType, needBid, true);
  }

  public static String buildSigmobTrackingUrl(
      String baseUrl,
      String c,
      String vid,
      String settlementPriceEnc,
      String eventName,
      RtbRequest rtbRequest,
      int adType,
      boolean needBid,
      boolean disableMraid2) {
    var requestFlowType = rtbRequest.getRequestFlowType();
    var isSdkRequestFlow = requestFlowType == RequestFlowType.SDK.getCode();
    StringBuilder b = new StringBuilder(128);
    var sdkVersion = rtbRequest.getSdkVersion();
    var osType = rtbRequest.getBidRequest().getDevice().getOsType();
    var isSplashAd = adType == AdType.SPLASH.getTypeNum();
    boolean isClickSupportNc =
        isSdkRequestFlow
            && isSplashAd
            && (osType == OsType.ANDROID.getTypeNum()
                    && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_22_0) >= 0
                || osType == OsType.IOS.getTypeNum()
                    && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_18_0) >= 0
                || osType == OsType.HARMONY_OS.getTypeNum());

    b.append(baseUrl)
        .append(
            baseUrl.indexOf(Constants.QUESTION_MARK) > -1
                ? Constants.SYMBOL_AND
                : Constants.QUESTION_MARK)
        .append("c=")
        .append(URLEncoder.encode(c, StandardCharsets.UTF_8))
        .append("&e=")
        .append(eventName)
        .append("&p=")
        .append(
            needBid ? RtbConstants.THIRD_PARTY_DSP_SIGMOB_TRACK_MACRO_PRICE : settlementPriceEnc)
        .append("&vid=")
        .append(vid)
        .append(AdMacro.SAAS_CHANNEL_ID_MACRO)
        .append(AdMacro.SAAS_SUB_CHANNEL_ID_MACRO);
    var isCompanionClick = TrackingEvent.companion_click.name().equals(eventName);
    if (isCompanionClick) {
      b.append("&ux=")
          .append(AdMacro.CLICK_UP_X)
          .append("&uy=")
          .append(AdMacro.CLICK_UP_Y)
          .append("&dx=")
          .append(AdMacro.CLICK_DOWN_X)
          .append("&dy=")
          .append(AdMacro.CLICK_DOWN_Y);
    }

    var isInterstitialAd = adType == AdType.INTERSTITIAL.getTypeNum();
    var isNativeAd = adType == AdType.NATIVE.getTypeNum();
    var isClick = TrackingEvent.click.name().equals(eventName);
    if (isClick) {
      var supportSplashTemplate =
          isSplashAd
              && SdkCapabilityUtils.supportNewSplashTemplate(requestFlowType, sdkVersion, osType);
      if (isSdkRequestFlow) {
        b.append("&click_area=")
            .append(AdMacro.CLICK_AREA)
            .append("&click_scene=")
            .append(AdMacro.CLICK_SCENE)
            .append("&is_final_click=")
            .append(AdMacro.FINAL_CLICK);
        if (adType != AdType.INTERSTITIAL.getTypeNum()) {
          b.append("&auto_click=").append(AdMacro.AUTO_CLICK);
        }
      }
      if (!isSdkRequestFlow || isInterstitialAd || supportSplashTemplate || isNativeAd) {
        b.append("&ux=")
            .append(AdMacro.CLICK_UP_X)
            .append("&uy=")
            .append(AdMacro.CLICK_UP_Y)
            .append("&dx=")
            .append(AdMacro.CLICK_DOWN_X)
            .append("&dy=")
            .append(AdMacro.CLICK_DOWN_Y);
      }
      if (isInterstitialAd || supportSplashTemplate || isNativeAd) {
        b.append("&template=")
            .append(AdMacro.TRACK_TEMPLATE_ID)
            .append("&sw=")
            .append(AdMacro.AD_SLOT_WIDTH)
            .append("&sh=")
            .append(AdMacro.AD_SLOT_HEIGHT);
        if (isInterstitialAd && disableMraid2
            || osType == OsType.ANDROID.getTypeNum()
                && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_19_0) < 0
            || osType == OsType.IOS.getTypeNum()
                && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_15_0) < 0) {
          b.append("&sld=0");
        } else {
          if (isInterstitialAd) {
            b.append("&accpt_ids=")
                .append(AdMacro.TRACK_AC_CPT_IDS)
                .append("&atcpt_ids=")
                .append(AdMacro.TRACK_AT_CPT_IDS)
                .append("&xx=")
                .append(AdMacro.TRACK_XX)
                .append("&xy=")
                .append(AdMacro.TRACK_XY)
                .append("&xradius=")
                .append(AdMacro.TRACK_X_RADIUS)
                .append("&ecr=")
                .append(AdMacro.TRACK_WRONG_CLICK_RATE)
                .append("&is_ec=")
                .append(AdMacro.TRACK_IS_WRONG_CLICK);
          }

          b.append("&ax=")
              .append(AdMacro.TRACK_AX)
              .append("&ay=")
              .append(AdMacro.TRACK_AY)
              .append("&aw=")
              .append(AdMacro.TRACK_AW)
              .append("&ah=")
              .append(AdMacro.TRACK_AH);
        }
      }
    }

    if (isSdkRequestFlow
        && (isClick
            || isCompanionClick
            || TrackingEvent.full_video_click.name().equals(eventName))) {
      b.append("&sld=").append(AdMacro.TRACK_SLD).append("&cpt_id=").append(AdMacro.TRACK_CPT_ID);
      if (isClickSupportNc) {
        b.append("&nc=").append(AdMacro.TRACK_IS_NC);
      }
    }

    // ssp v3.0.0版需求 激励视频&全屏视频，8号点需要在start、play相关事件、Finish、Complete点增加字段
    if ((adType == AdType.REWARDED_VIDEO.getTypeNum()
            || adType == AdType.FULL_SCREEN_VIDEO.getTypeNum())
        && (TrackingEvent.start.name().equals(eventName)
            || TrackingEvent.play_quarter.name().equals(eventName)
            || TrackingEvent.play_two_quarters.name().equals(eventName)
            || TrackingEvent.play_three_quarters.name().equals(eventName)
            || TrackingEvent.finish.name().equals(eventName)
            || TrackingEvent.complete.name().equals(eventName))) {
      b.append("&set_close_time=")
          .append(AdMacro.SET_CLOSE_TIME)
          .append("&is_truncation=")
          .append(AdMacro.IS_TRUNCATION)
          .append("&play_time=")
          .append(AdMacro.END_TIME)
          .append("&ad_scene=")
          .append(AdMacro.AD_SCENE);
    }

    if (isNativeAd
        && (TrackingEvent.video_start.name().equals(eventName)
            || TrackingEvent.video_restart.name().equals(eventName)
            || TrackingEvent.video_pause.name().equals(eventName)
            || TrackingEvent.complete.name().equals(eventName))) {

      b.append("&video_time=")
          .append(AdMacro.VIDEO_TIME)
          .append("&begin_time=")
          .append(AdMacro.BEGIN_TIME);
      if (TrackingEvent.video_pause.name().equals(eventName)
          || TrackingEvent.complete.name().equals(eventName)) {
        b.append("&play_time=")
            .append(AdMacro.END_TIME)
            .append("&ad_scene=")
            .append(AdMacro.AD_SCENE)
            .append("&is_first=")
            .append(AdMacro.IS_FIRST_FRAME)
            .append("&is_last=")
            .append(AdMacro.IS_LAST_FRAME)
            .append("&scene=")
            .append(AdMacro.SCENE)
            .append("&type=")
            .append(AdMacro.PLAY_TYPE)
            .append("&is_auto_play=")
            .append(AdMacro.PLAY_BEHAVIOR)
            .append("&status=")
            .append(AdMacro.STATUS);
      }
    }

    if (isInterstitialAd) {
      if (TrackingEvent.complete.name().equals(eventName)
          || TrackingEvent.video_pause.name().equals(eventName)) {
        b.append("&impid=")
            .append(AdMacro.TRACK_IMP_ID)
            .append("&video_time=")
            .append(AdMacro.VIDEO_TIME)
            .append("&begin_time=")
            .append(AdMacro.BEGIN_TIME)
            .append("&end_time=")
            .append(AdMacro.END_TIME)
            .append("&is_first=")
            .append(AdMacro.IS_FIRST_FRAME)
            .append("&is_last=")
            .append(AdMacro.IS_LAST_FRAME)
            .append("&scene=")
            .append(AdMacro.SCENE)
            .append("&type=")
            .append(AdMacro.PLAY_TYPE)
            .append("&is_auto_play=")
            .append(AdMacro.PLAY_BEHAVIOR)
            .append("&status=")
            .append(AdMacro.STATUS);
      } else if (TrackingEvent.video_start.name().equals(eventName)) {
        b.append("&video_time=")
            .append(AdMacro.VIDEO_TIME)
            .append("&begin_time=")
            .append(AdMacro.BEGIN_TIME);
      }
    }

    return b.toString();
  }

  public static List<String> restoreSigmobTrackUrl(
      Map<String, String[]> macros, List<String> trackList) {
    if (CollectionUtils.isEmpty(macros) || CollectionUtils.isEmpty(trackList)) {
      return trackList;
    }

    List<String> restoreTrackList = Lists.newArrayListWithCapacity(trackList.size());
    trackList.stream()
        .filter(track -> !Strings.isNullOrEmpty(track))
        .forEach(
            track -> {
              if (AdUtil.isSigmobTrackUrl(track)) {
                restoreTrackList.add(replaceMacro(macros, track));
              } else {
                restoreTrackList.add(track);
              }
            });
    return restoreTrackList;
  }

  public static List<String> replaceTrackUrlMacro(
      Map<String, String[]> macros, List<String> trackList, boolean replaceSigmobTrack) {
    if (CollectionUtils.isEmpty(macros) || CollectionUtils.isEmpty(trackList)) {
      return trackList;
    }

    List<String> restoreTrackList = Lists.newArrayListWithCapacity(trackList.size());
    trackList.stream()
        .filter(track -> !Strings.isNullOrEmpty(track))
        .forEach(
            track -> {
              var isSigmobTrackUrl = AdUtil.isSigmobTrackUrl(track);
              if (isSigmobTrackUrl) {
                if (replaceSigmobTrack) {
                  restoreTrackList.add(replaceMacro(macros, track));
                } else {
                  restoreTrackList.add(track);
                }
              } else {
                if (replaceSigmobTrack) {
                  restoreTrackList.add(track);
                } else {
                  restoreTrackList.add(replaceMacro(macros, track));
                }
              }
            });
    return restoreTrackList;
  }

  /** 替换链接中的宏 */
  public static List<String> replaceUrlMacro(Map<String, String[]> macros, List<String> trackList) {
    if (CollectionUtils.isEmpty(macros) || CollectionUtils.isEmpty(trackList)) {
      return trackList;
    }

    List<String> restoreTrackList = Lists.newArrayListWithCapacity(trackList.size());
    trackList.stream()
        .filter(track -> !Strings.isNullOrEmpty(track))
        .forEach(track -> restoreTrackList.add(replaceMacro(macros, track)));
    return restoreTrackList;
  }

  /** 宏替换 */
  public static String replaceMacro(Map<String, String[]> macroInfo, String source) {
    if (CollectionUtils.isEmpty(macroInfo) || Strings.isNullOrEmpty(source)) {
      return source;
    }
    return StringUtils.replaceEach(
        source,
        macroInfo.get(Constants.MACRO_INFO_KEYS),
        macroInfo.get(Constants.MACRO_INFO_VALUES));
  }

  /**
   * 设置请求dsp时广告转换类型
   *
   * @param supportMaterialType
   * @param dspProtocolType
   * @param adSlotBuilder
   * @param dspApiAdSlot
   */
  public static void setTransAdType(
      int supportMaterialType,
      int dspProtocolType,
      AdSlot.Builder adSlotBuilder,
      DspApiAdSlot dspApiAdSlot) {
    var originalAdType = adSlotBuilder.getAdslotType(0);
    if (dspProtocolType == DspProtocolType.SIGMOB_DSP.getType()) {

      if (originalAdType == AdType.NATIVE.getTypeNum()) {
        adSlotBuilder.setTransAdType(AdType.REWARDED_VIDEO.getTypeNum());
      }
    } else if (dspProtocolType == DspProtocolType.STANDARD.getType()
        && supportMaterialType != MaterialType.ONLY_VIDEO.getCode()
        && originalAdType == AdType.FULL_SCREEN_VIDEO.getTypeNum()) {
      adSlotBuilder.setTransAdType(AdType.SPLASH.getTypeNum());
    } else if (dspProtocolType == DspProtocolType.VOICEADS.getType()) {
      if (supportMaterialType != MaterialType.ONLY_VIDEO.getCode()
          && originalAdType == AdType.FULL_SCREEN_VIDEO.getTypeNum()) {
        adSlotBuilder.setTransAdType(AdType.SPLASH.getTypeNum());
      } else if (originalAdType == AdType.REWARDED_VIDEO.getTypeNum()) {
        adSlotBuilder.setTransAdType(AdType.SPLASH.getTypeNum());
      }
    } else if (null != dspApiAdSlot && dspProtocolType == DspProtocolType.STANDARD.getType()) {
      adSlotBuilder.setTransAdType(dspApiAdSlot.getAdSlotType());
    } else {
      adSlotBuilder.setTransAdType(originalAdType);
    }
  }

  public static String getTrackSPrice(
      Ad.Builder adBuilder,
      RtbRequest rtbRequest,
      RtbResponse.Builder rtbResponseBuilder,
      SecurityService securityService,
      String trackingSecurityKey,
      Optional<Developer> developerOptional) {

    var adSlot = rtbRequest.getBidRequest().getSlots(0);
    var settlementSetting = adSlot.getSettlementSetting();
    var requestFlowType = rtbRequest.getRequestFlowType();
    var apiConfigOptional =
        ApiAdBizHelper.getRequestApiConfig(requestFlowType, rtbRequest.getApiDeveloperId());
    if (settlementSetting == SettlementSetting.BIDDING.getCode()) {
      // 为外部流量在sigmob trackUrl后添加三方结算相关宏

      if (requestFlowType == RequestFlowType.SDK.getCode()) { // header bidding流量
        if (rtbRequest.getHbType() != HeaderBiddingType.NOT_HB.getCode()) {
          var headerBiddingConfig = ConfigManager.get(HeaderBiddingConfig.class);
          //          var appId = Integer.parseInt(rtbRequest.getBidRequest().getApp().getAppId());
          if (rtbRequest.getAt() == AuctionType.SECOND_PRICE.getCode()) { // 二价
            return headerBiddingConfig.getPriceMacro();
          } else { // 非二价默认一价
            if (rtbRequest.getSdkType() == SdkType.TO_BID.getType()
                && adSlot.getAdxId() != RtbConstants.TOBID_ADX_ID) {
              return HeaderBiddingConstants.WINDMILL_SIGMOB_BID_PRICE_MACRO;
            }

            // c2s 和 s2s都使用expectedFloor作为底价
            var expectedFloor = adSlot.getExpectedFloor();

            String vid = adBuilder.getVid();
            //            int tradingType = getTradingType(rtbResponseBuilder, vid);
            //            // if pdb 不走实验
            //            if (TradingType.PDB.getTypeNum() == tradingType) {
            //              RtbResponse rtbResponse = rtbResponseBuilder.build();
            //              int pdbSettlementPrice4Supply =
            //                  HbUtil.getPdbSettlementPrice4Supply(rtbResponse, vid,
            // expectedFloor);
            //              return securityService.encodeBase64URLSafe(
            //                  trackingSecurityKey, Integer.toString(pdbSettlementPrice4Supply));
            //            }
            var expiredAdPrice = rtbRequest.getExpiredAdPrice();
            if (expiredAdPrice > 0) {
              return securityService.encodeBase64URLSafe(
                  trackingSecurityKey, Integer.toString(expiredAdPrice));
            }
            if (rtbResponseBuilder.hasAdxExperiment()) {
              var adxExperiment = rtbResponseBuilder.getAdxExperiment();
              var adExpWinPrice = adxExperiment.getExpWinPriceMap().get(vid);
              if (null != adExpWinPrice) {
                var currency = rtbRequest.getCurrency();
                int finalPrice = adExpWinPrice;
                if (finalPrice == 0 || finalPrice < expectedFloor) {
                  if (Currency.CNY.getName().equals(currency)) {
                    finalPrice = expectedFloor + 1;
                  } else {
                    finalPrice = expectedFloor;
                  }
                }
                return securityService.encodeBase64URLSafe(
                    trackingSecurityKey, Integer.toString(finalPrice));
              }
              if (rtbResponseBuilder.getAdditionalResponsesCount() > 0) {
                var additionalResponse = rtbResponseBuilder.getAdditionalResponsesMap().get(vid);
                if (null != additionalResponse
                    && !additionalResponse.getFinalWinPriceEnc().isBlank()) {
                  return additionalResponse.getFinalWinPriceEnc();
                }
              }
              return rtbResponseBuilder.getFinalWinPriceEnc();

            } else {
              int settlementPrice = getSettlementPrice(rtbResponseBuilder, vid);
              return securityService.encodeBase64URLSafe(
                  trackingSecurityKey, Integer.toString(settlementPrice));
            }
          }
        } else { // sdk非hb流量竞价模式
          return AdMacro.TRACK_AUCTION_PRICE;
        }
      } else {

        // 对标准ssp协议的爱思助手流量，s_price宏在ssp中有特殊处理，如果此处逻辑有较大改动需要考虑爱思助手是否也要修改
        // http://wiki.sigmob.cn/pages/viewpage.action?pageId=103832063
        if (developerOptional.isPresent()) {
          return Constants.AES_CRYPTO.encryptWithUrlSafe(
              developerOptional.get().getSecretKey(), Integer.toString(adSlot.getBidfloor()));
        }
        return apiConfigOptional
            .map(apiConfig -> Strings.nullToEmpty(apiConfig.getPriceMacro()))
            .orElse(StringUtils.EMPTY);
      }

    } else {
      return securityService.encodeBase64URLSafe(
          trackingSecurityKey,
          Integer.toString(
              requestFlowType == RequestFlowType.YUEYOU.getCode()
                  ? adSlot.getBidfloor()
                  : adSlot.getCommerEcpm()));
    }
  }

  public static int getTradingType(RtbResponse.Builder rtbResponseBuilder, String vid) {
    int tradingType = rtbResponseBuilder.getTradingType();
    // native
    if (rtbResponseBuilder.getAdditionalResponsesCount() > 0) {
      var additionalResponse = rtbResponseBuilder.getAdditionalResponsesMap().get(vid);
      if (null != additionalResponse) {
        // 多广告会存在多个tradingType，可能来自不同的交易模式
        if (additionalResponse.getTradingType() > 0) {
          tradingType = additionalResponse.getTradingType();
        }
      }
    }
    return tradingType;
  }

  public static int getTradingType(RtbResponse rtbResponseBuilder, String vid) {
    int tradingType = rtbResponseBuilder.getTradingType();
    // native
    if (rtbResponseBuilder.getAdditionalResponsesCount() > 0) {
      var additionalResponse = rtbResponseBuilder.getAdditionalResponsesMap().get(vid);
      if (null != additionalResponse) {
        // 多广告会存在多个tradingType，可能来自不同的交易模式
        if (additionalResponse.getTradingType() > 0) {
          tradingType = additionalResponse.getTradingType();
        }
      }
    }
    return tradingType;
  }

  public static String getTrackUrlRebateSettlementPriceEnc(
      RtbRequest rtbRequest,
      RtbResponse.Builder rtbResponseBuilder,
      String vid,
      SecurityService securityService,
      String trackingSecurityKey) {

    int tradingType = rtbResponseBuilder.getTradingType();
    int dspRebateRatio = rtbRequest.getDspRebateRatio();
    int dspRebateSettlementPrice = rtbResponseBuilder.getDspRebateSettlementPrice();
    int settlementPriceForDsp = rtbResponseBuilder.getSettlementPriceForDsp();
    if (rtbResponseBuilder.getAdditionalResponsesCount() > 0) {
      var additionalResponsesMap = rtbResponseBuilder.getAdditionalResponsesMap();
      var additionalResponse = additionalResponsesMap.get(vid);
      if (null != additionalResponse) {
        if (additionalResponse.getDspRebateSettlementPrice() > 0) {
          dspRebateSettlementPrice = additionalResponse.getDspRebateSettlementPrice();
        }
        if (additionalResponse.getSettlementPriceForDsp() > 0) {
          settlementPriceForDsp = additionalResponse.getSettlementPriceForDsp();
        }
        if (additionalResponse.getTradingType() > 0) {
          tradingType = additionalResponse.getTradingType();
        }
        var additionalDspRebateRatio = additionalResponse.getDspRebateRatio();
        if (additionalDspRebateRatio != 0) {
          dspRebateRatio = additionalDspRebateRatio;
        }
      }
    }

    if ((tradingType == TradingType.RTB.getTypeNum()
        || tradingType == TradingType.PDB.getTypeNum()
        || tradingType == TradingType.DEAL.getTypeNum())) {
      if (dspRebateRatio != 0) {
        return securityService.encodeBase64URLSafe(
            trackingSecurityKey, Integer.toString(dspRebateSettlementPrice));
      } else {
        return securityService.encodeBase64URLSafe(
            trackingSecurityKey, Integer.toString(settlementPriceForDsp));
      }
    }

    return StringUtils.EMPTY;
  }

  public static int getSettlementPriceForDsp(RtbResponse.Builder rtbResponse, String adVid) {
    int settlementPriceForDsp = rtbResponse.getSettlementPriceForDsp();
    // native
    if (rtbResponse.getAdditionalResponsesCount() > 0) {
      var additionalResponse = rtbResponse.getAdditionalResponsesMap().get(adVid);
      if (null != additionalResponse) {
        if (additionalResponse.getSettlementPriceForDsp() > 0) {
          settlementPriceForDsp = additionalResponse.getSettlementPriceForDsp();
        }
      }
    }
    return settlementPriceForDsp;
  }

  public static int getSettlementPrice(RtbResponse.Builder rtbResponse, String adVid) {
    int settlementPrice = rtbResponse.getSettlementPrice();
    // native
    if (rtbResponse.getAdditionalResponsesCount() > 0) {
      var additionalResponse = rtbResponse.getAdditionalResponsesMap().get(adVid);
      if (null != additionalResponse) {
        if (additionalResponse.getSettlementPrice() > 0) {
          settlementPrice = additionalResponse.getSettlementPrice();
        }
      }
    }
    return settlementPrice;
  }

  public static int getSettlementPrice(RtbResponse rtbResponse, String adVid) {
    int settlementPrice = rtbResponse.getSettlementPrice();
    // native
    if (rtbResponse.getAdditionalResponsesCount() > 0) {
      var additionalResponse = rtbResponse.getAdditionalResponsesMap().get(adVid);
      if (null != additionalResponse) {
        if (additionalResponse.getSettlementPrice() > 0) {
          settlementPrice = additionalResponse.getSettlementPrice();
        }
      }
    }
    return settlementPrice;
  }

  /**
   * 广告请求是否支持mraid模版形式广告
   *
   * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=48434536">需求文档</a>
   * @param osType
   * @param osVersion
   * @param sdkVersion
   * @return
   */
  public static boolean enableAdMraidTemplate(int osType, Version osVersion, Version sdkVersion) {

    return !(osType == OsType.IOS.getTypeNum()
            && compareVersion(Constants.OS_VERSION_15_0_0, osVersion) <= 0
            && compareVersion(Constants.SDK_VERSION_3_2_0, sdkVersion) >= 0
        || osType == OsType.HARMONY_OS.getTypeNum());
  }

  /**
   * 广告请求是否禁用closeCard广告
   *
   * @param osType
   * @param osVersion
   * @param sdkVersion
   * @param disableCloseCardConfig 中控配置的是否支持关闭页推荐广告开关
   * @return true: 禁用；false: 启用
   */
  public static boolean disableAdCloseCard(
      int osType, Version osVersion, Version sdkVersion, boolean disableCloseCardConfig) {

    if (osType == OsType.ANDROID.getTypeNum()
        && (compareVersion(sdkVersion, Constants.SDK_VERSION_3_0_0) == 0
            || compareVersion(sdkVersion, Constants.SDK_VERSION_3_0_10) == 0)) {
      return true;
    }

    if (!enableAdMraidTemplate(osType, osVersion, sdkVersion)) {
      return true;
    }

    return disableCloseCardConfig;
  }

  /**
   * 生成广告计划id
   *
   * @param adType
   * @param videoUrl
   * @param imageUrl
   * @return
   */
  public static String generateAdCampaignId(int adType, String videoUrl, String imageUrl) {

    String materialId = null;
    String imgUrlMd5 = null;

    if (adType == AdType.REWARDED_VIDEO.getTypeNum()
        || adType == AdType.FULL_SCREEN_VIDEO.getTypeNum()) {
      materialId =
          MD5Util.encrypt(
              Strings.nullToEmpty(videoUrl)
                  + Constants.SYMBOL_UNDERSCORE
                  + Strings.nullToEmpty(imageUrl));
    } else if (adType == AdType.SPLASH.getTypeNum()) {
      if (!Strings.isNullOrEmpty(imageUrl)) {
        imgUrlMd5 = MD5Util.encrypt(imageUrl);
      }
      materialId = imgUrlMd5;
    }
    if (Strings.isNullOrEmpty(materialId)) {
      materialId = String.valueOf(Instant.now());
    }

    return materialId;
  }

  /**
   * 根据流量类型设置dsp下发激励视频创意类型
   *
   * @param dspProtocolType
   * @param requestFlowType
   * @return
   */
  public static int getRewardedVideoCreativeType(
      DspProtocolType dspProtocolType, int requestFlowType) {
    if (RequestFlowType.SDK.getCode() == requestFlowType) {
      return CreativeType.VIDEO_HTML_SNIPPET.getTypeNum();
    } else {
      return CreativeType.VIDEO_AND_IMAGE.getTypeNum();
    }
  }

  /**
   * 是否要求广告合规
   *
   * @param appId
   * @param adType
   * @param regionName
   * @return true-要求合规；false-不强制要求
   */
  public static boolean needComplianceAd(int appId, int adType, String regionName) {

    AppMiscellaneousConfig appMiscellaneousConfig = ConfigManager.get(AppMiscellaneousConfig.class);
    if (null != appMiscellaneousConfig) {
      var adComplianceConfig = appMiscellaneousConfig.getAdComplianceConfig();
      if (null != adComplianceConfig) {
        var adTypeConfig = adComplianceConfig.getAdTypeConfig();
        if (!CollectionUtils.isEmpty(adTypeConfig)) {
          var appAdTypeConfig = adTypeConfig.get(adType);
          if (null != appAdTypeConfig) {
            var appIdList = appAdTypeConfig.getAppIdList();
            if (!CollectionUtils.isEmpty(appIdList) && appIdList.contains(appId)) {
              return false;
            }
          }
        }

        if (!Strings.isNullOrEmpty(regionName)) {
          var compliantCityList = adComplianceConfig.getCompliantCityList();
          return !CollectionUtils.isEmpty(compliantCityList)
              && compliantCityList.contains(regionName);
        }
      }
    }
    // 默认不合规
    return false;
  }

  /** 2023.08.12加强版合规 */
  public static boolean needComplianceAdFilter(
      String appId, int adType, String deviceBrand, int requestFlowType, Version sdkVersion) {
    AppMiscellaneousConfig appMiscellaneousConfig = ConfigManager.get(AppMiscellaneousConfig.class);
    if (null != appMiscellaneousConfig) {
      var adFilterByComplianceConfigs = appMiscellaneousConfig.getAdFilterByComplianceConfigs();
      if (CollectionUtils.isEmpty(adFilterByComplianceConfigs)
          || RequestFlowType.SDK.getCode() != requestFlowType) {
        return false;
      }

      return adFilterByComplianceConfigs.stream()
          .anyMatch(
              c -> {
                Set<String> appBlackLists = c.getAppBlackLists();
                Set<Integer> filterResult = Sets.newHashSetWithExpectedSize(5);
                int filterAdByAppList = -1;
                if (!CollectionUtils.isEmpty(appBlackLists)) {
                  if (appBlackLists.contains(appId)) {
                    filterAdByAppList = 1;
                  } else {
                    return false;
                  }
                }
                filterResult.add(filterAdByAppList);

                int filterAdByBrand = -1;
                Set<String> brands = c.getBrands();
                if (!CollectionUtils.isEmpty(brands)) {
                  if (brands.stream().anyMatch(b -> b.equalsIgnoreCase(deviceBrand))) {
                    filterAdByBrand = 1;
                  } else {
                    return false;
                  }
                }
                filterResult.add(filterAdByBrand);

                int filterAdByAdType = -1;
                Set<Integer> adTypes = c.getAdTypes();
                if (!CollectionUtils.isEmpty(adTypes)) {
                  if (adTypes.contains(adType)) {
                    filterAdByAdType = 1;
                  } else {
                    return false;
                  }
                }
                filterResult.add(filterAdByAdType);

                int filterAdBySdkVersion = -1;
                var filterSdkVersion = DeviceUtil.strToVersion(c.getSdkVersion());
                String sdkCompareCondition = c.getSdkCompareCondition();
                if (Strings.isNullOrEmpty(c.getSdkVersion())
                    || filterSdkVersion == null
                    || Strings.isNullOrEmpty(sdkCompareCondition)
                    || (!Constants.COMPARE_GREATER.equalsIgnoreCase(sdkCompareCondition)
                        && !Constants.COMPARE_LITTLE.equalsIgnoreCase(sdkCompareCondition)
                        && !Constants.COMPARE_EQUAL.equalsIgnoreCase(sdkCompareCondition)
                        && !Constants.COMPARE_GREATER_AND_EQUAL.equalsIgnoreCase(
                            sdkCompareCondition)
                        && !Constants.COMPARE_LITTLE_AND_EQUAL.equalsIgnoreCase(
                            sdkCompareCondition))) {
                  filterAdBySdkVersion = -1;
                } else {
                  int compareVersion = compareVersion(sdkVersion, filterSdkVersion);
                  if ((Constants.COMPARE_GREATER.equalsIgnoreCase(sdkCompareCondition)
                              || Constants.COMPARE_GREATER_AND_EQUAL.equalsIgnoreCase(
                                  sdkCompareCondition))
                          && compareVersion == 1
                      || (Constants.COMPARE_LITTLE.equalsIgnoreCase(sdkCompareCondition)
                              || Constants.COMPARE_LITTLE_AND_EQUAL.equalsIgnoreCase(
                                  sdkCompareCondition))
                          && compareVersion == -1
                      || (Constants.COMPARE_EQUAL.equalsIgnoreCase(sdkCompareCondition)
                              || Constants.COMPARE_GREATER_AND_EQUAL.equalsIgnoreCase(
                                  sdkCompareCondition)
                              || Constants.COMPARE_LITTLE_AND_EQUAL.equalsIgnoreCase(
                                  sdkCompareCondition))
                          && compareVersion == 0) {
                    filterAdBySdkVersion = 1;
                  } else {
                    return false;
                  }
                }
                filterResult.add(filterAdBySdkVersion);

                if (filterResult.contains(0)) {
                  return false;
                } else {
                  return filterResult.contains(1);
                }
              });
    }
    return false;
  }

  /** 流量在sigmob adx内部竞价模式 */
  public static AuctionType getSigmobAdxAuctionType(RtbRequest rtbRequest) {
    if (rtbRequest.getRequestFlowType() == RequestFlowType.SDK.getCode()
            && rtbRequest.getHbType() != HeaderBiddingType.NOT_HB.getCode()
        || rtbRequest.getRequestFlowType() != RequestFlowType.SDK.getCode()
            && rtbRequest.getAt() == AuctionType.FIRST_PRICE.getCode()) {
      return AuctionType.FIRST_PRICE;
    } else {
      return AuctionType.SECOND_PRICE;
    }
  }

  /** copy from MixDspService */
  public static App.Builder buildSdkApp(
      App.Builder appBuilder,
      AppInfo ai,
      int osType,
      Version sdkVersion,
      SdkType sdkType,
      AdSlotInfo adSlotInfo) {

    // 如果广告位没设置方向或者设置了不限制
    if (null != adSlotInfo) {
      int originalOrientation = appBuilder.getOrientation();
      appBuilder.setOriginSdkOrientation(Integer.toString(originalOrientation));
      // harmony 支持所有的广告方向
      if (osType == OsType.HARMONY_OS.getTypeNum()) {
        appBuilder.setOrientation(OrientationType.All.getTypeNum());
      } else {
        // 1、如果广告位未设置方向，那么，ios保持sdk的请求设置，安卓使用app 的orientation
        // 2、如果广告位设置了方向 安卓完全按照广告位的设置，
        // ios根据情况进行调整，如果app的实际方向为横竖兼容，那么按广告位设置的方向发起向dsp的请求，否则按sdk请求原值发起向dsp的请求
        var appInfoOrientation = ai.getOrientation();
        var adSlotOrientation = adSlotInfo.getOrientation();
        if (adSlotOrientation == AdSlotOrientation.NOT_LIMIT.getTypeNum()) {
          // 安卓系统，按照媒体管理系统设置的app方向
          if (OsType.ANDROID.getTypeNum() == osType
              && appInfoOrientation >= 0
              && appInfoOrientation <= 2) {
            appBuilder.setOrientation(appInfoOrientation);
          }
        } else {
          // 广告位设置了方向
          // 仅2.9.3生效
          if (compareVersion(sdkVersion, RtbConstants.MIN_SDK_VERION_SUPPORT_ADSLOT_ORIETATION) >= 0
              || sdkType == SdkType.TO_BID) {
            int orientation;
            if (adSlotOrientation == AdSlotOrientation.All.getTypeNum()) {
              // 由于广告位方向的定义与app orientation不一致，需要转义
              orientation = OrientationType.All.getTypeNum();
            } else {
              orientation = adSlotOrientation;
            }
            if (OsType.ANDROID.getTypeNum() == osType) {
              appBuilder.setOrientation(orientation);
            } else if (OsType.IOS.getTypeNum() == osType) {
              // ios根据情况进行调整，如果app的实际方向为横竖兼容，那么按广告位设置的方向发起向dsp的请求，
              // 否则按sdk请求原值发起向dsp的请求
              if (originalOrientation == OrientationType.All.getTypeNum()) {
                appBuilder.setOrientation(orientation);
              }
            }
          }
        }

        int slotType = adSlotInfo.getType();
        // 如果是插屏
        if (AdType.INTERSTITIAL.getTypeNum() == slotType) {
          if (osType == OsType.IOS.getTypeNum()
              && DeviceUtil.compareVersion(sdkVersion, Constants.SDK_VERSION_4_15_3) <= 0) {
            if (originalOrientation == OrientationType.All.getTypeNum()) {
              appBuilder.setOrientation(OrientationType.Portrait.getTypeNum());
            } else {
              appBuilder.setOrientation(originalOrientation);
            }
          } else {
            appBuilder.setOrientation(
                adSlotOrientation == AdSlotOrientation.NOT_LIMIT.getTypeNum()
                        || adSlotOrientation == AdSlotOrientation.All.getTypeNum()
                    ? OrientationType.All.getTypeNum()
                    : adSlotOrientation);
          }
        }
      }
    }
    // 媒体分级
    appBuilder.setMediaLevel(ai.getLevel() == null ? 0 : ai.getLevel());
    // 将平台设置的包名或appid提交给dsp，帮助dsp实现同一产品不在同一开发者媒体上播放的功能
    // wiki: http://wiki.sigmob.cn/pages/viewpage.action?pageId=17367673
    if (!Strings.isNullOrEmpty(ai.getPackageName())) {
      appBuilder.setProductId(ai.getPackageName());
    }

    if (SdkCapabilityUtils.supportHttp(sdkType, sdkVersion, osType)) {
      appBuilder.setSupportHttp(true);
    }

    var appLabels = ai.getAppLabels();
    if (!CollectionUtils.isEmpty(appLabels)) {
      appBuilder.addAllLabel(appLabels);
    }

    return appBuilder;
  }

  /** 返回广告的监测链接中是否要将曝光和视频开始播放分开 */
  public static boolean isStartAndImpressionEventSeparated(RtbRequest rtbRequest) {
    return rtbRequest.getRequestFlowType() == RequestFlowType.VIVO.getCode();
  }

  public static void setHtmlSnippet(
      String imgUrl,
      int imgHeight,
      int imgWidth,
      String title,
      String description,
      String iconUrl,
      boolean isDownload,
      RtbRequest rtbRequest,
      int appId,
      int dspId,
      int standardAdCreativeType,
      DspProtocolType dspProtocolType,
      MaterialMeta.Builder mBuilder,
      IpService ipService,
      AdditionalResponse additionalResponse) {
    setHtmlSnippet(
        imgUrl,
        imgHeight,
        imgWidth,
        title,
        description,
        iconUrl,
        isDownload,
        rtbRequest,
        appId,
        dspId,
        standardAdCreativeType,
        dspProtocolType,
        mBuilder,
        ipService,
        false,
        false,
        additionalResponse);
  }

  /**
   * <AUTHOR>
   * @date 2022/10/26 11:09
   * @param imgUrl
   * @param imgHeight
   * @param imgWidth
   * @param title
   * @param description
   * @param iconUrl
   * @param isDownload
   * @param rtbRequest
   * @param appId
   * @param dspId
   * @param standardAdCreativeType 标准dsp协议返回广告创意类型，非标准dsp协议传0
   * @param dspProtocolType
   * @param mBuilder
   * @param priorityUse115Template 优先使用115模版
   */
  public static void setHtmlSnippet(
      String imgUrl,
      int imgHeight,
      int imgWidth,
      String title,
      String description,
      String iconUrl,
      boolean isDownload,
      RtbRequest rtbRequest,
      int appId,
      int dspId,
      int standardAdCreativeType,
      DspProtocolType dspProtocolType,
      MaterialMeta.Builder mBuilder,
      IpService ipService,
      boolean priorityUse115Template,
      boolean filter115Template,
      AdditionalResponse additionalResponse) {
    var bidRequest = rtbRequest.getBidRequest();
    var sdkVersion = rtbRequest.getSdkVersion();
    String requestId = bidRequest.getRequestId();
    var device = bidRequest.getDevice();
    var osType = device.getOsType();
    var osVersion = device.getOsVersion();

    if (Strings.isNullOrEmpty(imgUrl)) {
      throw new DspBusinessException(
          ErrorCode.DSP_API_INVALID_RESPONSE_MATERIALMETA, "no img_url return!");
    }

    HtmlTemplateParam htParam = new HtmlTemplateParam();
    htParam.setDescription(description);
    htParam.setDownload(isDownload);
    htParam.setIconUrl(iconUrl);
    htParam.setImageSize(Size.newBuilder().setHeight(imgHeight).setHeight(imgWidth).build());
    htParam.setImageUrl(imgUrl);
    htParam.setTitle(title);
    htParam.setLogUniqKey(DataLogMessageBuilder.buildUniqueKey(osType, appId));
    SettingConfig sc = ConfigManager.get(SettingConfig.class);
    htParam.setLogUrl(sc.getCommonLogUrl());
    htParam.setRequestId(requestId);
    var interactionType = mBuilder.getInteractionType();
    htParam.setWxProgram(interactionType == RtbResponse.InteractType.WX_PROGRAM_VALUE);

    if ((osType == OsType.IOS.getTypeNum()
            && (compareVersion(osVersion, Constants.OS_VERSION_8_0_0) <= 0
                || compareVersion(sdkVersion, Constants.SDK_VERSION_4_2_0) < 0))
        || (osType == OsType.ANDROID.getTypeNum()
            && (compareVersion(osVersion, Constants.OS_VERSION_6_0_0) < 0
                || compareVersion(sdkVersion, Constants.SDK_VERSION_4_4_0) < 0))) {
      HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
      if (null != config) {
        String htmlSnippet = config.getHtmlSnippet(htParam, dspId);
        if (!Strings.isNullOrEmpty(htmlSnippet)) {
          mBuilder
              .setTemplateId(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_10.getId())
              .setHtmlSnippet(com.google.protobuf.ByteString.copyFrom(htmlSnippet, Charsets.UTF_8))
              .setCreativeType(CreativeType.VIDEO_HTML_SNIPPET.getTypeNum())
              .setClickType(ClickType.BUTTON.getTypeNum());
          return;
        }
      }
    }

    if (bidRequest.getSlots(0).getAdslotType(0) == AdType.FULL_SCREEN_VIDEO.getTypeNum()
        && mBuilder.getVideoUrl().isBlank()) {
      var dspInfo = rtbRequest.getDspInfo();
      var interstitialTplId = dspInfo.getInterstitialTplId();
      AdTempActivityHelper.setInterstitialAdMaterial(
          requestId,
          rtbRequest.getDspId(),
          mBuilder,
          rtbRequest.getSupportMaterialType(),
          imgUrl,
          interstitialTplId);
      return;
    }

    var cityName =
        ipService.getRegionNameByIp(
            DeviceUtil.getDeviceIpv4(rtbRequest.getBidRequest().getNetwork()), RegionType.city);
    // 标准dsp协议只有StandardDspCreativeType.VIDEO_TEMPLATE才有高级模版
    HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
    var hasDspHtmlTemplateConfig = rtbRequest.hasDspHtmlTemplateConfig();
    if (!(DspProtocolType.STANDARD.equals(dspProtocolType)
            && standardAdCreativeType != StandardDspCreativeType.VIDEO_TEMPLATE.getTypeNum())
        && (hasDspHtmlTemplateConfig || priorityUse115Template)
        && null != config) {

      var isBaiduDsp =
          dspProtocolType == DspProtocolType.BAIDU_MOBADS_PB
              || dspProtocolType == DspProtocolType.BAIDU_MOBADS;

      String advancedHtmlSnippet = null;
      boolean isTemplateIdConfirmed = false;
      int templateId = 0;
      TemplateConfig templateConfig = null;
      var forbiddenTemplate115 =
          config.isForbiddenTemplate(HtmlTemplateConfig.TEMPLATE_100115, appId, cityName);
      if (priorityUse115Template && !filter115Template && !forbiddenTemplate115) {
        //        if (!isBaiduDsp) {
        //          DspMiscellaneousConfig dspMiscellaneousConfig =
        //              ConfigManager.get(DspMiscellaneousConfig.class);
        //          if (null != dspMiscellaneousConfig) {
        //            var adTemplateConfigOptional =
        //                dspMiscellaneousConfig.getAdTemplateConfig(
        //                    dspId, rtbRequest.getDspApiAdSlot().getAdSlot(), isDownload);
        //            if (adTemplateConfigOptional.isPresent()) {
        //              var adTemplateConfig = adTemplateConfigOptional.get();
        //              templateId = adTemplateConfig.getTemplateId();
        //              Map<String, String> configMacro = null;
        //              if (hasDspHtmlTemplateConfig) {
        //                var dspHtmlTemplateConfig = rtbRequest.getDspHtmlTemplateConfig();
        //                if (dspHtmlTemplateConfig.getTemplateConfigsCount() > 0) {
        //                  var template115ConfigOptional =
        //                      dspHtmlTemplateConfig.getTemplateConfigsList().stream()
        //                          .filter(tc -> tc.getTemplateId() ==
        // HtmlTemplateConfig.TEMPLATE_100115)
        //                          .findAny();
        //                  if (template115ConfigOptional.isPresent()) {
        //                    var template115Config = template115ConfigOptional.get();
        //                    if (template115Config.getMacroCount() > 0) {
        //                      configMacro = template115Config.getMacroMap();
        //                    } else {
        //                      configMacro =
        //                          Map.of(
        //                              HtmlTemplateConfig.FIELD_HIT_PERCENT,
        //                              Float.toString(adTemplateConfig.getWrongClickRate()));
        //                    }
        //                  }
        //                }
        //              } else {
        //                configMacro =
        //                    Map.of(
        //                        HtmlTemplateConfig.FIELD_HIT_PERCENT,
        //                        Float.toString(adTemplateConfig.getWrongClickRate()));
        //              }
        //
        //              advancedHtmlSnippet = config.getTemplateById(templateId, htParam,
        // configMacro);
        //              isTemplateIdConfirmed = true;
        //            }
        //          }
        //        } else {
        if (!isTemplateIdConfirmed && hasDspHtmlTemplateConfig) {
          var dspHtmlTemplateConfig = rtbRequest.getDspHtmlTemplateConfig();
          if (dspHtmlTemplateConfig.getTemplateConfigsCount() > 0) {
            TemplateConfig maxWrongHitPercentTc = null;
            float maxWrongHitPercent = 0f;

            for (int i = 0; i < dspHtmlTemplateConfig.getTemplateConfigsCount(); i++) {
              var tempConfig = dspHtmlTemplateConfig.getTemplateConfigs(i);
              if (tempConfig.getTemplateId() == HtmlTemplateConfig.TEMPLATE_100115
                  && AdUtil.checkInteractType(interactionType, tempConfig.getType())) {
                if (tempConfig.getMacroCount() > 0) {
                  var macroMap = tempConfig.getMacroMap();
                  if (macroMap.containsKey(HtmlTemplateConfig.FIELD_HIT_PERCENT)) {
                    var hitPercent = macroMap.get(HtmlTemplateConfig.FIELD_HIT_PERCENT);
                    float hitPercentFloat = 0;
                    if (NumberUtils.isCreatable(hitPercent)) {
                      try {
                        hitPercentFloat =
                            new BigDecimal(hitPercent)
                                .setScale(2, RoundingMode.HALF_UP)
                                .floatValue();
                      } catch (Exception e) {
                        LogUtil.localError(
                            "parse dsp({}) 115 template hitPercent({}) error:{}",
                            dspId,
                            hitPercent,
                            e.getMessage());
                      }
                    }
                    if (hitPercentFloat >= maxWrongHitPercent) {
                      maxWrongHitPercent = hitPercentFloat;
                      maxWrongHitPercentTc = tempConfig;
                    }
                  }
                }
              }
            } // end for

            if (null != maxWrongHitPercentTc) {
              templateConfig = maxWrongHitPercentTc;
            }
            if (null != templateConfig) {
              templateId = templateConfig.getTemplateId();
              advancedHtmlSnippet =
                  config.getTemplateById(
                      templateId, htParam, templateConfig.getMacroMap(), additionalResponse);
              isTemplateIdConfirmed = true;
            }
          }
        }
        //        }
      }
      if (!filter115Template && !forbiddenTemplate115 && isBaiduDsp && priorityUse115Template) {
        // 百度高点击用户没有配置115模版配置，依然走115模版，并且误点率为0
        advancedHtmlSnippet =
            config.getTemplateById(
                HtmlTemplateConfig.TEMPLATE_100115,
                htParam,
                Map.of(HtmlTemplateConfig.FIELD_HIT_PERCENT, "0"),
                additionalResponse);
        templateId = HtmlTemplateConfig.TEMPLATE_100115;
      }

      if (!isTemplateIdConfirmed && hasDspHtmlTemplateConfig) {
        var dspHtmlTemplateConfig = rtbRequest.getDspHtmlTemplateConfig();
        var templateConfigsCount = dspHtmlTemplateConfig.getTemplateConfigsCount();
        if (templateConfigsCount > 0) {
          int totalBucket = 100;
          int startCalculateRatio = 0; // 百分比计算初始数，第一次计算从0%开始
          int deviceBucket = RandomNumberGenerator.randomInt(100);
          //                DeviceUtil.getDeviceBucketForAlgorithm(
          //                    rtbRequest.getBidRequest().getDevice().getDid().getUid(),
          // totalBucket);
          for (int i = 0; i < templateConfigsCount; i++) {
            var tempConfig = dspHtmlTemplateConfig.getTemplateConfigs(i);
            if (null == templateConfig) {
              var tempConfigTemplateId = tempConfig.getTemplateId();

              var ratio = tempConfig.getRatio();
              var tempConfigType = tempConfig.getType();
              if (AdUtil.checkInteractType(interactionType, tempConfigType)
                  && startCalculateRatio < totalBucket
                  && ratio > 0
                  && (tempConfigTemplateId == HtmlTemplateConfig.TEMPLATE_100115
                          && !filter115Template
                          && !forbiddenTemplate115
                      || (tempConfigTemplateId != HtmlTemplateConfig.TEMPLATE_100115
                              && !config.isForbiddenTemplate(tempConfigTemplateId, appId, cityName))
                          && (tempConfigTemplateId != HtmlTemplateConfig.TEMPLATE_100327
                              || !isDownload))) {

                var endCalculateRatio = startCalculateRatio + ratio;
                if (endCalculateRatio > totalBucket) {
                  endCalculateRatio = totalBucket;
                }

                if (deviceBucket >= startCalculateRatio && deviceBucket < endCalculateRatio) {
                  templateConfig = tempConfig;
                }
                startCalculateRatio = endCalculateRatio + 1;
              }
            }
          } // end for
          if (null != templateConfig) {
            templateId = templateConfig.getTemplateId();
            advancedHtmlSnippet =
                config.getTemplateById(
                    templateId, htParam, templateConfig.getMacroMap(), additionalResponse);
            isTemplateIdConfirmed = true;
          }
        }
      }

      if (isTemplateIdConfirmed) {
        mBuilder
            .setHtmlSnippet(
                com.google.protobuf.ByteString.copyFrom(advancedHtmlSnippet, Charsets.UTF_8))
            .setTemplateId(templateId)
            .setCreativeType(CreativeType.MRAID.getTypeNum())
            .setClickType(ClickType.BUTTON.getTypeNum());
        return;
      }
    }

    MraidTemplateConfig mtc = ConfigManager.get(MraidTemplateConfig.class);
    if (null != mtc) {
      String htmlSnippet =
          mtc.generateHtml(
              htParam,
              bidRequest.getDevice().getDid().getUdid(),
              OsType.getType(osType),
              appId,
              dspProtocolType);
      if (!Strings.isNullOrEmpty(htmlSnippet)) {
        int clickType =
            RtbConstants.FULLSCREEN_CLICK_DSP_LIST.contains(dspId)
                ? ClickType.FULL_SCREEN.getTypeNum()
                : ClickType.BUTTON.getTypeNum();

        mBuilder
            .setCreativeType(CreativeType.MRAID.getTypeNum())
            .setHtmlSnippet(com.google.protobuf.ByteString.copyFrom(htmlSnippet, Charsets.UTF_8))
            .setTemplateId(50)
            .setClickType(clickType);
        return;
      }
    }

    if (null != config) {
      String htmlSnippet = config.getHtmlSnippet(htParam, dspId);
      if (!Strings.isNullOrEmpty(htmlSnippet)) {
        mBuilder
            .setTemplateId(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_10.getId())
            .setHtmlSnippet(com.google.protobuf.ByteString.copyFrom(htmlSnippet, Charsets.UTF_8))
            .setCreativeType(CreativeType.VIDEO_HTML_SNIPPET.getTypeNum())
            .setClickType(ClickType.BUTTON.getTypeNum());
        return;
      }
    }

    throw new DspBusinessException(ErrorCode.INVALID_ENDCARD, "cannot get valid htmlSnippet!");
  }

  public static HtmlTemplateParam buildHtmlTemplateParam(
      String imgUrl,
      int imgHeight,
      int imgWidth,
      String title,
      String description,
      String iconUrl,
      boolean isDownload,
      int appId,
      MaterialMeta.Builder mBuilder,
      int osType,
      String requestId) {
    HtmlTemplateParam htParam = new HtmlTemplateParam();
    htParam.setDescription(description);
    htParam.setDownload(isDownload);
    htParam.setScore(mBuilder.getScore());
    htParam.setIconUrl(iconUrl);
    htParam.setImageSize(Size.newBuilder().setHeight(imgHeight).setWidth(imgWidth).build());
    htParam.setImageUrl(imgUrl);
    htParam.setTitle(title);
    htParam.setLogUniqKey(DataLogMessageBuilder.buildUniqueKey(osType, appId));
    SettingConfig sc = ConfigManager.get(SettingConfig.class);
    htParam.setLogUrl(sc.getCommonLogUrl());
    htParam.setRequestId(requestId);
    var interactionType = mBuilder.getInteractionType();
    htParam.setWxProgram(interactionType == RtbResponse.InteractType.WX_PROGRAM_VALUE);
    return htParam;
  }

  /**
   * 媒体报价能力升级为组合配置
   *
   * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=72947053">需求</a>
   */
  public static void useMediaPriceStrategy(
      Ad.Builder adBuilder,
      RtbRequest rtbRequest,
      RtbResponse.Builder responseBuilder,
      Float configPriceRate,
      double hbSecondBidPriceRatio) {
    var adSlot = rtbRequest.getBidRequest().getSlots(0);
    var settlementSetting = adSlot.getSettlementSetting();

    if (settlementSetting == SettlementSetting.BIDDING.getCode()) {
      // 为外部流量在sigmob trackUrl后添加三方结算相关宏
      var requestFlowType = rtbRequest.getRequestFlowType();
      float extraPriceRatio = null == configPriceRate ? 0f : configPriceRate;
      // 非hb流量
      if (requestFlowType != RequestFlowType.SDK.getCode()
              && requestFlowType != RequestFlowType.STANDARD.getCode()
              && requestFlowType != RequestFlowType.HUAWEI.getCode()
              && requestFlowType != RequestFlowType.OPPO.getCode()
              && requestFlowType != RequestFlowType.GROMORE.getCode()
              && requestFlowType != RequestFlowType.VIVO.getCode()
          || requestFlowType == RequestFlowType.SDK.getCode()
              && rtbRequest.getHbType() == HeaderBiddingType.NOT_HB.getCode()) {
        var mediaPriceStrategyConfig = ConfigManager.get(MediaPriceStrategyConfig.class);
        if (null != mediaPriceStrategyConfig) {
          var mediaExtraPriceRateOptional =
              mediaPriceStrategyConfig.getMediaExtraPriceRate(
                  rtbRequest.getAppId(), adSlot.getAdslotId(), adBuilder.getAdSourceChannel());
          extraPriceRatio = mediaExtraPriceRateOptional.orElse(0f) * -1;
        }
      }
      var adSlotType = adSlot.getAdslotType(0);
      String vid = adBuilder.getVid();
      if (extraPriceRatio != 0f && !responseBuilder.hasAdxExperiment()) {
        if (adSlotType != AdType.NATIVE.getTypeNum()
            && adSlotType != AdType.INTERSTITIAL.getTypeNum()) {
          int settlementPriceForDsp = responseBuilder.getSettlementPriceForDsp();
          if (settlementPriceForDsp != 0) {
            responseBuilder.setSettlementPrice(
                Math.max(
                    adSlot.getExpectedFloor(),
                    new BigDecimal(settlementPriceForDsp)
                        .multiply(new BigDecimal(String.valueOf(1 - extraPriceRatio)))
                        .intValue()));
          }
        } else {
          if (responseBuilder.getAdditionalResponsesCount() > 0) {
            Map<String, AdditionalResponse> additionalResponsesMap =
                responseBuilder.getAdditionalResponsesMap();
            var additionalResponse = additionalResponsesMap.get(vid);
            if (null != additionalResponse) {
              if (additionalResponse.getSettlementPriceForDsp() > 0) {
                // TODO: 可以优化，不用经常new map
                var newAdditionalResponseMap = Maps.newHashMap(additionalResponsesMap);
                int settlementPriceForDsp = additionalResponse.getSettlementPriceForDsp();
                newAdditionalResponseMap.put(
                    vid,
                    additionalResponse.toBuilder()
                        .setSettlementPrice(
                            Math.max(
                                adSlot.getExpectedFloor(),
                                new BigDecimal(settlementPriceForDsp)
                                    .multiply(new BigDecimal(String.valueOf(1 - extraPriceRatio)))
                                    .intValue()))
                        .build());
                responseBuilder
                    .clearAdditionalResponses()
                    .putAllAdditionalResponses(newAdditionalResponseMap);
              }
            }
          }
        }
      } else {
        if (adSlotType != AdType.NATIVE.getTypeNum()
            && adSlotType != AdType.INTERSTITIAL.getTypeNum()) {
          int settlementPriceForDsp = responseBuilder.getSettlementPriceForDsp();
          if (settlementPriceForDsp != 0) {
            responseBuilder.setSettlementPrice(settlementPriceForDsp);
            if (responseBuilder.getAdditionalResponsesCount() > 0) {
              Map<String, AdditionalResponse> additionalResponsesMap =
                  responseBuilder.getAdditionalResponsesMap();
              var additionalResponse = additionalResponsesMap.get(vid);
              var reBidPrice =
                  BigDecimal.valueOf(settlementPriceForDsp)
                      .multiply(BigDecimal.valueOf(hbSecondBidPriceRatio))
                      .setScale(0, RoundingMode.CEILING)
                      .intValue();
              if (null != additionalResponse) {
                responseBuilder.putAdditionalResponses(
                    vid, additionalResponse.toBuilder().setReBidPrice(reBidPrice).build());
              } else {
                responseBuilder.putAdditionalResponses(
                    vid, AdditionalResponse.newBuilder().setReBidPrice(reBidPrice).build());
              }
            }
          }
        } else {
          if (responseBuilder.getAdditionalResponsesCount() > 0) {
            Map<String, AdditionalResponse> additionalResponsesMap =
                responseBuilder.getAdditionalResponsesMap();
            var additionalResponse = additionalResponsesMap.get(vid);
            if (null != additionalResponse) {
              if (additionalResponse.getSettlementPriceForDsp() > 0) {
                // TODO: 可以优化，不用经常new map
                //                var newAdditionalResponseMap =
                // Maps.newHashMap(additionalResponsesMap);
                int settlementPriceForDsp = additionalResponse.getSettlementPriceForDsp();
                var additionalResponseBuilder =
                    additionalResponse.toBuilder().setSettlementPrice(settlementPriceForDsp);
                if (hbSecondBidPriceRatio != 0d) {
                  additionalResponseBuilder.setReBidPrice(
                      BigDecimal.valueOf(settlementPriceForDsp)
                          .multiply(BigDecimal.valueOf(hbSecondBidPriceRatio))
                          .setScale(0, RoundingMode.CEILING)
                          .intValue());
                }
                responseBuilder.putAdditionalResponses(vid, additionalResponseBuilder.build());
                //                responseBuilder
                //                    .clearAdditionalResponses()
                //                    .putAllAdditionalResponses(newAdditionalResponseMap);
              }
            }
          }
        }
      }
    }
  }

  /** 比较中控配置的广告动作类型与adx广告类型能否对应 */
  public static boolean checkInteractType(Integer adInteractionType, int configInteractType) {
    boolean checkInteractType = false;
    if (configInteractType == ConfigInteractType.WEB.getCode()
        && (adInteractionType.equals(RtbResponse.InteractType.WEB_VIEW_VALUE)
            || adInteractionType.equals(RtbResponse.InteractType.DEEP_LINK_VALUE)
            || adInteractionType.equals(RtbResponse.InteractType.SYS_EXPLORE_VALUE)
            || adInteractionType.equals(RtbResponse.InteractType.QUICK_APP_VALUE))) {
      checkInteractType = true;
    } else if (configInteractType == ConfigInteractType.DOWNLOAD.getCode()
        && (adInteractionType.equals(RtbResponse.InteractType.APP_DOWNLOAD_VALUE))) {
      checkInteractType = true;
    } else if (configInteractType == ConfigInteractType.PROGRAM.getCode()
        && (adInteractionType.equals(RtbResponse.InteractType.WX_PROGRAM_VALUE)
            || adInteractionType.equals(RtbResponse.InteractType.SCHEMA_WX_PROGRAM_VALUE))) {
      checkInteractType = true;
    } else if (configInteractType == ConfigInteractType.QUICK_APP.getCode()
        && adInteractionType.equals(RtbResponse.InteractType.QUICK_APP_VALUE)) {
      checkInteractType = true;
    }
    return checkInteractType;
  }

  /** 获取广告中的productId */
  public static String getAdProductId(
      String adProductId, String landingPage, BidRequest bidRequest, int dspId) {
    // 如果productId为空，并且是ios，尝试从langding page中提取productid
    String productId = adProductId;
    if (bidRequest.getDevice().getOsType() == OsType.IOS.getTypeNum()) {
      if (Strings.isNullOrEmpty(productId)) {
        productId = DeviceUtil.parseIOSProductId(landingPage);
      }
      if (!Strings.isNullOrEmpty(productId)) {
        String appProductId = bidRequest.getApp().getProductId();
        if (productId.equals(appProductId)) {
          throw new DspBusinessException(
              ErrorCode.RTB_SIG_DSP_PACKAGE_NAME_FILTER,
              "request("
                  + bidRequest.getRequestId()
                  + ") dsp("
                  + dspId
                  + ") response package("
                  + productId
                  + ") equals request app package("
                  + appProductId
                  + ") !");
        }
      }
    }
    return productId;
  }

  /**
   * 设置mraid 2模板内容
   *
   * @param htmlTemplate
   * @param mBuilder
   * @param wrongClickRate
   * @param templateMacro
   * @return 最终替换到模版里的中控配置宏参数
   */
  public static Map<String, String> setMraid2Template(
      AdSlot adSlot,
      String requestId,
      int osType,
      int adType,
      Version sdkVersion,
      String htmlTemplate,
      MaterialMeta.Builder mBuilder,
      int wrongClickRate,
      Map<String, String> templateMacro,
      TemplateConfig templateConfig,
      SdkSetting sdkSetting,
      boolean needMiitCompliance,
      AdditionalResponse additionalResponse) {

    Map<String, Object> macroInfo = Maps.newHashMapWithExpectedSize(9);

    var iconUrl = mBuilder.getIconUrl();
    if (!Strings.isNullOrEmpty(iconUrl)) {
      macroInfo.put(Mraid2TemplateField.ICON.name(), iconUrl);
    }

    var desc = mBuilder.getDesc();
    if (!Strings.isNullOrEmpty(desc)) {
      if (adType == AdType.INTERSTITIAL.getTypeNum()) {
        macroInfo.put(Mraid2TemplateField.TITLE.name(), desc);
      } else {
        macroInfo.put(Mraid2TemplateField.DESC.name(), desc);
      }
    }

    var title = mBuilder.getTitle();
    if (!Strings.isNullOrEmpty(title)) {
      if (adType == AdType.INTERSTITIAL.getTypeNum()) {
        macroInfo.put(Mraid2TemplateField.PROD_NAME.name(), title);
      } else {
        macroInfo.put(Mraid2TemplateField.TITLE.name(), title);
      }
    }

    var score = mBuilder.getScore();
    if (score != 0) {
      macroInfo.put(Mraid2TemplateField.STAR.name(), Float.toString(score));
    } else {
      macroInfo.put(Mraid2TemplateField.STAR.name(), Float.toString(4.5f));
    }

    var imageSrc = mBuilder.getImageSrc();
    if (!Strings.isNullOrEmpty(imageSrc)) {
      macroInfo.put(Mraid2TemplateField.IMG.name(), imageSrc);
    }

    var videoUrl = mBuilder.getVideoUrl();
    if (!Strings.isNullOrEmpty(videoUrl)) {
      macroInfo.put(Mraid2TemplateField.VIDEO.name(), videoUrl);
    }

    var endcardImageSrc = mBuilder.getEndcardImageSrc();
    if (!Strings.isNullOrEmpty(endcardImageSrc)) {
      macroInfo.put(Mraid2TemplateField.ENDCARD_IMG.name(), endcardImageSrc);
    }

    //    if (wrongClickRate > 0) {
    //      Map<String, String> macroConfig = Maps.newHashMapWithExpectedSize(1);
    //      macroConfig.put(
    //          AdHtmlTemplateMacro.Mraid2TemplateField.HIT_PERCENT.name(),
    //          Integer.toString(wrongClickRate));
    //      macroInfo.put(AdHtmlTemplateMacro.Mraid2TemplateField.MACRO_CONFIG.name(), macroConfig);
    //    }

    Map<String, String> complianceMacro = null;
    if (!CollectionUtils.isEmpty(templateMacro)) {
      complianceMacro =
          AdUtil.processComplianceMacro(
              adSlot, osType, sdkVersion, templateMacro, needMiitCompliance);
      if (null != additionalResponse && additionalResponse.hasAdButton()) {
        complianceMacro =
            complianceMacro instanceof HashMap ? complianceMacro : Maps.newHashMap(complianceMacro);
        var adButton = additionalResponse.getAdButton();
        var bgColor = adButton.getBgColor();
        var text = adButton.getText();

        if (!Strings.isNullOrEmpty(bgColor)) {
          complianceMacro.put(Mraid2TemplateField.BTN_BG_COLOR.name(), bgColor);
        }
        if (!Strings.isNullOrEmpty(text)) {
          complianceMacro.put(Mraid2TemplateField.BTN_TXT.name(), text);
        }
      }

      if (complianceMacro.containsKey(HtmlTemplateConfig.FIELD_HIT_PERCENT)) {
        Map<String, String> macro =
            complianceMacro instanceof HashMap ? complianceMacro : Maps.newHashMap(complianceMacro);
        var hitPercent = macro.get(HtmlTemplateConfig.FIELD_HIT_PERCENT);
        var hitPercentNew = macro.get(HtmlTemplateConfig.FIELD_HIT_PERCENT_NEW);
        if (StringUtils.isBlank(hitPercent)) {
          hitPercentNew = hitPercent;
        }
        // 只有插屏广告的m2模板误点率需要乘100
        if (NumberUtils.isCreatable(hitPercent) && adType == AdType.INTERSTITIAL.getTypeNum()) {
          int hitPercentInt = 0;
          try {
            hitPercentInt =
                new BigDecimal(hitPercent)
                    .multiply(new BigDecimal(100))
                    .setScale(0, RoundingMode.HALF_UP)
                    .intValue();
          } catch (Exception e) {
            LogUtil.localError(
                "parse mraid2 template hitPercent({}) error:{}", hitPercent, e.getMessage());
          }
          macro.put(HtmlTemplateConfig.FIELD_HIT_PERCENT, Integer.toString(hitPercentInt));
        }

        macro.remove(HtmlTemplateConfig.FIELD_HIT_PERCENT_NEW);
        // 插屏,激励广告的m2模板误点率需要乘100
        if (NumberUtils.isCreatable(hitPercentNew)
            && (adType == AdType.INTERSTITIAL.getTypeNum()
                || adType == AdType.REWARDED_VIDEO.getTypeNum())) {
          int hitPercentInt = 0;
          try {
            hitPercentInt =
                new BigDecimal(hitPercentNew)
                    .multiply(new BigDecimal(100))
                    .setScale(0, RoundingMode.HALF_UP)
                    .intValue();
            if (hitPercentInt > 100) {
              hitPercentInt /= 100;
            }
          } catch (Exception e) {
            LogUtil.localError(
                "parse mraid2 template hitPercentNew({}) error:{}", hitPercentNew, e.getMessage());
          }
          // 媒体维度误点概率配置 http://wiki.sigmob.cn/pages/viewpage.action?pageId=135443361
          macroInfo.put(HtmlTemplateConfig.FIELD_HIT_PERCENT, hitPercentInt);
        }

        macroInfo.put(
            Mraid2TemplateField.MACRO_CONFIG.name(),
            JsonSerializationUtils.objToJson(
                AdUtil.processComplianceMacro(
                    adSlot, osType, sdkVersion, macro, needMiitCompliance)));
        complianceMacro = macro;
      } else {
        macroInfo.put(
            Mraid2TemplateField.MACRO_CONFIG.name(),
            JsonSerializationUtils.objToJson(complianceMacro));
      }
    }

    if (mBuilder.hasAdPrivacy()) {
      var adPrivacyBuilder = mBuilder.getAdPrivacyBuilder();
      if (adPrivacyBuilder.getPrivacyTemplateInfoCount() > 0) {
        var privacyTemplateInfoMap = adPrivacyBuilder.getPrivacyTemplateInfoMap();
        macroInfo.put(Mraid2TemplateField.PRIVACY_INFO.name(), privacyTemplateInfoMap);
        //        var privacyInfoUrl = adPrivacyBuilder.getPrivacyInfoUrl();
        //        if (!Strings.isNullOrEmpty(privacyInfoUrl)) {
        //          macroInfo.put(Mraid2TemplateField.PRIVACY_INFO_URL.name(), privacyInfoUrl);
        //        }
      }
    }

    if (null != templateConfig) {
      var templateConfigGlobalClick = templateConfig.getGlobalClick();
      if (!templateConfigGlobalClick) {
        macroInfo.put(Mraid2TemplateField.GLOBAL_CLICK.name(), templateConfigGlobalClick);
      } else {
        if (null == sdkSetting || !sdkSetting.hasInterstitial()) {
          macroInfo.put(Mraid2TemplateField.GLOBAL_CLICK.name(), templateConfigGlobalClick);
        } else {
          var disableFullClick = sdkSetting.getInterstitial().getDisableFullClick();
          if (disableFullClick) {
            macroInfo.put(Mraid2TemplateField.GLOBAL_CLICK.name(), !disableFullClick);
          } else {
            macroInfo.put(Mraid2TemplateField.GLOBAL_CLICK.name(), templateConfigGlobalClick);
          }
        }
      }
    }

    if (needMiitCompliance) {
      //      macroInfo.put(Mraid2TemplateField.GLOBAL_CLICK.name(), "false");
      macroInfo.remove(Mraid2TemplateField.GLOBAL_CLICK.name());
    }

    mBuilder.setMainTemplate(
        Template.newBuilder()
            .setType(Mraid2TemplateType.HTML.getCode())
            .setContext(
                ByteString.copyFrom(
                    htmlTemplate
                        .replace(
                            AdHtmlTemplateMacro.MACRO_TEMPLATE_CONFIG,
                            JsonSerializationUtils.objToJson(macroInfo))
                        .replace(HtmlTemplateConfig.MARCO_HTML_SNIPPET_LOG_REQUEST_ID, requestId),
                    StandardCharsets.UTF_8)));
    return complianceMacro;
  }

  /** 截取广告文本，所有字符标点都算1个字符 */
  public static String resetAdContext(
      int requestFlowType, String context, int minLength, int maxLength) {

    if (requestFlowType == RequestFlowType.GROMORE.getCode()) {
      return resetAdContext2(context, minLength, maxLength);
    }
    if (Strings.isNullOrEmpty(context)) {
      return context;
    }

    var contentLength = context.length();
    if (maxLength > 0 && contentLength > maxLength) {
      if (maxLength > 3) {
        return context.substring(0, maxLength - 3) + "...";
      } else {
        return context.substring(0, maxLength);
      }
    } else if (minLength > 0 && contentLength < minLength) {
      return Strings.padEnd(context, minLength, Constants.SYMBOL_DOT);
    } else {
      return context;
    }
  }

  /** 截取广告文本，中文字符标点算2个字符，其它算1个字符 */
  public static String resetAdContext2(String text, int minLength, int maxLength) {
    if (Strings.isNullOrEmpty(text)) {
      return text;
    }

    int totalLength = 0;
    for (int i = 0; i < text.length(); i++) {
      char tmp = text.charAt(i);
      totalLength += TextUtils.isChinese(tmp) ? 2 : 1;
    }
    int resetMinLength = minLength * 2;
    int resetMaxLength = maxLength * 2;
    int lastCharIdx = -1;
    if (resetMaxLength > 0 && totalLength > resetMaxLength) {
      for (int i = text.length() - 1; i >= 0; i--) {
        char tmp = text.charAt(i);
        totalLength -= TextUtils.isChinese(tmp) ? 2 : 1;
        lastCharIdx = i;
        if (totalLength <= resetMaxLength) {
          break;
        }
      }
      if (resetMaxLength == totalLength) {
        return text.substring(0, lastCharIdx);
      } else {
        var resetText = text.substring(0, lastCharIdx);
        return Strings.padEnd(
            resetText, resetText.length() + resetMaxLength - totalLength, Constants.SYMBOL_DOT);
      }
    } else if (resetMinLength > 0 && totalLength < resetMinLength) {
      return Strings.padEnd(
          text, text.length() + resetMinLength - totalLength, Constants.SYMBOL_DOT);
    } else {
      return text;
    }
  }

  /** dsp竞价失败回调是否由媒体发起 */
  public static boolean isDspWinAndLoseCallbackFromServer(
      RtbRequest rtbRequest, int dspProtocolType, TradingType tradingType) {
    var requestFlowType = rtbRequest.getRequestFlowType();
    if (isWinOrLoseCallBackFromOVH(rtbRequest)) {
      return false;
    }

    if (TradingType.RTB.equals(tradingType)
        && (requestFlowType == RequestFlowType.SDK.getCode()
                && rtbRequest.getBidRequest().getSlots(0).getIsHb()
            || requestFlowType != RequestFlowType.SDK.getCode()
                && rtbRequest.getBidRequest().getSlots(0).getSettlementSetting()
                    == SettlementSetting.BIDDING.getCode())) {

      return !RtbConstants.SUPPORT_MEDIA_CALLBACK_DSP_PROTOCOL_LIST.contains(dspProtocolType)
          || (rtbRequest.hasDspInfo() && rtbRequest.getDspInfo().getCallbackType() != 1);
    }

    return true;
  }

  /** dsp的竞价成功、失败回调是否由华为、vivo、oppo厂商完成完成，并透传部分错误码 */
  public static boolean isWinOrLoseCallBackFromOVH(RtbRequest rtbRequest) {
    var requestFlowType = rtbRequest.getRequestFlowType();
    if (rtbRequest.hasDspInfo()) {
      var dspInfo = rtbRequest.getDspInfo();
      if (dspInfo.getMediaBiddingCallbackCount() > 0) {
        var mediaBiddingCallbackList = dspInfo.getMediaBiddingCallbackList();
        return requestFlowType == RequestFlowType.HUAWEI.getCode()
                && mediaBiddingCallbackList.contains(RequestFlowType.HUAWEI.getName())
            || requestFlowType == RequestFlowType.VIVO.getCode()
                && mediaBiddingCallbackList.contains(RequestFlowType.VIVO.getName())
            || requestFlowType == RequestFlowType.OPPO.getCode()
            || mediaBiddingCallbackList.contains(Constants.REQUESTFLOW_OPPO_NAME);
      }
    }
    return false;
  }

  /** 是否dsp出价降权 */
  public static boolean changeTbaBidPrice(
      TradingType tradingType, DspInfo dspInfo, int auctionType) {
    return TradingType.RTB.equals(tradingType)
        && dspInfo.getRtbFirstPriceResponseChange() == 1
        && auctionType == AuctionType.FIRST_PRICE.getCode();
  }

  public static boolean hasMarketUrl(MaterialMeta.Builder mBuilder) {
    return mBuilder.hasAndroidMarket() && !mBuilder.getAndroidMarket().getMarketUrl().isBlank()
        || !mBuilder.getDeeplinkUrl().isBlank() && mBuilder.getDeeplinkUrl().startsWith("market:");
  }

  public static void setUniversalLink(
      int requestFlowType,
      String universalLink,
      String landingPage,
      MaterialMeta.Builder mBuilder,
      Ad.Builder adBuilder) {
    if (requestFlowType == RequestFlowType.STANDARD.getCode()) {
      mBuilder
          .setInteractionType(RtbResponse.InteractType.DEEP_LINK_VALUE)
          .setDeeplinkUrl(universalLink)
          .setLandingPage(landingPage);
    } else {
      mBuilder
          .setLandingPage(universalLink)
          .setInteractionType(RtbResponse.InteractType.WEB_VIEW_VALUE);
      adBuilder.setForbidenParseLandingpage(true);
    }
  }

  public static String generateHtml(
      RtbRequest rtbRequest,
      int templateId,
      String htmlSource,
      HtmlTemplateParam param,
      Map<String, String> macroMap,
      AdditionalResponse additionalResponse) {
    var bidRequest = rtbRequest.getBidRequest();
    var osType = bidRequest.getDevice().getOsType();
    var sdkVersion = bidRequest.getSdkVersion();
    var needMiitCompliance = rtbRequest.getNeedMiitCompliance();

    String btnBgColor = null;
    String btnText = null;
    if (null != additionalResponse && additionalResponse.hasAdButton()) {
      var adButton = additionalResponse.getAdButton();
      if (!adButton.getBgColor().isBlank()) {
        btnBgColor = adButton.getBgColor();
      }
      if (!adButton.getText().isBlank()) {
        btnText = adButton.getText();
      }
    }

    if (templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_100049.getId()
        || templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_100203.getId()) {
      MraidTemplateConfig mtc = ConfigManager.get(MraidTemplateConfig.class);

      String buttonText =
          param.isDownload()
              ? mtc.getButtonText().getDownloadText()
              : mtc.getButtonText().getOpenUrlText();

      int imgWidth = 0;
      int imageHeight = 0;
      if (param.getImageSize() != null) {
        if (param.getImageSize().getWidth() > 0) {
          imgWidth = param.getImageSize().getWidth();
        }
        if (param.getImageSize().getHeight() > 0) {
          imageHeight = param.getImageSize().getHeight();
        }
      }

      Map<String, String> mraidParam = Maps.newHashMapWithExpectedSize(9);
      mraidParam.put(
          MraidTemplateField.BODY_BG_IMG.name(), Strings.nullToEmpty(param.getImageUrl()));
      mraidParam.put(
          MraidTemplateField.BTN_TXT.name(),
          Strings.isNullOrEmpty(btnText) ? Strings.nullToEmpty(buttonText) : btnText);
      mraidParam.put(
          MraidTemplateField.DESCRIBE.name(), Strings.nullToEmpty(param.getDescription()));
      mraidParam.put(MraidTemplateField.ICON_IMG.name(), Strings.nullToEmpty(param.getIconUrl()));
      mraidParam.put(MraidTemplateField.TITLE.name(), Strings.nullToEmpty(param.getTitle()));
      mraidParam.put(MraidTemplateField.IMG_WIDTH.name(), Integer.toString(imgWidth));
      mraidParam.put(MraidTemplateField.IMG_HEIGHT.name(), Integer.toString(imageHeight));

      Float score = param.getScore();
      if (score != null && score >= 1 && score <= 5) {
        mraidParam.put(MraidTemplateField.STAR.name(), Float.toString(score));
      } else {
        mraidParam.put(MraidTemplateField.STAR.name(), "4.5");
      }

      if (!CollectionUtils.isEmpty(macroMap)) {
        var adSlot = bidRequest.getSlots(0);
        if (adSlot.getWidgetSensitivity() > 0
            && macroMap.containsKey(MraidTemplateField.SENSITIVITY.name())
            && NumberUtils.isParsable(macroMap.get(MraidTemplateField.SENSITIVITY.name()))) {
          Map<String, String> tempMacroMap = Maps.newHashMap(macroMap);
          tempMacroMap.put(
              MraidTemplateField.SENSITIVITY.name(),
              Integer.toString(
                  Math.max(
                      adSlot.getWidgetSensitivity(),
                      Integer.parseInt(macroMap.get(MraidTemplateField.SENSITIVITY.name())))));
          mraidParam.putAll(
              processComplianceMacro(adSlot, osType, sdkVersion, tempMacroMap, needMiitCompliance));
        } else {
          mraidParam.putAll(
              processComplianceMacro(adSlot, osType, sdkVersion, macroMap, needMiitCompliance));
        }
      }

      Map<String, String[]> macroInfo = Maps.newHashMapWithExpectedSize(2);
      List<String> keys = Lists.newArrayListWithCapacity(1);
      List<String> values = Lists.newArrayListWithCapacity(1);

      keys.add(AdHtmlTemplateMacro.MARCO_ENDCARD_CONFIG);
      values.add(Strings.nullToEmpty(JsonSerializationUtils.objToJson(mraidParam)));

      macroInfo.put(Constants.MACRO_INFO_KEYS, keys.toArray(new String[0]));
      macroInfo.put(Constants.MACRO_INFO_VALUES, values.toArray(new String[0]));

      return StringUtils.replaceEach(
          htmlSource,
          macroInfo.get(Constants.MACRO_INFO_KEYS),
          macroInfo.get(Constants.MACRO_INFO_VALUES));
    } else if (templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId()) {
      MraidTemplateConfig mtc = ConfigManager.get(MraidTemplateConfig.class);

      String buttonText =
          param.isDownload()
              ? mtc.getButtonText().getDownloadText()
              : mtc.getButtonText().getOpenUrlText();

      MraidTemplateConfig.ImageSize imageSize = new MraidTemplateConfig.ImageSize();
      if (param.getImageSize() != null && param.getImageSize().getWidth() > 0) {
        imageSize.setW(param.getImageSize().getWidth());
        imageSize.setH(param.getImageSize().getHeight());
      }

      MraidTemplateConfig.MraidParam mraidParam = new MraidTemplateConfig.MraidParam();
      mraidParam.setBodyBgImg(Strings.nullToEmpty(param.getImageUrl()));
      mraidParam.setBtnTxt(Strings.nullToEmpty(buttonText));
      mraidParam.setDescribe(Strings.nullToEmpty(param.getDescription()));
      mraidParam.setIconImg(Strings.nullToEmpty(param.getIconUrl()));
      mraidParam.setTitle(Strings.nullToEmpty(param.getTitle()));
      mraidParam.setImgSize(imageSize);

      Map<String, String[]> macroInfo = Maps.newHashMapWithExpectedSize(2);
      List<String> keys = Lists.newArrayListWithCapacity(1);
      List<String> values = Lists.newArrayListWithCapacity(1);

      keys.add(AdHtmlTemplateMacro.MARCO_ENDCARD_CONFIG);
      values.add(Strings.nullToEmpty(JsonSerializationUtils.objToJson(mraidParam)));

      macroInfo.put(Constants.MACRO_INFO_KEYS, keys.toArray(new String[0]));
      macroInfo.put(Constants.MACRO_INFO_VALUES, values.toArray(new String[0]));

      return StringUtils.replaceEach(
          htmlSource,
          macroInfo.get(Constants.MACRO_INFO_KEYS),
          macroInfo.get(Constants.MACRO_INFO_VALUES));
    } else if (templateId == BaseRvTemplate.BASE_ORIGIN_TEMPLATE_109.getId()) {
      HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
      String buttonText =
          param.isDownload()
              ? config.getButtonText().getDownloadText()
              : config.getButtonText().getOpenUrlText();

      Map<String, String[]> macroInfo = Maps.newHashMapWithExpectedSize(2);
      List<String> keys = Lists.newArrayListWithCapacity(7);
      List<String> values = Lists.newArrayListWithCapacity(7);

      keys.add(AdHtmlTemplateMacro.MACRO_BTN_TXT_COLOR);
      values.add(HtmlTemplateConfig.TEMPLATE_109_BTN_TEXT_COLOR);

      keys.add(AdHtmlTemplateMacro.MACRO_BTN_BG_COLOR);
      values.add(
          Strings.isNullOrEmpty(btnBgColor)
              ? HtmlTemplateConfig.TEMPLATE_109_BTN_BG_COLOR
              : btnBgColor);

      keys.add(AdHtmlTemplateMacro.MACRO_STAR);
      Float score = param.getScore();
      if (score != null && score >= 1 && score <= 5) {
        values.add(Float.toString(score));
      } else {
        values.add("4.5");
      }

      keys.add(AdHtmlTemplateMacro.MACRO_TITLE);
      values.add(Strings.nullToEmpty(param.getTitle()));

      keys.add(AdHtmlTemplateMacro.MACRO_ICON_IMG);
      values.add(Strings.nullToEmpty(param.getIconUrl()));

      keys.add(AdHtmlTemplateMacro.MACRO_BTN_TXT);
      values.add(Strings.isNullOrEmpty(btnText) ? Strings.nullToEmpty(buttonText) : buttonText);

      String desc = param.getDescription();
      if (Strings.isNullOrEmpty(desc)) {
        ConstantConfig cc = ConfigManager.get(ConstantConfig.class);
        if (cc != null) {
          if (param.isDownload()) {
            desc = cc.getDefaultDownloadDesc();
          } else {
            desc = cc.getDefaultWebviewDesc();
          }
        }
        if (Strings.isNullOrEmpty(desc)) {
          desc = RtbConstants.BLANK_SPACE;
        }
      }
      keys.add(AdHtmlTemplateMacro.MACRO_CONTENT);
      values.add(desc);

      macroInfo.put(Constants.MACRO_INFO_KEYS, keys.toArray(new String[0]));
      macroInfo.put(Constants.MACRO_INFO_VALUES, values.toArray(new String[0]));

      return StringUtils.replaceEach(
          htmlSource,
          macroInfo.get(Constants.MACRO_INFO_KEYS),
          macroInfo.get(Constants.MACRO_INFO_VALUES));
    } else if (templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_100129.getId()) {
      HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
      Map<String, String> macroInfo = Maps.newHashMapWithExpectedSize(12);
      macroInfo.put(HtmlTemplateConfig.FIELD_BODY_BG_IMG, Strings.nullToEmpty(param.getImageUrl()));
      macroInfo.put(HtmlTemplateConfig.FIELD_ICON_IMG, Strings.nullToEmpty(param.getIconUrl()));
      macroInfo.put(HtmlTemplateConfig.FIELD_TITLE, Strings.nullToEmpty(param.getTitle()));
      macroInfo.put(HtmlTemplateConfig.FIELD_CONTENT, Strings.nullToEmpty(param.getDescription()));
      Float score = param.getScore();
      if (score != null && score >= 1 && score <= 5) {
        macroInfo.put(HtmlTemplateConfig.FIELD_STAR, Float.toString(score));
      } else {
        macroInfo.put(HtmlTemplateConfig.FIELD_STAR, "4.5");
      }
      macroInfo.put(
          HtmlTemplateConfig.FIELD_BTN_BG_COLOR,
          Strings.isNullOrEmpty(btnBgColor)
              ? HtmlTemplateConfig.TEMPLATE_115_BTN_BG_COLOR
              : btnBgColor);
      macroInfo.put(
          HtmlTemplateConfig.FIELD_BTN_TXT_COLOR,
          Strings.isNullOrEmpty(btnText)
              ? HtmlTemplateConfig.TEMPLATE_115_BTN_TEXT_COLOR
              : btnText);
      macroInfo.put(
          HtmlTemplateConfig.FIELD_BTN_TXT,
          Strings.isNullOrEmpty(param.getBtnText())
              ? param.isDownload()
                  ? config.getButtonText().getDownloadText()
                  : config.getButtonText().getOpenUrlText()
              : param.getBtnText());

      if (!CollectionUtils.isEmpty(macroMap)) {
        var adSlot = bidRequest.getSlots(0);
        if (adSlot.getWidgetSensitivity() > 0
            && macroMap.containsKey(MraidTemplateField.SENSITIVITY.name())
            && NumberUtils.isParsable(macroMap.get(MraidTemplateField.SENSITIVITY.name()))) {
          Map<String, String> tempMacroMap = Maps.newHashMap(macroMap);
          tempMacroMap.put(
              MraidTemplateField.SENSITIVITY.name(),
              Integer.toString(
                  Math.max(
                      adSlot.getWidgetSensitivity(),
                      Integer.parseInt(macroMap.get(MraidTemplateField.SENSITIVITY.name())))));
          macroInfo.putAll(
              processComplianceMacro(adSlot, osType, sdkVersion, tempMacroMap, needMiitCompliance));
        } else {
          macroInfo.putAll(
              processComplianceMacro(adSlot, osType, sdkVersion, macroMap, needMiitCompliance));
        }
      }
      return htmlSource.replace(
          AdHtmlTemplateMacro.MARCO_ENDCARD_CONFIG,
          JsonSerializationUtils.objToJson(Map.of("obj", macroInfo)));
    }
    return StringUtils.EMPTY;
  }

  public static boolean checkGromoreApiFormatType(
      int templateActionType,
      boolean isDownloadAd,
      int templateFormatType,
      List<Integer> apiSupportActionList) {
    return templateActionType == RtbResponse.InteractType.APP_DOWNLOAD_VALUE
            && isDownloadAd
            && (templateFormatType == 600004
                    && apiSupportActionList.contains(
                        GromoreAdxAdType.UNION_FEED_APP_LARGE.getNumber())
                || templateFormatType == 600001
                    && apiSupportActionList.contains(GromoreAdxAdType.UNION_APP_VIDEO.getNumber())
                || templateFormatType == 600007
                    && apiSupportActionList.contains(
                        GromoreAdxAdType.UNION_APP_VERTICAL_VIDEO.getNumber())
                || templateFormatType == 600005
                    && (apiSupportActionList.contains(
                            GromoreAdxAdType.UNION_APP_VERTICAL_VIDEO.getNumber())
                        || apiSupportActionList.contains(
                            GromoreAdxAdType.UNION_APP_VIDEO.getNumber())))
        || templateActionType == RtbResponse.InteractType.WEB_VIEW_VALUE
            && !isDownloadAd
            && (templateFormatType == 600004
                    && apiSupportActionList.contains(
                        GromoreAdxAdType.UNION_FEED_LP_LARGE.getNumber())
                || templateFormatType == 600001
                    && apiSupportActionList.contains(GromoreAdxAdType.UNION_LP_VIDEO.getNumber())
                || templateFormatType == 600007
                    && apiSupportActionList.contains(
                        GromoreAdxAdType.UNION_LP_VERTICAL_VIDEO.getNumber())
                || templateFormatType == 600005
                    && (apiSupportActionList.contains(
                            GromoreAdxAdType.UNION_LP_VERTICAL_VIDEO.getNumber())
                        || apiSupportActionList.contains(
                            GromoreAdxAdType.UNION_LP_VIDEO.getNumber())));
  }

  /**
   * 激励+全屏+插屏，处理SDK流量请求与 mraid 版本信息 @See <a
   * href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=103824018">需求wiki</a>
   *
   * <AUTHOR>
   * @date 2024/6/11 下午5:47
   * @param requestFlowType
   * @param adType
   * @param osType
   * @param osVersion
   * @param sdkVersion
   * @param adSlotBuilder
   * @param appBuilder
   */
  public static void processReqMraidInfo(
      int requestFlowType,
      int adType,
      int osType,
      Version osVersion,
      Version sdkVersion,
      AdSlot.Builder adSlotBuilder,
      App.Builder appBuilder) {

    if (requestFlowType != RequestFlowType.SDK.getCode()
        || adType != AdType.REWARDED_VIDEO.getTypeNum()
            && adType != AdType.FULL_SCREEN_VIDEO.getTypeNum()
            && adType != AdType.INTERSTITIAL.getTypeNum()) {
      return;
    }

    var isIosDevice = osType == OsType.IOS.getTypeNum();
    var isAndroidDevice = osType == OsType.ANDROID.getTypeNum();
    if (isIosDevice
            && (compareVersion(osVersion, Constants.OS_VERSION_15_0_0) >= 0
                    && compareVersion(sdkVersion, Constants.SDK_VERSION_3_2_0) <= 0
                || compareVersion(osVersion, Constants.OS_VERSION_8_0_0) <= 0)
        || isAndroidDevice && compareVersion(osVersion, Constants.OS_VERSION_6_0_0) < 0) {
      // m1 m2 都不支持
      var creativeTypeList = adSlotBuilder.getCreativeTypeList();
      if (creativeTypeList.contains(CreativeType.MRAID.getTypeNum())
          || creativeTypeList.contains(CreativeType.SERVER_MRAID_2_0.getTypeNum())) {
        var filterCreativeTypeList =
            creativeTypeList.stream()
                .filter(
                    ct ->
                        !ct.equals(CreativeType.MRAID.getTypeNum())
                            && !ct.equals(CreativeType.SERVER_MRAID_2_0.getTypeNum()))
                .toList();
        adSlotBuilder.clearCreativeType().addAllCreativeType(filterCreativeTypeList);
      }
      appBuilder.clearMraid1Version().clearMraid2Version();

    } else if (isIosDevice && compareVersion(sdkVersion, Constants.SDK_VERSION_4_2_0) < 0
        || isAndroidDevice && compareVersion(sdkVersion, Constants.SDK_VERSION_4_4_0) < 0) {
      // m1 1.0
      var creativeTypeList = adSlotBuilder.getCreativeTypeList();
      if (creativeTypeList.contains(CreativeType.MRAID.getTypeNum())) {
        if (!appBuilder.hasMraid1Version()) {
          appBuilder.setMraid1Version(
              Version.newBuilder().setMajor(1).setMinor(0).setMicro(0).setVersionStr("1.0"));
        }
      } else {
        appBuilder.clearMraid1Version();
      }

      if (creativeTypeList.contains(CreativeType.SERVER_MRAID_2_0.getTypeNum())) {
        var filterCreativeTypeList =
            creativeTypeList.stream()
                .filter(ct -> !ct.equals(CreativeType.SERVER_MRAID_2_0.getTypeNum()))
                .toList();
        adSlotBuilder.clearCreativeType().addAllCreativeType(filterCreativeTypeList);
      }
      appBuilder.clearMraid2Version();

    } else if (isIosDevice
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_2_0) >= 0
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_9_0) < 0
        || isAndroidDevice
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_4_0) >= 0
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_12_4) < 0) {
      // m1 1.1
      var creativeTypeList = adSlotBuilder.getCreativeTypeList();
      if (creativeTypeList.contains(CreativeType.MRAID.getTypeNum())) {
        if (!appBuilder.hasMraid1Version()) {
          appBuilder.setMraid1Version(
              Version.newBuilder().setMajor(1).setMinor(1).setMicro(0).setVersionStr("1.1"));
        }
      } else {
        appBuilder.clearMraid1Version();
      }

      if (creativeTypeList.contains(CreativeType.SERVER_MRAID_2_0.getTypeNum())) {
        var filterCreativeTypeList =
            creativeTypeList.stream()
                .filter(ct -> !ct.equals(CreativeType.SERVER_MRAID_2_0.getTypeNum()))
                .toList();
        adSlotBuilder.clearCreativeType().addAllCreativeType(filterCreativeTypeList);
      }
      appBuilder.clearMraid2Version();
    } else if (isIosDevice
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_9_0) >= 0
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_11_0) < 0
        || isAndroidDevice
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_12_4) >= 0
            && compareVersion(sdkVersion, Constants.SDK_VERSION_4_15_0) < 0) {
      // m1 1.1 m2 2.1
      var creativeTypeList = adSlotBuilder.getCreativeTypeList();
      if (creativeTypeList.contains(CreativeType.MRAID.getTypeNum())) {
        if (!appBuilder.hasMraid1Version()) {
          appBuilder.setMraid1Version(
              Version.newBuilder().setMajor(1).setMinor(1).setMicro(0).setVersionStr("1.1"));
        }
      } else {
        appBuilder.clearMraid1Version();
      }
      if (creativeTypeList.contains(CreativeType.SERVER_MRAID_2_0.getTypeNum())) {
        if (adSlotBuilder.getDisableMraid2()) {
          var filterCreativeTypeList =
              creativeTypeList.stream()
                  .filter(ct -> !ct.equals(CreativeType.SERVER_MRAID_2_0.getTypeNum()))
                  .toList();
          adSlotBuilder.clearCreativeType().addAllCreativeType(filterCreativeTypeList);
          appBuilder.clearMraid2Version();
        } else {
          if (!appBuilder.hasMraid2Version()) {
            appBuilder.setMraid2Version(
                Version.newBuilder().setMajor(2).setMinor(1).setMicro(0).setVersionStr("2.1"));
          }
        }
      } else {
        appBuilder.clearMraid2Version();
      }
    } else {
      var creativeTypeList = adSlotBuilder.getCreativeTypeList();
      if (!creativeTypeList.contains(CreativeType.MRAID.getTypeNum())) {
        appBuilder.clearMraid1Version();
      }
      if (!creativeTypeList.contains(CreativeType.SERVER_MRAID_2_0.getTypeNum())) {
        appBuilder.clearMraid2Version();
      } else {
        if (adSlotBuilder.getDisableMraid2()) {
          var filterCreativeTypeList =
              creativeTypeList.stream()
                  .filter(ct -> !ct.equals(CreativeType.SERVER_MRAID_2_0.getTypeNum()))
                  .toList();
          adSlotBuilder.clearCreativeType().addAllCreativeType(filterCreativeTypeList);
          // 为了直投能够在不支持m2时依然能出兜底原生广告，暂时不删除mraid2 version
          //          appBuilder.clearMraid2Version();
        }
      }
    }
  }

  /** 是否mraid 1 模板 */
  public static boolean isMraidV1Template(int templateId) {
    return templateId >= 100000 && templateId < 200000;
  }

  /** 是否mraid 2 模板 */
  public static boolean isMraidV2Template(int templateId) {
    return !BaseRvTemplate.idList.contains(templateId);
  }

  public static boolean isDownloadAd(
      int interactType, int osType, int dspProtocolType, String deeplink, String productId) {
    return interactType == RtbResponse.InteractType.APP_DOWNLOAD_VALUE
        || (DspProtocolType.TENCENT_GDT.getType() == dspProtocolType
            && (interactType == RtbResponse.InteractType.REDIRECT_302_DOWNLOAD_VALUE
                || (osType == OsType.IOS.getTypeNum()
                    && (interactType == RtbResponse.InteractType.REDIRECT_302_JUMP_VALUE
                        || interactType == RtbResponse.InteractType.WEB_VIEW_VALUE))));
  }

  public static boolean isDeepLinkAd(int requestFlowType, MaterialMeta materials) {
    var interactionType = materials.getInteractionType();
    var deeplinkUrl = materials.getDeeplinkUrl();

    return (interactionType == RtbResponse.InteractType.WEB_VIEW_VALUE
                || interactionType == RtbResponse.InteractType.SYS_EXPLORE_VALUE)
            && !deeplinkUrl.isBlank()
            && (!materials.hasAndroidMarket() || requestFlowType != RequestFlowType.SDK.getCode())
        || interactionType == RtbResponse.InteractType.DEEP_LINK_VALUE && !deeplinkUrl.isBlank();
  }

  /** 是否忽略最大视频时长限制过滤 */
  public static boolean ignoreVideoMaxDuration(int requestFlowType, int adType) {
    return requestFlowType == RequestFlowType.SDK.getCode()
        && adType == AdType.REWARDED_VIDEO.getTypeNum();
  }

  /** sdk 是否会缓存广告以减少广告请求 */
  public static boolean isSdkCacheAd(
      int osType, Version sdkVersion, int requestFlowType, int adType, int hbType) {
    return requestFlowType == RequestFlowType.SDK.getCode()
        && hbType != HeaderBiddingType.S_TO_S.getCode()
        && adType == AdType.NATIVE.getTypeNum()
        && SdkCapabilityUtils.isNewBiddingAd(osType, sdkVersion);
  }

  /** 当sdk 缓存原生广告时，增加广告设置项 */
  public static void setCachedNativeAdSetting(
      NativeAdSetting.Builder nativeAdSettingBuilder,
      SdkCommonSetting sdkCommonSetting,
      SdkNativeSetting sdkNativeSetting,
      AdSlot adSlot,
      int hbType) {
    nativeAdSettingBuilder
        .setAdPoolSize(sdkNativeSetting.getAdPoolSize())
        .setReqIntervalTime(sdkNativeSetting.getReqIntervalTime());
    if (!SdkCommonSetting.getDefaultInstance().equals(sdkCommonSetting)) {
      nativeAdSettingBuilder.setLogIntervalTime(sdkCommonSetting.getLogIntervalTime());
    }

    if (hbType == HeaderBiddingType.C_TO_S.getCode()) {
      nativeAdSettingBuilder.setMediaExpectedFloor(adSlot.getBidfloor());
    } else {
      if (adSlot.getAdjustedErpm() > 0) {
        nativeAdSettingBuilder.setMediaExpectedFloor(adSlot.getAdjustedErpm());
      } else {
        nativeAdSettingBuilder.setMediaExpectedFloor(adSlot.getExpectedFloor());
      }
    }
  }

  public static void setCachedAdBidResponse(
      RtbRequest rtbRequest,
      BidResponse.Builder bidResponseBuilder,
      SdkSetting sdkSetting,
      SdkSetting appSdkSetting) {

    var hbType = rtbRequest.getHbType();
    var bidRequest = rtbRequest.getBidRequest();
    var osType = bidRequest.getDevice().getOsType();
    var sdkVersion = rtbRequest.getSdkVersion();
    var requestFlowType = rtbRequest.getRequestFlowType();
    var adSlot = bidRequest.getSlots(0);
    var adSlotType = adSlot.getAdslotType(0);
    var isSdkCacheAd = AdUtil.isSdkCacheAd(osType, sdkVersion, requestFlowType, adSlotType, hbType);

    if (isSdkCacheAd && sdkSetting.hasNative()) {
      SlotAdSetting.Builder slotAdSettingBuilder = SlotAdSetting.newBuilder();
      var nativeAdSettingBuilder = NativeAdSetting.newBuilder();
      AdUtil.setCachedNativeAdSetting(
          nativeAdSettingBuilder,
          SdkSetting.getDefaultInstance().equals(appSdkSetting) || !appSdkSetting.hasCommon()
              ? SdkCommonSetting.getDefaultInstance()
              : appSdkSetting.getCommon(),
          sdkSetting.getNative(),
          adSlot,
          hbType);
      slotAdSettingBuilder.setNativeSetting(nativeAdSettingBuilder);
      bidResponseBuilder.setSlotAdSetting(slotAdSettingBuilder);
    }
  }

  //  /**
  //   * 处理激励视频下发特定模版时点击track中_SLD_宏
  //   *
  //   * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=103830597">wiki</a>
  //   */
  //  public static void processRvAdSld(
  //      Ad.Builder adBuilder, int templateId, DspProtocolType dspProtocolType) {
  //
  //    String sldValue = null;
  //    if (templateId == BaseRvTemplate.BASE_ORIGIN_TEMPLATE_10.getId()
  //        || templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId()) {
  //      sldValue = "0";
  //    } else if (templateId == BaseRvTemplate.BASE_MRAID_TEMPLATE_100327.getId()) {
  //      sldValue = "3";
  //    }
  //
  //    if (!Strings.isNullOrEmpty(sldValue)) {
  //      for (Tracking.Builder trackBuilder : adBuilder.getAdTrackingBuilderList()) {
  //        var trackingEventType = trackBuilder.getTrackingEventType();
  //        if ((TrackingEvent.click.name().equalsIgnoreCase(trackingEventType)
  //                || TrackingEvent.companion_click.name().equalsIgnoreCase(trackingEventType)
  //                || TrackingEvent.full_video_click.name().equalsIgnoreCase(trackingEventType))
  //            && trackBuilder.getTrackingUrlCount() > 0) {
  //          var trackingUrlList = trackBuilder.getTrackingUrlList();
  //          List<String> newTrackList = Lists.newArrayListWithCapacity(trackingUrlList.size());
  //          for (String s : trackingUrlList) {
  //            var isSigmobTrackUrl = AdUtil.isSigmobTrackUrl(s);
  //            if (isSigmobTrackUrl) {
  //              newTrackList.add(s.replace(AdMacro.TRACK_SLD, sldValue));
  //            } else {
  //              newTrackList.add(s);
  //            }
  //          }
  //          trackBuilder.clearTrackingUrl().addAllTrackingUrl(newTrackList);
  //        }
  //      }
  //    }
  //  }

  /**
   * 检查返现后价格是否合法
   *
   * @param tradingType
   * @param price
   * @param adSlot
   * @return true - 合法； false - 不合法
   */
  public static boolean checkRebatePrice(TradingType tradingType, int price, AdSlot adSlot) {
    if (TradingType.DEAL.equals(tradingType) || TradingType.PDB.equals(tradingType)) {
      var settlementSetting = adSlot.getSettlementSetting();
      return price
          >= (settlementSetting == SettlementSetting.BIDDING.getCode()
                  || settlementSetting == SettlementSetting.DEVELOPER_ECPM.getCode()
              ? adSlot.getExpectedFloor()
              : adSlot.getBidfloor());
    }

    return true;
  }

  /** 设置用户安装应用列表 */
  public static void setUserInstalledApp(
      int requestFlowType, BidRequest.Builder bidRequestBuilder, Collection<String> installedApps) {
    if (CollectionUtils.isEmpty(installedApps)) {
      return;
    }

    var userBuilder = bidRequestBuilder.getUserBuilder();
    if (userBuilder == null) {
      userBuilder = User.newBuilder();
    }
    if (userBuilder.getInstalledAppCount() > 0) {
      Set<String> installAppSet = Sets.newHashSet(userBuilder.getInstalledAppList());
      installAppSet.addAll(installedApps);
      userBuilder.clearInstalledApp().addAllInstalledApp(installAppSet);
    } else {
      userBuilder.addAllInstalledApp(installedApps);
    }
    bidRequestBuilder.setUser(userBuilder);
  }

  /** 设置用户安装应用列表 */
  public static void setUserInstalledApp(
      RtbRequest.Builder rtbRequestBuilder,
      BidRequest.Builder bidRequestBuilder,
      Collection<String> opdInstalledApps,
      Set<String> mappingInstalledPackages,
      Set<String> mappingExcludedPackages) {
    var isOpdInstallPackageEmpty = CollectionUtils.isEmpty(opdInstalledApps);
    var isMappingExcludePackageEmpty = CollectionUtils.isEmpty(mappingExcludedPackages);
    var isMappingInstalledPackageEmpty = CollectionUtils.isEmpty(mappingInstalledPackages);
    if (isOpdInstallPackageEmpty
        && isMappingExcludePackageEmpty
        && isMappingInstalledPackageEmpty) {
      return;
    }

    var userBuilder = bidRequestBuilder.getUserBuilder();
    if (userBuilder == null) {
      userBuilder = User.newBuilder();
    }
    if (userBuilder.getInstalledAppCount() > 0) {
      Set<String> installAppSet = Sets.newHashSet(userBuilder.getInstalledAppList());
      if (!isOpdInstallPackageEmpty) {
        installAppSet.addAll(opdInstalledApps);
      }
      if (!isMappingInstalledPackageEmpty) {
        installAppSet.addAll(mappingInstalledPackages);
        rtbRequestBuilder.addAllStrategyMappingPackages(mappingInstalledPackages);
      }
      if (!isMappingExcludePackageEmpty) {
        installAppSet.removeAll(mappingExcludedPackages);
      }
      userBuilder.clearInstalledApp();
      if (!CollectionUtils.isEmpty(installAppSet)) {
        userBuilder.addAllInstalledApp(installAppSet);
      }
    } else {
      if (isOpdInstallPackageEmpty && isMappingInstalledPackageEmpty) {
        return;
      }
      Set<String> installAppSet = Sets.newHashSet();
      if (!isOpdInstallPackageEmpty) {
        installAppSet.addAll(opdInstalledApps);
      }
      if (!isMappingInstalledPackageEmpty) {
        installAppSet.addAll(mappingInstalledPackages);
        rtbRequestBuilder.addAllStrategyMappingPackages(mappingInstalledPackages);
      }
      if (!isMappingExcludePackageEmpty) {
        installAppSet.removeAll(mappingExcludedPackages);
      }
      userBuilder.addAllInstalledApp(installAppSet);
    }
    bidRequestBuilder.setUser(userBuilder);
  }

  /**
   * 设置模版配置灵敏度
   *
   * @param rtbResponseBuilder
   * @param adVid
   * @param templateConfigSensitivity
   */
  public static void setTemplateConfigSensitivity(
      RtbResponse.Builder rtbResponseBuilder, String adVid, int templateConfigSensitivity) {
    if (templateConfigSensitivity > 0) {
      var newAdditionalResponseHashMap =
          Maps.newHashMap(rtbResponseBuilder.getAdditionalResponsesMap());
      AdditionalResponse newAdditionalResponse = null;
      if (rtbResponseBuilder.getAdditionalResponsesCount() > 0) {
        var additionalResponse = rtbResponseBuilder.getAdditionalResponsesMap().get(adVid);
        if (null != additionalResponse) {
          newAdditionalResponse =
              additionalResponse.toBuilder()
                  .setWidgetSensitivity(templateConfigSensitivity)
                  .build();
        } else {
          newAdditionalResponse =
              AdditionalResponse.newBuilder()
                  .setWidgetSensitivity(templateConfigSensitivity)
                  .build();
        }
      } else {
        newAdditionalResponse =
            AdditionalResponse.newBuilder().setWidgetSensitivity(templateConfigSensitivity).build();
      }
      newAdditionalResponseHashMap.put(adVid, newAdditionalResponse);
      rtbResponseBuilder
          .clearAdditionalResponses()
          .putAllAdditionalResponses(newAdditionalResponseHashMap);
    }
  }

  public static Map<String, String> processComplianceMacro(
      AdSlot adSlot,
      int osType,
      Version sdkVersion,
      Map<String, String> originMacroMap,
      boolean needMiitCompliance) {
    if (CollectionUtils.isEmpty(originMacroMap)) {
      return originMacroMap;
    }
    if (originMacroMap.containsKey(MraidTemplateField.HIT_PERCENT.name())
        || originMacroMap.containsKey(MraidTemplateField.DECORATE.name())) {
      if (needMiitCompliance) {
        var tempMacroMap = Maps.newHashMap(originMacroMap);
        if (tempMacroMap.containsKey(MraidTemplateField.HIT_PERCENT.name())) {
          tempMacroMap.put(MraidTemplateField.HIT_PERCENT.name(), "0");
        }
        tempMacroMap.remove(MraidTemplateField.DECORATE.name());
        return tempMacroMap;
      } else if (adSlot.getDisableInteraction() == 1) {
        var tempMacroMap = Maps.newHashMap(originMacroMap);
        tempMacroMap.remove(MraidTemplateField.DECORATE.name());
        return tempMacroMap;
      }
    }
    return originMacroMap;
  }

  public static void setDeviceAppStorePackage(
      String deviceBrand,
      SlotAdSetting.Builder slotAdSettingBuilder,
      SdkSetting sdkSetting,
      SdkSetting appSdkSetting,
      Version osVersion) {
    if (!Strings.isNullOrEmpty(deviceBrand)
        && !SdkSetting.getDefaultInstance().equals(appSdkSetting)
        && appSdkSetting.hasCommon()
        && appSdkSetting.getCommon().getBrandsCount() > 0
        && appSdkSetting.getCommon().getBrandsList().contains(deviceBrand.toLowerCase())) {
      ConstantConfig constantConfig = ConfigManager.get(ConstantConfig.class);
      if (null != constantConfig) {
        var deviceAppStorePackageList =
            constantConfig.getDeviceBrandAppStorePackageNames(deviceBrand.toLowerCase(), osVersion);
        if (!CollectionUtils.isEmpty(deviceAppStorePackageList)) {
          slotAdSettingBuilder.addAllMarketPackageName(deviceAppStorePackageList);
        }
      }
    }
  }

  public static boolean useTobidAdx(int adxId) {
    return adxId == RtbConstants.TOBID_ADX_ID || adxId == RtbConstants.SIGMOB_TOBID_ADX_ID;
  }

  /** 设置广告所用cptId */
  public static void setAdCptId(RtbResponse.Builder rtbResponseBuilder, String adVid, int cptId) {
    if (rtbResponseBuilder.getAdditionalResponsesCount() > 0 && cptId > 0) {
      var additionalResponse = rtbResponseBuilder.getAdditionalResponsesMap().get(adVid);
      if (null != additionalResponse) {
        rtbResponseBuilder.putAdditionalResponses(
            adVid, additionalResponse.toBuilder().setCptId(cptId).build());
      }
    }
  }

  /** 广告中应用名称 */
  public static String getAdAppName(MaterialMeta materialMeta) {
    String appName = materialMeta.getAppName();
    if (Strings.isNullOrEmpty(appName) && materialMeta.hasAdPrivacy()) {
      var adPrivacy = materialMeta.getAdPrivacy();
      if (adPrivacy.getPrivacyTemplateInfoCount() > 0) {
        var privacyTemplateInfoMap = adPrivacy.getPrivacyTemplateInfoMap();
        if (privacyTemplateInfoMap.containsKey(AdPrivacyTemplateInfo.APP_NAME.getName())) {
          appName =
              Strings.nullToEmpty(
                  privacyTemplateInfoMap.get(AdPrivacyTemplateInfo.APP_NAME.getName()));
        }
      }
    }
    return appName;
  }

  /** 广告素材 */
  public static AdMaterial getAdMaterial(
      AdSlot adSlot, MaterialMeta materials, AdditionalResponse additionalResponse) {
    var videoUrl = materials.getVideoUrl();
    var imageSrc = materials.getImageSrc();
    var iconUrl = materials.getIconUrl();
    String title = materials.getTitle();
    String desc = materials.getDesc();
    String creativeTitle = materials.getCreativeTitle();
    List<String> imageUrlList = Collections.emptyList();

    if (null != additionalResponse
        && additionalResponse.hasImages()
        && additionalResponse.getImages().getUrlCount() > 0) {
      imageUrlList = additionalResponse.getImages().getUrlList();
    }

    if (adSlot.getAdslotType(0) == AdType.NATIVE.getTypeNum()
        && materials.hasNativeAd()
        && adSlot.getNativeTemplateCount() > 0) {

      var nativeTemplateList = adSlot.getNativeTemplateList();
      var nativeAd = materials.getNativeAd();
      var respTemplateId = nativeAd.getTemplateId();
      var requestNativeAdTemplateOptional =
          nativeTemplateList.stream()
              .filter(template -> template.getId().equals(respTemplateId))
              .findFirst();
      if (requestNativeAdTemplateOptional.isPresent()) {
        var requestNativeAdTemplate = requestNativeAdTemplateOptional.get();
        var requestNativeAdTemplateType = requestNativeAdTemplate.getType();
        var isMultiImageTemplate =
            requestNativeAdTemplateType == NativeAdTemplateType.THREE_IMAGE.getCode();
        if (isMultiImageTemplate) {
          imageUrlList = Lists.newArrayListWithCapacity(3);
        }

        for (var requestAsset : requestNativeAdTemplate.getAssetsList()) {
          if (requestAsset.hasVideo() || requestAsset.hasImage() || requestAsset.hasText()) {
            var requestAssetId = requestAsset.getId();
            var nativeAdAssetOptional =
                nativeAd.getAssetsList().stream()
                    .filter(asset -> asset.getIndex() == requestAssetId)
                    .findFirst();
            if (nativeAdAssetOptional.isPresent()) {
              var responseAsset = nativeAdAssetOptional.get();
              if (responseAsset.hasVideo()) {
                videoUrl = responseAsset.getVideo().getUrl();
              } else if (responseAsset.hasImage()) {
                var requestAssetImage = requestAsset.getImage();
                var requestAssetImageType = requestAssetImage.getType();
                if (requestAssetImageType == NativeImageAssetType.ICON.getCode()) {
                  iconUrl = responseAsset.getImage().getUrl();
                } else if (requestAssetImageType == NativeImageAssetType.MAIN.getCode()) {
                  if (requestNativeAdTemplateType == NativeAdTemplateType.VIDEO.getCode()
                      || requestNativeAdTemplateType == NativeAdTemplateType.IMAGE.getCode()) {
                    imageSrc = responseAsset.getImage().getUrl();
                  } else if (isMultiImageTemplate) {
                    imageUrlList.add(responseAsset.getImage().getUrl());
                  }
                } else if (requestAssetImageType == NativeImageAssetType.MULTI_IMG.getCode()) {
                  if (isMultiImageTemplate) {
                    imageUrlList.add(responseAsset.getImage().getUrl());
                  }
                }
              } else if (responseAsset.hasText()) {
                RequestAssetText requestAssetText = requestAsset.getText();
                int requestAssetTextType = requestAssetText.getType();
                if (requestAssetTextType == NativeTextAssetType.TITLE.getCode()) {
                  title = responseAsset.getText().getContext();
                } else if (requestAssetTextType == NativeTextAssetType.DESCRIPTION.getCode()) {
                  desc = responseAsset.getText().getContext();
                }
              }
            }
          }
        }
      }
    }
    return AdMaterial.builder()
        .creativeTitle(creativeTitle)
        .title(title)
        .desc(desc)
        .iconUrl(iconUrl)
        .imageUrl(imageSrc)
        .videoUrl(videoUrl)
        .imageUrlList(imageUrlList)
        .build();
  }

  /** 激励视频兜底模板逻辑 */
  public static void setRvAdDefaultTemplate(
      boolean isComplianceSdk,
      boolean hasImage,
      DspProtocolType dspProtocolType,
      RtbRequest rtbRequest,
      Map<Integer, DspTemplateInfo> templateInfoMap,
      int dspId,
      MaterialMeta.Builder mBuilder,
      HtmlTemplateParam htParam,
      AdditionalResponse additionalResponse) {

    if (!hasImage && templateInfoMap.containsKey(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_109.getId())) {
      var dspTemplateInfo = templateInfoMap.get(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_109.getId());
      if (null != dspTemplateInfo) {
        var html = dspTemplateInfo.getHtml();
        if (!Strings.isNullOrEmpty(html)) {
          mBuilder
              .setTemplateId(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_109.getId())
              .setCreativeType(CreativeType.VIDEO_KEEP.getTypeNum())
              .setClickType(ClickType.BUTTON.getTypeNum())
              .setHtmlSnippet(
                  ByteString.copyFrom(
                      AdUtil.generateHtml(
                          rtbRequest,
                          BaseRvTemplate.BASE_ORIGIN_TEMPLATE_109.getId(),
                          html,
                          htParam,
                          null,
                          additionalResponse),
                      StandardCharsets.UTF_8));
          return;
        }
      }
    }

    // 尝试匹配50模版
    if (!isComplianceSdk
        && templateInfoMap.containsKey(BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId())) {
      MraidTemplateConfig mtc = ConfigManager.get(MraidTemplateConfig.class);
      var dspTemplateInfo = templateInfoMap.get(BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId());
      if (null != mtc && null != dspTemplateInfo) {
        var html = dspTemplateInfo.getHtml();
        if (!Strings.isNullOrEmpty(html)) {
          var bidRequest = rtbRequest.getBidRequest();
          if (DspProtocolType.IQIYI.equals(dspProtocolType)
              || !mtc.isExclude(Integer.parseInt(bidRequest.getApp().getAppId()))
                  && mtc.allow(
                      bidRequest.getDevice().getDid().getUdid(),
                      OsType.getType(bidRequest.getDevice().getOsType()))) {
            int clickType =
                RtbConstants.FULLSCREEN_CLICK_DSP_LIST.contains(dspId)
                    ? ClickType.FULL_SCREEN.getTypeNum()
                    : ClickType.BUTTON.getTypeNum();

            mBuilder
                .setCreativeType(CreativeType.MRAID.getTypeNum())
                .setTemplateId(BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId())
                .setClickType(clickType)
                .setHtmlSnippet(
                    ByteString.copyFrom(
                        AdUtil.generateHtml(
                            rtbRequest,
                            BaseRvTemplate.BASE_MRAID_TEMPLATE_50.getId(),
                            html,
                            htParam,
                            null,
                            additionalResponse),
                        StandardCharsets.UTF_8));
            return;
          }
        }
      }
    }

    HtmlTemplateConfig config = ConfigManager.get(HtmlTemplateConfig.class);
    // 使用10模版兜底
    if (null != config) {
      mBuilder
          .setTemplateId(BaseRvTemplate.BASE_ORIGIN_TEMPLATE_10.getId())
          .setCreativeType(CreativeType.VIDEO_HTML_SNIPPET.getTypeNum())
          .setClickType(ClickType.BUTTON.getTypeNum())
          .setHtmlSnippet(
              ByteString.copyFrom(config.getHtmlSnippet(htParam, dspId), StandardCharsets.UTF_8));
    }
  }

  /**
   * @See <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135438952">需求wiki</a>
   *
   * @param deviceBrand
   * @param dspProtocolType
   * @return true: 支持； false： 不支持
   */
  public static boolean isDeviceAndDspSupportQuickApp(String deviceBrand, int dspProtocolType) {
    return Constants.DEVICE_BRAND_VIVO.equalsIgnoreCase(deviceBrand)
        && DspProtocolType.isSurgeDsp(dspProtocolType);
  }

  public static Optional<String> getDefaultIcon(int osType, int interactionType) {

    ConstantConfig constantConfig = ConfigManager.get(ConstantConfig.class);
    if (null != constantConfig) {
      int iconType = 0;
      if (interactionType == RtbResponse.InteractType.APP_DOWNLOAD_VALUE) {
        if (osType == OsType.IOS.getTypeNum()) {
          iconType = 1;
        } else if (osType == OsType.ANDROID.getTypeNum()) {
          iconType = 2;
        } else if (osType == OsType.HARMONY_OS.getTypeNum()) {
          iconType = 4;
        }
      } else if (interactionType == RtbResponse.InteractType.WEB_VIEW_VALUE) {
        iconType = 3;
      } else if (interactionType == RtbResponse.InteractType.DEEP_LINK_VALUE) {
        iconType = 5;
      } else if (interactionType == RtbResponse.InteractType.WX_PROGRAM_VALUE
          || interactionType == RtbResponse.InteractType.WX_CANVAS_VALUE
          || interactionType == RtbResponse.InteractType.SCHEMA_WX_PROGRAM_VALUE) {
        iconType = 6;
      }
      return Optional.ofNullable(constantConfig.getDefaultIcon(iconType));
    }
    return Optional.empty();
  }
}
