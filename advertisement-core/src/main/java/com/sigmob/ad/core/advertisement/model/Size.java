package com.sigmob.ad.core.advertisement.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class Size {
  private int width;
  private int height;

  @Override
  public boolean equals(Object o) {
    if (null != o) {
      if (o instanceof Size oSize) {
        return this.getHeight() == oSize.getHeight() && this.getWidth() == oSize.getWidth();
      }
    }
    return false;
  }
}
