/** */
package com.sigmob.ad.core.exception;

/** <AUTHOR> */
public class SigmobException extends AdException {
  private static final long serialVersionUID = -6725714464158233594L;

  public SigmobException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public SigmobException(int code) {
    super();
    this.code = code;
  }

  public SigmobException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SigmobException(int code, String message) {
    super(message);
    this.code = code;
  }

  public SigmobException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SigmobException(int code, String message, Throwable cause) {
    super(message, cause);
    this.code = code;
  }

  public SigmobException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SigmobException(int code, Throwable cause) {
    super(cause);
    this.code = code;
  }

  public SigmobException(
      int code, String message, boolean enableSuppression, boolean writableStackTrace) {
    this(code, message, null, enableSuppression, writableStackTrace);
  }

  public SigmobException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SigmobException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
