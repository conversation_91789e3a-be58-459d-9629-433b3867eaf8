package com.sigmob.ad.core.constants;

import com.sigmob.ad.core.device.SigDebugSupportInfo;
import com.sigmob.ad.core.security.crypto.AesCrypto;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import org.joda.time.DateTimeZone;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public class Constants {

  public static final AesCrypto AES_CRYPTO = new AesCrypto();

  /** '_' 符 */
  public static final String SYMBOL_UNDERSCORE = "_";

  /** '++'符 */
  public static final String SYMBOL_ASTERISK = "*";

  /** ',' 符 */
  public static final String SYMBOL_COMMA = ",";

  /** '.' 符 */
  public static final char SYMBOL_DOT = '.';

  /** 分号 */
  public static final String SYMBOL_SEMICOLON = ";";

  /** 冒号 */
  public static final String SYMBOL_COLON = ":";

  /** 连字符 */
  public static final String SYMBOL_HYPHEN = "-";

  public static final char QUESTION_MARK = '?';

  public static final String SYMBOL_AT = "@";

  public static final String SYMBOL_AND = "&";

  /** 大于 */
  public static final String COMPARE_GREATER = "gt";

  /** 小于 */
  public static final String COMPARE_LITTLE = "lt";

  /** 等于 */
  public static final String COMPARE_EQUAL = "eq";

  /** 大于等于 */
  public static final String COMPARE_GREATER_AND_EQUAL = "gte";

  /** 小于等于 */
  public static final String COMPARE_LITTLE_AND_EQUAL = "lte";

  /** 不限地域代码（跟平台保持同步） */
  public static final String UNLIMITED_REGION_CODE = "9999";

  public static final String SYMBOL_BLANK_SPACE = " ";

  public static final String MULTI_DSP_NOTIFY_URL_DELIMITER = "__!!!__";

  public static final String MULTI_DSP_TRACK_URL_DELIMITER = "__!T!__";

  /** 字符串常量 - unknown */
  public static final String CONSTANT_STR_UNKNOWN = "unknown";

  /** 字符串常量 - all */
  public static final String CONSTANT_STR_ALL = "all";

  /** http请求内容头 - protobuf */
  public static final String MEDIA_TYPE_PROTOBUF = "application/x-protobuf";

  public static final String MEDIA_TYPE_PROTOBUF_UTF8 = "application/x-protobuf;charset=UTF-8";

  /** 中国全部地区 */
  //  public static final String CONSTANT_ALL_CHINA_REGION = "全国";
  public static final Long LONG_VALUE_ZERO = 0L;

  public static final Integer Integer_VALUE_ZERO = 0;
  public static final Float FLOAT_VALUE_ZERO = 0f;
  public static final int DEFAULT_USER_ID = 0;
  public static final String URL_TYPE_THIRD_PARTY_TRACK = "third_party_track";
  public static final String URL_TYPE_THIRD_PARTY_TRACK_V2 = "third_party_track_v2";
  public static final String STANDARD_MEDIA_HOST = "standard_media_host";
  public static final String TRACKING_SECURITY_KEY = "Pr2df@*sgm&^b+k%";
  public static final String TRACKING_DMI_KEY = "M5ozqM,k?A>VW]d^";
  public static final String TRACKING_DMI_IV = "sigmob2025sigmob";
  public static final String SSV_CALLBACK_URL_ENCRYPT_KEY = "S#4ig&mob!-1*2~3";
  public static final String IOS_APP_STORE_DOMAIN = "https://itunes.apple.com";
  public static final String IOS_APPS_STORE_DOMAIN = "https://apps.apple.com";
  public static final String RTB_CALLBACK_HTTPS_URL = "https://rtbcallback.sigmob.cn";
  public static final String RTB_CALLBACK_HTTP_URL = "http://rtbcallback.sigmob.cn";
  public static final String RTB_CALLBACK_INNER_URL = "http://*************";

  /** 非法IDFA */
  public static final String INVALID_IDFA_VALUE = "00000000-0000-0000-0000-000000000000";

  /** 非法IDFA md5值 */
  public static final String INVALID_IDFA_MD5 = "9f89c84a559f573636a47ff8daed0d33";

  public static final String INVALID_IDFA_SHA1 = "b602d594afd2b0b327e07a06f36ca6a7e42546d0";
  public static final String INVALID_OAID_MD5 = "9f89c84a559f573636a47ff8daed0d33";
  public static final String INVALID_OAID_SHA1 = "b602d594afd2b0b327e07a06f36ca6a7e42546d0";

  /** 非法IMEI */
  public static final String INVALID_IMEI = "000000000000000";

  /** 空字符串md5值 */
  public static final String EMPTY_STRING_MD5 = "d41d8cd98f00b204e9800998ecf8427e";

  public static final String EMPTY_STRING_SHA1 = "da39a3ee5e6b4b0d3255bfef95601890afd80709";

  /** 非法IMEI md5值 */
  public static final String INVALID_IMEI_MD5 = "5284047f4ffb4e04824a2fd1d1f0cd62";

  public static final String INVALID_IMEI_SHA1 = "c02c705e98588f724ca046ac59cafece65501e36";
  public static final String INVALID_MAC_1 = "02:00:00:00:00:00";
  public static final String INVALID_MAC_2 = "02-00-00-00-00-00";
  // 开屏默认的展示时长,单位秒
  public static final int DEFAULT_SPLASH_SHOW_DURATION = 5;

  public static final String iOS = "iOS";
  public static final String ANDROID = "Android";
  public static final String UA_HEADER_PREFIX_MOZILLA = "mozilla";
  public static final String UA_HEADER_PREFIX_DALVIK = "dalvik";
  public static final String MACRO_INFO_KEYS = "KEYS";
  public static final String MACRO_INFO_VALUES = "VALUES";
  public static final String DEFAULT_WX_CANVAS_PATH = "/index";
  public static final String DEFAULT_WX_APP_USERNAME = "default";

  /** oppo流量名称，与中控枚举值同步 */
  public static final String REQUESTFLOW_OPPO_NAME = "oppo";

  /** ios支持deeplink的最小sdk版本 */
  public static final Version MIN_SUPPORT_DEEPLINK_IOS_SDK_VERSION =
      Version.newBuilder().setMajor(2).setMinor(7).build();

  /** 2.7版本以下下载按钮的文字 */
  public static final String OLD_DOWNLOAD_BUTTON_TEXT = "立即下载";

  /** 2.7版本以下网页打开按钮的文字 */
  public static final String OLD_WEB_VIEW_BUTTON_TEXT = "查看详情";

  //  /** android支持deeplink的最小sdk版本 */
  //  public static final Version MIN_SUPPORT_DEEPLINK_ANDROID_SDK_VERSION =
  //      Version.newBuilder().setMajor(2).setMinor(6).build();
  public static final String DEFAULT_INTERSTITIAL_TITLE = "点击体验";
  public static final String LIJICHAKAN_TEXT = "立即查看";
  public static final String DEFAULT_INTERSTITIAL_TITLE_2 = "点击查看详情";
  public static final String DEFAULT_INTERSTITIAL_DESC = "点击立即前往";
  public static final String DEFAULT_DESCRIPTION = "立即前往，查看详情";

  public static final String DEFAULT_TITLE_FOR_STANDARD_DSP = "查看详情";
  public static final String DEFAULT_DESCRIPTION_FOR_STANDARD_DSP = "立即前往";

  /** 默认广告主名称 */
  public static final String DEFAULT_ADVERTISER = "Sigmob";

  /** 最小使用伴随条小按钮的sdk版本 */
  public static final Version COMPANION_SMALL_BTN_MIN_SDK_VERSION =
      Version.newBuilder().setMajor(2).setMinor(7).build();

  public static final Integer MAX_SEND_LOG_COUNT = 100;

  /** 默认的config刷新间隔，单位秒 */
  public static final int DEFAULT_CONFIG_REFRESH_PERIOD = 3600;

  /** 渠道广告有效期，默认3600秒过期 */
  public static final int DEFAULT_SDK_CHANNEL_AD_EXPIRE_TIME = 3600;

  /** 广告位填充统计天数据过期时间：25小时 */
  public static final String EXPIRE_TIME_25H = Integer.toString(25 * 60 * 60);

  /** 广告tracking 重试次数 */
  public static final int DEFAULT_AD_RETRY_COUNT = 3;

  public static final String BEIJING_TIMEZONE_ID = "Asia/Shanghai";
  // 默认的广告过期时间，单位秒
  public static final int DEFAULT_AD_EXPIRATIME = 7200;
  // 默认的手机设备内存大小，当ios请求时，如果不能从配置文件中找到设备型号时，使用此值作为默认值
  public static final long DEFAULT_DEVICE_MEM_SIZE = 17179869184L; // 16Gb
  public static final String NULL_STRING = "null";
  public static final String EMPTY_JSON = "{}";
  public static final String REGION_ALPHA_CODE_CN = "CN";
  public static final byte[] EMPTY_BYTE_ARRAY = new byte[0];
  public static final String DEVICE_ROM_NAME_MIUI = "MIUI";

  /** 小米机型名 */
  public static final String DEVICE_MODEL_XIAOMI = "xiaomi";

  /** oppo品牌 */
  public static final String DEVICE_BRAND_OPPO = "oppo";

  /** vivo品牌 */
  public static final String DEVICE_BRAND_VIVO = "vivo";

  /** 荣耀品牌 */
  public static final String DEVICE_BRAND_HONOR = "honor";

  /** 华为品牌 */
  public static final String DEVICE_BRAND_HUAWEI = "huawei";

  /** 其它 */
  public static final String DEVICE_BRAND_OTHERS = "others";

  public static final String HTTP_ENCODING_GZIP = "gzip";
  public static final String HTTP_ENCODING_ZSTD = "zstd";

  /** 设备id校验正则表达式：只允许数字、英文小写字母、英文大写字母、英文链接符 */
  public static final String DEVICE_ID_VALID_REGEX = "^[a-zA-Z0-9-]+$";

  /** 支持双卡imei读取的sdk版本 */
  public static final Version SUPPORT_IMEI1_SDK_MIN_VERSION =
      Version.newBuilder().setMajor(2).setMinor(10).setMicro(0).build();

  public static final Version VERSION_2_2 =
      Version.newBuilder().setMajor(2).setMinor(2).setVersionStr("2.2").build();
  public static final Version SDK_VERSION_2_4_1 =
      Version.newBuilder().setMajor(2).setMinor(4).setMicro(1).setVersionStr("2.4.1").build();
  public static final Version SDK_VERSION_2_4_2 =
      Version.newBuilder().setMajor(2).setMinor(4).setMicro(2).setVersionStr("2.4.2").build();

  /** sdk version 2.10.0 */
  public static final Version SDK_VERSION_2_9_0 =
      Version.newBuilder().setMajor(2).setMinor(9).setMicro(0).setVersionStr("2.9.0").build();

  public static final Version SDK_VERSION_2_9_3 =
      Version.newBuilder().setMajor(2).setMinor(9).setMicro(3).setVersionStr("2.9.3").build();

  /** sdk version 2.10.0 */
  public static final Version SDK_VERSION_2_10_0 =
      Version.newBuilder().setMajor(2).setMinor(10).setMicro(0).setVersionStr("2.10.0").build();

  public static final Version SDK_VERSION_2_10_1 =
      Version.newBuilder().setMajor(2).setMinor(10).setMicro(1).setVersionStr("2.10.1").build();
  public static final Version SDK_VERSION_2_11_0 =
      Version.newBuilder().setMajor(2).setMinor(11).setMicro(0).setVersionStr("2.11.0").build();

  /** sdk version 2.13.0 */
  public static final Version SDK_VERSION_2_13_0 =
      Version.newBuilder().setMajor(2).setMinor(13).setMicro(0).setVersionStr("2.13.0").build();

  /** sdk version 2.13.1 */
  public static final Version SDK_VERSION_2_13_1 =
      Version.newBuilder().setMajor(2).setMinor(13).setMicro(1).setVersionStr("2.13.1").build();

  /** sdk version 2.14.0 */
  public static final Version SDK_VERSION_2_14_0 =
      Version.newBuilder().setMajor(2).setMinor(14).setMicro(0).setVersionStr("2.14.0").build();

  /** sdk version 2.15.0 */
  public static final Version SDK_VERSION_2_15_0 =
      Version.newBuilder().setMajor(2).setMinor(15).setMicro(0).setVersionStr("2.15.0").build();

  /** sdk version 2.15.1 */
  public static final Version SDK_VERSION_2_15_1 =
      Version.newBuilder().setMajor(2).setMinor(15).setMicro(1).setVersionStr("2.15.1").build();

  /** sdk version 2.15.2 */
  public static final Version SDK_VERSION_2_15_2 =
      Version.newBuilder().setMajor(2).setMinor(15).setMicro(2).setVersionStr("2.15.2").build();

  /** sdk version 2.16.0 */
  public static final Version SDK_VERSION_2_16_0 =
      Version.newBuilder().setMajor(2).setMinor(16).setMicro(0).setVersionStr("2.16.0").build();

  /** sdk version 2.18.0 */
  public static final Version SDK_VERSION_2_18_0 =
      Version.newBuilder().setMajor(2).setMinor(18).setMicro(0).setVersionStr("2.18.0").build();

  /** sdk version 2.19.0 */
  public static final Version SDK_VERSION_2_19_0 =
      Version.newBuilder().setMajor(2).setMinor(19).setMicro(0).setVersionStr("2.19.0").build();

  /** sdk version 2.20.0 */
  public static final Version SDK_VERSION_2_20_0 =
      Version.newBuilder().setMajor(2).setMinor(20).setMicro(0).setVersionStr("2.20.0").build();

  /** sdk version 2.21.0 */
  public static final Version SDK_VERSION_2_21_0 =
      Version.newBuilder().setMajor(2).setMinor(21).setMicro(0).setVersionStr("2.21.0").build();

  /** sdk version 2.22.0 */
  public static final Version SDK_VERSION_2_22_0 =
      Version.newBuilder().setMajor(2).setMinor(22).setMicro(0).setVersionStr("2.22.0").build();

  /** sdk version 2.23.0 */
  public static final Version SDK_VERSION_2_23_0 =
      Version.newBuilder().setMajor(2).setMinor(23).setMicro(0).setVersionStr("2.23.0").build();

  /** sdk version 2.25.0 */
  public static final Version SDK_VERSION_2_25_0 =
      Version.newBuilder().setMajor(2).setMinor(25).setMicro(0).setVersionStr("2.25.0").build();

  /** sdk version 3.0.0 */
  public static final Version SDK_VERSION_3_0_0 =
      Version.newBuilder().setMajor(3).setMinor(0).setMicro(0).setVersionStr("3.0.0").build();

  /** sdk version 3.0.1 */
  public static final Version SDK_VERSION_3_0_10 =
      Version.newBuilder().setMajor(3).setMinor(0).setMicro(10).setVersionStr("3.0.10").build();

  /** sdk version 3.2.0 */
  public static final Version SDK_VERSION_3_2_0 =
      Version.newBuilder().setMajor(3).setMinor(2).setMicro(0).setVersionStr("3.2.0").build();

  /** sdk version 3.4.0 */
  public static final Version SDK_VERSION_3_4_0 =
      Version.newBuilder().setMajor(3).setMinor(4).setMicro(0).setVersionStr("3.4.0").build();

  /** sdk version 3.5.2 */
  public static final Version SDK_VERSION_3_5_2 =
      Version.newBuilder().setMajor(3).setMinor(5).setMicro(2).setVersionStr("3.5.2").build();

  /** sdk version 3.5.5 */
  public static final Version SDK_VERSION_3_5_5 =
      Version.newBuilder().setMajor(3).setMinor(5).setMicro(5).setVersionStr("3.5.5").build();

  /** 支持服务端hb2.0 sdk最小版本 */
  public static final Version SDK_VERSION_4_1_0 =
      Version.newBuilder().setMajor(4).setMinor(1).setMicro(0).setVersionStr("4.1.0").build();

  /** sdk version 4.2.0 */
  public static final Version SDK_VERSION_4_2_0 =
      Version.newBuilder().setMajor(4).setMinor(2).setMicro(0).setVersionStr("4.2.0").build();

  /** sdk version 4.3.0 */
  public static final Version SDK_VERSION_4_3_0 =
      Version.newBuilder().setMajor(4).setMinor(3).setMicro(0).setVersionStr("4.3.0").build();

  /** sdk version 4.4.0 */
  public static final Version SDK_VERSION_4_4_0 =
      Version.newBuilder().setMajor(4).setMinor(4).setMicro(0).setVersionStr("4.4.0").build();

  /** iOS 4.9.0 & Android 4.12.0 插屏 && 同时支持m2 */
  public static final Version SDK_VERSION_4_9_0 =
      Version.newBuilder().setMajor(4).setMinor(9).setMicro(0).setVersionStr("4.9.0").build();

  /** iOS 4.9.4 & Android 4.13.2 rv改版 */
  public static final Version SDK_VERSION_4_9_4 =
      Version.newBuilder().setMajor(4).setMinor(9).setMicro(4).setVersionStr("4.9.4").build();

  /** iOS 4.10.0 & Android 4.14.0 支持新开屏模板 */
  public static final Version SDK_VERSION_4_10_0 =
      Version.newBuilder().setMajor(4).setMinor(10).setMicro(0).setVersionStr("4.10.0").build();

  /** iOS 4.11.0 之后上传mraid版本号 */
  public static final Version SDK_VERSION_4_11_0 =
      Version.newBuilder().setMajor(4).setMinor(11).setMicro(0).setVersionStr("4.11.0").build();

  /** iOS 4.9.0 &Android 4.12.0 插屏 && 同时支持m2 */
  public static final Version SDK_VERSION_4_12_0 =
      Version.newBuilder().setMajor(4).setMinor(12).setMicro(0).setVersionStr("4.12.0").build();

  public static final Version SDK_VERSION_4_12_2 =
      Version.newBuilder().setMajor(4).setMinor(12).setMicro(2).setVersionStr("4.12.2").build();
  public static final Version SDK_VERSION_4_12_4 =
      Version.newBuilder().setMajor(4).setMinor(12).setMicro(4).setVersionStr("4.12.4").build();

  /** 新版互动灵敏度配置后：Android4.17.0、iOS4.13.0档位模式 */
  public static final Version SDK_VERSION_4_13_0 =
      Version.newBuilder().setMajor(4).setMinor(13).setMicro(0).setVersionStr("4.13.0").build();

  public static final Version SDK_VERSION_4_14_0 =
      Version.newBuilder().setMajor(4).setMinor(14).setMicro(0).setVersionStr("4.14.0").build();
  public static final Version SDK_VERSION_4_15_0 =
      Version.newBuilder().setMajor(4).setMinor(15).setMicro(0).setVersionStr("4.15.0").build();
  public static final Version SDK_VERSION_4_15_1 =
      Version.newBuilder().setMajor(4).setMinor(15).setMicro(1).setVersionStr("4.15.1").build();
  public static final Version SDK_VERSION_4_15_2 =
      Version.newBuilder().setMajor(4).setMinor(15).setMicro(2).setVersionStr("4.15.2").build();
  public static final Version SDK_VERSION_4_15_3 =
      Version.newBuilder().setMajor(4).setMinor(15).setMicro(3).setVersionStr("4.15.3").build();
  public static final Version SDK_VERSION_4_15_7 =
      Version.newBuilder().setMajor(4).setMinor(15).setMicro(7).setVersionStr("4.15.7").build();
  public static final Version SDK_VERSION_4_16_0 =
      Version.newBuilder().setMajor(4).setMinor(16).setMicro(0).setVersionStr("4.16.0").build();
  public static final Version SDK_VERSION_4_16_1 =
      Version.newBuilder().setMajor(4).setMinor(16).setMicro(1).setVersionStr("4.16.1").build();
  public static final Version SDK_VERSION_4_16_3 =
      Version.newBuilder().setMajor(4).setMinor(16).setMicro(3).setVersionStr("4.16.3").build();

  public static final Version SDK_VERSION_4_19_5 =
      Version.newBuilder().setMajor(4).setMinor(19).setMicro(5).setVersionStr("4.19.5").build();
  public static final Version SDK_VERSION_4_20_0 =
      Version.newBuilder().setMajor(4).setMinor(20).setMicro(0).setVersionStr("4.20.0").build();
  public static final Version SDK_VERSION_4_21_0 =
      Version.newBuilder().setMajor(4).setMinor(21).setMicro(0).setVersionStr("4.21.0").build();

  /** 新版互动灵敏度配置后：Android4.17.0、iOS4.13.0档位模式 */
  public static final Version SDK_VERSION_4_17_0 =
      Version.newBuilder().setMajor(4).setMinor(17).setMicro(0).setVersionStr("4.17.0").build();

  public static final Version SDK_VERSION_4_18_0 =
      Version.newBuilder().setMajor(4).setMinor(18).setMicro(0).setVersionStr("4.18.0").build();

  public static final Version SDK_VERSION_4_17_1 =
      Version.newBuilder().setMajor(4).setMinor(17).setMicro(1).setVersionStr("4.17.1").build();

  public static final Version SDK_VERSION_4_18_1 =
      Version.newBuilder().setMajor(4).setMinor(18).setMicro(1).setVersionStr("4.18.1").build();;

  public static final Version SDK_VERSION_4_18_2 =
      Version.newBuilder().setMajor(4).setMinor(18).setMicro(2).setVersionStr("4.18.2").build();

  /** 新版互动灵敏度、原生挂件配置(原生)： Android>=4.19.0 || iOS >= 4.15.0 */
  public static final Version SDK_VERSION_4_19_0 =
      Version.newBuilder().setMajor(4).setMinor(19).setMicro(0).setVersionStr("4.19.0").build();

  public static final Version SDK_VERSION_4_19_1 =
      Version.newBuilder().setMajor(4).setMinor(19).setMicro(1).setVersionStr("4.19.1").build();
  public static final Version SDK_VERSION_4_19_2 =
      Version.newBuilder().setMajor(4).setMinor(19).setMicro(2).setVersionStr("4.19.2").build();
  public static final Version SDK_VERSION_4_19_7 =
      Version.newBuilder().setMajor(4).setMinor(19).setMicro(7).setVersionStr("4.19.7").build();

  /** V8.3.2 ADX、直投 对SDK接入，且版本（Android>= 4.22.0;iOS >=4.18.0）开屏 点击类 track 支持 新增的 nc=_ISNC_字段 */
  public static final Version SDK_VERSION_4_22_0 =
      Version.newBuilder().setMajor(4).setMinor(22).setMicro(0).setVersionStr("4.22.0").build();

  public static final Version SDK_VERSION_4_23_0 =
      Version.newBuilder().setMajor(4).setMinor(23).setMicro(0).setVersionStr("4.23.0").build();

  public static final Version SDK_VERSION_4_24_0 =
      Version.newBuilder().setMajor(4).setMinor(24).setMicro(0).setVersionStr("4.24.0").build();

  public static final Version OS_VERSION_6_0_0 =
      Version.newBuilder().setMajor(6).setMinor(0).setMicro(0).setVersionStr("6.0.0").build();
  public static final Version OS_VERSION_8_0_0 =
      Version.newBuilder().setMajor(8).setMinor(0).setMicro(0).setVersionStr("8.0.0").build();
  public static final Version OS_VERSION_9_0_0 =
      Version.newBuilder().setMajor(9).setMinor(0).setMicro(0).setVersionStr("9.0.0").build();
  public static final Version OS_VERSION_10_0_0 =
      Version.newBuilder().setMajor(10).setMinor(0).setMicro(0).setVersionStr("10.0.0").build();
  public static final Version OS_VERSION_13_0_0 =
      Version.newBuilder().setMajor(13).setMinor(0).setMicro(0).setVersionStr("13.0.0").build();
  public static final Version OS_VERSION_15_0_0 =
      Version.newBuilder().setMajor(15).setMinor(0).setMicro(0).setVersionStr("15.0.0").build();
  public static final Version OS_VERSION_17_0_0 =
      Version.newBuilder().setMajor(17).setMinor(0).setMicro(0).setVersionStr("17.0.0").build();

  /** API流量默认sdk版本——3.0.0 */
  public static final Version DEFAULT_API_SDK_VERSION =
      Version.newBuilder().setMajor(3).setMinor(3).setMicro(0).setVersionStr("3.3.0").build();

  /** 运营商-移动代码 */
  public static final String CARRIER_MOBILE_CODE_00 = "46000";

  public static final String CARRIER_MOBILE_CODE_02 = "46002";
  public static final String CARRIER_MOBILE_CODE_07 = "46007";
  public static final String CARRIER_MOBILE_CODE_08 = "46008";

  /** 运营商-中国卫通 */
  public static final String CARRIER_SATCOM_CODE_04 = "46004";

  /** 运营商-联通代码 */
  public static final String CARRIER_UNICOM_CODE_01 = "46001";

  public static final String CARRIER_UNICOM_CODE_06 = "46006";
  public static final String CARRIER_UNICOM_CODE_09 = "46009";

  /** 运营商-电信代码 */
  public static final String CARRIER_TELECOM_CODE_03 = "46003";

  public static final String CARRIER_TELECOM_CODE_05 = "46005";
  public static final String CARRIER_TELECOM_CODE_11 = "46011";

  /** 运营商-铁通代码 */
  public static final String CARRIER_TIETONG_CODE_20 = "46020";

  public static final Mono<Boolean> MONO_TRUE = Mono.just(Boolean.TRUE);
  public static final Mono<Boolean> MONO_FALSE = Mono.just(Boolean.FALSE);
  public static final SigDebugSupportInfo NOT_DEBUG_SUPPORT_INFO =
      new SigDebugSupportInfo(false, 0, 0);
  public static final SigDebugSupportInfo IS_DEBUG_SUPPORT_INFO =
      new SigDebugSupportInfo(true, 0, 0);
  public static final Mono<SigDebugSupportInfo> NOT_DEBUG_SUPPORT_INFO_MONO =
      Mono.just(NOT_DEBUG_SUPPORT_INFO);
  public static final Mono<Integer> MONO_ZERO = Mono.just(0);
  public static final Mono<byte[]> MONO_EMPTY_BYTE_ARRAY = Mono.just(new byte[0]);
  public static final List<Integer> FORBIDDEN_IN_APP_LANDINGPAGE_MEDIA =
      List.of(
          40835, 41492, 44066, 45620, 45677, 45678, 45930, 45931, 46455, 46589, 46737, 47252, 54376,
          54377, 54378);
  public static List<Integer> SPLASH_COMPLIANCE_TEMPLATE = List.of(2106, 2107, 2108);
  public static List<Integer> RV_COMPLIANCE_TEMPLATE = List.of(210112);
  public static DateTimeZone DATE_TIMEZONE = DateTimeZone.forID(BEIJING_TIMEZONE_ID);
}
