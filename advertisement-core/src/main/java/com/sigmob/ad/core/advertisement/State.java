/**
 * 
 */
package com.sigmob.ad.core.advertisement;

/**
 * 
 * <AUTHOR>
 *
 */
public enum State {

	CLOSED(0), OPPENED(1);
	private final int type;

	State(int type) {
		this.type = type;
	}

	public int getTypeNum() {
		return this.type;
	}
	
	public static boolean isValidType(int typeNum) {
		State[] types = values();
		for(State type:types) {
			if(type.getTypeNum() == typeNum) {
				return true;
			}
		}
		return false;
	}
	
	public static State getType(int i) {
		State[] types = values();
		for(State type:types) {
			if(type.getTypeNum() == i) {
				return type;
			}
		}
		return null;
	}
}
