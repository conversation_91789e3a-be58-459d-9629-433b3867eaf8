package com.sigmob.ad.core.security.crypto;

/** <AUTHOR> */
public interface Crypto {

  /**
   * 将明文字符串进行加密
   *
   * @param key 加密的key
   * @param plaintext 明文
   * @return
   */
  String encrypt(String key, String plaintext);

  /**
   * 将明文字符串进行解密
   *
   * @param key 加密的key
   * @param ciphertext 密文
   * @return
   */
  String decrypt(String key, String ciphertext);

  String decrypt(String key, byte[] cipher);

  String encryptWithUrlSafe(String key, String plaintext);

  String encryptWithUrlAndPadding(String key, String plaintext);

  String decryptWithUrlSafe(String key, String encryptText);
}
