/**
 * $Id$
 * Copyright(C) 2010-2016 happyelements.com. All rights reserved.
 */
package com.sigmob.ad.core.exception;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>">kai.huang</a>
 * @version 1.0
 * @since 1.0
 */
public class EncryptException extends SigmobException {

	/**  */
	private static final long serialVersionUID = 3139822381495355925L;

	public EncryptException(String message) {
		super(ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR,message);
	}
	public EncryptException(String message, Throwable cause) {
		super(ErrorCode.REQUEST_ERROR_INVALID_DECRYPT_ERROR, message, cause);
	}

}
