package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 喜马拉雅api常量
 *
 * <AUTHOR>
 * @date 2022/1/11 5:49 PM
 */
public class XimalayaConstants {

  // 默认最大请求时间 - 200毫秒
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {
            AdMacro.CLICK_DOWN_X,
            AdMacro.CLICK_DOWN_Y,
            AdMacro.CLICK_UP_X,
            AdMacro.CLICK_UP_Y,
            AdMacro.TIMEMILLIS
          },
          Constants.MACRO_INFO_VALUES,
          new String[] {
            Macro.DOWN_X.getName(),
            Macro.DOWN_Y.getName(),
            Macro.UP_X.getName(),
            Macro.UP_Y.getName(),
            Macro.TS.getName()
          });

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    UNKNOWN(0),
    PHONE(1),
    PC(2),
    TV(3),
    PAD(4),
    // outdoor（户外设备）目前设备类型只有1，4两种
    OUTDOOR(5);

    final int code;
  }

  /** 运营商 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    MOBILE(0),
    UNICOM(1),
    TELECOM(2),
    OTHER(3);

    final int code;
  }

  /** 网络连接类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    CELL_2G(0),
    CELL_3G(1),
    CELL_4G(2),
    WIFI(3),
    OTHER(4);

    final int code;
  }

  /** 广告点击类型 */
  @AllArgsConstructor
  @Getter
  public enum ClickType {
    // 默认跳转落地页
    WEB_VIEW(0),
    // 纯曝光，不跳转
    ONLY_IMP(1),
    // 唤醒
    DEEP_LINK(2),
    // app下载
    APP_DOWNLOAD(3),
    // 拨打电话
    PHONE(4);

    final int code;
  }

  /** 广告展示样式 */
  @AllArgsConstructor
  @Getter
  public enum Style {

    // 非全屏静态图3秒 —— 开机大图样式
    SPLASH_STYLE_4(4),
//    // 全屏动态GIF4秒 —— 开机大图样式
//    SPLASH_STYLE_5(5),
//    // 非全屏动态GIF4秒 —— 开机大图样式
//    SPLASH_STYLE_6(6),
    // FEED广告 —— 首页大图样式
    FEED_STYLE_7(7),
    // 无声贴片-通栏撒花 —— 声音贴片样式
    FEED_STYLE_26(26),
    // 无声贴片-竖版静态贴片 —— 声音贴片样式
    FEED_STYLE_27(27),
    // 全屏静态图3秒 —— 开机大图样式
    FEED_STYLE_34(34),
    // 有声贴片-竖版视频贴片 —— 声音贴片样式
    FEED_STYLE_77(77),
    //
    FEED_STYLE_2821(2821),
    // 专辑通知原生-FEED广告 —— 专辑通知原生样式
    FEED_STYLE_3207(3207),

    FEED_STYLE_6605(6605),
    // 大图样式 —— 播放页大图样式
    FEED_STYLE_7016(7016),

    FEED_STYLE_7019(7019),
    //
    FEED_STYLE_8001(8001),
    // 视频大图 —— 声音贴片样式
    FEED_STYLE_10011(10011),
    // 无声通栏贴片 —— 声音贴片样式
    FEED_STYLE_10016(10016),
    // 评论广告大图 —— 播放页评论信息流样式
    FEED_STYLE_12343(12343),
    // 评论广告视频 —— 播放页评论信息流样式
    FEED_STYLE_12344(12344),
    // 极速版-竖版贴片图文	—— 喜马拉雅极速版声音贴片样式
    FEED_STYLE_13916(13916),
    // 极速版通用图文	—— 喜马拉雅极速版声音贴片样式
    FEED_STYLE_13946(13946),
    // 极速版-沉浸式皮肤 —— 喜马拉雅极速版声音贴片样式
    FEED_STYLE_13988(13988);
    final int code;
  }

  /** 落地页、点击监测宏 */
  @AllArgsConstructor
  @Getter
  public enum Macro {
    /** 用户手指按下时的横坐标. */
    DOWN_X("__DOWN_X__"),

    /** 用户手指按下时的纵坐标. */
    DOWN_Y("__DOWN_Y__"),

    /** 用户手指离开设备屏幕时的横坐标 */
    UP_X("__UP_X__"),

    /** 用户手指离开设备屏幕时的纵坐标 */
    UP_Y("__UP_Y__"),

    /** 客户端触发时间戳(单位:毫秒) */
    TS("__TS__");

    private final String name;
  }
}
