package com.sigmob.ad.core.advertisement;

/**
 * 视频时长类型 1:不限制 2:播放到指定时间后关闭 3:返回视频时长不能大于指定的时长
 *
 * <AUTHOR>
 */
public enum VideoDurationType {

  /** 不限制 */
  NO_LIMITED(1),
  /** 播放到指定时间后关闭,此参数仅作用于sdk 2.16.0及以上版本 */
  TIME_TO_CLOSE(2),
  /** 返回视频时长不能大于指定的时长 */
  LIMITED(3);
  private final int type;

  VideoDurationType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    VideoDurationType[] types = values();
    for (VideoDurationType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static VideoDurationType getType(int i) {
    VideoDurationType[] types = values();
    for (VideoDurationType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return NO_LIMITED;
  }
}
