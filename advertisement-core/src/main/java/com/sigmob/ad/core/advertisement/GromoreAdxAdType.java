package com.sigmob.ad.core.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GromoreAdxAdType {

  /**
   *
   *
   * <pre>
   * 690 * 286
   * </pre>
   *
   * <code>TOUTIAO_FEED_LP_LARGE = 1;</code>
   */
  TOUTIAO_FEED_LP_LARGE(1),
  /**
   *
   *
   * <pre>
   * 228 * 150
   * </pre>
   *
   * <code>TOUTIAO_FEED_LP_SMALL = 2;</code>
   */
  TOUTIAO_FEED_LP_SMALL(2),
  /**
   *
   *
   * <pre>
   * 690 * 286
   * </pre>
   *
   * <code>TOUTIAO_FEED_APP_LARGE = 4;</code>
   */
  TOUTIAO_FEED_APP_LARGE(4),
  /**
   *
   *
   * <pre>
   * 228 * 150
   * </pre>
   *
   * <code>TOUTIAO_FEED_APP_SMALL = 3;</code>
   */
  TOUTIAO_FEED_APP_SMALL(3),
  /**
   *
   *
   * <pre>
   * 100 * 100 已不支持
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_LP_GRAPHIC = 5;</code>
   */
  TOUTIAO_DETAIL_LP_GRAPHIC(5),
  /**
   *
   *
   * <pre>
   * 690 * 238 已不支持
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_LP_BANNER = 6;</code>
   */
  TOUTIAO_DETAIL_LP_BANNER(6),
  /**
   *
   *
   * <pre>
   * 600 * 400
   * </pre>
   *
   * <code>TUANZI_FEED_APP = 7;</code>
   */
  TUANZI_FEED_APP(7),
  /**
   *
   *
   * <pre>
   * 428 * 238 已不支持
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_APP_BANNER = 9;</code>
   */
  TOUTIAO_DETAIL_APP_BANNER(9),
  /**
   *
   *
   * <pre>
   * 690 * 238
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_LP_TEXT_IMAGE = 10;</code>
   */
  TOUTIAO_DETAIL_LP_TEXT_IMAGE(10),
  /**
   *
   *
   * <pre>
   * 组图 228 * 150
   * </pre>
   *
   * <code>TOUTIAO_FEED_LP_GROUP = 11;</code>
   */
  TOUTIAO_FEED_LP_GROUP(11),
  /**
   *
   *
   * <pre>
   * 视频贴片广告 720p以上。视频比例16:9
   * </pre>
   *
   * <code>TOUTIAO_VIDEO_PATCH = 12;</code>
   */
  TOUTIAO_VIDEO_PATCH(12),
  /**
   *
   *
   * <pre>
   * 开屏广告 1080x1920,1242x1863,1200x1600,1280x1530,1280x755
   * </pre>
   *
   * <code>NETWORK_SPLASH = 13;</code>
   */
  NETWORK_SPLASH(13),
  /**
   *
   *
   * <pre>
   * 头条开屏广告 640*920 640*760 480*640 1242*1786
   * </pre>
   *
   * <code>TOUTIAO_SPLASH = 14;</code>
   */
  TOUTIAO_SPLASH(14),
  /**
   *
   *
   * <pre>
   * 段子推荐流-大图落地页，新样式690*286, 旧样式600*400 废弃
   * </pre>
   *
   * <code>DUANZI_FEED_LP_LARGE = 15;</code>
   */
  DUANZI_FEED_LP_LARGE(15),
  /**
   *
   *
   * <pre>
   * 段子推荐流-小图落地页，228*150
   * </pre>
   *
   * <code>DUANZI_FEED_LP_SMALL = 16;</code>
   */
  DUANZI_FEED_LP_SMALL(16),
  /**
   *
   *
   * <pre>
   * 段子推荐流-组图落地页，228*150,3张且有序
   * </pre>
   *
   * <code>DUANZI_FEED_LP_GROUP = 17;</code>
   */
  DUANZI_FEED_LP_GROUP(17),
  /**
   *
   *
   * <pre>
   * 头条推荐流应用组图
   * </pre>
   *
   * <code>TOUTIAO_FEED_APP_GROUP = 18;</code>
   */
  TOUTIAO_FEED_APP_GROUP(18),
  /**
   *
   *
   * <pre>
   * 头条视频相关推荐小图落地页
   * </pre>
   *
   * <code>TOUTIAO_RECOMMEND_LP_SMALL = 19;</code>
   */
  TOUTIAO_RECOMMEND_LP_SMALL(19),
  /**
   *
   *
   * <pre>
   * 头条信息流落地页视频
   * </pre>
   *
   * <code>TOUTIAO_FEED_LP_VIDEO = 20;</code>
   */
  TOUTIAO_FEED_LP_VIDEO(20),
  /**
   *
   *
   * <pre>
   * 头条信息流应用下载视频
   * </pre>
   *
   * <code>TOUTIAO_FEED_APP_VIDEO = 21;</code>
   */
  TOUTIAO_FEED_APP_VIDEO(21),
  /**
   *
   *
   * <pre>
   * 头条视频相关推荐应用下载小图
   * </pre>
   *
   * <code>TOUTIAO_RECOMMEND_APP_SMALL = 22;</code>
   */
  TOUTIAO_RECOMMEND_APP_SMALL(22),
  /**
   *
   *
   * <pre>
   * 头条图集落地页 全屏 750*1000
   * </pre>
   *
   * <code>TOUTIAO_GALLERY_LP = 23;</code>
   */
  TOUTIAO_GALLERY_LP(23),
  /**
   *
   *
   * <pre>
   * 段子推荐流-视频落地页
   * </pre>
   *
   * <code>DUANZI_FEED_LP_VIDEO = 24;</code>
   */
  DUANZI_FEED_LP_VIDEO(24),
  /**
   *
   *
   * <pre>
   * 段子推荐流-视频应用下载
   * </pre>
   *
   * <code>DUANZI_FEED_APP_VIDEO = 25;</code>
   */
  DUANZI_FEED_APP_VIDEO(25),
  /**
   *
   *
   * <pre>
   * 头条详情页小图落地页 228*150
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_LP_SMALL = 26;</code>
   */
  TOUTIAO_DETAIL_LP_SMALL(26),
  /**
   *
   *
   * <pre>
   * 头条详情页组图落地页 228*150*3
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_LP_GROUP = 27;</code>
   */
  TOUTIAO_DETAIL_LP_GROUP(27),
  /**
   *
   *
   * <pre>
   * 头条详情页应用下载新大图 690*388
   * </pre>
   *
   * <code>TOUTIAO_DETAIL_APP_LARGE = 28;</code>
   */
  TOUTIAO_DETAIL_APP_LARGE(28),
  /**
   *
   *
   * <pre>
   * 西瓜视频开屏广告 640*920 640*760 480*640 1242*1786
   * </pre>
   *
   * <code>VIDEO_SPLASH = 29;</code>
   */
  VIDEO_SPLASH(29),
  /**
   *
   *
   * <pre>
   * 视频后贴片落地页大图 690*388 1280*720
   * </pre>
   *
   * <code>VIDEO_PATCH_LP_LARGE = 30;</code>
   */
  VIDEO_PATCH_LP_LARGE(30),
  /**
   *
   *
   * <pre>
   * 段子开屏 640*920 640*760 480*640 1242*1786
   * </pre>
   *
   * <code>DUANZI_SPLASH = 31;</code>
   */
  DUANZI_SPLASH(31),
  /**
   *
   *
   * <pre>
   * 头条全屏视频开屏广告 640*1136 640*960 480*800 1242*2208
   * </pre>
   *
   * <code>TOUTIAO_SPLASH_VIDEO_FULL = 32;</code>
   */
  TOUTIAO_SPLASH_VIDEO_FULL(32),
  /**
   *
   *
   * <pre>
   * 头条插屏视频开屏广告
   * </pre>
   *
   * <code>TOUTIAO_SPLASH_VIDEO_IMG = 33;</code>
   */
  TOUTIAO_SPLASH_VIDEO_IMG(33),
  /**
   *
   *
   * <pre>
   * 头条信息流落地页微动 690*388
   * </pre>
   *
   * <code>TOUTIAO_FEED_LP_GIF = 34;</code>
   */
  TOUTIAO_FEED_LP_GIF(34),
  /**
   *
   *
   * <pre>
   * 火山信息流落地页竖图 870*540
   * </pre>
   *
   * <code>HOTSOON_FEED_LP_LARGE = 35;</code>
   */
  HOTSOON_FEED_LP_LARGE(35),
  /**
   *
   *
   * <pre>
   * 图集尾帧_大图_落地页 690 * 388
   * </pre>
   *
   * <code>TOUTIAO_GALLERY_LERGE_LP = 36;</code>
   */
  TOUTIAO_GALLERY_LERGE_LP(36),
  /**
   *
   *
   * <pre>
   * 图集尾帧_大图_应用下载 690 * 388
   * </pre>
   *
   * <code>TOUTIAO_GALLERY_LERGE_APP = 37;</code>
   */
  TOUTIAO_GALLERY_LERGE_APP(37),
  /**
   *
   *
   * <pre>
   * 火山信息流_落地页竖版视频
   * </pre>
   *
   * <code>HOTSOON_FEED_LP_VERTICAL_VIDEO = 38;</code>
   */
  HOTSOON_FEED_LP_VERTICAL_VIDEO(38),
  /**
   *
   *
   * <pre>
   * 火山信息流_应用下载竖版视频
   * </pre>
   *
   * <code>HOTSOON_FEED_APP_VERTICAL_VIDEO = 39;</code>
   */
  HOTSOON_FEED_APP_VERTICAL_VIDEO(39),
  /**
   *
   *
   * <pre>
   * 通用开屏gif
   * </pre>
   *
   * <code>COMMON_SPLASH_GIF = 41;</code>
   */
  COMMON_SPLASH_GIF(41),
  /**
   *
   *
   * <pre>
   * 通用开屏图片
   * </pre>
   *
   * <code>COMMON_SPLASH = 42;</code>
   */
  COMMON_SPLASH(42),
  /**
   *
   *
   * <pre>
   * 抖音信息流_落地页竖版视频
   * </pre>
   *
   * <code>AWEME_FEED_LP_VERTICAL_VIDEO = 43;</code>
   */
  AWEME_FEED_LP_VERTICAL_VIDEO(43),
  /**
   *
   *
   * <pre>
   * 抖音信息流_应用下载竖版视频
   * </pre>
   *
   * <code>AWEME_FEED_APP_VERTICAL_VIDEO = 44;</code>
   */
  AWEME_FEED_APP_VERTICAL_VIDEO(44),
  /**
   *
   *
   * <pre>
   * 联盟信息流_落地页_大图
   * </pre>
   *
   * <code>UNION_FEED_LP_LARGE = 45;</code>
   */
  UNION_FEED_LP_LARGE(45),
  /**
   *
   *
   * <pre>
   * 联盟信息流_落地页_小图
   * </pre>
   *
   * <code>UNION_FEED_LP_SMALL = 46;</code>
   */
  UNION_FEED_LP_SMALL(46),
  /**
   *
   *
   * <pre>
   * 联盟信息流_落地页_组图
   * </pre>
   *
   * <code>UNION_FEED_LP_GROUP = 47;</code>
   */
  UNION_FEED_LP_GROUP(47),
  /**
   *
   *
   * <pre>
   * 联盟信息流_应用下载_大图
   * </pre>
   *
   * <code>UNION_FEED_APP_LARGE = 48;</code>
   */
  UNION_FEED_APP_LARGE(48),
  /**
   *
   *
   * <pre>
   * 联盟信息流_应用下载_小图
   * </pre>
   *
   * <code>UNION_FEED_APP_SMALL = 49;</code>
   */
  UNION_FEED_APP_SMALL(49),
  /**
   *
   *
   * <pre>
   * 联盟信息流_应用下载_组图
   * </pre>
   *
   * <code>UNION_FEED_APP_GROUP = 50;</code>
   */
  UNION_FEED_APP_GROUP(50),
  /**
   *
   *
   * <pre>
   * 全屏视频开屏广告 640*1136 640*960 480*800 1242*2208
   * </pre>
   *
   * <code>COMMON_SPLASH_VIDEO_FULL = 51;</code>
   */
  COMMON_SPLASH_VIDEO_FULL(51),
  /**
   *
   *
   * <pre>
   * 详情页落地页卡片 目前有抖音POI详情页
   * </pre>
   *
   * <code>COMMON_TEXTLINK_LP_CARD = 52;</code>
   */
  COMMON_TEXTLINK_LP_CARD(52),
  /**
   *
   *
   * <pre>
   * 联盟开屏_落地页
   * </pre>
   *
   * <code>UNION_SPLASH_LP = 53;</code>
   */
  UNION_SPLASH_LP(53),
  /**
   *
   *
   * <pre>
   * 联盟开屏_应用下载
   * </pre>
   *
   * <code>UNION_SPLASH_APP = 54;</code>
   */
  UNION_SPLASH_APP(54),
  /**
   *
   *
   * <pre>
   * 通用_落地页_大图
   * </pre>
   *
   * <code>COMMON_LP_LARGE = 55;</code>
   */
  COMMON_LP_LARGE(55),
  /**
   *
   *
   * <pre>
   * 通用_落地页_小图
   * </pre>
   *
   * <code>COMMON_LP_SMALL = 56;</code>
   */
  COMMON_LP_SMALL(56),
  /**
   *
   *
   * <pre>
   * 通用_落地页_组图
   * </pre>
   *
   * <code>COMMON_LP_GROUP = 57;</code>
   */
  COMMON_LP_GROUP(57),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_大图
   * </pre>
   *
   * <code>COMMON_APP_LARGE = 58;</code>
   */
  COMMON_APP_LARGE(58),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_小图
   * </pre>
   *
   * <code>COMMON_APP_SMALL = 59;</code>
   */
  COMMON_APP_SMALL(59),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_组图
   * </pre>
   *
   * <code>COMMON_APP_GROUP = 60;</code>
   */
  COMMON_APP_GROUP(60),
  /**
   *
   *
   * <pre>
   * 通用_落地页_横版视频
   * </pre>
   *
   * <code>COMMON_LP_VIDEO = 61;</code>
   */
  COMMON_LP_VIDEO(61),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_横版视频
   * </pre>
   *
   * <code>COMMON_APP_VIDEO = 62;</code>
   */
  COMMON_APP_VIDEO(62),
  /**
   *
   *
   * <pre>
   * 通用_落地页_竖版视频
   * </pre>
   *
   * <code>COMMON_LP_VERTICAL_VIDEO = 63;</code>
   */
  COMMON_LP_VERTICAL_VIDEO(63),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_竖版视频
   * </pre>
   *
   * <code>COMMON_APP_VERTICAL_VIDEO = 64;</code>
   */
  COMMON_APP_VERTICAL_VIDEO(64),
  /**
   *
   *
   * <pre>
   * 西瓜视频后贴片视频落地页 690*388 1280*720
   * </pre>
   *
   * <code>COMMON_LP_PATCH_VIDEO = 65;</code>
   */
  COMMON_LP_PATCH_VIDEO(65),
  /**
   *
   *
   * <pre>
   * 通用_开屏_全屏静图 1242*2208、750*1624、720*1280、1080*1920、1080*2340
   * </pre>
   *
   * <code>COMMON_SPLASH_FULL = 66;</code>
   */
  COMMON_SPLASH_FULL(66),
  /**
   *
   *
   * <pre>
   * 通用_落地页_无图
   * </pre>
   *
   * <code>COMMON_LP_NO_IMAGE = 67;</code>
   */
  COMMON_LP_NO_IMAGE(67),
  /**
   *
   *
   * <pre>
   * 联盟视频_落地页_横版视频
   * </pre>
   *
   * <code>UNION_LP_VIDEO = 68;</code>
   */
  UNION_LP_VIDEO(68),
  /**
   *
   *
   * <pre>
   * 联盟视频_应用下载_横版视频
   * </pre>
   *
   * <code>UNION_APP_VIDEO = 69;</code>
   */
  UNION_APP_VIDEO(69),
  /**
   *
   *
   * <pre>
   * 联盟视频_落地页_竖版视频
   * </pre>
   *
   * <code>UNION_LP_VERTICAL_VIDEO = 70;</code>
   */
  UNION_LP_VERTICAL_VIDEO(70),
  /**
   *
   *
   * <pre>
   * 联盟视频_应用下载_竖版视频
   * </pre>
   *
   * <code>UNION_APP_VERTICAL_VIDEO = 71;</code>
   */
  UNION_APP_VERTICAL_VIDEO(71),
  /**
   *
   *
   * <pre>
   * 通用_落地页_竖版大图
   * </pre>
   *
   * <code>COMMON_LP_VERTICAL_LARGE = 72;</code>
   */
  COMMON_LP_VERTICAL_LARGE(72),
  /**
   *
   *
   * <pre>
   * 通用_应用下载_竖版大图
   * </pre>
   *
   * <code>COMMON_APP_VERTICAL_LARGE = 73;</code>
   */
  COMMON_APP_VERTICAL_LARGE(73),
  /**
   *
   *
   * <pre>
   * 开屏_全屏_静图_应用下载
   * </pre>
   *
   * <code>COMMON_SPLASH_APP_FULL = 74;</code>
   */
  COMMON_SPLASH_APP_FULL(74),
  /**
   *
   *
   * <pre>
   * 通用_焦点图
   * </pre>
   *
   * <code>COMMON_BANNER_FOCUS = 75;</code>
   */
  COMMON_BANNER_FOCUS(75),
  /**
   *
   *
   * <pre>
   * 国际化FEED大图 628*1200
   * </pre>
   *
   * <code>I18N_FEED_LP_LARGE = 201;</code>
   */
  I18N_FEED_LP_LARGE(201),
  /**
   *
   *
   * <pre>
   * 国际化视频
   * </pre>
   *
   * <code>I18N_FEED_VIDEO = 202;</code>
   */
  I18N_FEED_VIDEO(202),
  ;

  private static final GromoreAdxAdType[] VALUES = values();
  private final int value;

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  public static GromoreAdxAdType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static GromoreAdxAdType forNumber(int value) {
    return switch (value) {
      case 1 -> TOUTIAO_FEED_LP_LARGE;
      case 2 -> TOUTIAO_FEED_LP_SMALL;
      case 4 -> TOUTIAO_FEED_APP_LARGE;
      case 3 -> TOUTIAO_FEED_APP_SMALL;
      case 5 -> TOUTIAO_DETAIL_LP_GRAPHIC;
      case 6 -> TOUTIAO_DETAIL_LP_BANNER;
      case 7 -> TUANZI_FEED_APP;
      case 9 -> TOUTIAO_DETAIL_APP_BANNER;
      case 10 -> TOUTIAO_DETAIL_LP_TEXT_IMAGE;
      case 11 -> TOUTIAO_FEED_LP_GROUP;
      case 12 -> TOUTIAO_VIDEO_PATCH;
      case 13 -> NETWORK_SPLASH;
      case 14 -> TOUTIAO_SPLASH;
      case 15 -> DUANZI_FEED_LP_LARGE;
      case 16 -> DUANZI_FEED_LP_SMALL;
      case 17 -> DUANZI_FEED_LP_GROUP;
      case 18 -> TOUTIAO_FEED_APP_GROUP;
      case 19 -> TOUTIAO_RECOMMEND_LP_SMALL;
      case 20 -> TOUTIAO_FEED_LP_VIDEO;
      case 21 -> TOUTIAO_FEED_APP_VIDEO;
      case 22 -> TOUTIAO_RECOMMEND_APP_SMALL;
      case 23 -> TOUTIAO_GALLERY_LP;
      case 24 -> DUANZI_FEED_LP_VIDEO;
      case 25 -> DUANZI_FEED_APP_VIDEO;
      case 26 -> TOUTIAO_DETAIL_LP_SMALL;
      case 27 -> TOUTIAO_DETAIL_LP_GROUP;
      case 28 -> TOUTIAO_DETAIL_APP_LARGE;
      case 29 -> VIDEO_SPLASH;
      case 30 -> VIDEO_PATCH_LP_LARGE;
      case 31 -> DUANZI_SPLASH;
      case 32 -> TOUTIAO_SPLASH_VIDEO_FULL;
      case 33 -> TOUTIAO_SPLASH_VIDEO_IMG;
      case 34 -> TOUTIAO_FEED_LP_GIF;
      case 35 -> HOTSOON_FEED_LP_LARGE;
      case 36 -> TOUTIAO_GALLERY_LERGE_LP;
      case 37 -> TOUTIAO_GALLERY_LERGE_APP;
      case 38 -> HOTSOON_FEED_LP_VERTICAL_VIDEO;
      case 39 -> HOTSOON_FEED_APP_VERTICAL_VIDEO;
      case 41 -> COMMON_SPLASH_GIF;
      case 42 -> COMMON_SPLASH;
      case 43 -> AWEME_FEED_LP_VERTICAL_VIDEO;
      case 44 -> AWEME_FEED_APP_VERTICAL_VIDEO;
      case 45 -> UNION_FEED_LP_LARGE;
      case 46 -> UNION_FEED_LP_SMALL;
      case 47 -> UNION_FEED_LP_GROUP;
      case 48 -> UNION_FEED_APP_LARGE;
      case 49 -> UNION_FEED_APP_SMALL;
      case 50 -> UNION_FEED_APP_GROUP;
      case 51 -> COMMON_SPLASH_VIDEO_FULL;
      case 52 -> COMMON_TEXTLINK_LP_CARD;
      case 53 -> UNION_SPLASH_LP;
      case 54 -> UNION_SPLASH_APP;
      case 55 -> COMMON_LP_LARGE;
      case 56 -> COMMON_LP_SMALL;
      case 57 -> COMMON_LP_GROUP;
      case 58 -> COMMON_APP_LARGE;
      case 59 -> COMMON_APP_SMALL;
      case 60 -> COMMON_APP_GROUP;
      case 61 -> COMMON_LP_VIDEO;
      case 62 -> COMMON_APP_VIDEO;
      case 63 -> COMMON_LP_VERTICAL_VIDEO;
      case 64 -> COMMON_APP_VERTICAL_VIDEO;
      case 65 -> COMMON_LP_PATCH_VIDEO;
      case 66 -> COMMON_SPLASH_FULL;
      case 67 -> COMMON_LP_NO_IMAGE;
      case 68 -> UNION_LP_VIDEO;
      case 69 -> UNION_APP_VIDEO;
      case 70 -> UNION_LP_VERTICAL_VIDEO;
      case 71 -> UNION_APP_VERTICAL_VIDEO;
      case 72 -> COMMON_LP_VERTICAL_LARGE;
      case 73 -> COMMON_APP_VERTICAL_LARGE;
      case 74 -> COMMON_SPLASH_APP_FULL;
      case 75 -> COMMON_BANNER_FOCUS;
      case 201 -> I18N_FEED_LP_LARGE;
      case 202 -> I18N_FEED_VIDEO;
      default -> null;
    };
  }

  public final int getNumber() {
    return value;
  }
}
