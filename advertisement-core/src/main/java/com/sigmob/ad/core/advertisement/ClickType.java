package com.sigmob.ad.core.advertisement;

/**
 * 广告类型枚举类 1、激励视频
 *
 * <AUTHOR>
 */
public enum ClickType {
  BUTTON(1),
  FULL_SCREEN(2);
  private final int type;

  ClickType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    ClickType[] types = values();
    for (ClickType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static ClickType getType(int i) {
    ClickType[] types = values();
    for (ClickType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return FULL_SCREEN;
  }
}
