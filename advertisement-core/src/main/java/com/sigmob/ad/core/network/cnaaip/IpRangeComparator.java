/** */
package com.sigmob.ad.core.network.cnaaip;

import java.util.Comparator;

/**
 * 注意： ==========这个IpRangeCompartor仅能用在binarySearch方法时===========
 *
 * <p>Collections.binarySearch只有当两个进行比较的目标相等时才表示找到了，
 * 这里的IpRangeCompartor与IpRegionCode中的compareTo方法不同，
 * IpRangeCompartor的compare方法是用来检测目标ip地址是否包含在upperIp和lowerIP中，即在地址范围内，而不是简单的比较大小
 *
 * <AUTHOR>
 */
public class IpRangeComparator implements Comparator<IpV4Name> {

  @Override
  /**
   * 在被collections.binarySearch调用时，
   * target为list中的元素，key为要检索的对象（key的lowerIp和upperIp相同，即只表示一个ip）（参见IpRange.getIpRegionCode()方法)
   * 由于target所在的list是按照上界进行自然排序的，因此如果要匹配二分查找算法，那么； 如果target的upperIp小于key的upperIp，需要返回-1，表示要向下进行二分查找
   * 如果target的upperIp大于key的upperIp， 1、需要判断target的lowerIp是否小于key的lowerIp，小于则返回0，表示target包含key所表示的ip
   * 2、如果target的lowerIp大于key的lowerIp，需要返回1，表示要向上进行二分查找
   */
  public int compare(IpV4Name target, IpV4Name key) {
    if (target.getUpperIpNum() < key.getUpperIpNum()) {
      return -1;
    }
    if (target.getUpperIpNum().longValue() == key.getUpperIpNum().longValue()) {
      return 0;
    }
    if (target.getUpperIpNum() > key.getUpperIpNum()) {
      if (target.getLowerIpNum() <= key.getLowerIpNum()) {
        return 0;
      }
    }
    return 1;
  }
}
