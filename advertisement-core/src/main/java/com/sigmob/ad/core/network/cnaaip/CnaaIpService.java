package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.io.File;

/**
 * 广告协会ip地址解析
 *
 * <AUTHOR>
 */
public class CnaaIpService {

  public CnaaIpService() {}

  public void init(File basePath) {
    IpRangeManager.init(basePath);
    RegionManager.init(basePath);
    IpUniversityManager.init(basePath);
    CountryCodeManager.init(basePath);
  }

  public void init(String baseFilePath) {
    IpRangeManager.init(baseFilePath);
    RegionManager.init(baseFilePath);
    IpUniversityManager.init(baseFilePath);
    CountryCodeManager.init(baseFilePath);
  }

  /**
   * @param ip
   * @return
   */
  public String getRegionCodeByIp(String ip) {
    IpName irc = this.getIpName(ip);
    if (irc != null) {
      return irc.getName();
    }
    return null;
  }

  /**
   * @param ip
   * @return
   */
  @Nullable
  public IpName getIpName(String ip) {
    if (!ipCheck(ip)) {
      return null;
    }

    /** 获取ip对应的地区编码 */
    return IpRangeManager.getIpRegionCode(ip);
  }

  /**
   * @param ip
   * @param regionType
   * @return
   */
  public String getRegionNameByIp(String ip, RegionType regionType) {
    IpName irc = this.getIpName(ip);
    if (null == irc) {
      return StringUtils.EMPTY;
    }
    return this.getRegionName(regionType, irc);
  }

  /**
   * @param text
   * @return
   */
  private boolean ipCheck(String text) {
    return !Strings.isNullOrEmpty(text);
  }
  /**
   * 根据ip获取大学名称
   *
   * @param ip
   * @return
   */
  public String getUniversity(String ip) {
    IpV4Name ipName = IpUniversityManager.getIpUniversity(ip);
    if (ipName != null) {
      return ipName.getName();
    }
    return null;
  }

  /**
   * 获取中国地区的省级名称，中文，例如：北京市，河北省 非中国地区的ip返回null
   *
   * @param ip
   * @return
   */
  public String getProvinceName(String ip) {
    return this.getRegionNameByIp(ip, RegionType.province);
  }

  /**
   * 根据ip获取国家英文简写代码
   *
   * @param ip
   * @return
   */
  public String getCountryAlphaCode(String ip) {
    IpName irc = IpRangeManager.getIpRegionCode(ip);
    if (irc == null) {
      return null;
    }
    String regionCode = irc.getName();
    if (Strings.isNullOrEmpty(regionCode) || regionCode.length() < 4) {
      return null;
    }
    // 截取第2到第4位的3位国家数字编码
    String numericCode = regionCode.substring(1, 4);
    return CountryCodeManager.getCountryAlphaCode(numericCode);
  }

  /**
   * @param regionType
   * @param irc
   * @return
   */
  private String getRegionName(RegionType regionType, IpName irc) {
    return RegionManager.getRegionName(regionType, irc.getName());
  }

  /**
   * 国家区代码
   *
   * @param ip
   * @return
   */
  public String getCountryRegionCode(String ip) {
    return this.getRegionCode(RegionType.country, ip);
  }

  public String getProvinceRegionCode(String ip) {
    return this.getRegionCode(RegionType.province, ip);
  }

  public String getRegionCode(RegionType regionType, String ip) {
    IpName irc = this.getIpName(ip);
    if (null == irc) {
      return StringUtils.EMPTY;
    }
    return RegionManager.getRegionCode(regionType, irc);
  }
}
