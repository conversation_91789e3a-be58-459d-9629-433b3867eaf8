package com.sigmob.ad.core.advertisement.model;

import com.google.common.base.Strings;
import com.sigmob.ad.core.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> @Date 2021/5/18 12:34 下午 @Version 1.0 @Description */
@Getter
@Setter
@AllArgsConstructor
public class SigmobCaid {

  private String version;

  private String caid;

  @Override
  public String toString() {
    return Strings.isNullOrEmpty(version)
        ? "0"
        : version + Constants.SYMBOL_UNDERSCORE + Strings.nullToEmpty(caid);
  }
}
