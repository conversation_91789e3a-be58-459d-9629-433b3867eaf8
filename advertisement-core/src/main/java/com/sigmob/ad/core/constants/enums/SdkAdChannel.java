package com.sigmob.ad.core.constants.enums;

import com.google.common.collect.Maps;
import java.util.Map;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum SdkAdChannel {
  /** mintegral */
  MOBVISTA(1, "mintegral"),
  CENTRIXLINK(2, "centrixlink"),
  VUNGLE(4, "vungle"),
  APPLOVIN(5, "applovin"),
  UNITYADS(6, "unityads"),
  IRONSOURCE(7, "ironsource"),
  YOUMI(8, "youmi"),
  SIGMOB(9, "sigmob"),
  ADMOB(11, "admob"),
  TAPJOY(12, "tapjoy"),
  /** 头条-穿山甲 */
  TOUTIAO(13, "csj"),
  NONENONE(14, "nonenone"),
  FACEBOOK(15, "facebook"),
  /** 腾讯广点通、优量汇 */
  TENCENT(16, "gdt"),
  ONEWAY(18, "oneway"),
  KUAISHOU(19, "kuaishou"),
  /** 游可赢, */
  YOUKEYING(20, "klevin"),
  BAIDU(21, "baidu"),
  GRO_MORE(22, "gromore"),
  OPPO(23, "oppo"),
  VIVO(24, "vivo"),
  HUAWEI(25, "huawei"),
  XIAOMI(26, "mimo"),
  AD_SCOPE(27, "adscope"),
  QU_MENG(28, "qumeng"),
  TAP(29, "tapadn"),
  PANGLE(30, "pangle"),
  MAX(31, "max"),
  REKLAMUP(33, "reklamup"),
  OPPO_ADN(34, "oppo_adn"),
  AD_MATE(35, "admate"),
  HONOR(36, "honor"),
  INMOBI(37, "inmobi"),
  VIVO_ADN(38, "vivo_adn"),
  BILLOW(39, "Billowlink"),
  JD(40, "jingdongad"),
  OCTOPUS(41, "Octopus"),
  BAYES(42, "Bayes"),
  HUAWEI_LITE(43, "huawei_lite"),
  MT_BZ(44, "mtbeizi"),
  MT_TO_BID_SAAS_ADX(997, "mt_tbsa"),
  TO_BID_SAAS_ADX(998, "tbsa"),
  TO_BID_ADX(999, "Tobid_adx");

  @Getter private final int id;

  @Getter private final String name;

  private static final Map<Integer, SdkAdChannel> CHANNEL_MAP;

  static {
    CHANNEL_MAP = Maps.newHashMapWithExpectedSize(SdkAdChannel.values().length);
    for (SdkAdChannel channel : SdkAdChannel.values()) {
      CHANNEL_MAP.put(channel.getId(), channel);
    }
  }

  @Nullable
  public static SdkAdChannel valueById(Integer sdkChannelId) {

    return CHANNEL_MAP.get(sdkChannelId);
  }
}
