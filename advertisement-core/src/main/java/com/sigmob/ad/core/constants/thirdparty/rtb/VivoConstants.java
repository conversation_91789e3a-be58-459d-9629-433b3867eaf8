package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2022/9/8 17:13 @Description
 */
public class VivoConstants {

  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  public static final Version DEFAULT_ANDROID_OS_VERSION =
      Version.newBuilder().setMajor(10).setMinor(0).setMicro(0).setVersionStr("10.0.0").build();

  public static final String VIDEO_FORMAT_MP4 = "video/mp4";

  public static final int DP_ADVERTISER_CATEGORY = 220168;

  public static final int DP_ADVERTISER_ID = 3171197;

  public static final int QUICKAPP_ADVERTISER_CATEGORY = 220003;

  public static final int QUICKAPP_ADVERTISER_ID = 3171198;

  public static final int WEBVIEW_ADVERTISER_CATEGORY = 220168;

  public static final int WEBVIEW_ADVERTISER_ID = 3171197;

  /** 文件大小50k */
  public static final int FILE_SIZE_50K = 50 * 1024;

  public static final int FILE_SIZE_100K = 100 * 1024;

  public static final int FILE_SIZE_150K = 150 * 1024;

  /** 文件大小300k */
  public static final int FILE_SIZE_300K = 300 * 1024;

  /** 文件大小10240k */
  public static final int FILE_SIZE_10240K = 10240 * 1024;

  /** 文件大小51200k */
  public static final int FILE_SIZE_51200K = 51200 * 1024;

  /** 信息流相关模版图片最大尺寸 */
  public static final int TEMPLATE_MAX_FEED_IMAGE_FILE_SIZE = 100 * 1024;

  public static final int TEMPLATE_MAX_FEED_VIDEO_FILE_SIZE = 10240 * 1024;

  public static final int TEMPLATE_MAX_FEED_VIDEO_IMAGE_FILE_SIZE = 150 * 1024;

  public static final List<String> SIGMOB_TRACK_MACRO = List.of(AdMacro.TIMESTAMP);

  public static final List<String> VIVO_TRACK_MACRO = List.of(Macro.TIMESTAMP.getName());

  public static final String TRACK_REPORT_TRACK_PARAM_ENCRYPT_KEY = "!!sigmob8#abvivo";

  /** track服务端上报默认缓存一天(86400秒) */
  public static final int TRACK_CACHE_TIME = 86400;

  @Getter
  @AllArgsConstructor
  public enum Nbr {
    // 未知错误
    UNKNOWN_ERROR(0),
    // 技术错误
    TECHNICAL_ERROR(1),
    // 请求不正确
    INVALID_REQUEST(3),
    // 网络爬虫
    WEB_CRAWLER(4),
    // 非正常流量
    ABNORMAL_TRAFFIC(5),
    // 不支持的设备
    UNSUPPORTED_DEVICE(6),
    // 被屏蔽的媒体
    BLOCKED_MEDIA(7),
    // 不匹配的用户
    UNMATCHED_USER(8);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum BidType {
    CPM(0),
    CPC(1);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum AdType {
    FEED(1),
    SPLASH(2),
    BANNER(3),
    INTERSTITIAL(4),
    NATIVE(5),
    REWARDED_VIDEO(9),
    ICON(11);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum MediaType {
    /** 电子书 */
    EBOOK(0),
    /** vivo自有媒体 */
    VIVO_MEDIA(1),
    /** 联盟媒体 2 */
    NETWORK_MEDIA_2(2),
    /** 联盟媒体 3 */
    NETWORK_MEDIA_3(3),
    /** 联盟媒体 4 */
    NETWORK_MEDIA_4(4);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum ConnectionType {
    UNKNOWN(0),

    CELLULAR(1),

    CELL_2G(2),

    CELL_3G(3),

    CELL_4G(4),

    CELL_5G(5),

    WIFI(6);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum InstalledAppStatus {
    // 正常
    NORMAL(1),

    // 不正常
    ABNORMAL(2);

    final int code;
  }

  /** 人群id是否命中 */
  @Getter
  @AllArgsConstructor
  public enum CrowdStatus {
    // 命中
    IN_CROWD(1),
    // 未命中
    NOT_IN_CROWD(2);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum AdStyle {
    // 普通网址类
    WEB(1),
    // 应用下载类
    DOWNLOAD(2),
    // deeplink+普通网址类
    DEEPLINK_AND_WEB(3),
    // deeplink+应用下载类
    DEEPLINK_AND_DOWNLOAD(4),
    // 自定义h5+应用下载
    H5_AND_DOWNLOAD(5),
    // 微信小程序SDK调起
    WECHAT_SDK(11),
    // 游戏下载
    GAME_DOWNLOAD(21),
    /** 快应用 */
    FAST_APP(80);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum Template {
    // 信息流小图
    FEED_SMALL_IMAGE(1001),

    // 信息流组图
    FEED_IMAGES(1002),

    // 信息流大图
    FEED_BIG_IMAGE(1003),

    // 信息流-视频
    FEED_VIDEO(1010),

    // 插屏-插屏图片推荐
    INTERSTITIAL_IMAGE(2001),

    // 插屏-横版插屏视频推荐
    INTERSTITIAL_HORIZONTAL_VIDEO(2004),

    // 插屏-竖版插屏图片推荐
    INTERSTITIAL_VERTICAL_IMAGE(2012),

    // 插屏-横版插屏图片推荐
    INTERSTITIAL_HORIZONTAL_IMAGE(2013),

    // Banner
    BANNER(3001),

    // 广告联盟-Banner
    AD_NETWORK_BANNER(3005),

    BANNER_ICON(3006),

    // vivo视频-信息流大图
    VIVO_VIDEO_FEED_BIG_IMAGE(3014),

    // vivo视频-信息流竖图
    VIVO_VIDEO_FEED_VERTICAL_IMAGE(3015),

    //  vivo视频沉浸式广告
    VIVO_VIDEO_IMMERSIVE(3021),

    // 信息流大图（仅适用物料预审形式）
    FEED_BIG_IMAGE_PRE_AUDIT(3025),

    // 信息流小图（仅适用物料预审形式）
    FEED_SMALL_IMAGE_PRE_AUDIT(3026),

    // vivo视频-信息流大图
    VIVO_VIDEO_FEED_BIG_IMAGE_2(3027),

    // vivo视频-信息流竖图
    VIVO_VIDEO_FEED_VERTICAL_IMAGE_2(3028),

    // 信息流文字链
    FEED_TEXT_LINK(3047),

    // 开屏-竖版大图推荐
    SPLASH_VERTICAL_BIG_IMAGE(4009),

    // 开屏-横版大图推荐
    SPLASH_HORIZONTAL_BIG_IMAGE(4017),

    // 开屏-竖版大图
    SPLASH_VERTICAL_BIG_IMAGE_2(4029),

    // 原生-1:1 ICON
    NATIVE_ICON(5001),

    // 原生-横版大图推荐
    NATIVE_HORIZONTAL_BIG_IMAGE(5002),

    // 原生-4:3 三图
    NATIVE_MULTI_IMAGE(5003),

    // 原生-4:3 小图推荐
    NATIVE_SMALL_IMAGE(5004),

    // 原生-横版视频推荐
    NATIVE_HORIZONTAL_VIDEO(5005),

    // 原生-竖版大图推荐
    NATIVE_VERTICAL_BIG_IMAGE(5009),

    // 原生-竖版视频推荐
    NATIVE_VERTICAL_VIDEO(5015),

    // 锁屏
    LOCK_SCREEN(6001),

    // 激励视频-横版激励视频
    REWARDED_VIDEO_HORIZONTAL(9001),

    // 激励视频-横版激励视频推荐
    REWARDED_VIDEO_HORIZONTAL_2(9002),

    // 激励视频-竖版激励视频
    REWARDED_VIDEO_VERTICAL(9004),

    // 激励视频-竖版激励视频推荐
    REWARDED_VIDEO_VERTICAL_2(9005);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum ResponseStatus {

    /** 参与竞价 */
    PARTICIPATE(0),
    /** 不参与竞价 */
    NOT_PARTICIPATE(1);

    final int code;
  }

  @Getter
  @AllArgsConstructor
  public enum Macro {
    // 结算价格，价格数据比较重要，所以该数据会经过加密，价格加解密算法如下
    WIN_PRICE("${WIN_PRICE}"),

    // BidRequest .id
    AUCTION_ID("${AUCTION_ID}"),

    // BidResponse.bidId
    AUCTION_BID_ID("${AUCTION_BID_ID}"),

    // BidRequest.Impression.id
    AUCTION_IMP_ID("${AUCTION_IMP_ID}"),

    // Bid.creativeId
    AUCTION_AD_ID("${AUCTION_AD_ID}"),

    IMEI("${IMEI}"),

    IP("${IP}"),

    // 客户端操作系统 ，0：Android、1：IOS、 2 : WP
    OS("${OS}"),

    // DSP自定义的extend data ,vivo AdExchange会对其内容进行URL安全的base64编码
    EXT_DATA("${EXT_DATA}"),

    // Deeplink吊起状态，0-成功，1-失败
    DP_RESULT("__DP_RESULT__"),

    // 视频开始播放监测地址和视频播放结束监测地址宏，时间戳
    TIMESTAMP("__TS__"),

    // 视频开始播放监测地址宏，播放行为（0：无法识别、1：自动播放、2：点击播放）
    IF_AUTO("__IFAUTO__"),

    // 视频播放结束监测地址宏，播放结束类型（1：手动结束、2：自动结束）
    END_TYPE("__END_TYPE__"),

    // 参竞前被过滤错误码
    LOSE_CODE("__LOSS_CODE__");

    final String name;
  }
}
