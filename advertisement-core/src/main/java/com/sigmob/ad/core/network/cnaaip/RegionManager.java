/** */
package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Charsets;
import com.google.common.io.ByteSource;
import com.google.common.io.Files;
import com.google.common.io.LineProcessor;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystemNotFoundException;

/**
 * 初始化region code和region name的映射关系 注意： 直辖市由于属于省级单位，为了统计方便，在市级单位中单独做了处理， 参见常量MUNICIPALITY的定义
 *
 * <AUTHOR>
 */
public class RegionManager {

  private static RegionManager instance = new RegionManager();

  private boolean initialized = false;
  private Region regions;

  public static void init(File basePath) {
    synchronized (RegionManager.class) {
      if (instance.initialized) {
        return;
      }

      File cityFile = new File(basePath, Constants.COUNTY_REGION_FILE);
      if (!cityFile.exists()) {
        throw new FileSystemNotFoundException("can not find file: " + Constants.COUNTY_REGION_FILE);
      }

      ReadRegion rr = new ReadRegion();
      try {
        Files.readLines(cityFile, Charsets.UTF_8, rr);
        instance.regions = rr.getResult();
        initMunicipalities();
        instance.initialized = true;
      } catch (IOException e) {
        throw new RuntimeException("can not initiallize cnaa_city!", e);
      }
    }
  }

  public static void init(String basePath) {
    synchronized (RegionManager.class) {
      if (instance.initialized) {
        return;
      }

      String filePath = basePath + File.separator + Constants.COUNTY_REGION_FILE;
      ClassPathResource resource = new ClassPathResource(filePath);
      if (!resource.exists()) {
        throw new FileSystemNotFoundException("can not find file: " + Constants.COUNTY_REGION_FILE);
      }

      ReadRegion rr = new ReadRegion();
      try (InputStream inputStream = resource.getInputStream()) {
        ByteSource byteSource =
            new ByteSource() {
              @Override
              public InputStream openStream() {
                return inputStream;
              }
            };
        byteSource.asCharSource(Charsets.UTF_8).readLines(rr);
        instance.regions = rr.getResult();
        initMunicipalities();
        instance.initialized = true;
      } catch (IOException e) {
        throw new RuntimeException("can not initiallize cnaa_city!", e);
      }
    }
  }

  /** 将直辖市初始化到市级对象中。 说明： 广告协会的数据中，直辖市由于属于省级单位， 为了统计方便，需要将直辖市单独添加到市级单位 */
  private static void initMunicipalities() {
    for (String[] municipality : Constants.MUNICIPALITIES) {
      instance.regions.addRegion(RegionType.city, municipality[0], municipality[1]);
    }
  }

  public static String getRegionName(RegionType type, String regionCode) {
    if (!instance.initialized) {
      return null;
    }
    return instance.regions.getRegionName(type, regionCode);
  }

  static class ReadRegion implements LineProcessor<Region> {
    private Region regions = new Region();

    @Override
    public boolean processLine(String line) throws IOException {
      if (line != null) {
        String[] contents = line.trim().split(",");
        if (contents != null && contents.length == 3) {
          RegionType type = Utils.parseRegionTypeByRegionCode(contents[0]);
          regions.addRegion(type, contents[0], contents[1]);
        }
      }
      return true;
    }

    @Override
    public Region getResult() {
      return regions;
    }
  }

  public static String getRegionCode(RegionType regionType, IpName irc) {
    return instance.regions.parseRegionCode(regionType, irc.getName());
  }
}
