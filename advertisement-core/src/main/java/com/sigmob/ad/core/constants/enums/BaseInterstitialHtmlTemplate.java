package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 兜底html模版
 *
 * <AUTHOR> @Date 2023/4/26 14:33 @Description
 */
@Getter
@AllArgsConstructor
public enum BaseInterstitialHtmlTemplate {
  BASE_DSP_TEMPLATE_200003(200003, 1, 0),

  BASE_DSP_TEMPLATE_200004(200004, 1, 0),

  BASE_DSP_TEMPLATE_200005(200005, 1, 1),

  BASE_DSP_TEMPLATE_200006(200006, 1, 1),

  BASE_DSP_TEMPLATE_200011(200011, 2, 0),

  BASE_DSP_TEMPLATE_200012(200012, 2, 0),

  BASE_DSP_TEMPLATE_200013(200013, 2, 1),

  BASE_DSP_TEMPLATE_200014(200014, 2, 1);

  final int id;

  /** 0-横竖兼容；1-竖屏； 2-横屏 */
  final int orientation;

  /** 0-全屏；1-半屏 */
  final int showStyle;
}
