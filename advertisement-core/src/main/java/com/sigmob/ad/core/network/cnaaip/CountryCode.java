/**
 * 
 */
package com.sigmob.ad.core.network.cnaaip;

/**
 * 
 * 需要拷贝广告协会的ip文件：
 * 		将文件《区县ip库》重命名为《cnaa_ip.csv》，将文件《市级区县编码表.csv》重命名为《cnaa_county.csv》，
 * 		将《高校IP库.csv》重命名位《cnaa_university.csv》，
 * 		并复制到/目录下(以上这些源文件都在广协下载）
 * 		将iso3361国家编码文件(下载地址：http://www.mohrss.gov.cn/SYrlzyhshbzb/zhuanti/jinbaogongcheng/Jbgcbiaozhunguifan/201112/t20111206_47429.html）中的，
 * 			数字编码和英文简写编码两列提取成并存为《country_iso3361.csv》文件，
 * 将重命名后的文件复制到sdk-http-service/src/main/webapp/WEB-INF目录下
 * <AUTHOR>
 *
 */
public class CountryCode {

	/**
	 * 英文简写代码
	 */
	private String alphaCode;
	
	/**
	 * 3位数字代码，
	 */
	private String numericCode;

	public String getAlphaCode() {
		return alphaCode;
	}

	public void setAlphaCode(String alphaCode) {
		this.alphaCode = alphaCode;
	}

	public String getNumericCode() {
		return numericCode;
	}

	public void setNumericCode(String numericCode) {
		this.numericCode = numericCode;
	}
	
	
}
