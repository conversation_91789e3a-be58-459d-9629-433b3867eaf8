package com.sigmob.ad.core.exception;

/**
 * <AUTHOR> @Date 2022/3/31 4:01 PM @Description
 */
public class DspHttpCodeException extends AdException {

  public DspHttpCodeException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public DspHttpCodeException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public DspHttpCodeException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public DspHttpCodeException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public DspHttpCodeException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public DspHttpCodeException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public DspHttpCodeException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public DspHttpCodeException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public DspHttpCodeException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public DspHttpCodeException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
