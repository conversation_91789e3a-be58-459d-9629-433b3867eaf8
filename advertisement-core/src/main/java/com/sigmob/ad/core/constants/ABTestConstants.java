package com.sigmob.ad.core.constants;

/**
 * <AUTHOR> @Date 2023/8/1 15:34 @Description
 */
public class ABTestConstants {

  public static final String AB_EXPERIMENT_USER_REQ_FILTER = "userReqFilter";

  public static final String AB_EXPERIMENT_SDK_EXPIRED_AD_RELOAD = "sdkExpiredAdReload";

  public static final String EXPIERED_AD_PARAM_CAN_EXPIRE_RELOAD = "canExpireReload";
  public static final String EXPIERED_AD_PARAM_RELOAD_COUNT = "expireReloadCount";
  /**
   * 过期时间阀值字段
   *
   * @since <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135449448"></a>
   */
  public static final String EXPIRE_AD_PARAM_TIME_THRESHOLD_FILED = "expireTimeThreshold";

  /**
   * 过期实验是否为实验组
   *
   * @since <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135449448"></a>
   */
  public static final String EXPIRE_EXPERIMENT_GROUP_FILED = "experimentGroup";
  public static final String EXPIRE_EXPERIMENT_CATEGORY_NAME_1 = "ec1";
  public static final String EXPIRE_EXPERIMENT_CATEGORY_NAME_2 = "ec2";

  public static final String AB_EXPERIMENT_HB_SECOND_BID_REBID = "reBid";
}
