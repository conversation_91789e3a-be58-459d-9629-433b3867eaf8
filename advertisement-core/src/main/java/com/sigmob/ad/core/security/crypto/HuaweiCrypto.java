package com.sigmob.ad.core.security.crypto;

import org.apache.commons.codec.Charsets;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/** <AUTHOR> @Date 2021/10/13 5:34 下午 @Version 1.0 @Description */
public class  HuaweiCrypto implements Crypto {
  @Override
  public String encrypt(String key, String plaintext) {
    return null;
  }

  @Override
  public String decrypt(String key, String ciphertext) {

    byte[] tt = null;
    try {
      tt = Hex.decodeHex(ciphertext.toCharArray());
    } catch (DecoderException e) {
      // 此处请自己增加异常处理逻辑
    }
    if (null != tt) {
      byte[] iv = new byte[16];
      byte[] cipherText = new byte[tt.length - 16];
      System.arraycopy(tt, 0, iv, 0, 16);
      System.arraycopy(tt, 16, cipherText, 0, cipherText.length);
      try {
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        SecretKeySpec skeySpec = new SecretKeySpec(Base64.decodeBase64(key), "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivSpec);
        byte[] result = cipher.doFinal(cipherText);
        if (null != result && result.length > 0) {
          String clearPrice = new String(result, Charsets.UTF_8);
          return clearPrice;
        }
      } catch (NoSuchAlgorithmException
          | NoSuchPaddingException
          | InvalidKeyException
          | InvalidAlgorithmParameterException
          | IllegalBlockSizeException
          | BadPaddingException ex) {
        // 此处请自己增加异常处理逻辑
        return null;
      }
    }
    return null;
  }

  @Override
  public String decrypt(String key, byte[] cipher) {
    return null;
  }

  @Override
  public String encryptWithUrlSafe(String key, String plaintext) {
    return null;
  }

  @Override
  public String encryptWithUrlAndPadding(String key, String plaintext) {
    return null;
  }

  @Override
  public String decryptWithUrlSafe(String key, String encryptText) {
    return null;
  }

  public static void main(String[] args) {
    HuaweiCrypto huaweiCrypto = new HuaweiCrypto();
    System.out.println(
        huaweiCrypto.decrypt(
            "cKUu/Ge5KolOvzCwasy64g==",
            "722620fbeae6201271181f71c20527957d95dabc053872d1199cb5e7cb45d515"));
  }
}
