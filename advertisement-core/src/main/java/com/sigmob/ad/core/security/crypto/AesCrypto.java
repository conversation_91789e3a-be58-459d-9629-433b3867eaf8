package com.sigmob.ad.core.security.crypto;

import com.sigmob.ad.core.util.LogUtil;
import java.util.Base64;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.binary.StringUtils;

/**
 * 加密模式:ECB;<br>
 * 填充:pkcs5padding;<br>
 * 数据块:128 位;<br>
 * 输出:hex;<br>
 * 字符 集:utf8。
 */
public class AesCrypto implements Crypto {

  private static final String SECRET_AES_ECB_MODE = "ECB";
  private static final String SECRET_AES_CBC_MODE = "CBC";

  private static final String SECRET_AES_PKCS5_PADDING = "PKCS5Padding";

  private static final String SECRET_AES_NO_PADDING = "NoPadding";

  @Override
  public String encrypt(String key, String plaintext) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            StringUtils.getBytesUtf8(plaintext));

    return Hex.encodeHexString(encryptBytes, Boolean.FALSE);
  }

  public String encryptToLowerCase(String key, String plaintext) {
    byte[] encryptBytes =
            AesUtils.encrypt(
                    SECRET_AES_ECB_MODE,
                    SECRET_AES_PKCS5_PADDING,
                    StringUtils.getBytesUtf8(key),
                    null,
                    StringUtils.getBytesUtf8(plaintext));

    return Hex.encodeHexString(encryptBytes, Boolean.TRUE);
  }

  @Override
  public String decrypt(String key, String ciphertext) {

    try {
      return decrypt(key, Hex.decodeHex(ciphertext));
    } catch (Exception e) {
      LogUtil.localError("decrypt {} with AES(key:{})", ciphertext, key);
      return null;
    }
  }

  @Override
  public String decrypt(String key, byte[] cipher) {
    return StringUtils.newStringUtf8(
        AesUtils.decrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            cipher));
  }

  @Override
  public String encryptWithUrlSafe(String key, String plaintext) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            StringUtils.getBytesUtf8(plaintext));

    return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptBytes);
  }

  public String encryptNoPaddingWithUrlSafe(String key, String plaintext) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_NO_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            StringUtils.getBytesUtf8(plaintext));

    return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptBytes);
  }

  public String encryptWithUrlSafe(String key, byte[] plaintext) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            plaintext);

    return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptBytes);
  }

  @Override
  public String encryptWithUrlAndPadding(String key, String plaintext) {
    byte[] encryptBytes =
        AesUtils.encrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            StringUtils.getBytesUtf8(plaintext));

    return Base64.getEncoder().encodeToString(encryptBytes);
  }

  @Override
  public String decryptWithUrlSafe(String key, String encryptText) {
    return StringUtils.newStringUtf8(
        AesUtils.decrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_PKCS5_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            Base64.getUrlDecoder().decode(encryptText)));
  }

  public String decryptNoPaddingWithUrlSafe(String key, String encryptText) {
    return StringUtils.newStringUtf8(
        AesUtils.decrypt(
            SECRET_AES_ECB_MODE,
            SECRET_AES_NO_PADDING,
            StringUtils.getBytesUtf8(key),
            null,
            Base64.getUrlDecoder().decode(encryptText)));
  }

  public byte[] decryptBytesWithUrlSafe(String key, String encryptText) {
    return AesUtils.decrypt(
        SECRET_AES_ECB_MODE,
        SECRET_AES_PKCS5_PADDING,
        StringUtils.getBytesUtf8(key),
        null,
        Base64.getUrlDecoder().decode(encryptText));
  }

  public String decryptUnPaddingWithUrlSafe(String key, String encryptText)
      throws DecoderException {
    var keyBytes = StringUtils.getBytesUtf8(key);
    return StringUtils.newStringUtf8(
        AesUtils.decrypt(
            SECRET_AES_CBC_MODE,
            SECRET_AES_PKCS5_PADDING,
            keyBytes,
            keyBytes,
            Hex.decodeHex(encryptText)));
  }
}
