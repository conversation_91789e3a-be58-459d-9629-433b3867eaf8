package com.sigmob.ad.core.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> href="mailto:<EMAIL>">kai.huang</a>
 * @version 1.0
 * @since 1.0
 */
public class ErrorCode {

  /** 请求成功且有广告 */
  public static final int RTB_SUCCESS = 0;

  public static final String RTB_SUCCESS_MSG = "ok";

  // dsp无广告填充
  public static final int RTB_SIG_DSP_NO_ADS_ERROR = 200000;

  public static final String RTB_SIG_DSP_NO_ADS_ERROR_MSG = "no ad";

  // Sigmob Dsp控量导致无广告
  public static final int RTB_SIG_DSP_TRAFFIC_CONTROL_ERROR = 201028;

  /** 广告响应的所有广告都被错误过滤导致没有下发 */
  public static final int RTB_DSP_AD_ALL_FILTER = 500001;

  /** 通用错误类型 */
  public static final int NORMAL_ERROR = 500400;

  /** 请求超过设置的总时间 */
  public static final int REQUEST_TOTAL_TIME_OUT = 500401;

  // 该地区无服务
  public static final int NO_AD_SERVICE = 500402;
  // 错误的json格式
  public static final int INVALID_JSON_DATA = 500403;

  /** 未找到聚合场景 */
  public static final int REQUEST_MEDIATION_SCENE_NOT_FOUND = 500404;

  /** 聚合场景关闭 */
  public static final int REQUEST_MEDIATION_SCENE_CLOSED = 500405;

  /** 未找到聚合渠道 */
  public static final int REQUEST_MEDIATION_NO_CHANNELS = 500406;

  /** 无效的聚合广告位 */
  public static final int REQUEST_MEDIATION_INVALID_ELEMENT = 500407;

  /** 该广告位没有配置策略 */
  public static final int REQUEST_MEDIATION_NO_STRATEGY = 500408;

  /** 聚合应用未找到 */
  public static final int REQUEST_MEDIATION_APP_NOT_FOUND = 500409;

  /** 该广告位聚合瀑布流配置为空 */
  public static final int REQUEST_MEDIATION_NO_WATERFALL = 500410;

  /** 配置场景广告类型与请求中场景广告类型不一致 */
  public static final int REQUEST_SCENE_AD_TYPE_NOT_MATCH_REQUEST_AD_SLOT_TYPE = 500411;

  /** 聚合请求中场景应用id与请求中应用id不一致 */
  public static final int REQUEST_SCENE_APPID_NOT_MATCH = 500412;

  // 请求的app已经关闭广告服务
  public static final int REQUEST_APP_IS_CLOSED = 500420;
  // 请求参数缺少媒体app信息
  public static final int REQUEST_ERROR_NEED_MEDIA_INFO = 500421;
  // 请求参数缺少设备信息
  public static final int REQUEST_ERROR_NEED_DEVICE_INFO = 500422;
  // 请求参数缺少客户端网络信息
  public static final int REQUEST_ERROR_NEED_NETWORK_INFO = 500423;
  // 缺少设备id相关信息
  public static final int REQUEST_ERROR_NEED_DEVICE_ID_INFO = 500424;
  // 缺少包名
  public static final int REQUEST_ERROR_NEED_PACKAGE_NAME = 500425;
  // 缺少制造商
  public static final int REQUEST_ERROR_NEED_VENDOR = 500426;
  // 缺少设备型号
  public static final int REQUEST_ERROR_NEED_MODEL = 500427;
  // 缺少广告为信息
  public static final int REQUEST_ERROR_NEED_AD_SLOTS_INFO = 500428;
  // 缺少广告位类型信息
  public static final int REQUEST_NEED_AD_SLOT_TYPE = 500429;
  // 错误的广告位应用内竞价类型
  public static final int INVALID_ADSLOT_BID_TYPE = 500430;
  // 缺少客户端支持的素材类型
  public static final int REQUEST_NEED_MATERIAL_TYPE = 500431;
  // 广告位不存在，或者appid与广告位不匹配
  public static final int REQUEST_AD_SLOT_NOT_EXISTS = 500432;
  // 广告位不存在或是已关闭
  public static final int REQUEST_AD_SLOT_IS_CLOSED = 500433;
  // 缺少客户端ip地址
  public static final int REQUEST_ERROR_CLIENT_IP = 500434;
  // 设备的操作系统类型，与请求的app的系统类型不匹配，例如，iOS设备使用了安卓的appid发起广告请求（1.9版本实现）
  public static final int REQUEST_OS_TYPE_NOT_MATCH_APP_TYPE = 500435;
  // 广告单元id与请求的广告类型不匹配（1.9版本实现）
  public static final int REQUEST_AD_SLOT_NOT_MATCH_AD_TYPE = 500436;
  // 缺少idfa，仅（ios）
  public static final int REQUEST_ERROR_NEED_IDFA = 500437;
  // 不支持的操作系统类型
  public static final int NOT_SUPPORT_OS_TYPE = 500438;
  // 黑名单用户
  public static final int BLACK_LIST_USER = 500439;
  // 运营后台关闭了app，使之不能访问自有广告接口（聚合策略仍然可用）
  public static final int REQUEST_APP_CLOSED_BY_OPERATION = 500440;
  // 运营后台关闭了广告单元，使之不能访问自有广告接口（聚合策略仍然可用）
  public static final int REQUEST_AD_SLOT_CLOSED_BY_OPERATION = 500441;

  /** 请求媒体包名与中控配置媒体包名不一致 */
  public static final int REQUEST_BUNDLE_PACKAGE_NAME_NOT_MATCH = 500442;

  /** 请求地域被禁止请求广告 */
  public static final int REQUEST_REGION_BANNED = 500443;

  /** 错误的广告位开发者竞价类型 */
  public static final int REQUEST_DEVELOPER_BID_TYPE_ERROR = 500444;

  // 不是聚合广告位ID，而是使用Sigmob的广告单元当作聚合广告位请求导致的错
  public static final int REQUEST_NOT_SCENE_ID_AND_IS_PLACEMENT_ID_ERROR = 500445;

  /** 广告类型与请求设备的操作系统类型不匹配 */
  public static final int REQUEST_OS_TYPE_NOT_MATCH_AD_TYPE = 500446;

  /** 请求设备机型与品牌不匹配 */
  public static final int REQUEST_DEVICE_MODEL_BRAND_NOT_MATCH = 500447;

  /** 当前瀑布流仅包含竞价广告源，且无填充。 */
  public static final int WATER_FALL_IS_ONLY_HB_AND_NO_RESPONSE = 500448;

  /** */
  public static final int REQUEST_TAIL_TRAFFIC_DEVICE_REQ_DISCARDED = 500449;

  /** windmill app状态关闭 */
  public static final int WINDMILL_APP_CLOSE = 500450;

  /** 广告请求时使用了聚合场景id */
  public static final int REQUEST_SLOT_ID_IS_SCENE_ID = 500451;

  /** sdk版本过低不支持插屏广告类型 */
  public static final int REQUEST_SDK_VERSION_NOT_SUPPORT_INTERSTITIAL = 500452;

  /** dsp渠道不支持素材尺寸要求 */
  //  public static final int REQUEST_ERROR_MATERIAL_SIZE_NOT_SUPPORT = 500453;

  /** 缺少地理信息（经纬度、语言、时区、地区代码等） */
  public static final int REQUEST_ERROR_NEED_GEO = 500454;

  /** 设备制造商或品牌商不匹配 */
  public static final int REQUEST_ERROR_DEVICE_BRAND_NOT_MATCH = 500455;

  //  public static final int REQUEST_OS_NOT_SUPPORT_AD_CREATIVE_TYPE = 500456;

  /** 请求没有支持的创意类型 */
  public static final int REQUEST_NO_CREATIVE_TYPE = 500457;

  public static final int REQUEST_OS_VERSION_NOT_SUPPORT_M2 = 500458;

  // 过滤用户请求sigmob广告
  public static final int REQUEST_USER_AD_REQ_FILTER = 500459;

  /** 操作系统版本格式错误 */
  public static final int REQUEST_OS_VERSION_FORMAT_ERROR = 500460;

  /** dsp三方广告位qps过滤 */
  public static final int REQUEST_DSP_ADSLOT_QPS_FILTER = 500461;

  /** dsp媒体请求分流过滤 */
  public static final int REQUEST_DSP_APP_REQUEST_FILTER = 500462;

  /** sigmob广告位不支持任何广告动作类型 */
  public static final int REQUEST_ADSLOT_NO_SUPPORT_INTERACTION = 500463;

  /** Dsp广告位 流量开关关闭 */
  public static final int REQUEST_DSP_AD_SLOT_TRAFFIC_OFF = 500464;

  /** 设备品牌不允许请求dsp */
  public static final int REQUEST_DEVICE_BRAND_NOT_ALLOW_REQUEST_DSP = 500465;

  /** 设备品牌不允许请求dsp广告位 */
  public static final int REQUEST_DEVICE_BRAND_NOT_ALLOW_REQUEST_DSP_ADSLOT = 500466;

  /** 请求设备存在不合法的设备标识符 */
  public static final int REQUEST_DEVICE_ID_INVALID = 500467;

  /** 广告包名被媒体频控 */
  public static final int RESPONSE_AD_PACKAGE_FREQUENCY_CONTROL = 500468;

  // TODO: 新的错误码可以在这里加

  // 请求缺少必要参数
  public static final int REQUEST_ERROR_NEED_PARAM = 500470;
  // 参数不能为空值
  public static final int REQUEST_ERROR_EMPTY_VALUE = 500471;
  // 值非法
  public static final int REQUEST_ERROR_INVALID_VALUE = 500472;

  // 请求的app不存在
  public static final int REQUEST_ERROR_NO_SUCH_APP = 500473;
  // 错误的appkey
  public static final int REQUEST_ERROR_INVALID_APPID_APPKEY = 500474;
  // 加密错误
  public static final int REQUEST_ERROR_INVALID_DECRYPT_ERROR = 500475;
  // 错误的appId
  public static final int REQUEST_ERROR_INVALID_APP_ID = 500476;

  // 错误的广告类型
  public static final int REQUEST_ERROR_INVALID_AD_TYPE = 500477;
  // 地板价错误
  public static final int REQUEST_ERROR_INVALID_FlOOR_PRICE = 500478;
  // 广告位id为空
  public static final int REQUEST_ERROR_AD_SLOT_ID_IS_EMPTY = 500479;

  //  /** 当前提交的信息已过期，不能修改。可能是因为app已经被其他进程修改过或是已删除 */
  //  public static final int REQUEST_ERROR_APP_HAS_CHANGED = 500475;
  //
  //  /** 当前提交的信息已过期，不能修改。可能是因为渠道已经被其他进程修改过或是已删除 */
  //  public static final int REQUEST_ERROR_CHANNEL_HAS_CHANGED = 500476;
  //
  //  /** 当前提交的信息已过期，不能修改。可能是因为策略已经被其他进程修改过或是已删除 */
  //  public static final int REQUEST_ERROR_STRATEGY_HAS_CHANGED = 500477;
  //
  //  public static final int REQUEST_ERROR_APPSETTING_HAS_CHANGED = 500478;
  //
  //  public static final int REQUEST_ERROR_USER_WHITE_LIST_HAS_CHANGED = 500478;
  //
  //  public static final int REQUEST_ERROR_UWL_HAS_CHANGED = 500479;
  // 错误的Json格式
  public static final int REQUEST_ERROR_NOT_VALID_JSON = 500480;
  // 设备缺少uid
  public static final int REQUEST_ERROR_NEED_UID = 500481;

  /** invalid uid(系统生成的uid) */
  public static final int REQUEST_ERROR_INVALID_UID = 500482;

  /** 暂不为该地区提供服务 */
  public static final int NOT_SUPPORT_AREA = 500483;

  /** 广告位被控量，不能获取请求 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_AD_CONTROL = 500484;

  /** 设备请求广告位广告频次受限 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_DEVICE_FREQUENCY = 500485;

  /** 无法为设备生成uid */
  public static final int REQUEST_ERROR_GENERATE_UID = 500486;

  /** 设备请求广告被三方广告位请求频次限制 */
  public static final int DEVICE_REQUEST_FORBIDDEN_BY_FLOW_CONTROL = 500487;

  /** 广告位直投广告被控量，不能获取请求 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_AD_CONTROL_SIGDSP = 500488;

  /** 广告位三方广告被控量，不能获取请求 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_AD_CONTROL_API = 500489;

  /** 设备在广告位请求直投广告频次受限 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_DEVICE_FREQUENCY_SIGDSP = 500490;

  /** 设备在广告位请求三方广告频次受限 */
  public static final int AD_SLOT_REQUEST_FORBIDDEN_BY_DEVICE_FREQUENCY_API = 500491;

  /** 广告位广告价格配置缺失 */
  public static final int AD_SLOT_REQUEST_PRICE_MISSING = 500492;

  /** DSP关闭个性化推荐 */
  public static final int DSP_RECOMMENDATION_FORBIDDEN = 500493;

  /** 设备广告请求被丢弃 */
  public static final int DEVICE_REQUEST_DISCARDED = 500494;

  /** 设备广告请求次数达到限制 */
  public static final int DEVICE_REQUEST_FREQUENCY_LIMIT = 500495;

  /** 网盟渠道出价低于max(媒体期望底价，请求中底价) */
  public static final int AD_SLOT_NETWORK_FLOOR_FILTER = 500496;

  /** 广告请求被dsp过滤 */
  public static final int AD_REQUEST_BLOCKED_BY_DSP = 500497;

  /** sdk替换坐标宏参数有问题，不请求程序化渠道 */
  public static final int SDK_BUG_MACRO_COORDINATE_ERROR = 500498;

  /** 广告位流量放弃请求dsp */
  public static final int AD_SLOT_REQ_DSP_DISCARDED = 500499;

  /** 广告位属性不支持请求dsp渠道 */
  public static final int AD_SLOT_SELL_TYPE_NOT_SUPPORT_DSP = 500500;

  /** 设备操作系统类型不支持请求dsp */
  public static final int DEVICE_OS_TYPE_NOT_SUPPORT_DSP = 500501;

  // public static final int STRATEGY_ERROR_NO_STRATEGY_FOR_USER = 500-2000;
  //

  // 网络错误
  public static final int NETWORK_ERROR = 500550;
  // 持久化错误
  public static final int SERVER_ERROR_PERSISTENCE = 500551;
  // 用户白名单过长
  public static final int SERVER_ERROR_USER_WHITE_LIST_TOO_MUCH = 500552;
  // 初始化ip库失败
  public static final int CAN_NOT_INIT_IP_LOOKUP_SERVICE = 500553;
  // ip库未初始化，请先初始化
  public static final int IP_LOOKUP_SERVICE_HAS_NO_INITIALIZED = 500554;

  public static final int AD_SLOT_REQ_DSP_MATERIAL_NOT_SUPPORT = 500555;

  /** 媒体包名不允许请求dsp广告位 */
  public static final int APP_REQUEST_DSP_ADSLOT_FORBIDDEN = 500556;

  /** 返回原生广告格式错误 */
  public static final int RTB_SIG_DSP_NATIVE_AD_WRONG_FORMAT = 500593;

  /** dsp出价低于rtb dsp渠道请求底价 */
  public static final int RTB_SIG_DSP_PRICE_LOWER_THAN_DSP_EXPECTED = 500594;

  /** 后端广告主的target_url域名黑名单 */
  public static final int RTB_SIG_DSP_RESPONSE_TARGET_URL_DOMAIN_INVALID = 500595;

  /** 唤醒类广告未安装过滤 */
  public static final int RTB_SIG_DSP_RESPONSE_DEEPLINK_UNINSTALL = 500596;

  /** 广告缺少image url */
  public static final int RTB_SIG_DSP_RESPONSE_NEED_IMAGE_URL = 500597;

  /** ssp请求adx熔断 */
  public static final int RTB_SSP_ADX_GRPC_CIRCUIT_BREAKER = 500598;

  /** ssp请求adx超时 */
  public static final int RTB_SSP_ADX_GRPC_TIMEOUT = 500599;

  /** 通用Dsp请求错误 */
  public static final int RTB_SIG_DSP_ERROR = 500600;

  /** Adx通过Grpc请求Sigmob Dsp错误 */
  public static final int RTB_SIG_DSP_GRPC_ERROR = 500601;

  /** Ssp通过Grpc请求Adx错误 */
  public static final int RTB_SSP_ADX_GRPC_ERROR = 500602;

  /** 广告无物料信息 */
  public static final int RTB_SIG_DSP_NO_CAMP = 500603;

  /** 广告价格信息出错,返回广告的出价低于底价 */
  public static final int RTB_SIG_DSP_PRICE_ERROR = 500604;

  // Dsp响应参数错误
  public static final int RTB_SIG_DSP_RESPONSE_PARAM_ERROR = 500605;
  // PB格式数据错误
  public static final int SERVER_ERROR_PB_ERROR = 500607;
  // 广告缺少endcard url
  public static final int RTB_SIG_DSP_RESPONSE_NEED_ENDCARD_URL = 500608;

  /** 广告缺少video url */
  public static final int RTB_SIG_DSP_RESPONSE_NEED_VIDEO_URL = 500609;

  // 广告价格加密错误
  public static final int RTB_SIG_DSP_PRICE_ENCRYPT_ERROR = 500610;

  /** Ssp内部错误（未分类） */
  public static final int SSP_ERROR = 500611;

  public static final String SSP_ERROR_MSG = "internal server error";

  /** 广告包名与请求包名一致，广告被过滤 */
  public static final int RTB_SIG_DSP_PACKAGE_NAME_FILTER = 500612;

  /** 广告包大小超过设备剩余磁盘大小，广告被过滤 */
  public static final int RTB_SIG_DSP_PACKAGE_SIZE_FILTER = 500613;

  /** 广告包设备已安装或已激活过，广告被过滤 */
  public static final int RTB_SIG_DSP_PACKAGE_INSTALLED_FILTER = 500614;

  /** 返回广告的出价低于媒体期望底价 */
  public static final int RTB_SIG_DSP_PRICE_LOWER_THAN_EXPECTED = 500615;

  /** 返回广告链接包含设备不支持的http链接 */
  public static final int RTB_SIG_DSP_AD_HTTP_NOT_SUPPORT = 500616;

  /** 返回广告创意类型不被请求方支持而被过滤 */
  public static final int RTB_SIG_DSP_AD_CREATIVE_TYPE_FILTER = 500617;

  /** 流量不支持DSP返回广告的动作类型 */
  public static final int TRAFFIC_NOT_SUPPORT_DSP_ACTION = 500618;

  /** 流量不支持DSP返回广告的动作类型 */
  public static final int TRAFFIC_NOT_SUPPORT_DSP_ACTION_V2 = 510618;

  /** 返回广告视频时长不满足最小要求 */
  public static final int VIDEO_DURATION_INSUFFICIENT = 500619;

  /** 返回广告物料中图片尺寸不合适 */
  public static final int MATERIAL_IMAGE_SIZE_FILTER = 500620;

  /** 中控过滤下载广告包名配置生效 */
  public static final int RTB_SIG_DSP_AD_BUNDLE_FILTER = 500621;

  /** 中控过滤广告关键字配置生效 */
  public static final int RTB_SIG_DSP_AD_KEYWORD_FILTER = 500622;

  /** 广告物料中控未审核通过 */
  public static final int RTB_SIG_DSP_AD_CHECK_FILTER = 500623;

  /** 视频广告模版协议不支持 */
  public static final int RTB_AD_TEMPLATE_NOT_SUPPORTED = 500624;

  /** 因为广告填充原因不请求dsp */
  public static final int DSP_REQUEST_FORBIDDEN_BY_AD_FILL = 500625;

  /** dsp请求广告条件不满足 */
  public static final int DSP_REQUEST_CONDITION_NOT_SATISFIED = 500626;

  /** dsp返回的vast视频广告解析失败 */
  public static final int DSP_API_RESPONSE_VAST_RESOLVED_ERROR = 500627;

  /** dsp返回的原生广告没有对应的请求模版 */
  public static final int DSP_API_RESPONSE_NATIVE_TEMPLATE_NOT_MATCH = 500628;

  /** 流量不支持下载类广告 */
  public static final int RTB_SIG_DSP_AD_DISABLE_DOWNLOAD_FILTER = 500629;

  /** 流量不支持deeplink类广告 */
  public static final int RTB_SIG_DSP_AD_DISABLE_DEEPLINK_FILTER = 500630;

  /** dsp曝光达到限制被控量 */
  public static final int DSP_IMPRESSION_FORBIDDEN_AD_CONTROL = 500631;

  /** SDK流量deal不支持的标准api返回广告素材类型 */
  public static final int DSP_DEAL_CREATIVE_TYPE_FILTER = 500632;

  /** 交易不支持的attr素材类型 @See http://wiki.sigmob.cn/pages/viewpage.action?pageId=40077112 */
  public static final int DSP_API_RESPONSE_ATTR_NOT_SUPPORT = 500633;

  /** DSP请求广告不符合机型品牌定向 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_BRAND_TARGET = 500634;

  /** DSP返回广告attr类型不支持 */
  public static final int DSP_API_INVALID_RESPONSE_ATTR = 500635;

  /** DSP返回广告attr类型与广告请求不匹配 */
  public static final int DSP_API_RESPONSE_ATTR_NOT_MATCH_REQUEST = 500636;

  /** DSP返回广告内容与attr类型不匹配 */
  public static final int DSP_API_INVALID_CONTENT_ATTR = 500637;

  /** 未安装淘宝应用 */
  public static final int DSP_CONFIG_TAOBAO_NOT_INSTALLED = 500638;

  /** 缺少淘宝直播配置 */
  public static final int DSP_CONFIG_NO_TAOBAO_LIVE = 500639;

  /** 淘宝直播配置缺少必要配置项 */
  public static final int DSP_CONFIG_TAOBAO_LIVE_NO_DETAIL = 500640;

  /** dsp不支持的操作系统定向 */
  public static final int DSP_TRAFFIC_FILTER_OS = 500641;

  /** dsp不支持的广告形式定向 */
  public static final int DSP_TRAFFIC_FILTER_ADTYPE = 500642;

  /** dsp根据广告为媒体广告位过滤条件过滤 */
  public static final int DSP_TRAFFIC_FILTER_ADSLOT = 500643;

  /** dsp不支持的交易类型 */
  public static final int DSP_DEALTYPE_NOT_SUPPORTED = 500644;

  /** 未找到DSP广告位广告类型映射关系 */
  public static final int DSP_ADSLOT_ADTYPE_MAPPING_NOT_FOUND = 500645;

  /** dsp广告位映射被禁用 */
  public static final int DSP_ADSLOT_MAPPING_FORBIDDEN = 500646;

  /** 禁止的dsp广告交易类型 */
  public static final int DSP_TRADETYPE_FORBIDDEN = 500647;

  /** sdk版本被禁止请求dsp */
  public static final int DSP_SDKVERSION_NOT_MATCH = 500648;

  /** 不支持的dsp返回bid类型 */
  public static final int NOT_SUPPORT_DSP_BID_TYPE = 500649;

  // Dsp api请求时被系统中断
  public static final int DSP_API_ERROR_INTERRUPTED_EXCEPTION = 500650;
  // Dsp api请求时系统运行期错误
  public static final int DSP_API_ERROR_EXECUTION_ERROR = 500651;
  // Dsp api请求时，超过平台设置的超时时间
  public static final int DSP_API_ERROR_TIMEOUT = 500652;
  // Dsp信息错误
  public static final int ALL_DSP_INFO_IS_INVALID = 500653;
  // Dsp api多个错误原因
  public static final int DSP_API_MULTI_ERROR = 500654;
  // 请求dsp时，http状态码为非200
  public static final int DSP_API_INVALID_HTTP_STATUS = 500655;
  // 缺少广告位映射信息
  public static final int DSP_API_ERROR_NEED_ADSLOT_MAPPING = 500656;
  // 对方dsp错误的返回结果
  public static final int DSP_API_INVALID_RESPONSE_CONTENT = 500657;
  // 没有腾讯的宏替换配置文件
  public static final int NO_TENCENT_MACRO_CONFIG = 500658;
  // 广告缺少物料信息
  public static final int DSP_API_INVALID_RESPONSE_MATERIALMETA = 500659;
  // 不支持的创意类型
  public static final int NOT_SUPPORT_CREATIVE_TYPE = 500660;
  // 设置dspinfo时发生错误
  public static final int SET_DSP_INFO_ERROR = 500661;
  // 设置广告位映射信息出错
  public static final int SET_AD_SLOT_MAPPING_ERROR = 500662;
  // 设置dsp的请求策略出错
  public static final int SET_DSP_STRATEGY_ERROR = 500663;
  // 缺少今日头条配置信息
  public static final int NO_TOUTIAO_CONFIG = 500664;
  // 解析dsp返回中无法解析出tracking数据
  public static final int DSP_API_PARSE_ERROR_NO_TRACKING = 500665;

  /** 缺少rtb api config配置文件 */
  public static final int NO_RTB_API_CONFIG = 500666;

  // 请求dsp时，设备类型错误
  public static final int INVALID_DEVICE_TYPE = 500667;
  // 请求dsp时，缺少请求参数或者参数为空
  public static final int DSP_API_NEED_REQUEST_PARAM = 500668;
  // 无法解析出endcard，可能是因为api渠道返回的html为空，或者缺少素材
  public static final int INVALID_ENDCARD = 500669;
  // 没有落地页链接
  public static final int DSP_NO_LANDING_PAGE = 500670;
  // 没有dsp配置信息
  public static final int DSP_CONFIG_ERROR = 500671;

  // Api渠道不支持的广告类型
  public static final int NOT_SUPPORT_DSP_AD_TYPE = 500672;

  // 根据dsp过滤规则过滤后，没有可用的dsp信息
  //  @Deprecated public static final int ALL_DSP_INFO_IS_FILTERED = 500673;

  // 视频时长超限
  public static final int VIDEO_DURATION_EXCEED_LIMIT = 500673;

  // 错误的imei
  public static final int INVALID_IMEI = 500674;

  // 设置dsp app info出错
  public static final int SET_DSP_APP_INFO_ERROR = 500675;
  // 获取dsp app info出错
  public static final int GET_DSP_APP_INFO_ERROR = 500676;
  // 获取测试设备出错
  public static final int GET_DEBUG_DEVICE_ERROR = 500677;
  // 设置测试设备出错
  public static final int SET_DEBUG_DEVICE_ERROR = 500678;
  // 删除测试设备出错
  public static final int DEL_DEBUG_DEVICE_ERROR = 500679;
  // 用uid获取deviceId信息出错
  public static final int GET_DEVICE_ID_ERROR = 500680;
  // 设置deviceId信息出错
  public static final int SET_DEVICE_ID_ERROR = 500681;
  // Api返回的广告过期时间小于系统定义的过期时间
  public static final int DSP_API_INVALID_EXPIRATE_TIME = 500682;
  // 不支持的广告动作类型
  public static final int NOT_SUPPORT_INTERACTION_TYPE = 500683;
  // 竞价广告没有价格
  public static final int NO_PRICE_RESPONSE_ON_BIDING = 500684;
  // deal底价低于瀑布流底价或广告位底价
  public static final int DSP_PRICE_LOWER_THAN_FLOOR = 500685;

  /** 不在dsp允许访问的日期范围内 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_DATE_RANGE = 500686;

  /** 不在dsp允许访问的周期内 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_PERIOD = 500687;

  /** 不在dsp允许访问的地域范围内 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_REGION_LIMIT = 500688;

  /** dsp返回广告中dealId与请求dealId不一致 */
  public static final int DSP_REQUEST_DEAL_NOT_MATCH = 500689;

  /** 由于技术限制该dsp被过滤 */
  public static final int DSP_FILTERED_BY_TECH_LIMITED = 500690;

  /** 不支持的硬件，一般是硬件设备过于陈旧，比如内存过低等 */
  public static final int DSP_FILTERED_BY_DEVICE_LEVEL = 500691;

  /** 请求dsp被控量 */
  public static final int DSP_REQUEST_FORBIDDEN_AD_CONTROL = 500692;

  /** 价格不符合 */
  public static final int DSP_RESPONSE_FILTERED_BY_PRICE = 500693;

  /** Deal请求返回广告价格不符合设置 */
  public static final int DSP_INVALID_DEAL_PRICE = 500694;

  /** 由于deal交易概率被禁止访问 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_DEAL_PROBABILITY = 500695;

  /** Deal控量 */
  public static final int DSP_REQUEST_FORBIDDEN_DEAL = 500696;

  /** 处理rtb竞价请求的返回结果时出错 */
  public static final int PARSE_BID_TASK_RESPONSE_ERROR = 500697;

  /** Dsp 流量开关关闭 */
  public static final int DSP_TRAFFIC_OFF = 500698;

  /** 应用关联三方Dsp应用信息不可用 */
  public static final int DSP_APP_INFO_DISABLED = 500699;

  /** ************ 媒体端管理代码**************** */
  // App未设置聚合策略
  @Deprecated public static final int NO_REWARDED_VIDEO_STRATEGY = 500700;

  // deal或rtb的计算后返货价格低于请求底价
  public static final int REBATE_PRICE_LOWER_THAN_FLOOR = 500700;
  // 未开通任何渠道
  public static final int NO_ANY_REWARDED_VIDEO_CHANNEL = 500701;
  // 存储层发生错误
  public static final int STORAGE_ERROR = 500702;

  // TODO: 增加错误代码
  /** dsp广告不合规 * */
  public static final int DSP_RESPONSE_NOT_COMPLIANCE = 500703;

  /** 请求dsp的要求素材尺寸不符合要求 */
  public static final int DSP_REQUEST_ASSET_SIZE_NOT_MATCH = 500704;

  /** dsp响应广告素材不符合要求 */
  public static final int DSP_RESPONSE_MATERIAL_NOT_MATCH = 500705;

  /** 返回广告创意id为空 */
  public static final int DSP_RESPONSE_AD_CRID_EMPTY = 500706;

  /** 返回广告创意id过长 */
  public static final int DSP_RESPONSE_AD_CRID_TOO_LONG = 500707;

  /** 广告返回标题长度不符合要求 */
  public static final int DSP_RESPONSE_AD_TITLE_LENGTH_NOT_MATCH = 500708;

  /** 广告返回描述长度不符合要求 */
  public static final int DSP_RESPONSE_AD_DESC_LENGTH_NOT_MATCH = 500709;

  /** 广告返回视频分辨率尺寸不符合要求 */
  public static final int DSP_RESPONSE_AD_VIDEO_RESOLUTION_NOT_MATCH = 500710;

  /** 广告返回视频体积大小不符合要求 */
  public static final int DSP_RESPONSE_AD_VIDEO_FILE_SIZE_NOT_MATCH = 500711;

  /** 广告包名被请求白名单或黑名单过滤 */
  public static final int DSP_RESPONSE_AD_PACKAGE_FILTER = 500712;

  /** 广告行业被请求白名单或黑名单过滤 */
  public static final int DSP_RESPONSE_AD_CATEGORY_FILTER = 500713;

  /** 广告缺少包名过滤 */
  public static final int DSP_RESPONSE_PACKAGE_MISSING_FILTER = 500714;

  /** dsp的deal被关闭 */
  public static final int DSP_DEAL_CLOSED = 500715;

  /** 不支持deeplink广告定向投放 */
  public static final int DSP_RESPONSE_TARGET_DEEPLINK_NOT_SUPPORT = 500716;

  /** 广告定向未安装应用用户，但用户已安装应用 */
  public static final int DSP_RESPONSE_TARGET_UNINSTALL_BUT_INSTALLED = 500717;

  /** 未找到对应的流量广告主id */
  public static final int DSP_RESPONSE_ADVERTISER_ID_NOT_FOUND = 500718;

  /** 广告对应的流量广告主id被黑名单过滤 */
  public static final int DSP_RESPONSE_ADVERTISER_ID_FILTER = 500719;

  /** 未找到广告对应的流量所属行业 */
  public static final int DSP_RESPONSE_AD_CATEGORY_NOT_FOUND = 500720;

  /** 广告在流量端规定时间内重复投给相同用户被过滤 */
  public static final int DSP_RESPONSE_AD_FILTER_FOR_USER_BY_TRAFFIC = 500721;

  /** dsp返回广告返回的监测链接数过多 */
  public static final int DSP_RESPONSE_MONITOR_URL_COUNT_TOO_MUCH = 500722;

  /** 返回formatType错误 */
  public static final int DSP_RESPONSE_FORMAT_TYPE_ERROR = 500723;

  /** 返回广告的模版错误 */
  public static final int DSP_RESPONSE_TEMPLATE_ERROR = 500724;

  /** 没有可用模版 */
  public static final int DSP_REQUEST_NO_VALID_TEMPLATE = 500725;

  /** 缺少formatType信息 */
  public static final int FORMAT_INFO_MISSING = 500726;

  /** formatType.id与获取formatTypeInfo的请求id不一致 */
  public static final int FORMAT_INFO_ID_NOT_MATCH = 500727;

  /** 模版内容缺失 */
  public static final int TEMPLATE_INFO_MISSING = 500728;

  /** 模版id与获取模版的请求id不一致 */
  public static final int TEMPLATE_INFO_ID_NOT_MATCH = 500729;

  /** dsp返回广告被媒体屏蔽 */
  public static final int DSP_RESPONSE_MEDIA_FILTER = 500730;

  /** 广告返回图片文件大小不符合要求 */
  public static final int DSP_RESPONSE_AD_IMAGE_FILE_SIZE_NOT_MATCH = 500731;

  /** 根据算法策略过滤用户dsp广告请求 */
  public static final int DSP_REQUEST_FILTER_BY_STRATEGY = 500732;

  /** 根据vivo合规做的新合规过滤 */
  public static final int AD_FILTER_BY_COMPLIANCE_FILTER = 500733;

  /** 返回广告视频封面图缺失 */
  public static final int DSP_RESPONSE_AD_VIDEO_COVER_IMAGE_MISSING = 500734;

  /** 请求dsp使用了不被允许的ua */
  public static final int DSP_REQUEST_INVALID_UA = 500735;

  /** 返回广告视频内容或链接无效 */
  public static final int DSP_RESPONSE_AD_VIDEO_CONTENT_ERROR = 500736;

  /** 返回广告dp schema被联合频控 */
  public static final int DSP_RESPONSE_AD_DEEPLINK_CONTROL = 500737;

  /** dsp不支持广告位素材类型 */
  public static final int DSP_REQUEST_INVALID_ADSLOT_MATERIAL_TYPE = 500738;

  /** 模版定向后没有可用formatType */
  public static final int DSP_REQUEST_INVALID_FORMAT_TYPE = 500739;

  /** 广告违规、违法被过滤 */
  public static final int DSP_RESPONSE_AD_ILLEGAL = 500740;

  /** 设备因为没有安装指定包名无法请求dsp */
  public static final int DSP_REQUEST_PACKAGE_NOT_INSTALLED = 500741;

  /** dsp 不支持的售卖类型 */
  public static final int DSP_SELL_TYPE_NOT_SUPPORTED = 500742;

  /** dsp 禁用了流量组合 */
  public static final int DSP_REQUEST_TRAFFIC_FORBIDDEN = 500743;

  /** SDK过滤模版 */
  public static final int REQUEST_SDK_FILTER_TEMPLATE = 500744;

  /** dsp 返回广告物料url错误 */
  public static final int DSP_RESPONSE_AD_MATERIAL_URL_ERROR = 500745;

  /** dsp出价异常 */
  public static final int DSP_RESPONSE_PRICE_ABNORMAL = 500746;

  /** dsp deal请求被控量 */
  public static final int DSP_DEAL_REQUEST_CONTROL = 500747;

  /** 落地页链接长度过长 */
  public static final int DSP_RESPONSE_LANDING_PAGE_TOO_LONG = 500748;

  /** deeplink链接长度过长 */
  public static final int DSP_RESPONSE_DEEPLINK_TOO_LONG = 500749;

  /** 缺少deeplink */
  public static final int DSP_RESPONSE_DEEPLINK_MISSING = 500750;

  /** 下载类广告六要素不完整 */
  public static final int DSP_RESPONSE_MISSING_DOWNLOAD_CONTENT = 500751;

  /** 返回广告的链接协议不支持 */
  public static final int DSP_RESPONSE_LINK_SCHEMA_NOT_SUPPORTED = 500752;

  /** dsp售卖类型不支持广告位属性（例如888广告位不能请求包装售卖dsp渠道） */
  public static final int DSP_SELL_TYPE_NOT_MATCH_ADSLOT = 500753;

  /** 返回视频码率不匹配 */
  public static final int DSP_RESPONSE_VIDEO_BITRATE_NOT_MATCH = 500754;

  /** 返回视频编码格式不匹配 */
  public static final int DSP_RESPONSE_VIDEO_CODEC_FORMAT_NOT_MATCH = 500755;

  /** 返回广告价格低于过期广告出价 */
  public static final int DSP_RESPONSE_PRICE_LOWER_THAN_EXPIERD_AD_PRICE = 500756;

  /** 下载广告未安装过滤(厂商流量） */
  public static final int DSP_RESPONSE_DOWNLOAD_PACKAGE_INSTALLED = 500757;

  /** 因为请求dsp有track上报问题请求被屏蔽 */
  public static final int DSP_REQUEST_FORBIDDEN_BY_TRACK_PROBLEM = 500758;

  /** 缺少可用模版 */
  public static final int DSP_REQUEST_FILTER_MISSING_TEMPLATE = 500760;

  /** 特定条件过滤 */
  public static final int DSP_REQUEST_FILTER_SPECIAL = 500761;

  /** 缺少app label */
  public static final int DSP_REQUEST_FILTER_MISSING_APP_LABELS = 500762;

  /** dsp的未配置deal */
  public static final int DSP_DEAL_NOT_SET = 500763;

  /** 存储想关 */
  // redis 相关
  // 执行redis读操作时发生错误
  public static final int REDIS_READ_ERROR = 500800;

  // 执行redis写操作时发生错误
  public static final int REDIS_WRITE_ERROR = 500801;

  // 不能创建druid连接对象
  public static final int DRUID_GET_CLIENT_ERROR = 500820;
  // Druid查询错误
  public static final int DRUID_QUERY_ERROR = 500821;

  // 不能连接connection
  public static final int KAFKA_CONNECTION_ERROR = 500830;

  // 读取本地内存缓存出错
  public static final int LOCAL_CACHE_ERROR = 500840;

  /** SSP nativeTemplateId 丢失或不匹配 */
  public static final int SSP_FORMAT_TEMPLATE_ID_NOT_MATCH_OR_LOSE = 500841;

  // 控量相关
  // App控量配置读取出错
  public static final int GET_APP_AD_CONTROL_SETTING_ERROR = 500850;
  // App控量配置设置出错
  public static final int SET_AD_CONTROL_SETTING_ERROR = 500851;
  // App请求禁用标志读取出错
  public static final int GET_APP_REQUEST_FORBIDEN_FLAG_ERROR = 500852;
  // App请求禁用标志删除出错
  public static final int DEL_APP_REQUEST_FORBIDEN_FLAG_ERROR = 500853;
  // App请求禁用标志设置出错
  public static final int SET_APP_REQUEST_FORBIDEN_FLAG_ERROR = 500854;
  // 实时广告控量数据错误
  public static final Object AD_CONTROL_COUNT_REAL_TIME_DATA_ERROR = 500855;
  // 读取广告控量数据错误
  public static final int AD_CONTROL_COUNT_ERROR = 500856;

  // 无效的鸿蒙appId
  public static final int REQUEST_INVALID_HARMONY_APP_ID = 500857;

  // 保存模版错误
  public static final int SAVE_TEMPLATE_ERROR = 500902;
  // 保存视频md5错误
  public static final int SAVE_VIDEO_MD5 = 500903;
  // video的md5还未缓存好，不能下发
  public static final int NO_VIDEO_MD5_CACHE = 500904;

  /** 配置数据通知出错 */
  public static final int MANAGEMENT_SETTING_NOTIFY_ERROR = 500920;

  // 数据同步到海外异常
  public static final int MANAGEMENT_DAY_SYNC_ERROR = 500930;
  // 不支持的广告类型
  public static final int NOT_SUPPORT_AD_TYPE = 500931;

  /** dsp不支持api流量的结算方式 */
  public static final int DSP_NOT_SUPPORT_API_SETTLEMENT_MODE = 500932;

  /** 开启so加密 需要so header */
  public static final int NEED_SO_HEADER = 500998;

  public static final int DEVICE_FILTER = 500999;

  /**
   * <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135436916">多维度请求拦截过滤</a>
   */
  public static final int MULTI_DIMENSION_FILTER = 530000;

  /** 服务接口降级-被限流(代码范围：51001~51099) */
  @AllArgsConstructor
  @Getter
  public enum SERVICE {
    // 服务请求被熔断
    REQUEST_CIRCUIT_BREAKER(510001, "service circuit breaker"),
    // 服务请求被限流
    REQUEST_LIMITED(510002, "service request limited!");

    private final int code;
    private final String message;
  }

  @AllArgsConstructor
  @Getter
  public enum STANDARD_DSP {
    OK(0, "请求成功"),
    TECHNICAL_ERROR(1, "技术错误"),
    INVALID_REQUEST(2, "无效请求"),
    WEB_CRAWLER(3, "已知网络爬虫"),
    BLOCKED(4, "非人为阻塞"),
    DELEGATE_IP(5, "云、数据中心、或代理IP"),
    UNSUPPORTED_DEVICE(6, "不支持的设备"),
    BANNED_MEDIA(7, "封禁的媒体"),
    UNMATCHED_USER(8, "用户不匹配");

    private final int code;
    private final String message;
  }

  /** ******************广告审核相关(代码范围：51101~51199)*********** */

  /** 广告主审核结果 */
  @AllArgsConstructor
  @Getter
  public enum ADVERTISER_EXAMINE_RESULT {
    /** 广告主审核结果 */
    EXEMPT(0, "广告主免审"),
    PASS(1, "广告主审核通过"),
    FAIL(511001, "广告主审核不通过"),
    NO_EXAMINE_INFO(511002, "没有广告主审核信息"),
    MISSING_FIELD(511003, "缺少广告主审核字段"),
    ERROR(511004, "检查广告主审核过程异常");

    private final int code;
    private final String message;
  }

  /** 广告创意审核结果 */
  @AllArgsConstructor
  @Getter
  public enum AD_CREATIVE_EXAMINE_RESULT {
    /** 广告创意审核结果 */
    PASS(1, "广告创意审核通过"),
    FAIL(511011, "广告创意审核不通过"),
    NO_EXAMINE_INFO(511012, "没有广告创意审核信息"),
    MISSING_FIELD(511013, "缺少广告创意审核字段"),
    EXPIRED(511014, "广告创意过期"),
    SIGNATURE_FAILED(511015, "广告创意签名验证失败"),
    ERROR(511016, "检查广告创意审核过程异常");

    private final int code;
    private final String message;
  }

  /** 对接外部ADX 错误代码 */
  @AllArgsConstructor
  @Getter
  public enum THIRDPARTY_ADX {
    /** 对接外部ADX 错误代码 */
    PARAM_ERROR(520000, "参数错误"),
    NO_DEVELOPER_MAPPING(520001, "未找到sigmob开发者映射关系"),
    NO_APP_MAPPING(520002, "未找到sigmob应用映射关系"),
    NO_AD_SLOT_MAPPING(520003, "未找到sigmob广告位映射关系"),
    DEVELOPER_STATE_INVALID(520004, "开发者账户状态不可用"),
    NO_BID(520005, "请求不符合条件，sigmob不参与广告竞价"),
    REQUEST_LANGUAGE_NOT_SUPPORTED(520006, "请求语言不支持"),
    REQUEST_AD_INTERACTION_TYPE_NOT_SUPPORT(520007, "请求的广告推广类型不支持"),

    DSP_NOT_SUPPORT_AD_TEMPLATE(520008, "预算无法支持的广告规格"),
    REQUEST_AD_TYPE_NOT_SUPPORT(520009, "广告类型不支持"),
    RESPONSE_AD_ACTION_TYPE_NOT_SUPPORT(520010, "返回广告的交互类型错误"),
    ;

    private final int code;
    private final String message;
  }

  /** 激励视频奖励服务端验证异常 */
  @AllArgsConstructor
  @Getter
  public enum SSV {
    /** 激励视频奖励服务端验证异常 */
    PARAM_ERROR(521000, "接口参数错误"),
    REQUEST_CALLBACK_URL_ERROR(521001, "请求回调url错误"),
    CALLBACK_RESPONSE_ERROR(521002, "回调响应数据错误"),
    CALLBACK_RESPONSE_FALSE(521003, "回调响应为false"),
    NEED_EXT_INFO(521004, "缺少so扩展信息"),
    MASK_NOT_EQUALS_EXT_INFO_MASK(521005, "mask与so扩展信息mask不匹配"),
  /// CALLBACK_RESPONSE_FALSE(521005, "回调响应为false"),
  ;

    private final int code;
    private final String message;
  }

  /** hb相关错误码 */
  @AllArgsConstructor
  @Getter
  public enum HeaderBidding {
    /** 522000 ----> 522999 */
    NO_CACHE_AD(522000, "未找到缓存广告"),
    CACHE_AD_EXPIRED(522001, "缓存广告过期"),
    REQ_SLOT_NOT_MATCH(522002, "请求广告广告位与竞价广告位不一致"),
    AD_SLOT_TYPE_INVALID(522003, "非header bidding广告位"),
    INVALID_BID_TOKEN(522004, "无效的bidToken"),
    BID_UDID_NOT_MATCH(522005, "udid与bid阶段udid不匹配"),
    SDK_TOKEN_EXPIRE(522006, "sdkToken已过期"),
    NO_PACKAGE_CONFIG(522007, "sigmob平台未配置包名"),
    SDK_TOKEN_INVALID(522008, "sdkToken无效"),
    REQ_UID_NOT_MATCH(522009, "请求广告uid与竞价时uid不一致"),
    NOT_SUPPORT_BID_CUR(522010, "不支持货币币种"),
    REQ_NEED_UA(522011, "缺少设备network or ua"),

    SDK_TOKEN_IS_NULL_OR_EMPTY(522012, "sdkToken is null or empty"),
    UDID_IS_NULL_OR_EMPTY(522013, "sdkToken.deviceId.udid is null or empty"),
    BID_REQUEST_IS_NULL(522014, "bidRequest is null"),
    REQUEST_ID_IS_NULL(522015, "request id is null or empty"),
    REQUEST_TMAX_IS_NULL(522016, "request tmax is null"),
    IMP_IS_NULL(522017, "imp is null or empty"),
    IMP_FIRST_IS_NULL(522018, "imp[0] is null"),
    IMP_FIRST_ID_IS_NULL(522019, "imp.id is null or empty"),
    IMP_FIRST_PLACEMENTID_IS_NULL(522020, "imp.placementid is null or empty"),
    IMP_FIRST_DISPLAY_MANAGER_IS_NULL(522021, "imp.displaymanager is null or empty"),
    IMP_FIRST_DISPLAY_MANAGER_VER_IS_NULL(522022, "imp.displaymanagerver is null or empty"),
    IMP_BID_FLOOR_CUR_NOT_SUPPORTED(522023, "imp.bidfloorcur not supported"),
    IMP_FIRST_OR_ID_IS_NULL(522024, "imp or imp.id is null or empty"),
    APP_IS_NULL(522025, "app is null"),
    APP_ID_IS_NULL(522026, "app.id is null or empty"),
    APP_VER_IS_NULL(522027, "app.ver is null or empty"),
    APP_BUNDLE_IS_NULL(522028, "app.bundle is null or empty"),
    APP_ORIENTATION_IS_NULL(522029, "app.orientation is null"),
    SDK_TOKEN_APP_IS_NULL(522030, "sdkToken.app is null or empty"),
    SDK_TOKEN_APP_APP_VERSION_IS_NULL(522031, "sdkToken.app app_version is null or empty"),
    SDK_VERSION_IS_NULL(522032, "sdkVersion is null or empty"),
    SDK_TOKEN_APP_ID_IS_NULL(522033, "sdkToken.app_id is null or empty"),
    ;

    private final int code;
    private final String message;
  }

  /** mmp回掉验证异常 */
  @AllArgsConstructor
  @Getter
  public enum Mmp {
    /** mmp回掉验证异常 */
    PARAM_ERROR(523000, "接口参数错误"),
    REQUEST_CALLBACK_URL_ERROR(523001, "请求回调url错误"),
    CALLBACK_RESPONSE_ERROR(523002, "回调响应数据错误"),
    ;

    private final int code;
    private final String message;
  }

  /** ToBid相关错误码 599000 -> */
  @AllArgsConstructor
  @Getter
  public enum ToBid {
    /** 出价小于底价 */
    LESS_BID_FLOOR(599000, "出价小于底价"),
    NOT_SUPPORT_BID_CUR(599001, "不支持货币币种"),
    MAPPING_SCENE_NOT_FOUND(599003, "切量业务的映射关系表中指定的映射广告位在当前appid下找不到。请联系ToBid运营人员并提供聚合广告位信息"),
    MAPPING_SCENE_CLOSED(599004, "切量业务的映射关系表中指定的映射广告位已关闭,请前往平台开启聚合广告位或联系ToBid运营人员并提供聚合广告位信息"),
    MAPPING_SCENE_NO_STRATEGY(599005, "切量业务的映射关系表中指定的映射广告位下没有配置流量分组。请联系ToBid运营人员并提供聚合广告位信息"),
    /// REQUEST_SCENE_MAPPING_NOT_FOUNT(599006, "平台没有配置定向包映射"),
    TANGENT_STRATEGY_MAPPING_NOT_FOUNT(599007, "切量业务的映射关系表未配置。请联系ToBid运营人员并提供聚合广告位信息"),
    REQUEST_STRATEGY_REQ_NOT_FOUNT(599008, "切量业务中,ToBid客户端回传参数缺失。联系ToBid运营人员"),
    MAPPING_SCENE_STRATEGY_ID_NOT_FOUNT(
        599009, "切量业务中映射关系表中的映射分组id在映射聚合广告位下不存在。请联系ToBid运营人员并提供指定的映射聚合广告位信息"),
    STRATEGY_REQ_STRATEGY_ID_NOT_FOUNT(
        599010, "切量业务中,ToBid客户端回传的分组id在对应切量广告位下找不到。请联系ToBid运营人员并提供切量聚合广告位信息"),
    STRATEGY_MAPPING_STRATEGY_ID_NOT_FOUNT(
        599011, "切量业务的切量关系表中指定的切量广告位下找不到切量分组id。请联系ToBid运营人员并提供切量聚合广告位信息"),
    MAPPING_SCENE_PRECONDITION_STRATEGY_ID_NOT_FOUNT(
        599012, "切量业务的映射关系表前置条件分组id在对应切量广告位下找不到。请联系ToBid运营人员"),

    // 599050 --->  599100 bidder服务使用
    CHECK_MASK_ERR(599101, "check mask is not 1。请联系运营人员"),
    IMP_REPEAT(599102, "IMP REPEAT。请联系运营人员"),
    IMP_NOT_EXIST(599103, "IMP不存在。请联系运营人员"),
    REWARD_REPEAT(599104, "REWARD REPEAT。请联系运营人员"),
    MASK_NOT_EQUALS(599105, "MASK不匹配。请联系运营人员"),
    RISKY_USER(599106, "RISKY_USER。请联系运营人员"),

    TO_BID_SERVER_SUPPORT_ONLY(599997, "仅支持ToBid server。请联系运营人员"),
    LOSE_CALLBACK_ENC(599998, "CALLBACK ENC缺少。请联系ToBid运营人员"),
    CHANGE_PRICE_ERROR(599999, "改价了。请联系ToBid运营人员");

    private final int code;
    private final String msg;
  }
}
