package com.sigmob.ad.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class RedisConstants extends WindmillRedisConstants {

  public static final String REDIS_GROUP_UID = "android_uid";

  public static final String REDIS_GROUP_SETTINGS = "settings";

  public static final String REDIS_GROUP_RUNTIME_DATA = "runtime_data";

  public static final String REDIS_GROUP_REAL_TIME_DATA = "realtime_data";
  public static final String REDIS_GROUP_AD_CONTROL_DATA = "ad_control_data";

  /** dsp info在redis中的key的前缀 */
  public static final String KEY_PREFIX_DSP_INFO = "DSP_INFO_";

  /** adslot mapping在redis中的key的前缀 */
  @Deprecated public static final String KEY_PREFIX_ADSLOT_MAPPING = "DSP_AD_SLOT_MAPPING_";

  /** key的分隔符 */
  public static final String KEY_PREFIX_SEPARATOR = "_";

  /** adslot info的分隔符 */
  public static final String KEY_PREFIX_ADSLOT_INFO = "AD_SLOT_INFO_";

  /** adSlotId 与 appId映射关系 */
  public static final String KEY_PREFIX_ADSLOT_APP = "AD_SLOT_APP_";

  public static final String KEY_PREFIX_AD_SLOT_PRICE = "AD_SLOT_INFO_PRICE_";

  public static final String OK = "OK";

  public static final String KEY_PREFIX_USER_WHITE_LIST = "USER_WHITE_LIST_";
  public static final String KEY_PREFIX_DSP_APP_INFO = "DSP_API_APP_INFO_";

  public static final String KEY_PREFIX_DEBUG_DEVICE = "DEBUG_DEVICE_";
  // 新版测试设备
  public static final String KEY_PREFIX_SIG_DEBUG_DEVICE = "SIG_DEBUG_DEVICE_";

  /** harmony os scheme */
  public static final String KEY_H_SCHEME = "H_SCHEME";

  // sigmob 测试广告
  public static final String KEY_MOCK_SLOT_ID = "mock_slot_id_";
  public static final String KEY_PREFIX_DEVICE_ID = "DEVICE_ID_";

  public static final String KEY_PREFIX_AD_CONTROL_APP_SETTING = "AD_CONTROL_APP_SETTING_";

  //  public static final String KEY_PREFIX_AD_CONTROL_FORBIDEN_REQUEST =
  //      "AD_CONTROL_FORBIDEN_REQUEST_";
  public static final String KEY_PREFIX_DSP_API_DEAL = "DSP_API_DEAL_";
  // 根据dsp信息保存deal id时生成的key，此key用于找回deal id，再根据id找回deal
  public static final String KEY_PREFIX_DSP_API_DEAL_ID = "DSP_API_DEAL_ID_";

  public static final String KEY_PREFIX_DSP_API_DEAL_PDB_ID = "DSP_API_DEAL_PDB_ID_";

  /** 用户请求dsp频次 */
  public static final String KEY_PREFIX_AD_CONTROL_USER_DEVICE_FREQUENCY = "ADC_U_FRE_";

  /** 广告单元曝光 */
  public static final String KEY_PREFIX_AD_CONTROL_AD_SLOT_IMPRESSION = "ADC_SLOT_IMP_";

  public static final String KEY_PREFIX_AD_CONTROL_API_AD_SLOT_REQUEST =
      "AD_CONTROL_API_AD_SLOT_REQUEST_";
  public static final String KEY_PREFIX_AD_CONTROL_API_AD_SLOT_IMPRESSION =
      "AD_CONTROL_API_AD_SLOT_IMPRESSION_";

  // app级别sdk配置的key前缀
  public static final String KEY_PREFIX_SDK_SETTING_BY_APP = "SDK_SETTING_APP_";
  public static final String KEY_PREFIX_SDK_SETTING_BY_AD_SLOT_ID = "SDK_SETTING_AD_SLOT_";

  public static final String KEY_ANDROID_CAN_PKG_LIST = "can_pkg_list";
  public static final String KEY_ANDROID_CAN_PKG_LIST_APP = "can_pkg_list_app";
  public static final String KEY_ANDROID_CAN_PKG_APP_UDIDS = "app_";
  // DSP设备白名单key前缀
  public static final String KEY_PREFIX_DSP_DEVICE_WHITELIST = "DSP_DEVICE_WHITELIST_";

  // 广告单元瀑布流策略配置key前缀
  public static final String KEY_PREFIX_AD_SLOT_TRADING_SETTING = "AD_SLOT_TRADING_SETTING_";

  public static final String KEY_PREFIX_DSP_ADSLOT_MAPPING_HASH_SET_KEY =
      "DSP_ADSLOT_MAPPING_HASH_SET_KEY_";
  // dsp 控量设置
  public static final String KEY_PREFIX_DSP_API_AD_CONTROL_SETTING = "DSP_API_AD_CONTROL_SETTING_";
  public static final String KEY_PREFIX_DSP_API_AD_CONTROL_SETTING_ID =
      "DSP_API_AD_CONTROL_SETTING_ID_";

  // sdk策略key前缀
  public static final String KEY_PREFIX_SDK_STRATEGY_SETTING = "SDK_STRATEGY_SETTING_V2_";

  public static final String KEY_PREFIX_SDK_STRATEGY_SETTING_INDEX =
      "SDK_STRATEGY_SETTING_INDEX_V2_";

  public static final String KEY_PREFIX_SDK_STRATEGY_RULE = "SDK_STRATEGY_RULE_V2_";

  public static final String KEY_PREFIX_SDK_CHANNEL_ARGS = "SDK_CHANNEL_ARGS_V2_";

  public static final String KEY_PREFIX_APP_INFO = "APP_INFO_";

  /** app 不支持的个性化开关 （Recommendation） */
  public static final String KEY_PREFIX_APP_RECOMMENDATION = "APP_RE_";

  public static final String KEY_PREFIX_APP_LIST_UDID_INIT = "init_";
  public static final String KEY_PREFIX_APP_LIST_UDID_ON = "init_udid_on_";
  public static final String KEY_PREFIX_IOS_SCHEME = "IOS_SCHEME_";

  /** 广告主审核前缀 */
  public static final String KEY_PREFIX_ADVERTISER = "ADVERTISER_";

  /** 广告创意审核前缀 */
  public static final String KEY_PREFIX_CREATIVE = "CREATIVE_";

  /** 媒体开发者 */
  public static final String KEY_PREFIX_API_DEVELOPER = "PUBLISH_DEVELOPER_";

  /** 媒体开发者合规白名单 */
  public static final String KEY_PREFIX_DEVELOPER_COMPLIANCE = "DEVELOPER_C";

  /** 三方流量appId与sigmob app映射关系 */
  public static final String KEY_PREFIX_API_APP = "API_PUBLISHER_APP_";

  /** 三方流量广告单元id与sigmob 广告单元映射关系 */
  public static final String KEY_PREFIX_API_AD_SLOT = "API_PUBLISHER_AD_SLOT_";

  /** 用户请求控制 */
  public static final String KEY_PREFIX_USER_REQUEST_FREQUENCY_CONTROL = "U_REQ_FREQ_CONTROL_";

  public static final String KEY_PREFIX_DSP_ADSLOT_REQUEST_QPS = "DSP_ADSLOT_QPS_";

  /** */
  public static final String KEY_PREFIX_DSP_CALLBACK = "DSP_CB_";

  /** adx流量过滤策略 @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=86383807 */
  public static final String KEY_PREFIX_USER_ADSLOT_DSP_REQUEST_FILTER = "UFN_";

  /** 媒体应用过滤广告配置 */
  public static final String KEY_PREFIX_AD_FILTER_APP = "M_AD_FILTER_APP_";

  /** 媒体广告单元过滤广告配置 */
  public static final String KEY_PREFIX_AD_FILTER_SLOT = "M_AD_FILTER_SLOT_";

  /** 外币转人民币汇率 */
  public static final String KEY_PREFIX_CUR_EXCHANGE_RATE = "CUR_RATE_";

  /** 广告缓存 */
  public static final String KEY_PREFIX_AD_CACHE = "AD_CACHE_";

  /** 广告track */
  public static final String KEY_PREFIX_AD_TRACK_CACHE = "TRACK_CACHE_";

  /** 媒体广告单元尺寸映射 */
  public static final String KEY_PREFIX_AD_SLOT_SIZE_MAPPING = "M_SLOT_SIZE_MAP_";

  /** Dsp流量策略定向 */
  public static final String KEY_PREFIX_DSP_TARGET_STRATEGY = "DSP_S_TARGET_";

  /** 设备机型映射 */
  public static final String KEY_PREFIX_DEVICE_MODEL_BRAND = "rk_";

  public static final String KEY_PREFIX_DEVICE_NO_AD_FILL_ADSLOT_WHITELIST = "rp_";

  /** 广告位并行请求 */
  public static final String KEY_PREFIX_AD_SLOT_PARALLEL_RATIO = "AD_SLOT_P_R_";

  /** 尾量请求无广告填充控制 */
  public static final String KEY_PREFIX_DEVICE_NO_AD_FILL_CONTROL = "D_C_";

  /** DSP要求过滤的请求 */
  public static final String KEY_PREFIX_DSP_FILTER_REQ = "DSP_F_";

  /** */
  public static final String KEY_PREFIX_DSP_FILTER_REQ_BY_TRACK_PROBLEM = "D_F_T_";

  /** API流量过滤广告 */
  public static final String KEY_PREFIX_API_FILTER_AD = "A_F_";

  /** 过滤用户请求广告 */
  public static final String KEY_PREFIX_USER_REQ_AD_FILTER = "U_F_";

  /** 用户广告模板频次控制 */
  public static final String KEY_PREFIX_USER_TEMPLATE_FREQ_CONTROL = "U_TFQ_";

  //  /** 网盟dsp预估ecpm得分后缀 */
  //  public static final String KEY_SUFFIX_DSP_ECPM_SOCRE = "-score";
  //
  //  /** 网盟dsp预估ecpm系数后缀 */
  //  public static final String KEY_SUFFIX_DSP_ECPM_RATIO = "-ratio";

  /** 华为api广告位映射sigmob广告位策略 */
  public static final String KEY_PREFIX_AD_SLOT_MAPPING_STRATEGY_HUAWEI =
      "HUAWEI_STRATEGY_AD_SLOT_ID_";

  /** oppo api广告位映射sigmob广告位策略 */
  public static final String KEY_OPPO_STRATEGY = "OPPO_STRATEGY";

  public static final String KEY_GROMORE_STRATEGY = "GROMORE_STRATEGY";

  public static final String KEY_VIVO_STRATEGY = "VIVO_STRATEGY";

  /** adx广告价格策略 */
  public static final String KEY_PREFIX_ADX_PRICE_STRATEGY = "aap";

  /** adx hb出价策略005 价格系数 */
  public static final String KEY_PREFIX_ADX_HB_PRICE_PARAM = "ahp_";

  /** 广告所属行业信息 流量类型+包名为key的组成部分，一、二级行业为field */
  public static final String KEY_PREFIX_PACKAGE_INDUSTRY = "ad_industry_";

  /** dsp在流量方的广告主id dspid作为key的组成部分，流量类型为field */
  public static final String KEY_PREFIX_DSP_TO_ADVERTISERID = "dsp_adverid_";

  /** dsp 返利配置 */
  public static final String KEY_PREFIX_DSP_REBATE_CONFIG = "dsp_rebate_";

  /** dsp 三方广告位qps控制 */
  public static final String KEY_PREFIX_DSP_ADSLOT_QPS_CONTROL = "dqc_";

  /** dsp RTB渠道成交价格调整系数配置 */
  public static final String KEY_PREFIX_DSP_RTB_BID_PRICE_RATIO = "dsp_rtb_bid_price_";

  /** dsp 包装售卖 */
  public static final String KEY_PREFIX_DSP_PACKAGE_SELL = "dsp_html_temp_";

  /** dsp html模板配置 */
  public static final String KEY_PREFIX_DSP_HTML_TEMPLATE = "dsp_html_temp_";

  /** 流量方屏蔽响应广告 */
  public static final String KEY_PREFIX_TRAFFIC_FILTER_AD = "traffic_f_ad_";

  /** 媒体广告位保填充价格策略 */
  public static final String KEY_PREFIX_AD_FILL_PRICE_STRATEGY_PREFIX = "AF_";

  //  /** 模板html source */
  //  public static final String KEY_PREFIX_DSP_TEMPLATE_SOURCE_INFO = "dsp_temp_info_source_";
  /** 模板信息 */
  public static final String KEY_PREFIX_DSP_TEMPLATE_INFO = "dsp_temp_info_";

  /** format_type */
  public static final String KEY_PREFIX_DSP_FORMAT_TYPE_INFO = "dsp_format_type_info_";

  public static final String KEY_PREFIX_SSP_FORMAT_TYPE_INFO = "ssp_format_type_info_";

  /** 模板屏蔽 */
  public static final String KEY_PREFIX_DSP_TEMPLATE_SHIELD = "dsp_temp_shield_";

  /** 模板屏蔽V2 */
  public static final String KEY_PREFIX_DSP_TEMPLATE_SHIELD_V2 = "dsp_temp_shield_v2_";

  /** 模板配置 */
  public static final String KEY_PREFIX_DSP_TEMPLATE = "dsp_temp_conf_";

  /** dsp广告位二次包装配置 */
  public static final String KEY_PREFIX_DSP_PLACEMENT_MAPPER = "dsp_placement_mapper_";

  /** dsp流量组合 */
  public static final String KEY_DSP_REQUEST_TRAFFIC = "dsp_request_traffic";

  /** dsp流量组合配置 */
  public static final String KEY_PREFIX_DSP_REQUEST_TRAFFIC_CONFIG = "dsp_request_traffic_config_";

  /** dsp广告模板使用统计 */
  public static final String KEY_PREFIX_DSP_TEMPLATE_USAGE = "dsp_temp_use_";

  public static final String KEY_NEW_SELL_DSP_IDS = "new_sell_dsp_ids";

  public static final String KEY_REAL_TOBID_ADX_DSP_IDS = "real_tobid_adx_dsp_ids";

  /** api 流量媒体请求低价系数redis key */
  public static final String KEY_API_BID_FLOOR_RATIO = "API_BID_FLOOR_RATIO_";

  /** api 和 sdk hb流量媒体报价系数redis key（媒体配置 与 广告位配置用同一个key前缀） */
  public static final String KEY_QUOTATION_RATIO = "QUOTATION_RATIO_";

  /** DSP 账号级媒体请求分流 */
  @Deprecated public static final String KEY_DSP_APP_TRAFFIC_FILTER = "dsp_app_traffic_filter_";

  /** DSP 请求分流配置 */
  public static final String KEY_DSP_TRAFFIC_FILTER = "dsp_traffic_filter_";

  /** app 屏蔽的动作类型 */
  public static final String KEY_PREFIX_APP_INTERACTION = "APP_INTERACT_";

  /** 流量广告所属广告主id映射关系 */
  public static final String KEY_PREFIX_TRAFFIC_ADVERTISER_MAPPING = "BUDGET_CONFIG_";

  /** 广告主id所属行业映射关系 */
  public static final String KEY_PREFIX_TRAFFIC_INDUSTRY_MAPPING = "MEDIA_INDUSTRY_MAPPING_";

  /** 流量请求策略-包名映射关系 */
  public static final String KEY_PREFIX_TRAFFIC_STRATEGY_MAPPING = "T_S_M_";

  /**
   * ssp过滤规则key
   *
   * @since <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135436916">多维度请求拦截配置功能</a>
   */
  public static final String FILTER_RULE_MAP_KEY = "FILTER_RULE_MAP_KEY";

  /**
   * 需要修改监测链接的APPID
   *
   * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=135445722">标准api媒体替换监测链接域名</a>
   */
  public static final String KEY_MODIFY_TRACK_URL_HOST_APP_ID = "MODIFY_TRACK_URL_HOST";

  // 广告主审核信息字段
  @AllArgsConstructor
  @Getter
  public enum ADVERTISER_EXAMINE_FIELDS {
    INDUSTRY("industry"), // 广告主行业类别，json数组
    EXAMINE_TYPE("examineType"), // 广告主审核类型，0:免审，1:抽审
    EXAMINE_STATE("examineState"); // 审核状态，1:待审核，2:通过，3:不通过，4:二审

    final String name;
  }

  // 广告创意审核信息字段
  @AllArgsConstructor
  @Getter
  public enum CREATIVE_EXAMINE_FIELDS {
    CAT("cat"), // 推广产品或服务的分类，例如:[1,2,3]
    ATTR("attr"), // 创意类型: 3-纯静态图片广告(一般适用开屏广告); 4-video+endcard(视频+图片，一般适用激励视频);
    // 5-video+ endcard(html source，一般适用激励视频)
    INTERACTION_TYPE("interactionType"), // 广告交互类型。1=使用浏览器打开;2= 下载应用
    BUNDLE("bundle"), // Android，应用包名；iOS，传 iTunes ID， 针对应用下载类广 告
    EXPIRE_TIME("expireTime"), // 创意过期时间戳，精确到（秒）
    EXAMINE_STATE("examineState"), // 审核状态，1:待审核，2:通过，3:不通过，4:二审
    SIGNATURE("signature"); // 创意签名：投放时用来校验创意是否合法

    final String name;
  }
}
