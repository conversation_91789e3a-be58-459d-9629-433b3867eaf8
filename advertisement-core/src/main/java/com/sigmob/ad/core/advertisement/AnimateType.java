package com.sigmob.ad.core.advertisement;

public enum AnimateType {

  /**  中心加弹簧的模式 */
  CENTER(1),
  /** 自下而上 */
  DOWN_TO_UP(2),
  /** 无动画 */
  NONE(3);
  private final int type;

  AnimateType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    AnimateType[] types = values();
    for (AnimateType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static AnimateType getType(int i) {
    AnimateType[] types = values();
    for (AnimateType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return DOWN_TO_UP;
  }
}
