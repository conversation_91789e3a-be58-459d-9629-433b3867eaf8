package com.sigmob.ad.core.advertisement;

/** <AUTHOR> */
public enum AdSlotOrientation {
  // 不限
  NOT_LIMIT(0),
  //竖屏
  Portrait(1),
  // 横屏
  Landscape(2),
  All(3);

  private final int type;

  AdSlotOrientation(int type) {
    this.type = type;
  }

  public static boolean isValidType(int typeNum) {
    AdSlotOrientation[] types = values();
    for (AdSlotOrientation type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static AdSlotOrientation getType(int i) {
    AdSlotOrientation[] types = values();
    for (AdSlotOrientation type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return Portrait;
  }

  public int getTypeNum() {
    return this.type;
  }
}
