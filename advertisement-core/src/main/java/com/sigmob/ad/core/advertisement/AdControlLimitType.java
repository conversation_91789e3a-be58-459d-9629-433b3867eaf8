package com.sigmob.ad.core.advertisement;

/**
 * 广告控量类型
 *
 * <AUTHOR>
 */
public enum AdControlLimitType {

  /** 不限 */
  NO_LIMITED(0),
  /** 按请求量限制 */
  LIMIT_BY_REQUEST(1),
  /** 按曝光量限制 */
  LIMIT_BY_IMPRESSION(2);
  private final int type;

  AdControlLimitType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    AdControlLimitType[] types = values();
    for (AdControlLimitType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static AdControlLimitType getType(int i) {
    AdControlLimitType[] types = values();
    for (AdControlLimitType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return NO_LIMITED;
  }
}
