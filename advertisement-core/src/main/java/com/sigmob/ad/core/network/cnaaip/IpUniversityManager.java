/** */
package com.sigmob.ad.core.network.cnaaip;

import java.io.File;

/** <AUTHOR> */
public class IpUniversityManager {
  private static IpUniversityManager instance = new IpUniversityManager();
  private IpRange ipUniversityRange = null;
  private volatile boolean initliazed = false;
  private static File ipFile = null;

  private IpUniversityManager() {
    ipUniversityRange = new IpRange();
  }

  public static void init(File basePath) {
    synchronized (IpUniversityManager.class) {
      if (instance.initliazed) {
        return;
      }
      ipFile = new File(basePath, Constants.CNAA_BASE_IP_FILE);
      instance.ipUniversityRange.init(ipFile);
    }
  }

  public static void init(String basePath) {
    synchronized (IpUniversityManager.class) {
      if (instance.initliazed) {
        return;
      }
      String filePath = basePath + File.separator + Constants.CNAA_BASE_IP_FILE;
      instance.ipUniversityRange.init(filePath);
    }
  }

  public static IpV4Name getIpUniversity(String ip) {
    return instance.ipUniversityRange.getIpName(ip);
  }
}
