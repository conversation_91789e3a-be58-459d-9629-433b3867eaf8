package com.sigmob.ad.core.advertisement;

/**
 * 广告创意类型
 *
 * <AUTHOR>
 */
public enum CreativeType {
  /** 1=奖励视频广告的资源包形式(endcard为tgz包)，一般由video、endcard资源包构成； */
  VIDOE_TGZ(1),
  /** 预留 */
  SINGLE_IMAGE(2),
  /** 纯静态图片广告，一般由单张image_src构成 */
  SPLASH_IMAGE(3),
  /** =video+html源代码的模式 */
  VIDEO_HTML_SNIPPET(4),
  /** video定帧+htmlsrc */
  VIDEO_KEEP(6),
  /** video+html_url(相当于在线endcard) */
  VIDEO_AND_NO_ENDCARD(7),
  /** 单个视频, 用在视频开屏 */
  ONLY_VIDEO(8),
  /** MRAID支持 */
  MRAID(9),
  /** API流量专用：video + image */
  VIDEO_AND_IMAGE(10),

  /** SDK流量专用， mraid 2.0*/
  SDK_MRAID_2_0(10),
  /** 图文 */
  IMAGE_AND_TEXT(11),
  /** html链接 */
  HTML_URL(12),
  /** html源码 */
  HTML_SRC(13),
  /** 文字链 */
  TEXT_LINK(14),
  /** 原生（信息流） */
  NATIVE(15),

  /** sdk原生模版（当不支持mraid、mraid2模版时用作兜底） */
  SDK_NATIVE_TEMPLATE(16),

  /** 服务端SDK流量专用， mraid 2.0*/
  SERVER_MRAID_2_0(20);

  final int type;

  CreativeType(int type) {
    this.type = type;
  }

  public static boolean isValidType(int typeNum) {
    CreativeType[] types = values();
    for (CreativeType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static CreativeType getType(int i) {
    CreativeType[] types = values();
    for (CreativeType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return VIDOE_TGZ;
  }

  public int getTypeNum() {
    return this.type;
  }
}
