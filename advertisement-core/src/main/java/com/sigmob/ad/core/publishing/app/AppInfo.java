package com.sigmob.ad.core.publishing.app;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@EqualsAndHashCode
public class AppInfo implements Serializable {

  private Integer id;
  private String appKey;
  private Integer state; // 0 app开关
  private Integer orientation; // 2
  private Integer osType; // 0
  private Integer adState; // 1 运营开关
  private Integer gId; // 0
  private String packageName;
  private long developerId;
  private Integer adCheckType; // 0-非前审；1-前审
  private Integer level;
  // 0:未绑定，1：已绑定
  private int bindWindmill;
  // ios 包名
  private String bundle;
  // windmill运营开关 0：开启，1：关闭
  private int windmillState;

  // 需要替换的广告请求设备ua内容
  private String replaceUa;

  private String wxAppId;

  private String universalLink;

  /** feedbackDebug false:未开启，true：已开启 */
  private boolean debug;

  /** 鸿蒙的应用appId信息; */
  private String ohosAppId;

  /** JS 缓存配置天数 */
  private int jsCache;

  /** 媒体网赚属性 */
  private List<String> appLabels;

  // ======= logo配置 since http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444838 ===
  /**
   * 流量类型 1:自定义，2:toBid logo，3:预算侧logo
   */
  private Integer toBidType;
  /**
   * Logo URL地址
   */
  private String toBidLogoUrl;
  /**
   * 流量类型 1:自定义，2:sigmob logo，3:预算侧logo
   */
  private Integer sigmobType;
  /**
   * Logo URL地址
   */
  private String sigmobLogoUrl;

  public boolean isClosed() {
    return this.state == 0;
  }

  public boolean isSigmobAdClose() {
    return this.adState == 2;
  }

  public boolean isNotBindWindmill() {
    return this.bindWindmill == 0;
  }

  public boolean isWindmillClose() {
    return this.windmillState == 1;
  }
}
