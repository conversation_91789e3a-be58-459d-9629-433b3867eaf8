/** */
package com.sigmob.ad.core.dspmanagement;

/** <AUTHOR> */
public enum DealState {
  CLOSED(0),
  OPPENED(1);
  private int type;

  private DealState(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    DealState types[] = values();
    for (DealState type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static DealState getType(int i) {
    DealState types[] = values();
    for (DealState type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return null;
  }
}
