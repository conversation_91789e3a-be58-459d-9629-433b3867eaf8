package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 墨迹天气API 常量 */
public class MoWeatherConstants {

  /** 墨迹天气 rtb api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(2).setMinor(5).setMicro(3).setVersionStr("2.5.3").build();

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;


  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum AdType {
    BANNER(2),
    SPLASH(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum OsType {
    ANDROID(0),
    IOS(1),
    WP(2),
    OTHERS(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Orientation {
    LANDSCAPE(1), // 横屏
    PORTRAIT(2); // 竖屏

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    WIFI(1),
    CELL_2G(2),
    CELL_3G(3),
    CELL_4G(4);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN(0),
    MOBILE(1),
    UNICOM(2),
    TELECOM(3);

    final int code;
  }

  /** 广告请求返回代码 */
  @AllArgsConstructor
  @Getter
  public enum ResultCode {
    OK(200),
    NO_AD(400),
    ILLEGAL_ADID(401),
    PARAM_MISSING(402),
    SERVER_BUSY(500),
    SERVER_ERROR(501);

    final int code;
  }

  /**
   * 开屏类型
   *
   * <p>0 - 图片<br>
   * 1 - 视屏 <br>
   */
  @AllArgsConstructor
  @Getter
  public enum SplashType {
    IMAGE(0),
    VIDEO(1);

    final int code;
  }
}
