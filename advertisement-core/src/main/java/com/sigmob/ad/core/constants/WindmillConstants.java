package com.sigmob.ad.core.constants;

import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聚合常量定义
 *
 * <AUTHOR>
 */
public class WindmillConstants {

  /** 激励视频策略并行请求渠道数：4 */
  public static final Integer DEFAULT_REWARDED_VIDEO_STRATEGY_CONCURRENT_COUNT = 4;

  /** 开屏策略并行请求渠道数：2 */
  public static final Integer DEFAULT_SPLASH_SCREEN_STRATEGY_CONCURRENT_COUNT = 2;

  public static final Integer DEFAULT_ALGORITHM_CONCURRENT_COUNT = 6;

  public static final Integer DEFAULT_SPLASH_SINGLE_CHANNEL_TIMEOUT = 2;

  /** AB分组seed */
  public static final Integer AB_GROUP_SEED = 1299689;

  public static final Integer TWO_HOURS = 2;

  public static final String WINDMILL_STRATEGY_HEADER = "hb-args";

  public static final String PRIVACY_URL = "https://n.sigmob.cn/mraid/tobid6elements/index-v3.html";

  public static final String Y = "y";

  public static final String N = "n";

  public static final String WINDMILL_PLATFORM = "windmill";
  public static final String WINDMILL_PRICE_ENC = "tb-ttt";
  public static final String WINDMILL_SSV_ENC = "tbvsss";

  /** sdk version 1.6.0 支持sigmob hb 2.0 */
  public static final Version VER_1_6_0 =
      Version.newBuilder().setMajor(1).setMinor(6).setMicro(0).setVersionStr("1.6.0").build();

  /** sdk version 1.7.0 支持csj和groMore bidding 和普通瀑布流全排序 */
  public static final Version VER_1_7_0 =
      Version.newBuilder().setMajor(1).setMinor(7).setMicro(0).setVersionStr("1.7.0").build();

  /** sdk version 1.9.0 支持相同渠道多个bidding广告源 */
  public static final Version VER_1_9_0 =
      Version.newBuilder().setMajor(1).setMinor(9).setMicro(0).setVersionStr("1.9.0").build();

  /** sdk version 1.11.0 支持 sig、ks、gdt、mtg 客户端bidding */
  public static final Version VER_1_11_0 =
      Version.newBuilder().setMajor(1).setMinor(11).setMicro(0).setVersionStr("1.11.0").build();

  /** sdk version 1.12.0 android 支持o v h m 定制化 */
  public static final Version VER_1_12_0 =
      Version.newBuilder().setMajor(1).setMinor(12).setMicro(0).setVersionStr("1.12.0").build();

  /** sdk version 2.0.0 支持自定义adn 和harmony sig */
  public static final Version VER_2_0_0 =
      Version.newBuilder().setMajor(2).setMinor(0).setMicro(0).setVersionStr("2.0.0").build();

  /** sdk version 2.3.0 支持客户端发送bidding win、lose notice,服务端不再发，同时服务端处理统一宏替换 */
  public static final Version VER_2_3_0 =
      Version.newBuilder().setMajor(2).setMinor(3).setMicro(0).setVersionStr("2.3.0").build();

  /** sdk version 2.5.0 支持趣盟渠道、oppo bidding广告源 */
  public static final Version VER_2_5_0 =
      Version.newBuilder().setMajor(2).setMinor(5).setMicro(0).setVersionStr("2.5.0").build();

  /** sdk version 2.6.0 支持tap */
  public static final Version VER_2_6_0 =
      Version.newBuilder().setMajor(2).setMinor(6).setMicro(0).setVersionStr("2.6.0").build();

  /** sdk version 2.8.0 支持pangle */
  public static final Version VER_2_8_0 =
      Version.newBuilder().setMajor(2).setMinor(8).setMicro(0).setVersionStr("2.8.0").build();

  /** sdk version 2.10.0 支持max、reklamup */
  public static final Version VER_2_10_0 =
      Version.newBuilder().setMajor(2).setMinor(10).setMicro(0).setVersionStr("2.10.0").build();

  /** sdk version 2.11.0 支持tap bidding */
  public static final Version VER_2_11_0 =
      Version.newBuilder().setMajor(2).setMinor(11).setMicro(0).setVersionStr("2.11.0").build();

  /** sdk version 2.12.0 支持strategy/v6 & waterfall */
  public static final Version VER_2_12_0 =
      Version.newBuilder().setMajor(2).setMinor(12).setMicro(0).setVersionStr("2.12.0").build();

  /** sdk version 2.13.0 支持 oppo_adn */
  public static final Version VER_2_13_0 =
      Version.newBuilder().setMajor(2).setMinor(13).setMicro(0).setVersionStr("2.13.0").build();

  /** sdk version 2.15.0 支持 roi */
  public static final Version VER_2_15_0 =
      Version.newBuilder().setMajor(2).setMinor(15).setMicro(0).setVersionStr("2.15.0").build();

  /** sdk version 3.0.0 支持 原生自渲染转换其他广告类型 */
  public static final Version VER_3_0_0 =
      Version.newBuilder().setMajor(3).setMinor(0).setMicro(0).setVersionStr("3.0.0").build();

  public static final Version VER_3_1_0 =
      Version.newBuilder().setMajor(3).setMinor(1).setMicro(0).setVersionStr("3.1.0").build();

  /** sdk version 3.2.0 支持 inmobi、honor && dc_options */
  public static final Version VER_3_2_0 =
      Version.newBuilder().setMajor(3).setMinor(2).setMicro(0).setVersionStr("3.2.0").build();

  /** sdk version 3.3.0 支持 csj、gromore插件更新、 支持admate美数 */
  public static final Version VER_3_3_0 =
      Version.newBuilder().setMajor(3).setMinor(3).setMicro(0).setVersionStr("3.3.0").build();

  /** sdk version 3.5.0 才支持 最大并发数 */
  public static final Version VER_3_5_0 =
      Version.newBuilder().setMajor(3).setMinor(5).setMicro(0).setVersionStr("3.5.0").build();

  /** sdk version 3.7.0 才支持 开屏客户端竞价以及637 */
  public static final Version VER_3_7_0 =
      Version.newBuilder().setMajor(3).setMinor(7).setMicro(0).setVersionStr("3.7.0").build();

  /** sdk version 3.7.3 才支持时间为ms */
  public static final Version VER_3_7_3 =
      Version.newBuilder().setMajor(3).setMinor(7).setMicro(3).setVersionStr("3.7.3").build();

  /** sdk version 3.8.0 才支持 混出 以及 原生渲染banner */
  public static final Version VER_3_8_0 =
      Version.newBuilder().setMajor(3).setMinor(8).setMicro(0).setVersionStr("3.8.0").build();

  /** sdk version 3.9.0 才支持 VIVO_ADN BILLOW (VIVO BIDDING) */
  public static final Version VER_3_9_0 =
      Version.newBuilder().setMajor(3).setMinor(9).setMicro(0).setVersionStr("3.9.0").build();

  public static final Version VER_4_0_0 =
      Version.newBuilder().setMajor(4).setMinor(0).setMicro(0).setVersionStr("4.0.0").build();

  public static final Version VER_4_0_3 =
      Version.newBuilder().setMajor(4).setMinor(0).setMicro(3).setVersionStr("4.0.3").build();
  public static final Version VER_4_1_0 =
      Version.newBuilder().setMajor(4).setMinor(1).setMicro(0).setVersionStr("4.1.0").build();
  public static final Version VER_4_1_10 =
      Version.newBuilder().setMajor(4).setMinor(1).setMicro(10).setVersionStr("4.1.10").build();
  public static final Version VER_4_2_22 =
      Version.newBuilder().setMajor(4).setMinor(2).setMicro(22).setVersionStr("4.2.22").build();

  public static final Version VER_4_3_0 =
      Version.newBuilder().setMajor(4).setMinor(3).setMicro(0).setVersionStr("4.3.0").build();
  public static final Version VER_4_3_10 =
      Version.newBuilder().setMajor(4).setMinor(3).setMicro(10).setVersionStr("4.3.10").build();
  public static final Version VER_4_3_11 =
      Version.newBuilder().setMajor(4).setMinor(3).setMicro(11).setVersionStr("4.3.11").build();
  public static final Version VER_4_3_20 =
      Version.newBuilder().setMajor(4).setMinor(3).setMicro(20).setVersionStr("4.3.20").build();

  public static final Version VER_4_4_30 =
      Version.newBuilder().setMajor(4).setMinor(4).setMicro(30).setVersionStr("4.4.30").build();

  /** 华为客户端bidding */
  public static final Version VER_4_6_0 =
      Version.newBuilder().setMajor(4).setMinor(6).setMicro(0).setVersionStr("4.6.0").build();

  public static final Integer MIN_CUSTOM_CHANNEL = 10000;
  public static final String N0_WATERFALL_ERR_MSG =
      """
                  该聚合广告位下的分组没开启任意广告源。可能原因如下：
                  1.该分组下没有开启任何广告源；
                  2.ToBid SDK1.11.0版本开始支持客户端竞价。若集成版本低于1.11.0版本且瀑布流仅配置客户端竞价广告源会导致无法下发广告源；
                  3.用户关闭个性化广告推荐后,ToBid过滤不支持个性化广告的广告网络导致分组为空；
                  4.瀑布流仅配置原生广告源转换渲染的广告源，此功能依赖 SDK 3.0.0 版本及其以上。低版本将被过滤；
          """;

  /** 曝光宏参数 */
  @Getter
  @AllArgsConstructor
  public enum TrackClientMacro {
    /** event */
    EVENT("_EVENT_", "event"),
    /** timestamp */
    TIMESTAMP("_TIMESTAMP_", "t"),
    /** 三方adn的应用id */
    THIRD_CHANNEL_ID("_THIRDCHANNEL_", "thirdChannel"),
    /** 三方adn的应用id */
    THIRD_APP_ID("_THIRDAPPID_", "thirdAppId"),
    /** thirdPlacementId */
    THIRD_PLACEMENT_ID("_AGGRPLACEMENTID_", "thirdPlacementId"),
    /** 是否header bidding 1:是，0:不是 */
    HB("_HB_", "hb"),
    /** ECPM */
    ECPM("_ECPM_", "ecpm");

    private final String macro;
    private final String name;
  }

  /** 媒体服务端宏参数 */
  @Getter
  @AllArgsConstructor
  public enum MmpCallbackParam {
    /** D */
    D("d"),
    /** loadID */
    LOAD_ID("loadId"),
    APP_ID("appId"),
    /** 聚合广告位id */
    PLACEMENT_ID("placementId"),
    PLATFORM("platform"),
    USER_CODE("userCode"),
    ;

    private final String name;
  }

  /** 媒体服务端宏参数 */
  @Getter
  @AllArgsConstructor
  public enum ToBidStrategyCategory {
    /** v5 */
    V5("v5"),
    /** v6 */
    V6("v6"),
    WATERFALL("waterfall"),
    ;

    private final String name;
  }

  /** ToBidRouterPath */
  @Getter
  @AllArgsConstructor
  public enum ToBidRouterPath {
    /** v5 */
    V5("/strategy/v5"),
    W_CONFIG("/w/config"),
    WINDMILL_CONFIG("/windmill/config"),
    /** v6 */
    V6("/strategy/v6"),

    WATERFALL("/waterfall/v1"),
    ;

    private final String path;
  }

  public static final class ExtOptions {
    public static final String USER_SOURCE = "user_source";
    public static final String CHANNEL = "channel";
    public static final String SUB_CHANNEL = "sub_channel";

    /** installTime=1679369089 */
    public static final String INSTALL_TIME = "installTime";

    /** 定义值为 gameVersionCode，该值为int值 */
    public static final String APP_VERSION_CODE = "gameVersionCode";
  }

  /** 增强校验参数 */
  @Getter
  public static class MaskKey {

    public static String loadId = "load_id";
    public static String ecpm = "ecpm";
    public static String timestamp = "timestamp";
    public static String placementId = "placement_id";
    public static String channelId = "channel_id";
    public static String thirdPlacementId = "aggr_placement_id";
    public static String userId = "user_id";
    public static String checkMask = "check_mask";
    public static String sdkVersion = "sdk_version";
  }

  /** 634打点参数（通过dc_options实现） */
  @Getter
  public static class DataLogDcOptionsKey {
    // 是否命中防刷策略过滤
    public static String filter = "filter";

    public static String isChangeParallelSwitch = "is_change_parallel_switch";
    public static String openParallelProb = "open_parallel_prob";
  }
}
