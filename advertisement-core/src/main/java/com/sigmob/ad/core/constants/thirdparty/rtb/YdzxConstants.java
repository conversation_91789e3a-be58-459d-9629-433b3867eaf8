package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse.InteractType;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/** <AUTHOR> @Date 2021/5/24 2:16 下午 @Version 1.0 @Description */
public class YdzxConstants {

  /** 激励视屏最大时间 —— 60秒 */
  public static final int MAX_VIDEO_DURATION = 60;

  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(9).setMicro(7).setVersionStr("1.9.8").build();

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  /** 默认支持动作类型 */
  public static final List<Integer> supportActionList =
      List.of(
          InteractType.WEB_VIEW_VALUE,
          InteractType.APP_DOWNLOAD_VALUE,
          InteractType.DEEP_LINK_VALUE);

  /** 广告模版类型 */
  @AllArgsConstructor
  @Getter
  public enum Template {
    SPLASH(101),
    SPLASH_GIF(102),
    SPLASH_VIDEO(103);

    final int id;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    UNKNOWN(0),
    IPHONE(1),
    ANDROID_PHONE(2),
    IPAD(3),
    WINDOWS_PHONE(4),
    ANDROID_PAD(5),
    TV(6);

    final int code;
  }

  /** 联网类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    ETHERNET(1),
    WIFI(2),
    CELLULAR(3),
    CELL_2G(4),
    CELL_3G(5),
    CELL_4G(6);

    final int code;
  }

  /** 运营商 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    MOBILE("中国移动"),
    UNICOM("中国联通"),
    TELECOM("中国电信");

    final String name;
  }
}
