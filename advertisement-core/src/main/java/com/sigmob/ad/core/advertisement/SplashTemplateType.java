/** */
package com.sigmob.ad.core.advertisement;

/**
 * 广告类型枚举类 1、激励视频
 *
 * <AUTHOR>
 */
public enum SplashTemplateType {

  /** 带logo的模版 */
  LOGO(1),
  /** 不带logo的模版 */
  NO_LOGO(2),
  /** COMPANION */
  COMPANION(3),

  /** 普通 */
  COMMON(4),

  /** 摇一摇 */
  SHAKE(5);

  private final int type;

  SplashTemplateType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    SplashTemplateType[] types = values();
    for (SplashTemplateType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static SplashTemplateType getType(int i) {
    SplashTemplateType[] types = values();
    for (SplashTemplateType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return LOGO;
  }
}
