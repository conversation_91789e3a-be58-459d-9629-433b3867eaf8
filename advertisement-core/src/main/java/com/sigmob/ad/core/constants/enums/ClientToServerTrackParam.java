package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户端竞价广告返回track中额外参数
 *
 * @see <a href="http://wiki.sigmob.cn/pages/viewpage.action?pageId=63736749">...</a>
 */
@AllArgsConstructor
@Getter
public enum ClientToServerTrackParam {

  /** 开发者传入的结算价格 */
  settlement_publisher_price("settlement_publisher_price", "_PUBLISHERPRICE_"),

  /** 瀑布流次高价 */
  any_highest_loss_price("analysis_highest_loss_price", "_HIGHESTLOSSPRICE_"),
  /**
   * 开发者传入的币种：
   *
   * <p>CNY：人民币
   *
   * <p>USD：美元
   */
  settlement_publisher_cur("settlement_publisher_cur", "_CURRENCY_");

  final String key;

  final String macro;
}
