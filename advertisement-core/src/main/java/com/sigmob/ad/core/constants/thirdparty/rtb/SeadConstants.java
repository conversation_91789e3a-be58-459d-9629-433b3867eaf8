package com.sigmob.ad.core.constants.thirdparty.rtb;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2022/5/11 17:07 @Description
 */
public class SeadConstants {

  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN(0),
    CUCC(1),
    CMCC(2),
    CTCC(3);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    CELLULAR(1),
    WIFI(2),
    ETHERNET(3);

    final int code;
  }
}
