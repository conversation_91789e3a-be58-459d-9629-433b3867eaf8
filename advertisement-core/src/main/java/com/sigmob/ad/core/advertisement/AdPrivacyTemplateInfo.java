package com.sigmob.ad.core.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Date 2021/7/4 11:45 下午 @Version 1.0 @Description
 */
@AllArgsConstructor
@Getter
public enum AdPrivacyTemplateInfo {
  APP_NAME("app_name"),

  APP_SIZE("app_size"),

  APP_VERSION("app_version"),

  APP_COMPANY("app_company"),

  APP_PRIVACY_URL("app_privacy_url"),

  APP_PRIVACY_TEXT("app_privacy_text"),

  APP_PERMISSION_URL("app_permission_url"),

  APP_LANDING_PAGE_URL("app_landingpage"),

  APP_PERMISSION("app_permission"),

  APP_FUNCTION("app_func"),

  APP_FUNCTION_URL("app_func_url");

  final String name;
}
