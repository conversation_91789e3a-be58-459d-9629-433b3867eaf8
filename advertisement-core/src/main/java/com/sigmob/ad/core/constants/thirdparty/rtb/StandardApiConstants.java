package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.google.common.collect.Lists;
import com.sigmob.ad.core.advertisement.CreativeType;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** SSP标准API常数 */
public class StandardApiConstants {

  /** api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(4).setMicro(0).setVersionStr("1.6.0").build();

  /** 默认素材有效期：7200秒 */
  public static final int DEFAULT_AD_EXPIRE_TIME = 7200;

  /** 默认最大处理请求时间：300ms */
  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  public static final String MACRO_DSP_CALLBACK_PRICE = "_AUCTION_PRICE_";

  /** 爱思助手开发者id */
  public static final String AISIZHUSHOU_DEVELOPER_ID = "13804";

  /** 支持的广告类型 */
  public static final List<Integer> SUPPORT_AD_TYPES =
      Lists.newArrayList(
          AdType.REWARDED_VIDEO.getCode(),
          AdType.SPLASH.getCode(),
          AdType.FULL_SCREEN_VIDEO.getCode(),
          AdType.BANNER.getCode(),
          AdType.NATIVE.getCode());

  public static final Map<Integer, List<Integer>> SUPPORT_MATERIAL_TYPES =
      Map.of(
          com.sigmob.ad.core.advertisement.AdType.REWARDED_VIDEO.getTypeNum(),
          List.of(CreativeType.VIDEO_AND_IMAGE.getTypeNum()),
          com.sigmob.ad.core.advertisement.AdType.SPLASH.getTypeNum(),
          List.of(CreativeType.SPLASH_IMAGE.getTypeNum(), CreativeType.ONLY_VIDEO.getTypeNum()),
          com.sigmob.ad.core.advertisement.AdType.FULL_SCREEN_VIDEO.getTypeNum(),
          List.of(CreativeType.VIDEO_AND_IMAGE.getTypeNum()),
          com.sigmob.ad.core.advertisement.AdType.BANNER.getTypeNum(),
          List.of(
              CreativeType.SPLASH_IMAGE.getTypeNum(),
              CreativeType.ONLY_VIDEO.getTypeNum(),
              CreativeType.IMAGE_AND_TEXT.getTypeNum(),
              CreativeType.HTML_URL.getTypeNum(),
              CreativeType.HTML_SRC.getTypeNum(),
              CreativeType.TEXT_LINK.getTypeNum()),
          com.sigmob.ad.core.advertisement.AdType.NATIVE.getTypeNum(),
          List.of(CreativeType.NATIVE.getTypeNum()));

  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum AdType {
    REWARDED_VIDEO(1),

    SPLASH(2),

    FULL_SCREEN_VIDEO(3),

    INTERSTITIAL(4),

    BANNER(5),

    NATIVE(6);

    private final int code;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {

    /** 移动设备 */
    MOBILE(1),

    /** 手机 */
    PHONE(2),

    /** 平板 */
    TABLET(3);

    private final int code;
  }

  /** 是否支持http */
  @AllArgsConstructor
  @Getter
  public enum SupportHttp {

    /** 不支持 */
    NO(0),

    /** 支持 */
    YES(1);

    private final int code;
  }

  /** 广告投放目标类型 */
  @AllArgsConstructor
  @Getter
  public enum AdGoal {

    /** 忽略不计 */
    IGNORE(0),

    /** 已安装 */
    INSTALLED(1),

    /** 未安装 */
    NOT_INSTALL(2);

    private final int code;
  }

  /** 是否测试广告流量 */
  @AllArgsConstructor
  @Getter
  public enum TestRequest {

    /** 非测试 */
    NO(0),

    /** 测试 */
    YES(1);

    private final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum SspDecisionCode {
    FAIL(0),
    WIN(1),
    APP_INSTALLED(1000),
    DEEPLINK_APP_NOT_INSTALLED(1002),
    APP_SIZE_TOO_LARGE(1003),
    TIME_OUT(2000),
    AD_MATERIAL_INVALID(2001);

    final int code;
  }
}
