package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Charsets;
import com.google.common.io.ByteSource;
import com.google.common.io.Files;
import com.google.common.io.LineProcessor;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystemNotFoundException;
import java.util.HashMap;
import java.util.Map;

/**
 * 读取iso3361文件（国家英文简写代码和国家数字代码的映射文件）
 * 并根据广协ip地址库中的regionCode转换出国家数字码（第2位到4位，共3位），再根据国家数字码找到英文简写代码（目前提供二位的简写码）
 *
 * <AUTHOR>
 */
public class CountryCodeManager {

  private static CountryCodeManager instance = new CountryCodeManager();
  private volatile boolean initialized = false;
  private Map<String, CountryCode> codeMaps;

  public static void init(File basePath) {
    synchronized (CountryCodeManager.class) {
      if (instance.initialized) {
        return;
      }

      File cityFile = new File(basePath, Constants.COUNTRY_CODE_FILE);
      if (!cityFile.exists()) {
        throw new FileSystemNotFoundException("can not find " + Constants.COUNTRY_CODE_FILE);
      }

      ReadCountryCode rr = new ReadCountryCode();
      try {
        Files.readLines(cityFile, Charsets.UTF_8, rr);
        instance.codeMaps = rr.getResult();
        instance.initialized = true;
      } catch (IOException e) {
        throw new RuntimeException(
            "can not initiallize " + Constants.COUNTY_REGION_FILE + ",error:" + e.getMessage(), e);
      }
    }
  }

  public static void init(String basePath) {
    synchronized (CountryCodeManager.class) {
      if (instance.initialized) {
        return;
      }

      String filePath = basePath + File.separator + Constants.COUNTRY_CODE_FILE;
      ClassPathResource resource = new ClassPathResource(filePath);
      if (!resource.exists()) {
        throw new FileSystemNotFoundException("can not find " + Constants.COUNTRY_CODE_FILE);
      }

      ReadCountryCode rr = new ReadCountryCode();
      try {
        InputStream inputStream = resource.getInputStream();
        ByteSource byteSource =
            new ByteSource() {
              @Override
              public InputStream openStream() throws IOException {
                return inputStream;
              }
            };
        byteSource.asCharSource(Charsets.UTF_8).readLines(rr);
        instance.codeMaps = rr.getResult();
        instance.initialized = true;
      } catch (IOException e) {
        throw new RuntimeException(
            "can not initiallize " + Constants.COUNTY_REGION_FILE + ",error:" + e.getMessage(), e);
      }
    }
  }

  static class ReadCountryCode implements LineProcessor<Map<String, CountryCode>> {
    private Map<String, CountryCode> codeMaps = new HashMap<String, CountryCode>();

    @Override
    public boolean processLine(String line) throws IOException {
      if (line != null) {
        String[] contents = line.trim().split(",");
        if (contents != null && contents.length >= 2) {
          //					RegionType type = Utils.parseRegionTypeByRegionCode(contents[0]);
          //					regions.addRegion(type, contents[0], contents[1]);
          try {
            String alphaCode = contents[0].trim();
            int numeric = Integer.valueOf(contents[1]);
            String numericCode = String.format("%03d", numeric);
            CountryCode cc = new CountryCode();
            cc.setAlphaCode(alphaCode);
            cc.setNumericCode(numericCode);
            codeMaps.put(numericCode, cc);
          } catch (Exception e) {

          }
        }
      }
      return true;
    }

    @Override
    public Map<String, CountryCode> getResult() {
      return codeMaps;
    }
  }

  /**
   * 根据国家数字代码，获取国家英文简写代码
   *
   * @param numericCode 3位国家代码，不足3位前补0
   * @return
   */
  public static String getCountryAlphaCode(String numericCode) {
    if (!instance.initialized) {
      return null;
    }

    CountryCode cc = instance.codeMaps.get(numericCode);
    if (cc != null) {
      return cc.getAlphaCode();
    }

    return null;
  }
}
