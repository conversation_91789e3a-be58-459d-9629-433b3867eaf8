package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.google.common.collect.Sets;
import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/** 美数API常量定义 */
public class AdxDataConstants {

  /** 激励视屏最大时间 —— 60秒 */
  public static final int MAX_VIDEO_DURATION = 60;

  /** 美数 rtb api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(3).setMinor(11).setMicro(0).setVersionStr("3.15.0").build();

  public static final String responseAppVersion = "1.0.0";

  /** 默认最大请求时间 - 300毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  /** */
  public static final List<Integer> DEFAULT_ACTION_LIST =
      List.of(RtbResponse.InteractType.WEB_VIEW_VALUE);

  /** 支持的原生广告模版 */
  public static final Set<String> SUPPORTED_NATIVE_TEMPLATES =
      Sets.newHashSet(Template.ONE_IMAGE_ONE_TEXT.getId(), Template.NATIVE_FEED_VIDEO.getId());

  public static final Integer SIZE_640 = 640;

  public static final Integer SIZE_720 = 720;

  public static final Integer SIZE_960 = 960;

  public static final Integer SIZE_1280 = 1280;

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {
            AdMacro.CLICK_DOWN_X,
            AdMacro.CLICK_DOWN_Y,
            AdMacro.CLICK_UP_X,
            AdMacro.CLICK_UP_Y,
            AdMacro.TIMESTAMP,
            AdMacro.TIMEMILLIS
          },
          Constants.MACRO_INFO_VALUES,
          new String[] {
            Macro.DOWN_X.getName(),
            Macro.DOWN_Y.getName(),
            Macro.UP_X.getName(),
            Macro.UP_Y.getName(),
            Macro.MS_EVENT_SEC.getName(),
            Macro.MS_EVENT_MSEC.getName()
          });

  /** 服务运行模式 */
  @AllArgsConstructor
  @Getter
  public enum ApplicationMode {

    /** 生产模式 */
    PRODUCTION(0),

    /** 测试模式 */
    TEST(1);

    private final int code;
  }

  /** 标记返回链接是否必须https */
  @AllArgsConstructor
  @Getter
  public enum Secure {
    /** 0-否 */
    NOT_SUPPORT_HTTPS(0),
    /** 1-是 */
    SUPPORT_HTTPS(1);

    private final int code;
  }

  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum ImpType {

    /** 横幅 */
    BANNER(0),

    /** 开屏 */
    SPLASH(1),

    /** 插屏 */
    INTERSTITIAL(3),

    /** 视频贴片 */
    PRE_MOVIE(4),

    /** 原生 */
    NATIVE(5),

    /** VR 广告 */
    VR(6),

    /** 激励视频 */
    REWARDED_VIDEO(7);

    private final int type;
  }

  @AllArgsConstructor
  @Getter
  public enum Support {
    /** 未支持 */
    UNKNOWN(0),

    /** 支持 */
    YES(1),

    /** 不支持 */
    NO(2);

    final int code;
  }

  /** 媒体文件格式 */
  @AllArgsConstructor
  @Getter
  public enum MimeType {
    JPG("jpg"),
    PNG("png"),
    GIF("gif"),
    JPEG("jpeg"),
    MP4("mp4"),
    FLV("flv");

    private final String name;
  }

  /** 售卖方式 */
  @AllArgsConstructor
  @Getter
  public enum BidType {

    /** CPM售卖 */
    CPM(0),

    /** CPC售卖 */
    CPC(1);

    private final int code;
  }

  /** 广告位支持的交互类型 */
  @AllArgsConstructor
  @Getter
  public enum InteractType {

    /** 网页打开;(默认) */
    WEB_VIEW(1),

    /** 网页打开+点击下载; */
    APP_DOWNLOAD(2),

    /** 网页打开+deeplink; */
    DEEPLINK(3),

    /** 网页打开+点击下载+deeplink */
    DEEP_LINK_AND_OPEN(4);

    private final int code;
  }

  /** 运营商 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    MOBILE(46000, "中国移动"),

    UNICOM(46001, "中国联通"),

    TELECOM(46002, "中国电信"),

    UNKNOWN(-1, "未知");

    private final int code;

    private final String name;
  }

  /** 网络类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {

    /** 未知 */
    UNKNOWN(0),

    /** Ethernet */
    ETHERNET(1),

    /** Wifi */
    WIFI(2),

    /** 蜂窝数据网络-未知 */
    CELL_UNKNOWN(3),

    /** 蜂窝数据网络-2G */
    CELL_2G(4),

    /** 蜂窝数据网络-3G */
    CELL_3G(5),

    /** 蜂窝数据网络-4G */
    CELL_4G(6),

    /** 蜂窝数据网络-5G */
    CELL_5G(7);

    private final int code;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {

    /** 未知 */
    UNKNOWN(0),

    /** 移动手机 */
    PHONE(2),

    /** 平板电脑 */
    TABLET(3),

    /** 互联网电视 */
    TV(6);

    private final int code;
  }

  /** 操作系统类型 */
  @AllArgsConstructor
  @Getter
  public enum OsType {

    /** Android */
    ANDROID("Android"),

    /** iOS */
    IOS("iOS"),

    /** wp */
    WP("WP"),

    /** others */
    OTHERS("others");

    private final String name;
  }

  /** 性别 */
  @AllArgsConstructor
  @Getter
  public enum Gender {
    /** 男 */
    MALE("M"),

    /** 女 */
    FEMALE("F"),

    /** 未知 */
    UNKNOWN("U");

    private final String name;
  }

  /** 到达页 durl 打开类型 */
  @AllArgsConstructor
  @Getter
  public enum Adck {
    /** 网页类型(默认) */
    WEB_VIEW(1),

    /** 下载类型 */
    DOWNLOAD(2);

    private final int code;
  }

  /** 创意类型 */
  @AllArgsConstructor
  @Getter
  public enum AdType {
    /** 图片 */
    IMAGE(1),
    /** 视频 */
    VIDEO(2);
    private final int code;
  }

  /** 落地页、点击监测宏 */
  @AllArgsConstructor
  @Getter
  public enum Macro {
    /** 用户手指按下时的横坐标. */
    DOWN_X("__DOWN_X__"),

    /** 用户手指按下时的纵坐标. */
    DOWN_Y("__DOWN_Y__"),

    /** 用户手指离开设备屏幕时的横坐标 */
    UP_X("__UP_X__"),

    /** 用户手指离开设备屏幕时的纵坐标 */
    UP_Y("__UP_Y__"),

    /** 客户端触发时间戳(单位:秒) */
    MS_EVENT_SEC("__MS_EVENT_SEC__"),

    /** 客户端触发时间戳(单位:毫秒) */
    MS_EVENT_MSEC("__MS_EVENT_MSEC__");

    private final String name;
  }

  /** 屏幕方向 */
  @AllArgsConstructor
  @Getter
  public enum Orientation {
    UNKNOWN(-1), // 未知
    PORTRAIT(0), // 竖屏
    LANDSCAPE(1); // 横屏

    private final int code;
  }

  /** 广告模版 */
  @AllArgsConstructor
  @Getter
  public enum Template {
    /** 一图一文 */
    ONE_IMAGE_ONE_TEXT("1-1"),
    /** 图文摘要 */
    IMAGE_AND_TEXT_DIGEST("1-2"),
    /** 三图一文 */
    THREE_IMAGE_ONE_TEXT("1-4"),
    /** 两图一文 */
    TWO_IMAGE_ONE_TEXT("1-8"),
    /** 原生信息流视频 */
    NATIVE_FEED_VIDEO("2-2"),
    /** 音频贴片 */
    PRE_MOVIE_AUDIO("2-3"),
    /** 激励视频 */
    REWARDED_VIDEO("2-4");

    private final String id;
  }

  /**
   * 广告位支持的交互类型:<br>
   * 1 - 网页打开;(默认)<br>
   * 2 - 网页打开+点击下载;<br>
   * 3 - 网页打开+deeplink;<br>
   * 4 - 网页打开+点击下载+deeplink
   */
  @AllArgsConstructor
  @Getter
  public enum AType {
    WEB_PAGE(1),

    WEB_PAGE_DOWNLOAD(2),

    WEB_PAGE_DEEPLINK(3),

    WEB_PAGE_DOWNLOAD_DEEPLINK(4);

    final int code;
  }
}
