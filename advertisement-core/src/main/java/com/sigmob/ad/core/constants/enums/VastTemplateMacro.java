package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** <AUTHOR> @Date 2021/6/18 2:50 下午 @Version 1.0 @Description */
@AllArgsConstructor
@Getter
public enum VastTemplateMacro {
  AD_TITLE("{{AD_TITLE}}"),
  AD_DESCRIPTION("{{AD_DESC}}"),
  IMPRESSION_URL("{{IMPRESSION_URL}}"),
  CREATIVE_ID("{{CREATIVE_ID}}"),
  VIDEO_DURATION("{{VIDEO_DURATION}}"),
  TRACKING_EVENT_START("{{TRACKING_EVENT_START}}"),
  TRACKING_EVENT_CLICK("{{TRACKING_EVENT_CLICK}}"),
  TRACKING_EVENT_COMPLETE("{{TRACKING_EVENT_COMPLETE}}"),
  LANDING_PAGE("{{CLICK_THROUGH}}"),
  MEDIA_FILE_DELIVERY("{{MEDIA_FILE_DELIVERY}}"),
  MEDIA_FILE_TYPE("{{MEDIA_FILE_TYPE}}"),
  MEDIA_FILE_WIDTH("{{MEDIA_FILE_WIDTH}}"),
  MEDIA_FILE_HEIGHT("{{MEDIA_FILE_HEIGHT}}"),
  MEDIA_FILE_URL("{{MEDIA_FILE_URL}}");

  final String name;
}
