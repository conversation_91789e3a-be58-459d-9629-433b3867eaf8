package com.sigmob.ad.core.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * api流量广告返回track中额外参数
 *
 * @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=34082598
 * @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=34083003
 * @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=34083302
 * @see http://wiki.sigmob.cn/pages/viewpage.action?pageId=37559353
 */
@AllArgsConstructor
@Getter
public enum ApiTrackParam {
  /** supply 结算价格 */
  SUPPLY_PRICE("s_price"),
  /** wiki: http://wiki.sigmob.cn/pages/viewpage.action?pageId=40076421 */
  /** 标识流量是否需要走全局扣减+分成结算: 0-平台配置结算；1-竞价结算; 2-参与分成，每条曝光的结算价格打在数据中。走固定ecpm结算，媒体结算价以随机数形式/衰减算法上下波动 */
  SUPPLY_PROFIT_SHARING_EFFECTIVE("s_effective"),
  /** 中控配置广告位结算模式 */
  SUPPLY_SETTLEMENT_SETTING("settlement"),
  /** 美数上线时给数据用于区分track是oppo还是美数，取值参考{@link RequestFlowType} */
  FLOW_TYPE("f_type"),
  /** 媒体侧计费方式，取值参考{@link com.sigmob.ad.core.advertisement.BidType} */
  SUPPLY_CHARGE_MODE_TYPE("s_type"),
  /** 竞价价格类型；取值参考{@link com.sigmob.ad.core.constants.enums.AuctionType} */
  AD_BID_PRICE_TYPE("ad_bidtype"),
  /**
   *
   */
  COMMER_ECPM_STATE("becpm_state");

  final String name;
}
