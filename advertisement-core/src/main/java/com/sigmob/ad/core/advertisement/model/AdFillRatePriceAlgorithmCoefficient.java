package com.sigmob.ad.core.advertisement.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 媒体广告填充率调价算法系数
 *
 * <AUTHOR> @Date 2023/3/2 16:39 @Description
 */
@Getter
@Builder
public class AdFillRatePriceAlgorithmCoefficient {

  /** 调整后价格 */
  private int adjustedPrice;

  /** 上一次总请求数 */
  private long lastTotalReqCount;

  /** Req_m = Req_m * w_0 +(req - Req_last) */
  private long tempReqCount;

  /** 上一次总填充数 */
  private long lastTotalAdFillCount;

  /** Res_m = Res_m * w_0 +(res - Res_last) */
  private long tempAdFillCount;

  /** 本次计算后的调价因子 */
  private double nowVErr;
}
