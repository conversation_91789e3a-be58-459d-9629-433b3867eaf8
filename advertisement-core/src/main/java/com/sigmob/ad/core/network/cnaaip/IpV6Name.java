package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Strings;
import java.math.BigInteger;

/**
 * cnaa地址库中，ip范围和region code的对应关系
 * 
 * <AUTHOR>
 *
 */
public class IpV6Name extends IpName implements Comparable<IpV6Name> {

	private BigInteger upperIpNum;
	private BigInteger lowerIpNum;
	private String name;

	/**
	 * 如果lowerIp upperIp regionCode 任意参数为空，则返回null
	 * 
	 * @param lowerIp
	 * @param upperIp
	 * @param regionCode
	 * @return
	 */
	public static IpV6Name build(String lowerIp, String upperIp, String regionCode) {
		if (Strings.isNullOrEmpty(lowerIp) || Strings.isNullOrEmpty(upperIp) || Strings.isNullOrEmpty(regionCode)) {
			return null;
		}
		BigInteger lowerIpNum = Utils.ipv6StrToBigInteger(lowerIp.trim());
		
		if (lowerIpNum == null) {
			return null;
		}
		BigInteger upperIpNum = Utils.ipv6StrToBigInteger(upperIp.trim());

		if (upperIpNum == null) {
			return null;
		}
		IpV6Name irc = new IpV6Name();
		irc.lowerIpNum = lowerIpNum;
		irc.upperIpNum = upperIpNum;
		irc.name = regionCode.trim();
		return irc;

	}

	@Override
	public int compareTo(IpV6Name o) {
		return upperIpNum.compareTo(o.upperIpNum);
	}

	public BigInteger getUpperIpNum() {
		return upperIpNum;
	}

	public void setUpperIpNum(BigInteger upperIpNum) {
		this.upperIpNum = upperIpNum;
	}

	public BigInteger getLowerIpNum() {
		return lowerIpNum;
	}

	public void setLowerIpNum(BigInteger lowerIpNum) {
		this.lowerIpNum = lowerIpNum;
	}

	@Override
	public String getName() {
		return name;
	}

	public void setName(String regionCode) {
		this.name = regionCode;
	}
}
