package com.sigmob.ad.core.advertisement.model;

import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> @Date 2021/7/6 8:41 下午 @Version 1.0 @Description
 */
@Getter
@Setter
@Builder
public class AdPrivacyModel {

  private String appName;

  private String appSize;

  private String appVersion;

  private String appCompany;

  private String appPrivacyUrl;

  private String appPrivacyText;

  private String appPermissionUrl;

  private String appPermission;

  private String appDesc;

  private String appDescUrl;

  private String appLandingpage;

  private String privacyWithBtnUrl;

  public boolean isEmpty() {
    return Strings.isNullOrEmpty(appName)
        && Strings.isNullOrEmpty(appSize)
        && Strings.isNullOrEmpty(appVersion)
        && Strings.isNullOrEmpty(appCompany)
        && Strings.isNullOrEmpty(appPrivacyUrl)
        && Strings.isNullOrEmpty(appPrivacyText)
        && Strings.isNullOrEmpty(appPermissionUrl)
        && Strings.isNullOrEmpty(appLandingpage)
        && Strings.isNullOrEmpty(appPermission)
        && Strings.isNullOrEmpty(privacyWithBtnUrl)
        && Strings.isNullOrEmpty(appDesc)
        && Strings.isNullOrEmpty(appDescUrl);
  }
}
