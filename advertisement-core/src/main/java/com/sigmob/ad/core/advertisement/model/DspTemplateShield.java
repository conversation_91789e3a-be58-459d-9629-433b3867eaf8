package com.sigmob.ad.core.advertisement.model;

import com.google.gson.annotations.Expose;
import java.util.List;
import lombok.Data;

@Data
public class DspTemplateShield {

  /** 模板id */
  @Expose private Integer templateId;

  /** 1:app，2:sdk版本，3:全局配置 */
  @Expose(serialize = false)
  private Integer type;

  @Expose private MediaShield mediaShield;

  @Expose private SdkVersionShield sdkVersionShield;

  @Expose private GlobalShield globalShield;

  @Data
  public static class MediaShield {

    @Expose private List<Integer> appSheildList;

    @Expose private List<String> slotSheildList;

    @Expose private List<String> regionCodeList;
  }

  @Data
  public static class SdkVersionShield {

    @Expose private List<VersionInfo> androidList;

    @Expose private List<VersionInfo> iosList;

    @Expose private List<String> regionCodeList;
  }

  @Data
  public static class VersionInfo {

    /** 类型：1:>= 2: =， 3<= */
    @Expose private Integer type;

    @Expose private String version;
  }

  @Data
  public static class GlobalShield {

    /** 1-ios; 2-android */
    @Expose private List<Integer> globalList;

    @Expose private List<String> regionCodeList;
  }
}
