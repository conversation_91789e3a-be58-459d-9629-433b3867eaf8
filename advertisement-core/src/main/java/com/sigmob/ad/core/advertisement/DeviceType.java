package com.sigmob.ad.core.advertisement;

/**
 * 设备类型
 *
 * <AUTHOR>
 */
public enum DeviceType {
  iPhone(1),
  iPad(2),
  iPod(3),
  AndroidPhone(4),
  AndroidPad(5);
  private final int type;

  DeviceType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    DeviceType[] types = values();
    for (DeviceType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static DeviceType getType(int i) {
    DeviceType[] types = values();
    for (DeviceType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return iPhone;
  }
}
