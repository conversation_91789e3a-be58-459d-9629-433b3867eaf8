package com.sigmob.ad.core.constants.enums;

import com.sigmob.ad.core.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流量类型
 *
 * <p>0: sdk<br>
 * 1: api-oppo<br>
 * 2: api-adxData<br>
 * 3: api-standard<br>
 * 4: api-moWeather<br>
 * 5: api-iReader<br>
 * 6: api-dongqiudi<br>
 * 7: api-eastday<br>
 * 8: api-douguo<br>
 * 9: api-maimai<br>
 * 10: api-kingSoft<br>
 * 11: api-mgTv<br>
 * 12: api-yidianzixun<br>
 * 13: api-vungle<br>
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum RequestFlowType {
  SDK(0, "SDK", "3.2.0"),
  OPPO(1, "oppoAdx", "1.0.0"),
  ADX_DATA(2, "adxData", "3.15.0"),
  STANDARD(3, "standard", "1.6.0"),
  MO_WEATHER(4, "moWeather", "2.5.3"),
  IREADER(5, "iReader", "5.0.2"),
  DONGQIUDI(6, "dongqiudi", "1.6.0"),
  EASTDAY(7, "eastDay", "1.0.4"),
  DOUGUO(8, "douguo", "3.15.0"),
  MAIMAI(9, "maimai", "1.0.6"),
  KINGSOFT(10, "kingSoft", "1.0.0"),
  MGTV(11, "mgTv", "3.4.1"),
  YDZX(12, "yidianzixun", "1.9.8"),
  VUNGLE(13, "vungle", "2.5.0"),
  HUAWEI(14, "huawei", "1.9.19"),
  DONGMAN(15, "dongman", "3.15.0"),
  XIMALAYA(16, "ximalaya", "1.0.0"),
  HW_SEAD(17, "hwSead", "1.0.0"),
  VIVO(18, "vivo", "7.9.1"),
  OPENRTB(19, "openRtb", "2.5.0"),
  YUEYOU(20, "yueyou", "1.2.0"),
  YOYO(21, "yoyo", "1.0.1"),
  GROMORE(22, "gromore", "1.0");

  private final int code;
  private final String name;
  private final String apiVersion;

  public static String getApiName(int requestFlowType, long developerId) {
    for (RequestFlowType rft : RequestFlowType.values()) {
      if (requestFlowType == rft.getCode()) {
        if (requestFlowType == RequestFlowType.STANDARD.getCode()) {
          return rft.getName() + Constants.SYMBOL_UNDERSCORE + developerId;
        } else {
          return rft.getName();
        }
      }
    }
    return "unknown";
  }
}
