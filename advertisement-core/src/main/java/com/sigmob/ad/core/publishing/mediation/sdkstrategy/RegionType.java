/**
 * 
 */
package com.sigmob.ad.core.publishing.mediation.sdkstrategy;

/**
 * 
 * <AUTHOR>
 *
 */
public enum RegionType {

	ALL(0),INCLUDE(1),EXCLUDE(2);
	private int type;

	private RegionType(int type) {
		this.type = type;
	}

	public int getTypeNum() {
		return this.type;
	}
	
	public static boolean isValidType(int typeNum) {
		RegionType types [] = values();
		for(RegionType type:types) {
			if(type.getTypeNum() == typeNum) {
				return true;
			}
		}
		return false;
	}
	
	public static RegionType getType(int i) {
		RegionType types [] = values();
		for(RegionType type:types) {
			if(type.getTypeNum() == i) {
				return type;
			}
		}
		return null;
	}
}
