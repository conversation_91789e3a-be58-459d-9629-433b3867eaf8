package com.sigmob.ad.core.advertisement.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/** formatType详细规格信息 */
@Getter
@Setter
public class FormatTypeInfo {

  private Integer id;

  private Video video;

  private Image image;

  @SerializedName("endcard_image")
  private Image endcardImage;

  private Icon icon;

  private Text title;

  private Text desc;

  @SerializedName("prod_name")
  private Text prodName;

  private Score score;

  @Getter
  @Setter
  public static class Video {
    private Boolean required;

    @SerializedName("min_duration")
    private Integer minDuration;

    @SerializedName("max_duration")
    private Integer maxDuration;

    private List<Size> size;
  }

  @Getter
  @Setter
  public static class Image {
    private Boolean required;

    private List<Size> size;
  }

  @Getter
  @Setter
  public static class Icon {
    private Boolean required;

    @SerializedName("min_ratio")
    private Double minRatio;

    @SerializedName("max_ratio")
    private Double maxRatio;
  }

  @Getter
  @Setter
  public static class Text {
    private Boolean required;

    @SerializedName("min_length")
    private Integer minLength;

    @SerializedName("max_length")
    private Integer maxLength;
  }

  @Getter
  @Setter
  public static class Size {

    @SerializedName("min_ratio")
    private Double minRatio;

    @SerializedName("max_ratio")
    private Double maxRatio;
  }

  @Getter
  @Setter
  public static class Score {
    private Boolean required;

    private Integer min;

    private Integer max;
  }
}
