package com.sigmob.ad.core.advertisement;

/**
 * 枚举类
 *
 * <AUTHOR>
 */
public enum VideoPlayMode {
  /** 0: 预加载 */
  LOCAL_CACHE(0),
  /** 1:边下边播（广告只要加载，素材就会下载）----（边播边下） */
  CACHE_STREAM(1),
  /** 2:边下边播（广告只在播放时，素材才会下载）---（流播） */
  STREAM(2);
  private final int type;

  VideoPlayMode(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    VideoPlayMode[] types = values();
    for (VideoPlayMode type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static VideoPlayMode getType(int i) {
    VideoPlayMode[] types = values();
    for (VideoPlayMode type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return LOCAL_CACHE;
  }
}
