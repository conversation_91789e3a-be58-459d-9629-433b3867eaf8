package com.sigmob.ad.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聚合竞价常量
 *
 * <AUTHOR>
 */
public class WindmillBiddingConstants {

  public static final String BID_ID = "bidId";

  public static final String PLACEMENT_ID = "placementId";

  public static final String SCENE_ID = "sceneId";

  public static final String STRATEGY_ID = "strategyId";

  public static final String STRATEGY_LOAD_ID = "load_id";

  public static final String ELEMENT_ID = "elementId";

  public static final String EXPERIMENT_ID = "experiment_id";
  public static final String SUB_EXPERIMENT_ID = "sub_experiment_id";
  public static final String NO_HB_PRICES = "no_hb_prices";
  public static final String HAS_INTELLIGENCE = "hasIntelligence";
  public static final String EXPIRE_TIME = "expireTime";
  public static final Integer DEFAULT_EXPIRE_TIME = 300;
  public static final String AB_FLAG = "abFlag";

  public static final String THIRD_OPTION_APPID = "thirdOptionAppid";

  public static final String SIG_SDK_VERSION = "sdk_version";

  /** 2.12.0版本支持 */
  public static final String SIG_FILTER = "filter";

  public static final String FILTER_BY_DEL = "del";

  public static final String PRICE = "price";

  public static final String SERVER_BIDDING_TIMEOUT = "serverBiddingTimeout";

  public static final String BIDDING_FLOOR = "biddingFloor";

  public static final String BIDDING_TYPE = "biddingType";

  @AllArgsConstructor
  @Getter
  public enum BiddingType {
    /** c-s */
    CLIENT("1"),
    SERVER("0");

    private final String type;
  }

  public static class KuaiShouParameter {

    public static final String SDK_TOKEN = "sdkToken";

    public static final String MEDIUM_ID = "mediumId";
  }

  public static class GdtParameter {

    public static final String BUYER_ID = "buyer_id";

    public static final String SDK_INFO = "sdk_info";

    public static final String THR_MEI_SWITCH = "thrMeiSwitch";
  }

  public static class MtgParameter {

    public static final String BUYER_UID = "buyeruid";

    /** 集成的MTG-SDK version */
    public static final String DISPLAY_MANAGER_VER = "displaymanagerver";

    public static final String BANNER_TYPE = "bannerType";
  }

  public static class SigParameter {

    public static final String SIGMOB_SDK_VERSION = "sdkVersion";

    public static final String SDK_TOKEN = "sdkToken";
  }

  public static class BayesParameter {

    public static final String SDK_INFO = "sdkInfo";

    public static final String URL_AES_KEY = "urlAk";
  }

  /** 统一胜出价宏 */
  public static final String MACRO_AUCTION_PRICE = "__AUCTION_PRICE__";

  /** 优量汇固定notify_url */
  public static final String GDT_FIXED_NOTIFY_URL =
      "https://win.gdt.qq.com/win_notice.fcg?viewid=&position_id=__POSITION_ID__&loss=2&win_price=__AUCTION_PRICE__&server_bidding_type=__SERVER_BIDDING_TYPE__&win_seat=__AUCTION_SEAT_ID__&mrqid=__THR_MEI__&meSrc=499";
}
