package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse;
import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 金山WPS API常量 */
public class KingSoftConstants {

  /** 金山WPS API版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(0).setMicro(0).setVersionStr("1.0.0").build();

  /** 默认最大请求时间 - 300毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  /** 赢价回调通知价格宏 */
  public static final String MACRO_WIN_NOTICE_PRICE = "{bid_price}";

//  /** 默认支持动作类型 */
//  public static final List<Integer> SUPPORT_ACTION_LIST =
//      List.of(
//          RtbResponse.InteractType.WEB_VIEW_VALUE,
//          RtbResponse.InteractType.APP_DOWNLOAD_VALUE,
//          RtbResponse.InteractType.DEEP_LINK_VALUE);

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {
            AdMacro.TIMESTAMP,
            AdMacro.TIMEMILLIS,
            AdMacro.CLICK_DOWN_X,
            AdMacro.CLICK_DOWN_Y,
            AdMacro.CLICK_UP_X,
            AdMacro.CLICK_UP_Y
          },
          Constants.MACRO_INFO_VALUES,
          new String[] {
            Macro.__TS__.value,
            Macro.__TS__.value,
            Macro.__DOWN_X__.value,
            Macro.__DOWN_Y__.value,
            Macro.__UP_X__.value,
            Macro.__UP_Y__.value
          });

  /** 广告位类型 */
  @AllArgsConstructor
  @Getter
  public enum BidType {
    /** 开屏 */
    SPLASH(1),

    /** 信息流大卡 */
    FEED_BIG_IMG(3),

    /** 信息流单图 */
    FEED_SINGLE_IMG(5),

    /** 视频开屏 */
    SPLASH_VIDEO(34);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum App {
    WPS_ANDROID("5"),
    WPS_IOS("27");

    final String name;
  }

  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    ETHERNET(1),
    WIFI(2),
    CELLULAR(3),
    CELL_2G(4),
    CELL_3G(5),
    CELL_4G(6);

    final int code;
  }

  /** 广告类型 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    WIFI("wifi"),
    UNICOM("中国联通"),
    MOBILE("中国移动"),
    TELECOM("中国电信");

    final String name;
  }

  /** 计费方式 */
  @AllArgsConstructor
  @Getter
  public enum ChargeType {
    CPM(1),
    CPC(2);

    final int code;
  }

  /** 宏 */
  @AllArgsConstructor
  @Getter
  public enum Macro {

    /** 广告请求的request id */
    REQUEST_ID("{request_id}"),

    /** 广告id */
    BID("{bid}"),

    /** 最终成交价，加密价格，单位（人民币 分） */
    BID_PRICE("{bid_price}"),

    /** 用户的ip */
    IP("{ip}"),

    /** 用户的设备id */
    DID("{did}"),

    __TS__("__TS__"),

    __DOWN_X__("__DOWN_X__"),

    __DOWN_Y__("__DOWN_Y__"),

    __UP_X__("__UP_X__"),

    __UP_Y__("__UP_Y__");

    final String value;
  }
}
