package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** <AUTHOR> @Date 2021/6/15 2:22 下午 @Version 1.0 @Description */
public class VungleConstants {

  /** 激励视屏最大时间 —— 60秒 */
  public static final int MAX_VIDEO_DURATION = 60;

  public static final Version API_VERSION =
      Version.newBuilder().setMajor(2).setMinor(5).setMicro(0).setVersionStr("2.5.0").build();

  public static final Version OPEN_RTB_VERSION =
      Version.newBuilder().setMajor(2).setMinor(5).setMicro(0).setVersionStr("2.5.0").build();

  public static final String RESPONSE_HEADER_PARAM_OPENRTB_VERSION = "X-OpenRTB-Version";

  /** 是否支持https */
  @AllArgsConstructor
  @Getter
  public enum Test {
    NO(0),
    YES(1);

    final int code;
  }

  /** 全屏/插屏 */
  @AllArgsConstructor
  @Getter
  public enum InstlType {
    FULL_SCREEN(0),
    INTERSTITIAL(1);

    final int code;
  }

  /** 操作系统 */
  @AllArgsConstructor
  @Getter
  public enum OS {
    IOS("iOS"),
    ANDROID("Android"),
    WINDOWS("Windows");

    final String name;
  }

  /** 设备类型 */
  @AllArgsConstructor
  @Getter
  public enum DeviceType {
    MOBILE_TABLET(1),
    PC(2),
    TV(3),
    PHONE(4),
    TABLET(5),
    CONNECTED_DEVICE(6),
    SET_TOP_BOX(7);

    final int code;
  }

  /** 网络连接类型 */
  @AllArgsConstructor
  @Getter
  public enum ConnectionType {
    UNKNOWN(0),
    ETHERNET(1),
    WIFI(2),
    CELL_UNKNOWN(3),
    CELL_2G(4),
    CELL_3G(5),
    CELL_4G(6);

    final int code;
  }

  /** 是否支持https */
  @AllArgsConstructor
  @Getter
  public enum SupportHttps {
    NO(0),
    YES(1);

    final int code;
  }

  /** 广告响应协议 */
  @AllArgsConstructor
  @Getter
  public enum Protocols {
    VAST_1_0(1),
    VAST_2_0(2),
    VAST_3_0(3),
    VAST_1_0_WRAPPER(4),
    VAST_2_0_WRAPPER(5),
    VAST_3_0_WRAPPER(6),
    VAST_4_0(7),
    VAST_4_0_WRAPPER(8),
    DAAST_1_0(9),
    DAAST_1_0_WRAPPER(10);

    final int code;

    public static Protocols getProtocol(int i) {
      Protocols[] protocols = values();
      for (Protocols protocol : protocols) {
        if (protocol.getCode() == i) {
          return protocol;
        }
      }
      return null;
    }
  }

  @AllArgsConstructor
  @Getter
  public enum ContentDeliveryMethod {
    STREAMING(1, "streaming"),
    PROGRESSIVE(2, "progressive"),
    DOWNLOAD(3, "download");

    final int code;
    final String name;
  }

  /** 竞价结果原因 */
  @AllArgsConstructor
  @Getter
  public enum NBR {
    /** Unknown Error */
    UNKNOWN(0),
    /** Technical Error */
    TECHNICAL_ERROR(1),
    /** Invalid Request */
    INVALID_REQUEST(2),
    /** Known Web Spider */
    KNOWN_WEB_SPIDER(3),
    /** Suspected Non-Human Traffic */
    CHEAT_TRAFFIC(4),
    /** Cloud, Data center, or Proxy IP */
    INVALID_IP(5),
    /** Unsupported Device */
    UNSUPPORTED_DEVICE(6),
    /** Blocked Publisher or Site */
    BLOCKED_PUBLISHER(7),
    /** Unmatched User */
    UNMATCHED_USER(8),
    /** Daily Reader Cap Met */
    USER_DAILY_CAP(9),
    /** Daily Domain Cap Met */
    DOMAIN_DAILY_CAP(10);

    final int code;
  }
}
