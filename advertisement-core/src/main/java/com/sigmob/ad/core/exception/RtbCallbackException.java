package com.sigmob.ad.core.exception;

/** 三方adx竞价回调通知异常 */
public class RtbCallbackException extends AdException {

  public RtbCallbackException() {
    this(0);
  }

  public RtbCallbackException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public RtbCallbackException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public RtbCallbackException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public RtbCallbackException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public RtbCallbackException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public RtbCallbackException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public RtbCallbackException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public RtbCallbackException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public RtbCallbackException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
