package com.sigmob.ad.core.publishing.windmill;

import lombok.Data;

import java.util.List;

@Data
public class StrategyScene {

  private String id;

  /** 名称 */
  private String name;

  /** 状态，0-关闭；1-开启 */
  private Integer state;

  /** 广告类型 */
  private Integer adType;

  /** 场景类型 */
  private Integer sceneType;

  /** Sigmob appId */
  private Integer appId;

  /** 按优先级排序后的策略列表 */
  private List<Integer> strategyIdList;

  /** 奖励数量 */
  private int rewardNum;

  /** 奖励名称 */
  private String rewardName;

  /** 奖励事件服务端回调地址 */
  private String rewardUrl;

  private String appSecurityKey;

  /** 强校验逻辑 */
  private int checkMask;
}
