package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 东方头条常量定义 */
public class EastDayConstants {

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  /** 东方头条 rtb api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(0).setMicro(4).setVersionStr("1.0.4").build();

  @AllArgsConstructor
  @Getter
  public enum StatusCode {
    OK(1000), // 取广告成功
    INTERNAL_ERROR(1002), // 内部处理异常
    NO_AD(1011), // DSP 匹配素材后无广告
    DROP_REQUEST(1012); // 被 DSP drop
    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum TrackingEvent {
    SHOW("1"),
    CL<PERSON>K("2"),
    DOWNLOAD_START("3"),
    DOWNLOAD_FINISH("4"),
    INSTALL_FINISH("5");
    final String value;
  }
}
