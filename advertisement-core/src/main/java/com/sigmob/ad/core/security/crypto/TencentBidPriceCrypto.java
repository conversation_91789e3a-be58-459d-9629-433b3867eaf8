package com.sigmob.ad.core.security.crypto;

import com.sigmob.ad.core.util.LogUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

public class TencentBidPriceCrypto {

  /** 类型 */
  public static final String RSA_TYPE = "RSA/ECB/PKCS1Padding";
  public static final String PUBLIC_KEY =
      "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALjMT+wA6DuUbhfoa6y048s5MXW+8F6n"
          + "q6LsoaZ1cCuRt08KSFhgy0bjwujKVLKymgQRQQaFRHEjavi3Wwo/PocCAwEAAQ==";
  /** 算法 */
  private static final String KEY_ALGORITHM = "RSA";

  /**
   * 获取转换后的公钥
   *
   * @param publicKey 公钥字符串
   * @return 转换后的公钥
   */
  private static PublicKey getPublicKey(String publicKey) {
    try {
      byte[] byteKey = Base64.decodeBase64(publicKey);
      X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(byteKey);
      KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
      return keyFactory.generatePublic(x509EncodedKeySpec);
    } catch (Exception e) {
      LogUtil.localError("getPublicKey:{} error:", publicKey, e);
    }
    return null;
  }

  /**
   * 明文加密
   *
   * @param publicKey 公钥
   * @param plainText 明文
   * @return 密文
   */
  public static String encrypt(String publicKey, String plainText) {
    String encryptedBase64 = StringUtils.EMPTY;
    try {
      Key key = getPublicKey(publicKey);
      final Cipher cipher = Cipher.getInstance(RSA_TYPE);
      cipher.init(Cipher.ENCRYPT_MODE, key);
      // 转换
      byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
      encryptedBase64 = Base64.encodeBase64String(encryptedBytes);
    } catch (Exception e) {
      LogUtil.localError("TencentBidPriceCrypto.encrypt:{} error:", plainText, e);
    }
    return encryptedBase64;
  }
}
