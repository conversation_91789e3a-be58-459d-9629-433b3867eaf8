package com.sigmob.ad.core.exception;

/** ssp请求业务异常 */
public class SspBusinessException extends AdException {

  public SspBusinessException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public SspBusinessException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
  }

  public SspBusinessException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SspBusinessException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
  }

  public SspBusinessException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SspBusinessException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
  }

  public SspBusinessException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SspBusinessException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
  }

  public SspBusinessException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SspBusinessException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
  }
}
