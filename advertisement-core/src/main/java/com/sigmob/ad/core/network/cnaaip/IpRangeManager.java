/** */
package com.sigmob.ad.core.network.cnaaip;

import com.sigmob.ad.core.device.DeviceUtil;
import java.io.File;

/**
 * 广告协会基础ip库管理类 对cnaa_ip.csv进行读取，并提供查询接口
 *
 * <AUTHOR>
 */
public class IpRangeManager {
  private static IpRangeManager instance = new IpRangeManager();
  private static File ipFile = null;
  private static File ipV6File = null;
  private IpRange ipRegionCodeRange = null;

  private IpV6Range ipv6RegionCodeRange = null;
  private volatile boolean initialized = false;

  private IpRangeManager() {
    ipRegionCodeRange = new IpRange();
    ipv6RegionCodeRange = new IpV6Range();
  }

  public static void init(File basePath) {
    synchronized (RegionManager.class) {
      if (instance.initialized) {
        return;
      }
      ipFile = new File(basePath, Constants.CNAA_BASE_IP_FILE);
      instance.ipRegionCodeRange.init(ipFile);

      ipV6File = new File(basePath, Constants.CNAA_BASE_IPV6_FILE);
      instance.ipv6RegionCodeRange.init(ipV6File);

      instance.initialized = true;
    }
  }

  public static void init(String basePath) {
    synchronized (RegionManager.class) {
      if (instance.initialized) {
        return;
      }
      //			ipFile = new File(basePath,Constants.CNAA_BASE_IP_FILE);
      String filePath = basePath + File.separator + Constants.CNAA_BASE_IP_FILE;
      instance.ipRegionCodeRange.init(filePath);

      filePath = basePath + File.separator + Constants.CNAA_BASE_IPV6_FILE;
      instance.ipv6RegionCodeRange.init(filePath);
      instance.initialized = true;
    }
  }

  public static IpName getIpRegionCode(String ip) {
    if (DeviceUtil.isIpv4(ip)) {
      return instance.ipRegionCodeRange.getIpName(ip);
    } else {
      return instance.ipv6RegionCodeRange.getIpName(ip);
    }
  }
}
