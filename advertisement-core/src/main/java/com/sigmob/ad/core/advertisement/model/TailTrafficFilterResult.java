package com.sigmob.ad.core.advertisement.model;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> @Date 2022/3/22 3:03 PM @Description
 */
@Data
@AllArgsConstructor
public class TailTrafficFilterResult {

  /** 实验id */
  private String experimentId;

  /** 占比，小数表示 */
  private float rate;

  /** 是否过滤请求， true-过滤；false-不过滤 */
  private Boolean filterReq;

  /** 是否需要受到尾流控制 */
  private Boolean filterControl;
}
