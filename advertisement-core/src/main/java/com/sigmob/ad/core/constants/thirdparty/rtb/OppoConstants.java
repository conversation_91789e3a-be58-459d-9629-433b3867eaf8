package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.core.advertisement.ImageType;
import com.sigmob.ad.core.advertisement.VideoType;
import com.sigmob.sigdsp.pb.Version;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** oppo rtb流量常量定义 */
public class OppoConstants {

  /** Oppo rtb api版本 */
  public static final Version OPPO_API_VERSION =
      Version.newBuilder().setMajor(2).setMinor(5).setMicro(5).setVersionStr("2.5.5").build();

  /** Oppo rtb api对应sdk版本 */
  public static final Version OPPO_API_SDK_VERSION =
      Version.newBuilder().setMajor(2).setMinor(19).setMicro(0).setVersionStr("2.19.0").build();

  /** 默认最大请求时间 - 300毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 300;

  /** 激励视屏最大时间 —— 30秒 */
  public static final int MAX_VIDEO_DURATION = 60;

  /** */
  public static final String RESPONSE_ADM_VIDEO = "video";

  public static final String RESPONSE_ADM_SPLASH_SCREEN = "splashScreen";

  public static final String RESPONSE_ADM_NATIVE = "native";

  /** */
  public static final String MACRO_AUCTION_PRICE = "${AUCTION_PRICE}";

  public static final String MACRO_AUCTION_ERROR_CODE = "${ERROR_CODE}";
  //  public static final String WIN_CALLBACK_TEMP =
  //      "https://adstage.sigmob.cn/rtb/callback/oppo/win?ap=${AUCTION_PRICE}";

  //  public static final int CHANNEL_ID_TEMP = 1028;

  public static final List<Integer> SUPPORT_IMAGE_TYPE =
      List.of(ImageType.JPG.getTypeNum(), ImageType.PNG.getTypeNum());

  public static final List<String> SUPPORT_VIDEO_TYPE = List.of(VideoType.MP4.getName());

  /** */
  @Getter
  @AllArgsConstructor
  public enum ImpType {
    BANNER(1, "横幅"),
    NATIVE(8, "原生"),
    NATIVE_SPECIFIC_FEEDS(16, "信息流"),
    PMP(32, "PMP"),
    INTERSTITIAL(64, "插屏（弹窗）"),
    SEARCH(128, "搜索直达"),
    VIDEO(256, "激励视频"),
    SPLASH_SCREEN(512, "开屏");

    final int type;
    final String desc; // 描述
  }

  /** 网络连接类型 */
  @Getter
  @AllArgsConstructor
  public enum ConnectionType {
    UNKNOWN(0, "Unknown"),
    ETHERNET(1, "Ethernet"),
    WIFI(2, "Wifi"),
    CELLULAR_UNKNOWN(3, "Cellular Network – Unknown Generation"),
    CELLULAR_2G(4, "2G"),
    CELLULAR_3G(5, "3G"),
    CELLULAR_4G(6, "4G"),

    CELLULAR_5G(7, "5G");

    final int code;
    final String desc;
  }

  /** 运营商 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    UNKNOWN("unknown"), // 未知
    MOBILE("mobile"), // 移动
    UNICOM("unicom"), // 联通
    TELECOM("telecom"); // 电信

    final String name;
  }

  /** 跟踪事件 */
  @AllArgsConstructor
  @Getter
  public enum TrackEvent {
    SHOW(10001), // 曝光(仅搜索直达类型使用)
    CLICK(10002), // 点击(仅搜索直达类型使用)
    VIDEO_PLAY(10010), // 【视频】开始播放(仅视频类型触发)
    VIDEO_FINISH(10011), // 【视频】播放完成(仅视频类型触发)
    VIDEO_CLICK(10012), // 【视频】视频点击(仅视频类型触发)
    VIDEO_CLOSE(10013); // 【视频】视频关闭(仅视频类型触发)

    final int code;
  }

  /** 素材规格 */
  @AllArgsConstructor
  @Getter
  public enum FormatType {
    HORIZONTAL_IMAGE(1001, "横板大图"),

    NOTIFICATION_BAR(1004, "通知栏"),

    HORIZONTAL_SPLASH(1005, "横板开屏"),

    HORIZONTAL_SMALL_IMAGE(1020, "横板小图"),

    THREE_IMAGES(1030, "组图三张"),

    VERTICAL_SPLASH(1034, "竖板开屏"),

    VERTICAL_IMAGE(1050, "竖版大图"),

    HORIZONTAL_VIDEO(1060, "横板视频"),

    VERTICAL_VIDEO(1063, "竖板视频"),

    ICON(1071, "图标");

    final int code;
    final String desc;
  }

  /** 推广类型 */
  @AllArgsConstructor
  @Getter
  public enum ContentType {
    LINK(1, "链接推广"),

    APP(2, "应用推广"),

    FAST_APP(3, "快应用推广"),

    DEEPLINK_OR_DOWNLOAD(4, "deeplink 推广+应用下载"),

    THIRDPARTY_APP(5, "第三方应用推广(仅联盟部分流量场景支持)"),

    WX_PROGRAM(7, "微信小程序推广");

    final int code;
    final String desc;
  }

  /** 原生数据对象类型 */
  @AllArgsConstructor
  @Getter
  public enum NativeDataType {
    BRAND(1, "赞助商（品牌）"),
    DESC(2, "描述"),
    SCORE(3, "星级评分，5 星制，支持显示半星，示例：“4.5”表示星级评分为四星半；“0”表示无星级评分；取值范围“0~5”，范围之外均取默认值“0”。"),
    APP_NAME(4, "应用名称"),
    APP_ICON(5, "应用 icon 地址"),
    GOOGLE_PLAY_LINK(6, "应用资源 GooglePlayLink,谷歌商店下载链接"),
    APP_SIZE(7, "应用资源大小文本，示例：“43.3MB”表示资源大小为 43.3MB");

    final int code;
    final String desc;
  }

  @AllArgsConstructor
  @Getter
  public enum CostType {
    CPC(2),

    CPM(4);

    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Nbr {
    UNKNOWN(1001, "未知"),

    NO_AD(1002, "无有效广告"),

    REQ_PARAM_ERROR(1003, "无有效参数(缺少必填字段)"),

    NO_VALID_FORMAT_TYPE(1004, "无有效规格"),

    TIMEOUT(1005, "超时"),

    ERROR(1006, "异常响应"),

    FLOOR_PRICE_TOO_HIGH(2001, "底价过高"),

    LOW_VALUE_TRAFFIC(2002, "低质量流量 smartdrop"),

    ANTI_CHEATING(2003, "命中反作弊"),

    BUY_FROM_OTHERS(2004, "已通过其他渠道采买");

    final int code;

    final String desc;
  }
}
