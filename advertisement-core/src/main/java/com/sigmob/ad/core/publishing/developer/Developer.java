package com.sigmob.ad.core.publishing.developer;

import java.util.List;
import java.util.Map;

import lombok.Data;

/** 媒体开发者账户信息 */
@Data
public class Developer {
  /** 开发者账户id */
  private Long id;

  /** 账户名称，一般是邮箱名 */
  private String name;

  /** 所属账户组（公司）id */
  private Long gId;

  /** 账户角色类型，35、开发者账户；36、联运账户 */
  private Integer roleId;

  /** 账户类型，1、公司账户；2、个人账户 */
  private Integer dType;

  /** 账户的状态：0、未激活；1、正常使用；2、禁用 */
  private Integer state;

  /** 前审类型：0、无需前审；1、需要前审 */
  private Integer preCheck;

  /** 前审的广告类型，数组，可能的取值：1、激励视频；2、开屏；4、全凭视频。例如：[1,4] */
  private List<Integer> preCheckAdType;

  /** 流量接入方式：1、sdk接入；2、api接入 */
  private Integer accessMode;

  /** 前审服务id */
  private Integer preCheckServiceId;

  /** 开发者公钥 */
  private String publicKey;

  /** 开发者私钥 */
  private String secretKey;

  /** 开发者有自设底价权限广告类型 */
  private List<Integer> defineFloorAdType;

  /** 开发者网赚属性 */
  private List<String> developerLabels;

  /**
   * 屏蔽广告动作类型
   *
   * <p>第一层key：动作类型： （目前仅支持8 ：快应用）
   *
   * <p>第二层key：dspId 或者 0（adx渠道）；value: 0:开启，1:禁用
   */
  private Map<Integer, Map<Integer, Integer>> interaction;

  /** 是否开启屏蔽素材 */
  private boolean enableShield;

  // ======= logo配置 since http://wiki.sigmob.cn/pages/viewpage.action?pageId=135444838 ===
  /**
   * 流量类型 1:自定义，2:toBid logo，3:预算侧logo
   */
  private Integer toBidType;
  /**
   * Logo URL地址
   */
  private String toBidLogoUrl;
  /**
   * 流量类型 1:自定义，2:sigmob logo，3:预算侧logo
   */
  private Integer sigmobType;
  /**
   * Logo URL地址
   */
  private String sigmobLogoUrl;

  public Developer(Long id) {
    this.id = id;
  }
}
