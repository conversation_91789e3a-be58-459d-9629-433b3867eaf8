package com.sigmob.ad.core.exception;

/** */
public class AdException extends RuntimeException {

  protected int code;

  public AdException() {
    super();
  }

  public AdException(String message) {
    super(message);
  }

  public AdException(String message, Throwable cause) {
    super(message, cause);
  }

  public AdException(Throwable cause) {
    super(cause);
  }

  public AdException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }

  public int getCode() {
    return this.code;
  }
}
