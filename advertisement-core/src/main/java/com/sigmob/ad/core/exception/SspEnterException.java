package com.sigmob.ad.core.exception;

/** ssp请求准入异常 */
public class SspEnterException extends AdException {

  /** 返回给请求方的错误码（通常由请求方定义，如果没有则默认使用sigmob定义错误码） */
  private int returnCode;

  public SspEnterException() {
    this(ErrorCode.NORMAL_ERROR);
  }

  public SspEnterException(int code) {
    this(code, "code:" + code, null, false, false);
    this.code = code;
    this.returnCode = code;
  }

  public SspEnterException(String message) {
    this(ErrorCode.NORMAL_ERROR, message);
  }

  public SspEnterException(int code, String message) {
    this(code, message, null, false, false);
    this.code = code;
    this.returnCode = code;
  }

  public SspEnterException(int code, int returnCode, String message) {
    this(code, message, null, false, false);
    this.code = code;
    this.returnCode = returnCode;
  }

  public SspEnterException(Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, cause);
  }

  public SspEnterException(int code, Throwable cause) {
    this(code, "code:" + code, cause);
    this.code = code;
    this.returnCode = code;
  }

  public SspEnterException(String message, Throwable cause) {
    this(ErrorCode.NORMAL_ERROR, message, cause);
  }

  public SspEnterException(int code, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
    this.returnCode = code;
  }

  public SspEnterException(int code, int returnCode, String message, Throwable cause) {
    this(code, message, cause, false, true);
    this.code = code;
    this.returnCode = returnCode;
  }

  public SspEnterException(
      String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    this(ErrorCode.NORMAL_ERROR, message, cause, enableSuppression, writableStackTrace);
  }

  public SspEnterException(
      int code,
      String message,
      Throwable cause,
      boolean enableSuppression,
      boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
    this.code = code;
    this.returnCode = code;
  }

  public int getReturnCode() {
    return returnCode;
  }

  public void setReturnCode(int returnCode) {
    this.returnCode = returnCode;
  }
}
