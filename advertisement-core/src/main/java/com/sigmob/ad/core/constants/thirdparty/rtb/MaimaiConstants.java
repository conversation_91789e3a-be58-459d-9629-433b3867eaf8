package com.sigmob.ad.core.constants.thirdparty.rtb;

import com.sigmob.ad.adx.rpc.grpc.RtbResponse.InteractType;
import com.sigmob.ad.core.advertisement.AdMacro;
import com.sigmob.ad.core.constants.Constants;
import com.sigmob.sigdsp.pb.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/** 脉脉常量定义 */
public class MaimaiConstants {

  /** 默认最大请求时间 - 200毫秒 */
  public static final int DEFAULT_MAX_REQUEST_TIME = 200;

  /** 脉脉 rtb api版本 */
  public static final Version API_VERSION =
      Version.newBuilder().setMajor(1).setMinor(1).setMicro(0).setVersionStr("1.1.0").build();

  /** 信息流图片最大大小：300KB */
  public static final int IMAGE_MAX_SIZE = 300 * 1024;

  /** 信息流视频最大大小：3MB */
  public static final int VIDEO_MAX_SIZE = 3 * 1024 * 1024;

  /** 默认支持动作类型 */
  public static final List<Integer> supportActionList =
      List.of(
          InteractType.WEB_VIEW_VALUE,
          InteractType.APP_DOWNLOAD_VALUE,
          InteractType.SYS_EXPLORE_VALUE,
          InteractType.DEEP_LINK_VALUE);

  public static final Map<String, String[]> macroMap =
      Map.of(
          Constants.MACRO_INFO_KEYS,
          new String[] {
            AdMacro.CLICK_DOWN_X, AdMacro.CLICK_DOWN_Y, AdMacro.CLICK_UP_X, AdMacro.CLICK_UP_Y
          },
          Constants.MACRO_INFO_VALUES,
          new String[] {
            Macro.DOWN_X.getName(),
            Macro.DOWN_Y.getName(),
            Macro.UP_X.getName(),
            Macro.UP_Y.getName()
          });

  /** 广告模版 */
  @AllArgsConstructor
  @Getter
  public enum Template {

    // 开屏模版
    SPLASH("1"),

    // 静态信息流模版
    FEED("2"),

    // 左图右文模版
    LEFT_IMG_RIGHT_TEXT("10"),

    // banner通用模版
    BANNER("11"),

    // 开屏动态模版
    DYNAMIC_SPLASH("20"),

    // 动态信息流模版
    DYNAMIC_FEED("26");

    final String code;
  }

  /** 竞价类型 */
  @AllArgsConstructor
  @Getter
  public enum BidType {
    CPM(1),

    CPC(2);

    final int code;
  }

  /** 操作系统类型 */
  @AllArgsConstructor
  @Getter
  public enum Os {
    ANDROID(1),

    IOS(2);

    final int code;
  }

  /** 网络方式 */
  @AllArgsConstructor
  @Getter
  public enum Network {
    WIFI(1),
    CELL_4G(2),
    CELL_5G(3),
    UNKNOWN(4);

    final int code;
  }

  /** 设备运营商 */
  @AllArgsConstructor
  @Getter
  public enum Carrier {
    MOBILE(1),

    UNICOM(2),

    TELECOM(3),

    OTHERS(4);

    final int code;
  }

  /** API应答状态码 */
  @AllArgsConstructor
  @Getter
  public enum ResponseStatus {

    // 返回广告OK
    OK(1000),

    // 无广告返回
    NO_AD(1001),

    // 请求参数格式异常
    PARAM_ERROR(1002),

    /** 请求方法异常 */
    REQUEST_METHOD_ERROR(1003),

    /** 必传参数校验失败 */
    REQUIRED_PARAM_MISSING(1004),

    /** 内部服务异常 */
    INTERNAL_SERVER_ERROR(1005);
    final int code;
  }

  @AllArgsConstructor
  @Getter
  public enum Macro {
    /** IP 地址 */
    IP("__IP__"),

    /** androidId原始值 */
    ANDROIDID("__ANDROIDID1__"),

    /** androidId md5值 */
    ANDROIDID_MD5("__ANDROIDID__"),

    /** 手机唯一标识 */
    IMEI("__IMEI__"),

    /** 苹果手机用于追踪用户的广告标识符 */
    IDFA("__IDFA__"),
    /** Android用于追踪用户的广告标识符 */
    OAID("__OAID__"),

    /** 去除分隔符”:”的大写 MAC地址取MD5 */
    MAC_MD5_NO_COMMA("__MAC1__"),

    /** 保留分隔符”:”的大写 MAC地址取MD5摘要 */
    MAC_MD5_COMMA("__MAC__"),

    /** 用户手指按下时的横坐标 */
    DOWN_X("__MM_DX__"),

    /** 用户手指按下时的纵坐标 */
    DOWN_Y("__MM_DY__"),

    /** 用户手指离开时的横坐标 */
    UP_X("__MM_UX__"),

    /** 用户手指离开时的纵坐标 */
    UP_Y("__MM_UY__"),

    /** 当次竞价成功的实际价格，单位分 */
    AUCTION_PRICE("__MM_AUCTION_PRICE__");

    private final String name;
  }
}
