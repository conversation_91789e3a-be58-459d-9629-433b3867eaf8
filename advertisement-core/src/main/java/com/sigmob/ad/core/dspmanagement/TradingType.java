package com.sigmob.ad.core.dspmanagement;

/**
 * <AUTHOR>
 */
public enum TradingType {
  DEAL(2),
  RTB(3),
  ADNETWORK(4),
  PDB(5);
  private final int type;

  TradingType(int type) {
    this.type = type;
  }

  public int getTypeNum() {
    return this.type;
  }

  public static boolean isValidType(int typeNum) {
    TradingType[] types = values();
    for (TradingType type : types) {
      if (type.getTypeNum() == typeNum) {
        return true;
      }
    }
    return false;
  }

  public static TradingType getType(int i) {
    TradingType[] types = values();
    for (TradingType type : types) {
      if (type.getTypeNum() == i) {
        return type;
      }
    }
    return null;
  }
}
