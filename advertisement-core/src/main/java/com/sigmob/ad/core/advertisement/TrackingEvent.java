package com.sigmob.ad.core.advertisement;

public enum TrackingEvent {
  /** 曝光 */
  start,
  /** 广告画面（容器）根据设置条件露出（暂用于信息流广告） */
  ad_show,
  /** 显示endcard */
  show,
  /** 广告画面（容器）结束漏出（暂用于信息流广告） */
  ad_hide,
  /** 点击 */
  click,
  /** 成功观看广告，sdk将发出奖励回调（激励视频） */
  finish,
  /** 视频播放完成 */
  complete,
  /** 关闭广告 */
  ad_close,
  /** 开始下载 */
  download_start,
  /** 下载完成 */
  download_finish,
  /** 安装开始 */
  install_start,
  /** 安装完成 */
  install_finish,
  /** 视频开始播放（暂用于信息流广告） */
  video_start,

  video_restart,
  /** 视频播放25% */
  play_quarter,
  /** 视频播放50% */
  play_two_quarters,
  /** 视频播放75% */
  play_three_quarters,
  /** 视频播放暂停（暂用于信息流广告） */
  video_pause,
  /** 跳过广告 */
  skip,
  load,
  load_success,
  load_failure,
  /** 伴随条点击 */
  companion_click,
  /** 成功调起deeplink */
  open_deeplink,

  can_dp,

  no_can_dp,

  /** 弹出跳过按钮事件 */
  show_skip,
  /** 调起deeplink失败 */
  open_deeplink_failed,
  /** 进入详情页，详情页曝光 */
  landing_page_show,

  /** */
  charge,
  /** 视频播放时点击屏幕事件 */
  full_video_click,

  /** dsp dp回调 */
  dpback,

  open_pkg;
}
