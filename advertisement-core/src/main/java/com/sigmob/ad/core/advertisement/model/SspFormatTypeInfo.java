package com.sigmob.ad.core.advertisement.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;

/**
 * formatType详细规格信息
 *
 * <AUTHOR>
 */
@Data
public class SspFormatTypeInfo {

  private String id;

  private int type;

  private Video video;

  private Image image;

  private Icon icon;

  @SerializedName("cover_image")
  private Image coverImage;

  private Text title;

  private Text desc;

  @Data
  public static class Video {
    private Boolean required;

    @SerializedName("min_duration")
    private Integer minDuration;

    @SerializedName("max_duration")
    private Integer maxDuration;

    private List<Size> size;
  }

  @Data
  public static class Image {
    private Boolean required;

    private List<Size> size;
  }

  @Data
  public static class Icon {
    private Boolean required;

    @SerializedName("min_ratio")
    private Double minRatio;

    @SerializedName("max_ratio")
    private Double maxRatio;
  }

  @Data
  public static class Text {
    private Boolean required;

    @SerializedName("min_length")
    private Integer minLength;

    @SerializedName("max_length")
    private Integer maxLength;
  }

  @Data
  public static class Size {

    @SerializedName("min_ratio")
    private Double minRatio;

    @SerializedName("max_ratio")
    private Double maxRatio;

    private int w;

    private int h;
  }
}
