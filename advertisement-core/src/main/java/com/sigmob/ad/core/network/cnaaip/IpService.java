package com.sigmob.ad.core.network.cnaaip;

import com.google.common.base.Strings;
import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.AddressNotFoundException;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CountryResponse;
import com.maxmind.geoip2.record.Country;
import com.sigmob.ad.core.exception.ErrorCode;
import com.sigmob.ad.core.exception.SigmobException;
import com.sigmob.ad.core.util.LogUtil;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Optional;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
public class IpService {

  @Value("${ipdata.config}")
  private String filePath;

  private DatabaseReader dbr;

  private CnaaIpService cityLookup;

  private boolean initialized = false;

  @PostConstruct
  public void init() throws IOException {
    ClassPathResource ipdataFileResource = new ClassPathResource(filePath);
    //    File ipDataFile = ipdataFileResource.getFile();

    ClassPathResource basePathFileResource = new ClassPathResource("ip");
    //    File basePathFile = basePathFileResource.getFile();
    //    File ipDataFile = ResourceUtils.getFile(filePath);
    //    File basePathFile = ResourceUtils.getFile("ip");
    //    if (!ipDataFile.exists()) {
    //      throw new FileNotFoundException("ipDataFile not exist.");
    //    }
    dbr =
        new DatabaseReader.Builder(ipdataFileResource.getInputStream())
            .withCache(new CHMCache())
            .build();
    cityLookup = new CnaaIpService();
    cityLookup.init(basePathFileResource.getPath());
    initialized = true;
  }

  /**
   * 是否是欧盟用户
   *
   * @param ip
   * @return
   */
  public boolean isInEuropeanUnion(String ip) {
    Optional<CountryResponse> countryResponseOptional = getCountryResponse(ip);
    if (countryResponseOptional.isPresent()) {
      Country country = countryResponseOptional.get().getCountry();
      if (country != null) {
        return country.isInEuropeanUnion();
      } else {
        return false;
      }
    }
    return false;
  }

  /**
   * 通过geoip判断是否属于中国
   *
   * @param ip ip
   * @return boolean
   */
  public boolean isChina(String ip) {
    Optional<CountryResponse> countryResponseOptional = getCountryResponse(ip);
    if (countryResponseOptional.isPresent()) {
      Country country = countryResponseOptional.get().getCountry();
      // isoCode CN
      if (country != null) {
        return isChinaByIsoCode(country.getIsoCode());
      } else {
        return false;
      }
    }
    return false;
  }

  public boolean isChinaByIsoCode(String isoCode) {
    return StringUtils.isNotEmpty(isoCode) && "CN".equals(isoCode);
  }

  public String getCountryIsoCode(String ip) {
    Optional<CountryResponse> countryResponseOptional = getCountryResponse(ip);
    if (countryResponseOptional.isPresent()) {
      Country country = countryResponseOptional.get().getCountry();
      // isoCode CN
      if (country != null) {
        return country.getIsoCode();
      }
    }
    return null;
  }

//  public IpName getIpName(String ip) {
//    return this.cityLookup.getIpName(ip);
//  }
//
//  public String getNullAbleRegionCode(IpV4Name ipName, RegionType regionType) {
//    if (null != ipName) {
//      return RegionManager.getRegionCode(regionType, ipName);
//    }
//    return null;
//  }

  public String getRegionCode(RegionType regionType, String ip) {
    return this.cityLookup.getRegionCode(regionType, ip);
  }

  /**
   * @param ip
   * @return
   * @throws SigmobException
   */
  public Optional<String> getRegionAlphaCode(String ip) {
    Optional<CountryResponse> countryResponse = getCountryResponse(ip);
    if (countryResponse.isPresent()) {
      Country country = countryResponse.get().getCountry();
      if (country != null) {
        return Optional.of(country.getIsoCode());
      }
    }
    return Optional.empty();
  }

  public String getCountryRegionCode(String ip) {
    return this.cityLookup.getCountryRegionCode(ip);
  }

  public String getProvinceRegionCode(String ip) {
    return this.cityLookup.getProvinceRegionCode(ip);
  }

  /**
   * @param ip
   * @return
   * @throws SigmobException
   */
  public Optional<CountryResponse> getCountryResponse(String ip) {
    if (!this.initialized) {
      throw new SigmobException(
          ErrorCode.IP_LOOKUP_SERVICE_HAS_NO_INITIALIZED, "IPLookupService has no initialize!");
    }
    InetAddress ipAddress;
    try {
      ipAddress = InetAddress.getByName(ip);
      CountryResponse res = dbr.country(ipAddress);
      return Optional.of(res);
    } catch (UnknownHostException e) {
      throw new SigmobException(
          ErrorCode.REQUEST_ERROR_CLIENT_IP, "ip is invalid,message:" + e.getMessage());
    } catch (AddressNotFoundException e) {
      /// com.maxmind.geoip2.exception.AddressNotFoundException: The address ************ is not in
      /// the database.
      /// LogUtil.localWarn("IPLookupService error:" + e.getMessage(), e);
    } catch (IOException | GeoIp2Exception e) {
      LogUtil.localError("IPLookupService error:" + e.getMessage(), e);
    }
    return Optional.empty();
  }

  /**
   * @param ip
   * @return
   */
  public String getRegionCodeByIp(String ip) {
    return this.cityLookup.getRegionCodeByIp(ip);
  }

  /**
   * @param ip
   * @param regionType
   * @return
   */
  public String getRegionNameByIp(String ip, RegionType regionType) {
    if (Strings.isNullOrEmpty(ip)) {
      return StringUtils.EMPTY;
    }
    return this.cityLookup.getRegionNameByIp(ip, regionType);
  }
}
